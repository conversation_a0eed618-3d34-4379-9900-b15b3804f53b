package com.icarus.common.cotnode;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.PathNotFoundException;
import lombok.extern.slf4j.Slf4j;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 参数值解析器
 * 用于解析流程节点中的参数表达式，支持多种引用方式：
 * 1. 当前节点参数引用：{current.参数名}
 * 2. 流程变量引用：{参数名}
 * 3. 多层嵌套属性引用：{参数名.子属性.子属性}，如{result.byname.result}
 * 4. 文本内嵌参数引用：任意文本{参数名}任意文本
 *
 * @autor ksw
 * @date 2025-04-17
 */
@Slf4j
public class NodeParameterParser {

    /**
     * 当前节点参数变量引用正则表达式：针对仅引用当前节点变量的匹配规则 {current.*}
     * 1. 参数引用格式：{current.参数名},参数名支持任意字母、数字、下划线、横线
     * 2. 参数嵌套格式：{current.参数名.参数名}
     */
    private final static Pattern CURRENT_PARAMETER_VARIABLE_PATTERN = Pattern
            .compile("^\\{current([._-][a-zA-Z0-9_-]+)+\\}$");

    /**
     * 参数变量引用正则表达式：针对仅引用变量的匹配规则 {参数名}
     * 1. 参数引用格式：{参数名},参数名支持任意字母、数字、下划线、横线
     * 2. 参数嵌套格式：{参数名.参数名},支持多级嵌套，如：{a.b.c}、{result.byname.result}
     */
    private final static Pattern PARAMETER_VARIABLE_PATTERN = Pattern
            .compile("^\\{([a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)*)?\\}$");

    /**
     * 文本参数变量引用正则表达式：针对文本内容中引用变量的匹配规则 任意文本内容{参数名}任意文本内容
     * 1. 参数引用格式：*{参数名}*,参数名支持任意字母、数字、下划线、横线
     * 2. 参数嵌套格式：*{参数名.参数名}*,支持多级嵌套，如：*{a.b.c}*、*{result.byname.result}*
     */
    private final static Pattern TEXT_PARAMETER_VARIABLE_PATTERN = Pattern
            .compile("\\{([a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)*)?\\}");

    /**
     * 解析参数值
     * 根据参数表达式从流程上下文中解析出实际值
     *
     * @param expression 参数值表达式，如{current.name}、{result}、{result.byname.result}
     * @param context    流程上下文，包含所有流程变量
     * @param node       当前节点实例，用于获取当前节点属性
     * @return 解析后的值，如果解析失败则返回null
     */
    protected static Object parserParameterValue(String expression, Map<String, Object> context, BaseNode node) {
        // 参数值表达式、流程上下文不能为空、流程变量上下文不能为空
        if (StrUtil.isEmpty(expression) || CollUtil.isEmpty(context) || ObjUtil.isNull(BaseNode.getFlowData(context)))
            return null;

        // 场景一：当前节点参数变量匹配 - 匹配引用当前节点参数值表达式 {current.*}
        Matcher currentMatcher = CURRENT_PARAMETER_VARIABLE_PATTERN.matcher(expression);
        if (currentMatcher.find()) { // 正则匹配成功 表示当前表达式为 {current.*}
            // 获取匹配到的参数表达式
            String expression_ = currentMatcher.group();
            // 提取属性路径，去掉{current.和}
            String property = expression_.substring("{current.".length(), expression_.length() - 1);
            // 获取当前节点的属性值仅支持 {current.*}按照*去获取值,若出现{current.*.*}则会按照*.*去获取值
            return ObjUtil.isNull(node) ? null : node.getCurrentNodeProperty(property);
        }

        // 场景二：变量匹配 - 匹配完整的参数值表达式 {*}、{*.*}
        Matcher matcher = PARAMETER_VARIABLE_PATTERN.matcher(expression);
        if (matcher.find()) { // 正则匹配成功 表示当前表达式为{*}、{*.*}
            // 获取匹配到的参数表达式
            String expression_ = matcher.group();
            // 提取属性路径，去掉{和}
            String path = expression_.substring(1, expression_.length() - 1);

            try {
                // 检查是否包含点号，表示需要访问嵌套属性（如result.byname.result）
                if (path.contains(".")) {
                    // 分离第一级属性名和后续路径
                    String objName = path.substring(0, path.indexOf("."));
                    String propPath = path.substring(path.indexOf(".") + 1);

                    // 先尝试获取第一级对象
                    String objJsonPath = "$." + objName;
                    Object objValue = null;
                    try {
                        objValue = JsonPath.read(BaseNode.getFlowData(context), objJsonPath);
                    } catch (PathNotFoundException e) {
                        log.debug("Object not found: " + objName);
                        throw e; // 重新抛出异常，让外层catch处理
                    }

                    // 特殊处理：如果对象是JSON字符串，先解析成JSON对象再获取属性
                    // 这种情况适用于某些属性值是序列化的JSON字符串的场景
                    if (objValue instanceof String) {
                        String strValue = (String) objValue;
                        if (strValue.trim().startsWith("{") && strValue.trim().endsWith("}")) {
                            try {
                                // 解析JSON字符串为JSONObject
                                JSONObject jsonObj = JSONUtil.parseObj(strValue);
                                // 使用路径获取嵌套属性值，支持多层级如byname.result
                                return jsonObj.getByPath(propPath);
                            } catch (Exception e) {
                                log.debug("Failed to parse JSON string or get property: " + path, e);
                            }
                        }
                    }
                }

                // 如果上面的特殊处理没有返回结果，或者路径不包含点号，则使用标准JsonPath直接访问
                // JsonPath本身支持多层级属性访问，如$.result.byname.result
                String jsonPath = "$." + path;
                return JsonPath.read(BaseNode.getFlowData(context), jsonPath);
            } catch (PathNotFoundException e) {
                log.error("Failed to parse the parameter value: " + expression_, e);
            }
        }

        // 场景三：文本内嵌变量匹配 - 处理文本中包含的参数引用，如"Hello, {name}!"
        Matcher textMatcher = TEXT_PARAMETER_VARIABLE_PATTERN.matcher(expression);
        String textContent = expression;
        while (textMatcher.find()) {
            // 获取匹配到的变量表达式，如{name}、{result.byname.result}
            String variableExpression = textMatcher.group();

            // 子场景一：检查是否为当前节点变量引用 {current.*}
            Matcher curMatcher = CURRENT_PARAMETER_VARIABLE_PATTERN.matcher(variableExpression);
            if (curMatcher.find()) {
                // 获取匹配到的参数表达式
                String curExpression_ = curMatcher.group();
                // 提取属性路径，去掉{current.和}
                String property = curExpression_.substring("{current.".length(), curExpression_.length() - 1);
                // 获取当前节点的属性值
                Object currentNodeProperty = ObjUtil.isNull(node) ? null : node.getCurrentNodeProperty(property);
                if (ObjUtil.isNotNull(currentNodeProperty)) {
                    // 全局替换(将文本中{current.*}替换为具体值)
                    textContent = textContent.replace(curExpression_, JSONUtil.toJsonStr(currentNodeProperty));
                }
            } else {
                // 子场景二：处理普通变量引用 {*}、{*.*}
                // 提取属性路径，去掉{和}
                String path = variableExpression.substring(1, variableExpression.length() - 1);
                Object variableValue = null;

                // 处理嵌套属性路径，如result.byname.result
                if (path.contains(".")) {
                    // 分离第一级属性名和后续路径
                    String objName = path.substring(0, path.indexOf("."));
                    String propPath = path.substring(path.indexOf(".") + 1);

                    try {
                        // 先获取第一级对象
                        String objJsonPath = "$." + objName;
                        Object objValue = JsonPath.read(BaseNode.getFlowData(context), objJsonPath);

                        // 特殊处理：如果对象是JSON字符串，先解析成JSON对象再获取属性
                        if (objValue instanceof String) {
                            String strValue = (String) objValue;
                            if (strValue.trim().startsWith("{") && strValue.trim().endsWith("}")) {
                                try {
                                    // 解析JSON字符串为JSONObject
                                    JSONObject jsonObj = JSONUtil.parseObj(strValue);
                                    // 使用路径获取嵌套属性值，支持多层级如byname.result
                                    variableValue = jsonObj.getByPath(propPath);
                                } catch (Exception e) {
                                    log.debug("Failed to parse JSON string or get property: " + path, e);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.debug("Failed to process JSON path: " + path, e);
                    }
                }

                // 如果上面的特殊处理没有获取到值，则使用标准JsonPath直接访问
                if (variableValue == null) {
                    String jsonPath = "$." + path;
                    try {
                        variableValue = JsonPath.read(BaseNode.getFlowData(context), jsonPath);
                    } catch (PathNotFoundException e) {
                        log.error("Failed to parse the parameter value: " + variableExpression, e);
                    }
                }

                // 如果成功获取到值，则替换文本中的变量表达式
                if (ObjUtil.isNotNull(variableValue)) {
                    // 全局替换(将文本中{*}/{*.*}替换为具体值)
                    textContent = textContent.replace(variableExpression, JSONUtil.toJsonStr(variableValue));
                }
            }
        }
        return textContent;
    }

}
