package com.icarus.common.cotnode;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.icarus.common.runtime.NodeContext;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 节点基类，所有类型的节点都继承自此类
 * 提供了节点的基本属性和通用方法
 */
@Data
@NoArgsConstructor
@Slf4j
public abstract class BaseNode implements Node{

    // 流程上下文中流程数据KEY
    protected final static String FLOW_DATA = "flowData";
    /** 节点的唯一标识符 */
    @Getter
    @Setter
    private String id;

    /** 节点的输入参数列表 */
    protected List<Parameter> inputs;

    /** 节点的输出参数列表 */
    protected List<Parameter> outputs;

    /** 节点的配置属性 */
    protected Object properties;

    /** 下一个节点的ID列表 */
    protected List<String> nextNodeIds;

    /** 前一个节点的ID列表 */
    protected List<String> prevNodeIds;

    /**
     * 参数类，用于描述节点的输入输出参数
     * 支持参数值的引用解析和参数嵌套
     */
    @Data
    @NoArgsConstructor
    public static class Parameter {
        private String name;        // 参数名称
        //        private Object contextPath;       // 参数值
        private Object value;       // 参数值
        private String type;        // 参数类型（如：string, quote, number等）
        private List<Parameter> children = new ArrayList<>();  // 子参数列表，用于支持复杂的参数结构

        /**
         * 解析参数值，主要用于处理引用类型的参数
         * 例如：如果参数类型是 "quote"，且值为 "nodeA.output.name"
         * 则会从上下文中查找 nodeA 的输出，并获取其中的 name 值
         * @param context 流程上下文，包含所有节点的输入输出数据
         * @return 解析后的参数值
         */
        public Object resolveValue(Map<String, Object> context) {
            /**
             * modify by ksw 2025-04-17 统一使用参数解析器解析参数值
             * 注意: 第三个参数为null所以此方式不支持引用当前节点的参数 例如：{current.*}
             */
            return NodeParameterParser.parserParameterValue(value.toString(), context, null);
        }
    }

    /**
     * 解析所有输入参数的值
     * 遍历所有输入参数，将引用类型的参数解析为实际值
     *
     * @param context 流程上下文
     * @return 解析后的参数映射表，key为参数名，value为解析后的参数值
     */
    protected Map<String, Object> resolveAllInputs(Map<String, Object> context) {
        Map<String, Object> resolvedInputs = new HashMap<>();
        if (inputs != null) {
            for (Parameter input : inputs) {
                resolvedInputs.put(input.getName(), input.resolveValue(context));
            }
        }
        return resolvedInputs;
    }

    /**
     * 添加输出参数
     * 用于节点执行完成后，设置输出结果
     *
     * @param name 参数名称
     * @param value 参数值
     * @param type 参数类型
     */
    protected void addOutput(String name, Object value, String type) {
        if (outputs == null) {
            outputs = new ArrayList<>();
        }

        // 查找是否已存在同名参数
        Parameter existingParam = outputs.stream()
                .filter(p -> p.getName().equals(name))
                .findFirst()
                .orElse(null);

        if (existingParam != null) {
            // 更新已存在的参数
            existingParam.setValue(value);
            existingParam.setType(type);
        } else {
            // 创建新参数
            Parameter param = new Parameter();
            param.setName(name);
            param.setValue(value);
            param.setType(type);

            // 如果是对象类型，创建子参数
            if (value instanceof JSONObject) {
                JSONObject jsonObj = (JSONObject) value;
                for (String key : jsonObj.keySet()) {
                    Parameter childParam = new Parameter();
                    childParam.setName(key);
                    Object childValue = jsonObj.get(key);
                    childParam.setValue(childValue);
                    childParam.setType(getValueType(childValue));
                    param.getChildren().add(childParam);
                }
            }

            outputs.add(param);
        }
    }

    private String getValueType(Object value) {
        if (value == null) return "string";
        if (value instanceof JSONObject) return "object";
        if (value instanceof Number) return "number";
        if (value instanceof Boolean) return "boolean";
        return "string";
    }

    /**
     * 解析属性配置的值
     * 遍历所有属性，将引用类型的值解析为实际值
     *
     * @param context 流程上下文
     * @return 解析后的属性对象
     */
    protected <T> T resolveProperties(Map<String, Object> context, Class<T> propertiesClass) {
        Object properties = this.getProperties();
        if (properties == null) {
            return null;
        }

        // 将properties转换为JSONObject
        JSONObject propsJson = JSONUtil.parseObj(properties);
        JSONObject resolvedProps = new JSONObject();
        log.info("propsJson: {}", propsJson);
        
        // 遍历所有属性
        for (Map.Entry<String, Object> entry : propsJson.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (value instanceof String) {
                /**
                 * modify by ksw 2025-04-17 统一使用参数解析器解析参数值
                 */
                Object result = NodeParameterParser.parserParameterValue(value.toString(), context, this);
                resolvedProps.put(key, result);
            } else {
                // 非字符串类型的值直接使用
                resolvedProps.put(key, value);
            }
        }

        log.info("resolvedProps: {}", resolvedProps);

        try {
            // 将解析后的JSON转换为指定的类型
            return JSONUtil.toBean(resolvedProps, propertiesClass);
        } catch (Exception e) {
            log.error("转换Properties对象失败: {}", e.getMessage(), e);
            throw e;
        }
    }
    /**
     * 执行节点逻辑的抽象方法
     * 每个具体的节点类都需要实现此方法，定义自己的业务逻辑
     *
     * @param context 流程上下文，包含：
     *               1. 请求参数
     *               2. 每个节点的执行结果
     *               3. 流程状态信息
     * @return 更新后的上下文
     */
    public abstract Map<String, Object> execute(Map<String, Object> context);

    /**
     * 根据路径获取值，支持 current 引用和带括号的路径
     * @param context 上下文
     * @param path 路径，可能带有括号，如 "{data.output}" 或 "{current.result}"
     * @return 解析后的值
     */
    protected Object resolveValueByPath(Map<String, Object> context, String path) {
        /**
         * modify by ksw 2025-04-17 统一使用参数解析器解析参数值
         */
        return NodeParameterParser.parserParameterValue(path, context, this);
    }

    /**
     * 获取当前节点的属性值
     */
    protected Object getCurrentNodeProperty(String propertyName) {
        // TODO: 实现获取当前节点属性的逻辑
        // 这里需要根据实际情况来实现，可能需要将一些局部变量改为类的属性
        return null;
    }

    /**
     * 辅助方法：根据路径设置值
     * 用于在 JSONObject 中按照指定路径设置值
     * 如果路径中的某些对象不存在，会自动创建
     *
     * @param flowData 要设置值的 JSONObject
     * @param path 目标路径，可能带有括号，如 "{data.output}"
     * @param value 要设置的值
     */
    protected void setValueByPath(JSONObject flowData, String path, Object value) {
        if (path == null) return;

        // 处理括号
        String realPath = path;
        if (path.startsWith("{") && path.endsWith("}")) {
            realPath = path.substring(1, path.length() - 1);
        }

        String[] parts = realPath.split("\\.");
        JSONObject current = flowData;

        // 遍历路径的每一部分，直到倒数第二个
        for (int i = 0; i < parts.length - 1; i++) {
            String part = parts[i];
            if (!current.containsKey(part)) {
                current.put(part, new JSONObject());
            }
            current = current.getJSONObject(part);
        }

        // 设置最终值
        current.put(parts[parts.length - 1], value);
    }

    /**
     * 处理节点输出，将结果写入上下文
     * @param context 流程上下文
     */
    protected void processOutputs(Map<String, Object> context) {
        List<Parameter> outputs = getOutputs();
        if (outputs != null && !outputs.isEmpty()) {
            JSONObject flowData = (JSONObject) context.getOrDefault(FLOW_DATA, new JSONObject());
            Map<String, Object> processedOutputs = new HashMap<>();

            for (Parameter output : outputs) {
                String sourcePath = output.getValue().toString();
                String targetPath = output.getName();

                /**
                 * modify by ksw 2025-04-17 统一使用参数解析器解析参数值
                 */
                Object value = NodeParameterParser.parserParameterValue(sourcePath, context, this);
                setValueByPath(flowData, targetPath, value);
                processedOutputs.put(targetPath, value);
            }

            // 更新flowData
            context.put(FLOW_DATA, flowData);

            // 更新NodeContext
            String nodeId = getId();
            NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
            nodeContext.setOutputs(new JSONObject(processedOutputs));
            updateNodeContext(context, nodeId, nodeContext);
        }
    }

    /**
     * 更新节点执行状态
     */
    protected void updateExecutionStatus(Map<String, Object> context, String status, String error) {
        context.put(getId() + ".status", status);
        context.put(getId() + ".executionTime", System.currentTimeMillis());
        if (error != null) {
            context.put(getId() + ".error", error);
        }
    }

    protected void updateNodeContext(Map<String, Object> context, String nodeId, NodeContext nodeContext) {
        context.put(nodeId, nodeContext);
    }

    protected NodeContext getOrCreateNodeContext(Map<String, Object> context, String nodeId) {
        NodeContext nodeContext = (NodeContext) context.get(nodeId);
        if (nodeContext == null) {
            nodeContext = new NodeContext();
            nodeContext.setNodeId(nodeId);
            nodeContext.setNodeType(this.getClass().getSimpleName());
            nodeContext.setStartTime(System.currentTimeMillis());
            nodeContext.setExecuteId(IdUtil.getSnowflakeNextIdStr());
            nodeContext.setStatus("running");
            context.put(nodeId, nodeContext);
        }
        return nodeContext;
    }

    /**
     * 设置下一个要执行的节点
     * @param context 上下文
     * @param nextNodeId 下一个节点的ID
     */
    protected void setNextNode(Map<String, Object> context, String nextNodeId) {
        NodeContext nodeContext = getOrCreateNodeContext(context, getId());
        Map<String, Object> metadata = nodeContext.getMetadata();
        if (metadata == null) {
            metadata = new HashMap<>();
            nodeContext.setMetadata(metadata);
        }
        metadata.put("nextNode", nextNodeId);
        updateNodeContext(context, getId(), nodeContext);
    }

    /**
     * 获取下一个要执行的节点
     * @param context 上下文
     * @return 下一个节点的ID
     */
    protected String getNextNode(Map<String, Object> context) {
        NodeContext nodeContext = getNodeContext(context, getId());
        if (nodeContext != null && nodeContext.getMetadata() != null) {
            return (String) nodeContext.getMetadata().get("nextNode");
        }
        return null;
    }

    protected NodeContext getNodeContext(Map<String, Object> context, String nodeId) {
        return (NodeContext) context.get(nodeId);
    }

    protected boolean isJsonArrString(String str) {
        try {
            str = str.trim();
            return (str.startsWith("[") && str.endsWith("]"));
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 设置流程数据
     * @param context 上下文
     * @param headers
     * @param commons
     * @param data
     */
    public static void setFlowData(Map<String, Object> context, Map<String, String> headers, Object commons, Object data){
        JSONObject flowData = new JSONObject();
        if(CollUtil.isNotEmpty(headers))
            flowData.put("headers", headers);
        if(ObjUtil.isNotNull(commons))
            flowData.put("commons", commons);
        if(ObjUtil.isNotNull(data))
            flowData.put("data", parseJsonRecursively(JSONUtil.toJsonStr(data)));
        if(ObjUtil.isNotNull(context))
            context.put(FLOW_DATA, flowData);
    }

    /**
     * 获取流程数据
     * @param context
     * @return
     */
    public static JSONObject getFlowData(Map<String, Object> context){
        Object obj = context.get(FLOW_DATA);
        return ObjUtil.isNotNull(obj) ? (JSONObject) obj : null;
    }

    /**
     * 递归解析JSON字符串
     */
    private static JSONObject parseJsonRecursively(String jsonStr) {
        JSONObject json = JSONUtil.parseObj(jsonStr);
        JSONObject result = new JSONObject();

        for (Map.Entry<String, Object> entry : json.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (value instanceof String) {
                String strValue = (String) value;
                // 尝试解析字符串值
                if (isJsonString(strValue)) {
                    if (isJsonObjectString(strValue)) {
                        result.put(key, parseJsonRecursively(strValue));
                    } else {
                        result.put(key, JSONUtil.parseArray(strValue));
                    }
                } else {
                    result.put(key, value);
                }
            } else if (value instanceof JSONObject) {
                // 递归处理嵌套的对象
                result.put(key, parseJsonRecursively(value.toString()));
            } else  {
                result.put(key, value);
            }
        }
        return result;
    }

    protected static boolean isJsonObjectString(String str) {
        try {
            str = str.trim();
            return (str.startsWith("{") && str.endsWith("}"));

        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 判断字符串是否为JSON格式
     */
    private static boolean isJsonString(String str) {
        try {
            str = str.trim();
            return (str.startsWith("{") && str.endsWith("}")) ||
                    (str.startsWith("[") && str.endsWith("]"));
        } catch (Exception e) {
            return false;
        }
    }
}