package com.icarus.common.runtime;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.Data;
import java.util.HashMap;
import java.util.Map;

@Data
public class NodeContext {
    private String nodeId;                   // 节点ID
    private String nodeType;                 // 节点类型
    private String executeId;                // 执行ID, 每次发起都会生成唯一Id
    private Map<String, Object> inputs;      // 节点输入
    private Map<String, Object> outputs;     // 节点输出
    private Map<String, Object> properties;  // 节点属性
    private Map<String, Object> metadata;    // 节点元数据，用于存储如 nextNode 等信息
    private String status;                   // 执行状态：pending/running/completed/failed
    private String errorMessage;             // 异常信息
    private Long startTime;                  // 开始时间
    private Long endTime;                    // 结束时间

    public void setInputs(JSONObject inputs) {
        this.inputs = convertToMap(inputs);
    }

    public void setOutputs(JSONObject outputs) {
        this.outputs = convertToMap(outputs);
    }

    public void setProperties(JSONObject properties) {
        this.properties = convertToMap(properties);
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }

    public Map<String, Object> getMetadata() {
        if (this.metadata == null) {
            this.metadata = new HashMap<>();
        }
        return this.metadata;
    }

    private Map<String, Object> convertToMap(JSONObject json) {
        if (json == null) {
            return new HashMap<>();
        }
        // 将 JSONObject 转换为普通的 Map，并处理 JSONNull
        Map<String, Object> map = new HashMap<>();
        for (Map.Entry<String, Object> entry : json.entrySet()) {
            Object value = entry.getValue();
            if (value == null || JSONUtil.isNull(value)) {
                map.put(entry.getKey(), null);
            } else if (value instanceof JSONObject) {
                map.put(entry.getKey(), convertToMap((JSONObject) value));
            } else {
                map.put(entry.getKey(), value);
            }
        }
        return map;
    }
} 