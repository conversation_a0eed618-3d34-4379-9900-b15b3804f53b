package com.icarus.plugin.container;

import com.icarus.plugin.spring.BasePlugin;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @description pluginManager容器 用来存放启动完成插件的pluginManager方便获取对应插件的applicationContext 全局单例
 * @date 2025/2/25 9:50
 **/
@Slf4j
public class PluginManagerContainer {

    private PluginManagerContainer(){}

    private static volatile PluginManagerContainer INSTANCE = null;

    private final Map<String, BasePlugin> PLUGIN_MANAGER_CACHE = new ConcurrentHashMap<>();

    public synchronized static PluginManagerContainer getInstance(){
        if(INSTANCE == null){
            INSTANCE = new PluginManagerContainer();
        }
        return INSTANCE;
    }

    public Map<String, BasePlugin> getPluginManagerCache() {
        return PLUGIN_MANAGER_CACHE;
    }

    public BasePlugin getPluginManagerById(String pluginId) {
        return PLUGIN_MANAGER_CACHE.get(pluginId);
    }

    /**
     * 新增插件manager
     * @param pluginId 插件ID
     * @param basePlugin basePlugin
     */
    public synchronized void addPluginManager(String pluginId, BasePlugin basePlugin){
        log.info("插件{},applicationContext容器创建,容器信息:{}",pluginId,basePlugin);
        PLUGIN_MANAGER_CACHE.put(pluginId, basePlugin);
    }

    /**
     * 删除插件manager
     * @param pluginId 插件ID
     */
    public synchronized void removePluginManager(String pluginId){
        BasePlugin basePlugin=PLUGIN_MANAGER_CACHE.get(pluginId);
        log.info("插件{},applicationContext容器销毁,容器信息:{}",pluginId,basePlugin);
        if(basePlugin != null) {
            PLUGIN_MANAGER_CACHE.remove(pluginId,basePlugin);
        }
    }

    /**
     * 获取单个插件上下文
     * @param pluginId 插件ID
     * @return ApplicationContext
     */
    public ApplicationContext getPluginApplicationContext(String pluginId) {
        BasePlugin basePlugin = PLUGIN_MANAGER_CACHE.get(pluginId);
        if(null == basePlugin){
            return null;
        }
        return basePlugin.getApplicationContext();
    }


    /**
     * 获取所有插件上下文
     * @return List<ApplicationContext>
     */
    public List<ApplicationContext> getPluginApplicationContexts() {
        Collection<BasePlugin> values = PLUGIN_MANAGER_CACHE.values();
        if(values.isEmpty()){
            return Collections.emptyList();
        }
        List<ApplicationContext> pluginApplicationContexts = new ArrayList<>();
        for (BasePlugin value : values) {
            pluginApplicationContexts.add(value.getApplicationContext());
        }
        return pluginApplicationContexts;
    }
}
