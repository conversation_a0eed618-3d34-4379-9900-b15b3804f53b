package com.icarus.plugin.spring;

import org.springframework.beans.BeansException;
import org.springframework.boot.env.OriginTrackedMapPropertySource;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.CompositePropertySource;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.EnumerablePropertySource;
import org.springframework.core.env.PropertySource;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collection;
import java.util.LinkedHashSet;
import java.util.Set;

/**
 * SpringContextHolder
 * <AUTHOR>
 */
@Service
@Order(Integer.MIN_VALUE)
public class PluginSpringContextHolder implements ApplicationContextAware {
    public static ApplicationContext applicationContext;

    @SuppressWarnings("unchecked")
    public static <T> T getBean(String name) {
        return (T) applicationContext.getBean(name);
    }

    public static <T> T getBean(Class<T> clzz) {
        return applicationContext.getBean(clzz);
    }

    public static <T> T getBean(String name, Class<T> requiredType) {
        return applicationContext.getBean(name, requiredType);
    }

    public static <T> Collection<T> getBeans(Class<T> clazz) {
        return applicationContext.getBeansOfType(clazz).values();
    }

    public static <T> boolean containsBean(Class<T> clazz) {
        return !applicationContext.getBeansOfType(clazz).values().isEmpty();
    }

    public static boolean containsBean(String name) {
        return applicationContext.containsBean(name);
    }

    public static boolean isSingleton(String name) {
        return applicationContext.isSingleton(name);
    }

    public static Class<? extends Object> getType(String name) {
        return applicationContext.getType(name);
    }

    /**
     * 获取配置文件配置项的值，没有则返回null
     * @param key 配置项key
     * @return 属性值
     */
    public static String getProperty(String key) {
        return applicationContext.getEnvironment().getProperty(key);
    }

    /**
     * 获取配置文件配置项的值
     *
     * @param key 配置项key
     * @param defaultValue 如果获取不到用默认值代替
     * @return 属性值
     */
    public static String getProperty(String key, String defaultValue) {
        return applicationContext.getEnvironment().getProperty(key, defaultValue);
    }

    /**
     * 指定配置项的Class，获取配置文件配置项的值，没有则返回null
     * @param key 配置项key
     * @return 属性值
     */
    public static <T> T getProperty(String key, Class<T> targetType) {
        return applicationContext.getEnvironment().getProperty(key, targetType);
    }

    /**
     * 指定配置项的Class，，获取配置文件配置项的值
     * @param key 配置项key
     * @param defaultValue 如果获取不到用默认值代替
     * @return 属性值
     */
    public static <T> T getProperty(String key, Class<T> targetType, T defaultValue) {
        return applicationContext.getEnvironment().getProperty(key, targetType, defaultValue);
    }

    /**
     * 获取配置文件和者配置中心的所有key
     * 已知兼容的配置中心：apollo、nacos、consul、springcloudConfig
     * @return 配置名称列表
     */
    public static Set<String> getPropertyNames() {
        Set<String> result = new LinkedHashSet<>();
        ConfigurableEnvironment environment = (ConfigurableEnvironment) applicationContext.getEnvironment();
        for (PropertySource<?> propertySource : environment.getPropertySources()) {
            String cloneApolloPropertySources = "cloneApolloPropertySources";
            if (propertySource instanceof OriginTrackedMapPropertySource || propertySource instanceof CompositePropertySource || cloneApolloPropertySources.equals(propertySource.getName())) {
                result.addAll(Arrays.asList(((EnumerablePropertySource<?>) propertySource).getPropertyNames()));
            }
        }
        return result;
    }


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        PluginSpringContextHolder.applicationContext = applicationContext;
    }
}