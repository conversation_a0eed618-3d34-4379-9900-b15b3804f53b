package com.icarus.plugin.spring;

import com.icarus.plugin.container.PluginManagerContainer;
import org.pf4j.PluginWrapper;
import org.pf4j.spring.SpringPlugin;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

/**
 * <AUTHOR>
 * @description pf4j-spring 插件扩展基类 自定义扫描路径
 * @date 2025/2/24 11:40
 **/
public class BasePlugin extends SpringPlugin {
    public BasePlugin(PluginWrapper wrapper) {
        super(wrapper);
    }

    /**
     * 默认创建SpringApplicationContext 方法
     */
    @Override
    protected final ApplicationContext createApplicationContext() {
        AnnotationConfigApplicationContext applicationContext = new AnnotationConfigApplicationContext();
        applicationContext.setClassLoader(getWrapper().getPluginClassLoader());
        applicationContext.scan(this.scanPackage());
        applicationContext.refresh();
        return applicationContext;
    }

    @Override
    public final void stop() {
        super.stop();
        afterStop();
    }

    private void afterStop(){
        PluginManagerContainer.getInstance().removePluginManager(getWrapper().getPluginId());
    }

    @Override
    public final void start() {
        afterStart();
    }

    private void afterStart(){
        PluginManagerContainer.getInstance().addPluginManager(getWrapper().getPluginId(),this);
        PluginManagerContainer.getInstance().getPluginApplicationContext(getWrapper().getPluginId());
    }

    @Override
    public final void delete() {
        afterDelete();
    }

    private void afterDelete(){
        PluginManagerContainer.getInstance().removePluginManager(getWrapper().getPluginId());
    }

    /**
     * springContext to scan bean by package default is empty,
     * use @see SpringConfiguration to load bean
     * @return String
     */
    public final String scanPackage(){
        return this.getClass().getPackage().getName();
    }
}
