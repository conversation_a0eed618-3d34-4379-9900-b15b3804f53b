package com.icarus.plugin.spring;

import com.icarus.plugin.container.PluginManagerContainer;
import com.icarus.plugin.utils.SpringBeanUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 默认bean操作对象
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Slf4j
public class DefaultBeanOperator implements BeanOperator {

    private final PluginManagerContainer pluginManagerContainer;

    /**
     * 通过bean名称得到插件的bean。（Spring管理的bean）
     * @param name 插件bean的名称。spring体系中的bean名称。可以通过注解定义，也可以自定义生成。具体可百度
     * @param <T> bean的类型
     * @return 返回bean
     */
    @Override
    public <T> T getBean(String name){
        return getBean(name, true);
    }

    @Override
    public <T> T getBean(Class<T> aClass) {
        return getBean(aClass, true);
    }

    @Override
    public <T> T getPluginBean(String name) {
        return getBean(name, false);
    }

    /**
     * 在主程序中定义的接口。插件或者主程序实现该接口。可以该方法获取到实现该接口的所有实现类。（Spring管理的bean）
     * @param aClass 接口的类
     * @param <T> bean的类型
     * @return List
     */
    @Override
    public <T> List<T> getBeans(Class<T> aClass){
        return getBeans(aClass, 3);
    }

    @Override
    public <T> List<T> getMainBeans(Class<T> aClass) {
        return getBeans(aClass, 1);
    }

    /**
     * 在主程序中定义的接口。获取插件中实现该接口的实现类。（Spring管理的bean）
     * @param aClass 接口的类
     * @param <T> bean的类型
     * @return List
     */
    @Override
    public <T> List<T> getPluginBeans(Class<T> aClass) {
        return getBeans(aClass, 2);
    }

    @Override
    public <T> List<T> getPluginBeans(String pluginId, Class<T> aClass) {
        ApplicationContext pluginApplicationContext = pluginManagerContainer.getPluginApplicationContext(pluginId);
        if(pluginApplicationContext == null){
            return Collections.emptyList();
        }
        return SpringBeanUtils.getBeans(pluginApplicationContext, aClass);
    }


    private <T> T getBean(String name, boolean haveParent){
        List<ApplicationContext> pluginApplicationContexts = pluginManagerContainer.getPluginApplicationContexts();
        if(haveParent){
            ApplicationContext mainApplicationContext = PluginSpringContextHolder.applicationContext;
            pluginApplicationContexts.add(mainApplicationContext);
        }
        for (ApplicationContext pluginApplicationContext : pluginApplicationContexts) {
            if(pluginApplicationContext.containsBean(name)){
                return (T) pluginApplicationContext.getBean(name);
            }
        }
        return null;
    }

    private <T> T getBean(Class<T> aClass,  boolean haveParent) {
        List<ApplicationContext> pluginApplicationContexts = pluginManagerContainer.getPluginApplicationContexts();
        if(haveParent){
            ApplicationContext mainApplicationContext = PluginSpringContextHolder.applicationContext;
            pluginApplicationContexts.add(mainApplicationContext);
        }
        for (ApplicationContext pluginApplicationContext : pluginApplicationContexts) {
            try {
                return pluginApplicationContext.getBean(aClass);
            } catch (Exception e){
                // 忽略
            }
        }
        return null;
    }

    /**
     * 获取多个bean.
     * @param aClass 接口或者抽象类类类型
     * @param type 1 获取主程序的, 2 获取插件中的, 3 获取所有的
     * @param <T> 类类型
     * @return List
     */
    private <T> List<T> getBeans(Class<T> aClass, int type) {
        List<ApplicationContext> pluginApplicationContexts = new ArrayList<>(1);
        if(type == 1){
            ApplicationContext mainApplicationContext = PluginSpringContextHolder.applicationContext;
            pluginApplicationContexts.add(mainApplicationContext);
        } else if(type == 2){
            pluginApplicationContexts.addAll(pluginManagerContainer.getPluginApplicationContexts());
        } else if(type == 3){
            ApplicationContext mainApplicationContext = PluginSpringContextHolder.applicationContext;
            pluginApplicationContexts.add(mainApplicationContext);
            pluginApplicationContexts.addAll(pluginManagerContainer.getPluginApplicationContexts());
        } else {
            return Collections.emptyList();
        }
        List<T> result = new ArrayList<>();
        for (ApplicationContext pluginApplicationContext : pluginApplicationContexts) {
            List<T> pluginBeans = SpringBeanUtils.getBeans(pluginApplicationContext, aClass);
            if(!pluginBeans.isEmpty()){
                result.addAll(pluginBeans);
            }
        }
        return result;
    }
}
