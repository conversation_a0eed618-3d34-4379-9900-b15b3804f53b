package com.icarus.plugin.info;

import lombok.AllArgsConstructor;
import lombok.Data;
import org.pf4j.PluginDescriptor;
import org.pf4j.PluginState;


/**
 * 插件信息
 * <AUTHOR>
 */
@AllArgsConstructor
@Data
public class PluginInfo {
    /**
     * 插件基本信息
     */
    private PluginDescriptor pluginDescriptor;
    /**
     * 插件状态
     */
    private PluginState pluginState;
    /**
     * 插件路径
     */
    private String path;
}
