package com.icarus.plugin.properties;

import jakarta.annotation.PostConstruct;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * <AUTHOR>
 * @description 插件相关配置
 * @date 2025/2/24 13:47
 **/
@Data
@ConfigurationProperties(prefix = "plugin")
@Slf4j
public class PluginProperties {
    /**
     * 插件的路径
     */
    private String installPath = "./plugins";

    private String uploadTempPath = "/opt/plugin/upload";

    @PostConstruct
    public void init() {
        Path path = Paths.get(installPath);
        if(!path.toFile().exists()){
            path.toFile().mkdirs();
        }
        path = Paths.get(uploadTempPath);
        if(!path.toFile().exists()){
            path.toFile().mkdirs();
        }
        log.info("plugin properties init");

    }
}
