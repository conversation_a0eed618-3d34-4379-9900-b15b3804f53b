/*
 * Copyright (C) 2012-present the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.icarus.plugin.configuration;

import org.pf4j.spring.SpringPluginManager;
import com.icarus.plugin.container.PluginManagerContainer;
import com.icarus.plugin.properties.PluginProperties;
import com.icarus.plugin.spring.DefaultBeanOperator;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Primary;

import java.nio.file.Paths;

/**
 * 插件自动配置类
 * <AUTHOR>
 */
@Configuration
@EnableConfigurationProperties(PluginProperties.class)
public class SpringConfiguration {

    /**
     * 注入springPluginManager
     * @param properties
     * @return
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean(SpringPluginManager.class)
    @DependsOn("pluginSpringContextHolder")
    public SpringPluginManager pluginManager(PluginProperties properties) {
        return new SpringPluginManager(Paths.get(properties.getInstallPath()));
    }

    /**
     * 上下文容器
     * @return PluginManagerContainer
     */
    @Bean
    @Primary
    public PluginManagerContainer pluginManagerContainer() {
        return PluginManagerContainer.getInstance();
    }

    /**
     * <AUTHOR>
     * @description bean操作对象
     * @param pluginManagerContainer
     * @return com.yss.fomp.core.plugin.spring.BeanOperator
     * @date 2025/2/25 10:12
     **/

    @Bean
    @Primary
    public DefaultBeanOperator defaultBeanOperator(PluginManagerContainer pluginManagerContainer){
        return new DefaultBeanOperator(pluginManagerContainer);
    }

}
