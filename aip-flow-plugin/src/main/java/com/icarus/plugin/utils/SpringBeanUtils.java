package com.icarus.plugin.utils;

import org.springframework.context.ApplicationContext;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * spring bean工具类
 * <AUTHOR>
 */
public class SpringBeanUtils {
    /**
     * 得到ApplicationContext中的bean的实现
     * @param applicationContext ApplicationContext
     * @param aClass 接口或者抽象类型bean类型
     * @param <T> 接口或者抽象类型bean类型
     * @return 所有的实现对象
     */
    public static <T> List<T> getBeans(ApplicationContext applicationContext, Class<T> aClass) {
        Map<String, T> beansOfTypeMap = applicationContext.getBeansOfType(aClass);
        if(beansOfTypeMap.isEmpty()){
            return Collections.emptyList();
        }
        return new ArrayList<>(beansOfTypeMap.values());
    }
}
