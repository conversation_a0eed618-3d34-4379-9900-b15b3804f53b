package com.icarus.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.URL;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 扩展SPI加载器.
 * <AUTHOR>
 * @date 2025-02-27
 */
public class SpiUtil<T> {

    private static final Logger logger = LoggerFactory.getLogger(SpiUtil.class);

    private static final String SERVICES_DIRECTORY = "META-INF/services/";

    //对应接口类的加载器
    private static final ConcurrentMap<Class<?>, SpiUtil<?>> EXTENSION_LOADERS = new ConcurrentHashMap<Class<?>, SpiUtil<?>>();

    //对应接口的实现类的class与对象
    private static final ConcurrentMap<Class<?>, Object> EXTENSION_INSTANCES = new ConcurrentHashMap<Class<?>, Object>();

    //接口Class
    private final Class<?> type;

    //实现类与name[spi扩展文件中name=***]的对应关系
    private final ConcurrentMap<Class<?>, String> cachedNames = new ConcurrentHashMap<Class<?>, String>();

    //name与实现类[spi扩展文件中name=***]的对应关系
    private Map<String, Class<?>> cachedClasses = null;

    //name[spi扩展文件中name=***] 与 实现类对象 的对应关系
    private final ConcurrentMap<String, Object> cachedInstances = new ConcurrentHashMap<String, Object>();

    private SpiUtil(Class<?> type) {
        this.type = type;
    }

    @SuppressWarnings("unchecked")
    public static <T> SpiUtil<T> getExtensionLoader(Class<T> type) {
        if (type == null)
            throw new IllegalArgumentException("Extension type == null");
        if (!type.isInterface()) {
            throw new IllegalArgumentException("Extension type[" + type + "] is not interface!");
        }
        SpiUtil<T> loader = (SpiUtil<T>) EXTENSION_LOADERS.get(type);
        if (loader == null) {
            EXTENSION_LOADERS.put(type, new SpiUtil<T>(type));
            loader = (SpiUtil<T>) EXTENSION_LOADERS.get(type);
        }
        return loader;
    }

    //获取类加载器
    private static ClassLoader findClassLoader() {
        return SpiUtil.class.getClassLoader();
    }

    //通过对象获取[spi扩展文件中对应name]
    public String getExtensionName(T extensionInstance) {
        return getExtensionName(extensionInstance.getClass());
    }

    //通过class获取[spi扩展文件中对应name]
    public String getExtensionName(Class<?> extensionClass) {
        return cachedNames.get(extensionClass);
    }

    /**
     * 根据name查找扩展类
     */
    @SuppressWarnings("unchecked")
    public synchronized T getExtension(String name) {
        if (name == null || name.length() == 0)
            throw new IllegalArgumentException("Extension name == null");
       Object instance = cachedInstances.get(name);
        if (instance == null) {
            instance = createExtension(name);
            cachedInstances.put(name, instance);
        }
        return (T) instance;
    }

    /**
     * 根据name查找扩展类返回全类名
     */
    public String getExtensionName(String name){
        return getExtensionClass(name).getCanonicalName();
    }

    public boolean hasExtension(String name) {
        if (name == null || name.length() == 0)
            throw new IllegalArgumentException("Extension name == null");
        try {
            this.getExtensionClass(name);
            return true;
        } catch (Throwable t) {
            return false;
        }
    }

    //返回支持的所有扩展name
    public Set<String> getSupportedExtensionsName() {
        Map<String, Class<?>> clazzes = getExtensionClasses();
        return Collections.unmodifiableSet(new TreeSet<String>(clazzes.keySet()));
    }

    @SuppressWarnings("unchecked")
    private T createExtension(String name) {
        Class<?> clazz = getExtensionClasses().get(name);
        if (clazz == null) {
            throw new IllegalStateException("No such extension " + type.getName() + " by name [" + name+ "]");
        }
        try {
            T instance = (T) EXTENSION_INSTANCES.get(clazz);
            if (instance == null) {
                EXTENSION_INSTANCES.putIfAbsent(clazz, clazz.newInstance());
                instance = (T) EXTENSION_INSTANCES.get(clazz);
            }
            return instance;
        } catch (Throwable t) {
            throw new IllegalStateException("Extension instance[name: " + name + ", class: " +
                    type + "]  could not be instantiated: " + t.getMessage(), t);
        }
    }

    private Class<?> getExtensionClass(String name) {
        if (type == null)
            throw new IllegalArgumentException("Extension type == null");
        if (name == null)
            throw new IllegalArgumentException("Extension name == null");
        Class<?> clazz = getExtensionClasses().get(name);
        if (clazz == null)
            throw new IllegalStateException("No such extension " + name + " for " + type.getName() + "!");
        return clazz;
    }

    private synchronized Map<String, Class<?>> getExtensionClasses() {
        if (CollectionUtils.isEmpty(cachedClasses)) cachedClasses = loadExtensionClasses();
        return cachedClasses;
    }

    // synchronized in getExtensionClasses
    private Map<String, Class<?>> loadExtensionClasses() {
        Map<String, Class<?>> extensionClasses = new HashMap<String, Class<?>>();
        loadDirectory(extensionClasses, SERVICES_DIRECTORY);
        return extensionClasses;
    }

    //加载目录下资源
    private void loadDirectory(Map<String, Class<?>> extensionClasses, String dir) {
        String fileName = dir + type.getName();
        try {
            Enumeration<URL> urls;
            ClassLoader classLoader = findClassLoader();
            if (classLoader != null) {
                urls = classLoader.getResources(fileName);
            } else {
                urls = ClassLoader.getSystemResources(fileName);
            }
            if (urls != null) {
                while (urls.hasMoreElements()) {
                    logger.info("load Resource [{}]", fileName);
                    URL resourceURL = urls.nextElement();
                    loadResource(extensionClasses, classLoader, resourceURL);
                }
            }
        } catch (Throwable t) {
            logger.error("Exception when load extension class[interface: " + type + ", description file: " + fileName + "].", t);
        }
    }

    //加载资源
    private void loadResource(Map<String, Class<?>> extensionClasses, ClassLoader classLoader, URL resourceURL) {
        try {
            BufferedReader reader = new BufferedReader(new InputStreamReader(resourceURL.openStream(), "utf-8"));
            try {
                String line;
                while ((line = reader.readLine()) != null) {
                    final int ci = line.indexOf('#');
                    if (ci >= 0) line = line.substring(0, ci);
                    line = line.trim();
                    if (line.length() > 0) {
                        try {
                            String name = null;
                            int i = line.indexOf('=');
                            if (i > 0) {
                                name = line.substring(0, i).trim();
                                line = line.substring(i + 1).trim();
                            }
                            if (line.length() > 0) {
                                loadClass(extensionClasses, resourceURL, Class.forName(line, true, classLoader), name);
                            }
                        } catch (Throwable t) {
                            logger.error("Failed to load extension class[interface:{}, class line:{}] in {}, cause: {}", type, line, resourceURL, t.getMessage());
                        }
                    }
                }
            } finally {
                reader.close();
            }
        } catch (Throwable t) {
            logger.error("Exception when load extension class[interface: " + type + ", class file: " + resourceURL + "] in " + resourceURL, t);
        }
    }

    private void loadClass(Map<String, Class<?>> extensionClasses, URL resourceURL, Class<?> clazz, String name) throws NoSuchMethodException {
        //TODO 判断type是否为clazz的父类或接口
        if (!type.isAssignableFrom(clazz)) {
            throw new IllegalStateException("Error when load extension class[interface: " +
                    type + ", class line: " + clazz.getName() + "], class "
                    + clazz.getName() + "is not subtype of interface.");
        }
        if(null == cachedNames.get(clazz)) cachedNames.put(clazz, name);
        if(null == extensionClasses.get(name)) extensionClasses.put(name, clazz);
    }

    @Override
    public String toString() {
        return this.getClass().getName() + "[" + type.getName() + "]";
    }

}