llm-client:
  defaults:
    chat: deepseek-32B
    embedding: BGE
  models:
    openai:
      apiKey: "sk-e7EOBec9WzR3ZaGf5b22BfDbA8F2494699Ed70D4Af356112"
      baseUrl: "http://one-api.yssdata.net/"
      aiType: OPEN_AI
      deployment: gpt-35-turbo-16k
      model: gpt-3.5-turbo-16k
    azure:
      apiKey: "********************************"
      baseUrl: "https://gpt4vatjpe.openai.azure.com/openai/deployments/"
      aiType: AZURE_AI
      deployment: gpt-35-turbo-16k
      model: gpt-3.5-turbo-16k
    GPT4:
      apiKey: "sk-e7EOBec9WzR3ZaGf5b22BfDbA8F2494699Ed70D4Af356112"
      baseUrl: "http://one-api.yssdata.net/"
      aiType: OPEN_AI
      deployment: gpt-4
      model: gpt-4
    llama3:
      apiKey: "sk-TFhyy2kUSgAdk3do4b294b1097Ec4fCe9bD7Ee6a1dD64518"
      baseUrl: "http://1.94.144.12:30000/"
      aiType: OPEN_AI
      deployment: llama3.1:8b
      model: llama3.1:8b
    M3E:
      apiKey: "sk-e7EOBec9WzR3ZaGf5b22BfDbA8F2494699Ed70D4Af356112"
      baseUrl: "http://1.94.144.12:33001/"
      aiType: OPEN_AI
      deployment: M3E
      model: M3E
    BGE:
      apiKey: "sk-TFhyy2kUSgAdk3do4b294b1097Ec4fCe9bD7Ee6a1dD64518"
      baseUrl: "http://10.0.76.30:30000/"
      aiType: OPEN_AI
      deployment: bge
      model: bge
    BGE-OLD:
      apiKey: "sk-XJCXrNRnEVJZ5wJNEa77Bd0aA5B8400d8940D6700cC71035"
      baseUrl: "http://1.94.144.12:33001/"
      aiType: OPEN_AI
      deployment: BGE
      model: BGE
    deepseek-32B:
      apiKey: "sk-TFhyy2kUSgAdk3do4b294b1097Ec4fCe9bD7Ee6a1dD64518"
      baseUrl: "http://10.0.76.30:30000/"
      aiType: OPEN_AI
      model: ds_32b
    Qwen3-8B:
      apiKey: "sk-TFhyy2kUSgAdk3do4b294b1097Ec4fCe9bD7Ee6a1dD64518"
      baseUrl: "http://10.0.76.30:30000/"
      aiType: OPEN_AI
      model: qwen3_8b
    VL_7B:
      apiKey: "sk-TFhyy2kUSgAdk3do4b294b1097Ec4fCe9bD7Ee6a1dD64518"
      baseUrl: "http://10.0.76.30:30000/"
      aiType: OPEN_AI
      model: qwen25_vl_7b