package com.icarus;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication
@EnableScheduling
@EnableConfigurationProperties
@EnableTransactionManagement
@MapperScan({"com.icarus.**.mapper"})
public class AIPBigGroupApplication {

	public static void main(String[] args) {
		SpringApplication.run(AIPBigGroupApplication.class, args);
	}
}
