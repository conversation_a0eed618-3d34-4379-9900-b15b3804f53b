<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.metateam.biggroup</groupId>
        <artifactId>aip-biggroup-platform</artifactId>
        <version>2.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>aip-biggroup-starter</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.metateam.icarus</groupId>
            <artifactId>icarus-starter-vlm</artifactId>
        </dependency>

        <dependency>
            <groupId>com.metateam.icarus</groupId>
            <artifactId>icarus-starter-license</artifactId>
        </dependency>

        <dependency>
            <groupId>com.metateam.biggroup</groupId>
            <artifactId>aip-designcanvas</artifactId>
        </dependency>

        <dependency>
            <groupId>com.metateam.biggroup</groupId>
            <artifactId>aip-runtime</artifactId>
        </dependency>
        <dependency>
            <groupId>com.metateam.biggroup</groupId>
            <artifactId>aip-testplatform</artifactId>
        </dependency>
        <dependency>
            <groupId>com.metateam.biggroup</groupId>
            <artifactId>aip-blueprint</artifactId>
        </dependency>
        <dependency>
            <groupId>com.metateam.biggroup</groupId>
            <artifactId>aip-sysmanager</artifactId>
        </dependency>
        <dependency>
            <groupId>com.metateam.biggroup</groupId>
            <artifactId>aip-monitor</artifactId>
        </dependency>
        <dependency>
            <groupId>com.metateam.biggroup</groupId>
            <artifactId>aip-manager-log</artifactId>
        </dependency>
        <dependency>
            <groupId>com.metateam.biggroup</groupId>
            <artifactId>aip-devops-docker</artifactId>
        </dependency>
        <dependency>
            <groupId>com.metateam.biggroup</groupId>
            <artifactId>aip-gateway</artifactId>
        </dependency>
        <dependency>
            <groupId>com.metateam.biggroup</groupId>
            <artifactId>aip-bi-workbench</artifactId>
        </dependency>
    </dependencies>

    <build>
        <!-- 指定启动类 -->
        <plugins>
            <plugin>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>*.yml</exclude>
                        <exclude>*.yaml</exclude>
                        <exclude>aip-license/**</exclude>
                    </excludes>
                    <archive>
                        <manifest>
                            <mainClass>com.icarus.AIPBigGroupApplication</mainClass>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>

            <!-- smart-doc 插件 -->
            <plugin>
                <groupId>com.github.shalousun</groupId>
                <artifactId>smart-doc-maven-plugin</artifactId>
                <version>2.7.3</version>
                <configuration>
                    <configFile>src/main/resources/smart-doc.json</configFile>
                    <excludes>
                        <!-- 排除示例类 -->
                        <exclude>com/example/demo/*</exclude>
                    </excludes>
                </configuration>
            </plugin>

            <!-- 打包脚本-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>clean-temp</id>
                        <phase>clean</phase>
                        <configuration>
                            <target>
                                <echo message="清理aip-deploy/aip-runtime目录" level="info"/>
                                <delete dir="../aip-deploy/aip-runtime/apps"/>
                                <delete dir="../aip-deploy/aip-runtime/config"/>
                                <delete dir="../aip-deploy/aip-runtime/exlibs"/>
                                <delete dir="../aip-deploy/aip-runtime/logs"/>
                            </target>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>copy</id>
                        <phase>package</phase>
                        <configuration>
                            <target>
                                <echo message="清理目录" level="info"/>
                                <delete dir="../aip-deploy/aip-runtime/apps"/>
                                <delete dir="../aip-deploy/aip-runtime/config"/>
                                <delete dir="../aip-deploy/aip-runtime/exlibs"/>

                                <echo message="复制给docker工程:starter" level="info"/>
                                <copy file="${project.build.directory}/${project.artifactId}-${project.version}.jar"
                                      tofile="../aip-deploy/aip-runtime/apps/${project.artifactId}-${project.version}.jar"
                                      overwrite="true"/>

                                <echo message="复制给docker工程:apps" level="info"/>
                                <copy todir="../aip-deploy/aip-runtime/apps/"> <!-- 目标目录 -->
                                    <fileset dir="${project.build.directory}/icarus-dist/exlibs/"> <!-- 源文件目录 -->
                                        <include name="aip-*"/> <!-- 包含文件 -->
                                    </fileset>
                                </copy>

                                <echo message="复制给docker工程:资源文件" level="info"/>
                                <copy todir="../aip-deploy/aip-runtime/config/"> <!-- 目标目录 -->
                                    <fileset dir="${project.build.directory}/../../aip-deploy/aip-configs/"> <!-- 源文件目录 -->
                                        <include name="**/*"/> <!-- 包含文件 -->
                                    </fileset>
                                </copy>

                                <echo message="复制给docker工程:exlibs" level="info"/>
                                <copy todir="../aip-deploy/aip-runtime/exlibs/"> <!-- 目标目录 -->
                                    <fileset dir="${project.build.directory}/icarus-dist/exlibs/"> <!-- 源文件目录 -->
                                        <include name="**/*"/> <!-- 包含文件 -->
                                        <exclude name="aip-*"/>
                                    </fileset>
                                </copy>
                            </target>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>