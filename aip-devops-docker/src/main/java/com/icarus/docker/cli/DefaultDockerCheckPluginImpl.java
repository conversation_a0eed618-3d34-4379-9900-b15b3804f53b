package com.icarus.docker.cli;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.github.dockerjava.api.DockerClient;
import com.github.dockerjava.api.command.PingCmd;
import com.github.dockerjava.api.exception.UnauthorizedException;
import com.github.dockerjava.api.model.AuthConfig;
import com.github.dockerjava.api.model.AuthResponse;
import com.github.dockerjava.api.model.Info;
import com.github.dockerjava.api.model.Version;
import com.github.dockerjava.core.DefaultDockerClientConfig;
import com.github.dockerjava.core.DockerClientConfig;
import com.github.dockerjava.core.DockerClientImpl;
import com.github.dockerjava.core.RemoteApiVersion;
import com.github.dockerjava.httpclient5.ApacheDockerHttpClient;
import com.github.dockerjava.transport.DockerHttpClient;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.net.ssl.SSLHandshakeException;
import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.net.URI;
import java.time.Duration;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * docker 验证 实现
 */
@Slf4j
@Service
public class DefaultDockerCheckPluginImpl {

    public  <T> T execute(Object main, Map<String, Object> parameter, Class<T> cls) {
        Object execute = this.execute(main, parameter);
        return this.convertResult(execute, cls);
    }

    public Object execute(Object main, Object... parameters) throws Exception {
        int length = parameters.length;
        Map<String, Object> map = new HashMap(length / 2);

        for(int i = 0; i < length; i += 2) {
            map.put(parameters[i].toString(), parameters[i + 1]);
        }

        return this.execute(main, (Map)map);
    }

    public  <T> T convertResult(Object execute, Class<T> cls) {
        if (execute == null) {
            return null;
        } else {
            Class<?> aClass = execute.getClass();
            if (ClassUtil.isSimpleValueType(aClass)) {
                return (T)Convert.convert(aClass, execute);
            } else if (execute instanceof JSONObject) {
                JSONObject jsonObject = (JSONObject)execute;
                return JSONUtil.toBean(jsonObject.toString(), cls);
            } else {
                return (T)execute;
            }
        }
    }

    @SneakyThrows
    public Object execute(Object main, Map<String, Object> parameter){
        String type = main.toString();
        switch (type) {
            case "certPath":
                return this.checkCertPath(parameter);
            case "apiVersions":
                return getApiVersions();
            case "host":
                return this.checkUrl(parameter);
            case "ping":
                return this.checkPing(parameter);
            case "info":
                return this.infoCmd(parameter);
            case "testLocal":
                return this.testLocal();
            case "testAuth":
                return this.auth(parameter);
            case "hasDependPlugin":
                return DockerBuild.hasDependPlugin(parameter);
            default:
                break;
        }
        return null;
    }


    private JSONObject auth(Map<String, Object> parameter) {
        DockerClient dockerClient = DockerUtil.get(parameter);
        AuthConfig authConfig = dockerClient.authConfig();
        AuthResponse exec = dockerClient.authCmd().withAuthConfig(authConfig).exec();
        return DockerUtil.toJSON(exec);
    }

    private String testLocal() throws IOException {
        DockerClientConfig config = DefaultDockerClientConfig.createDefaultConfigBuilder().build();
        URI dockerHost = config.getDockerHost();
        String host = dockerHost.toString();
        try (DockerHttpClient httpClient = new ApacheDockerHttpClient.Builder()
            .dockerHost(dockerHost).connectionTimeout(Duration.ofSeconds(3)).build()) {
            try (PingCmd pingCmd = DockerClientImpl.getInstance(config, httpClient).pingCmd()) {
                pingCmd.exec();
                return host;
            }
        }
    }

    /**
     * 获取 docker info 信息
     *
     * @param parameter 参数
     * @return info
     */
    private JSONObject infoCmd(Map<String, Object> parameter) {
        DockerClient dockerClient = DockerUtil.get(parameter);
        Info exec = dockerClient.infoCmd().exec();
        return DockerUtil.toJSON(exec);
    }

    /**
     * 获取版本信息
     *
     * @param parameter 参数
     * @return true 可以通讯
     */
    private Version pullVersion(Map<String, Object> parameter) {
        DockerClient dockerClient = DockerUtil.get(parameter);
        return dockerClient.versionCmd().exec();

    }

    /**
     * 检查 ping docker 超时时间 5 秒
     *
     * @param parameter 参数
     * @return true 可以通讯
     */
    private String checkPing(Map<String, Object> parameter) {
        try {
            DockerClient dockerClient = DockerUtil.get(parameter);
            dockerClient.pingCmd().exec();
            return null;
        } catch (UnauthorizedException unauthorizedException) {
            log.warn(unauthorizedException.getMessage());
            return unauthorizedException.getMessage();
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
            if (ExceptionUtil.isCausedBy(e, SSLHandshakeException.class)) {
                return e.getMessage();
            }
            log.warn(e.getMessage(), e);
            return StrUtil.emptyToDefault(e.getMessage(), "未知异常");
        }
    }

    /**
     * 检查 docker url 是否可用
     *
     * @param parameter 参数
     * @return true 可用
     */
    private boolean checkUrl(Map<String, Object> parameter) {
        String url = (String) parameter.get("host");
        URI dockerHost;
        try {
            dockerHost = URI.create(url);
        } catch (Exception e) {
            return false;
        }
        if (dockerHost == null) {
            return false;
        }
        switch (dockerHost.getScheme()) {
            case "tcp":
            case "unix":
            case "npipe":
            case "ssh":
                return true;
            default:
                return false;
        }
    }

    /**
     * 获取支持到所有 api 版本
     *
     * @return list
     */
    private List<JSONObject> getApiVersions() {
        Field[] fields = ReflectUtil.getFields(RemoteApiVersion.class);
        return Arrays.stream(fields)
            .map(field -> {
                boolean aFinal = Modifier.isFinal(field.getModifiers());
                boolean aStatic = Modifier.isStatic(field.getModifiers());
                boolean aPublic = Modifier.isPublic(field.getModifiers());
                if (!aFinal || !aStatic || !aPublic) {
                    return null;
                }
                Object fieldValue = ReflectUtil.getFieldValue(null, field);
                if (fieldValue instanceof RemoteApiVersion) {
                    return (RemoteApiVersion) fieldValue;
                }
                return null;
            })
            .filter(apiVersion -> apiVersion != null && apiVersion != RemoteApiVersion.UNKNOWN_VERSION)
            .sorted((o1, o2) -> StrUtil.compareVersion(o2.getVersion(), o1.getVersion())).map(apiVersion -> {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("webVersion", apiVersion.asWebPathPart());
                jsonObject.put("version", apiVersion.getVersion());
                return jsonObject;
            })
            .collect(Collectors.toList());
    }

    /**
     * 验证 证书是否满足条件
     *
     * @param parameter 参数
     * @return 都是文件满足条件
     */
    private boolean checkCertPath(Map<String, Object> parameter) {
        String certPath = (String) parameter.get("certPath");
        Assert.hasText(certPath, "certPath is empty");
        File caPemPath = FileUtil.file(certPath, "ca.pem");
        File keyPemPath = FileUtil.file(certPath, "key.pem");
        File certPemPath = FileUtil.file(certPath, "cert.pem");
        return FileUtil.isFile(caPemPath) && FileUtil.isFile(keyPemPath) && FileUtil.isFile(certPemPath);
    }
}
