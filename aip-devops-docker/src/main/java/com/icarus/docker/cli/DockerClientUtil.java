package com.icarus.docker.cli;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.github.dockerjava.api.DockerClient;
import com.github.dockerjava.api.async.ResultCallback;
import com.github.dockerjava.api.command.LogContainerCmd;
import com.github.dockerjava.api.exception.NotFoundException;
import com.github.dockerjava.api.model.Frame;
import com.icarus.docker.utils.LogRecorder;
import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import java.io.File;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @since 2022/2/7
 */
public class DockerClientUtil {

    private static final Logger log = LoggerFactory.getLogger(DockerClientUtil.class);

    /**
     * 拉取容器日志
     *
     * @param dockerClient 连接
     * @param containerId  容器ID
     * @param charset      字符编码
     * @param consumer     回调
     * @throws InterruptedException 打断异常
     */
    public static void pullLog(DockerClient dockerClient,
                               String containerId,
                               Boolean timestamps,
                               Integer tail,
                               Charset charset,
                               Consumer<String> consumer,
                               Consumer<AutoCloseable> consumerAdapter) throws InterruptedException {
        Assert.state(tail == null || tail > 0, "tail > 0");
        // 获取日志
        LogContainerCmd logContainerCmd = dockerClient.logContainerCmd(containerId);
        if (tail == null) {
            logContainerCmd.withTailAll();
        } else {
            logContainerCmd.withTail(tail);
        }
        ResultCallback.Adapter<Frame> exec = logContainerCmd
            .withTimestamps(timestamps)
            .withStdOut(true)
            .withStdErr(true)
            .withFollowStream(true)
            .exec(new ResultCallback.Adapter<Frame>() {
                @Override
                public void onNext(Frame object) {
                    byte[] payload = object.getPayload();
                    if (payload == null) {
                        return;
                    }
                    String s = new String(payload, charset);
                    try {
                        if (StrUtil.isNotEmpty(s)) {
                            List<String> split = StrUtil.split(s, " ");
                            String dateTimeStr = split.get(0);
                            
                            try {
                                String isoTimestamp = dateTimeStr;
                                if (!isoTimestamp.endsWith("Z")) {
                                    isoTimestamp = isoTimestamp + "Z"; // Ensure Z is present to indicate UTC
                                }
                                ZonedDateTime utcDateTime = ZonedDateTime.parse(isoTimestamp, DateTimeFormatter.ISO_DATE_TIME);
                                ZonedDateTime cstDateTime = utcDateTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
                                String formattedDate = cstDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                                split.set(0, formattedDate);
                                s = StrUtil.join(" ", split);
                            } catch (Exception ex) {
                                // 忽略
                            }
                        }
                    } catch (Exception e) {
                        // 忽略
                    }
                    consumer.accept(s);
                }
            });
        Optional.ofNullable(consumerAdapter).ifPresent(consumer1 -> consumer1.accept(exec));
        //
        exec.awaitCompletion();
    }

    /**
     * 将容器文件下载到本地
     *
     * @param dockerClient  容器连接
     * @param containerId   容器ID
     * @param logRecorder   日志记录
     * @param resultFile    结果文件
     * @param resultFileOut 保存目录
     */
    public static void copyArchiveFromContainerCmd(DockerClient dockerClient, String containerId, LogRecorder logRecorder, String resultFile, String resultFileOut) {
        logRecorder.system("download file from : {}", resultFile);
        File tmpDir = FileUtil.getTmpDir();
        File fileArchive = FileUtil.file(tmpDir, "jpom", "docker-temp-archive", containerId);
        try {
            try (InputStream stream = dockerClient.copyArchiveFromContainerCmd(containerId, resultFile).exec();
                 TarArchiveInputStream tarStream = new TarArchiveInputStream(stream)) {
                TarArchiveEntry tarArchiveEntry;
                while ((tarArchiveEntry = tarStream.getNextEntry()) != null) {
                    if (!tarStream.canReadEntryData(tarArchiveEntry)) {
                        logRecorder.systemWarning("tarStream不可读", tarArchiveEntry.getName());
                    }
                    if (tarArchiveEntry.isDirectory()) {
                        continue;
                    }
                    String archiveEntryName = tarArchiveEntry.getName();
                    // 截取第一级目录
                    archiveEntryName = StrUtil.subAfter(archiveEntryName, StrUtil.SLASH, false);
                    // 可能中包含文件 使用原名称
                    archiveEntryName = StrUtil.emptyToDefault(archiveEntryName, tarArchiveEntry.getName());
                    File currentFile = FileUtil.file(fileArchive, archiveEntryName);
                    FileUtil.mkParentDirs(currentFile);
                    FileUtil.writeFromStream(tarStream, currentFile, false);
                }
            } catch (NotFoundException notFoundException) {
                logRecorder.systemWarning(notFoundException.getMessage(), notFoundException);
            } catch (Exception e) {
                logRecorder.error(e.getMessage(), e);
            }
            // github pr 71
            // https://github.com/dromara/Jpom/pull/71
            File[] files = fileArchive.listFiles();
            if (files == null) {
                logRecorder.systemWarning("temporary_result_file_does_not_exist", fileArchive.getAbsolutePath());
                return;
            }
            File resultFileOutFile = FileUtil.file(resultFileOut);
            if (ArrayUtil.length(files) == 1) {
                FileUtil.mkParentDirs(resultFileOutFile);
                FileUtil.move(files[0], resultFileOutFile, true);
            } else {
                FileUtil.mkdir(resultFileOutFile);
                FileUtil.moveContent(fileArchive, resultFileOutFile, true);
            }
        } finally {
            FileUtil.del(fileArchive);
        }
    }

    /**
     * 删除容器
     *
     * @param dockerClient docker 连接
     * @param containerId  容器ID
     */
    public static void removeContainerCmd(DockerClient dockerClient, String containerId) {
        if (containerId == null) {
            return;
        }
        // 清除容器
        dockerClient.removeContainerCmd(containerId)
            .withRemoveVolumes(true)
            .withForce(true)
            .exec();
    }
}
