package com.icarus.docker.cli;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.extra.spring.SpringUtil;
import com.icarus.docker.utils.FileUtils;
import lombok.Lombok;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @since 2023/1/4
 */
public interface IDockerConfigPlugin{


    /**
     * 获取 配置资源
     *
     * @param name 配置文件名称
     * @return 文件流
     */
    default InputStream getConfigResourceInputStream(String name) {
        String newName = "docker/" + name;
        FileUtils.checkSlip(name);
        File configResourceDir = getConfigResourceDir();
        return Opt.ofBlankAble(configResourceDir)
                .map((Function<File, InputStream>) configDir -> {
                    File file = FileUtil.file(configDir, name);
                    if (FileUtil.isFile(file)) {
                        return FileUtil.getInputStream(file);
                    }
                    return null;
                })
                .orElseGet(() -> {
                    return tryGetDefaultConfigResourceInputStream(name);
                });
    }

    /**
     * 动态获取外部配置文件的 resource
     *
     * @return File
     */
    public static InputStream tryGetDefaultConfigResourceInputStream(String name) {
        String normalize = FileUtil.normalize("/config_default/" + name);
        ClassPathResource classPathResource = new ClassPathResource(normalize);
        if (!classPathResource.exists()) {
            return null;
        }
        try {
            return classPathResource.getInputStream();
        } catch (IOException e) {
            throw Lombok.sneakyThrow(e);
        }
    }

    /**
     * 获取对应的配置资源目录 对象
     */
    static File getConfigResourceDir() {
        String property = SpringUtil.getApplicationContext().getEnvironment().getProperty("spring.config.location");
        return Opt.ofBlankAble(property).map(s -> {
            File file = FileUtil.file(s);
            return FileUtil.getParent(file, 1);
        }).orElse(null);
    }

    /**
     * 获取配置文件
     *
     * @param name    配置文件名称
     * @param tempDir 保存目录
     * @return file
     */
    default File getResourceToFile(String name, File tempDir) {
        InputStream stream = this.getConfigResourceInputStream(name);
        if (stream == null) {
            return null;
        }
        File tempFile = DockerUtil.createTemp(name, tempDir);
        FileUtil.writeFromStream(stream, tempFile);
        return tempFile;
    }
}
