package com.icarus.docker.controller;


import com.icarus.docker.cli.DefaultDockerPluginImpl;
import com.icarus.docker.controller.base.BaseDockerImagesController;
import com.icarus.docker.entity.DockerInfo;
import com.icarus.docker.entity.MachineDockerInfo;
import com.icarus.docker.service.DockerInfoService;
import com.icarus.docker.service.MachineDockerInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.Map;
import java.util.concurrent.Executor;

@RestController
@RequestMapping(value = "/aip/docker/images")
public class DockerImagesController  extends BaseDockerImagesController {
    @Autowired
    private DockerInfoService dockerInfoService;
    @Autowired
    private MachineDockerInfoService machineDockerInfoService;

    public DockerImagesController(DefaultDockerPluginImpl defaultDockerPlugin, Executor flowExecutorPool) {
        super(defaultDockerPlugin, flowExecutorPool);
    }

    /**
     * 根据参数 id 获取 docker 信息
     *
     * @param id id
     * @return docker 信息
     */
    @Override
    protected Map<String, Object> toDockerParameter(String id) {
        DockerInfo dockerInfoModel = dockerInfoService.getById(id);
        Assert.notNull(dockerInfoModel, "dockerInfoModel不能为空");
        MachineDockerInfo machineDockerModel = machineDockerInfoService.getById(dockerInfoModel.getMachineDockerId());
        Assert.notNull(machineDockerModel, "machineDockerModel不能为空");
        return machineDockerInfoService.toParameter(machineDockerModel);
    }
}
