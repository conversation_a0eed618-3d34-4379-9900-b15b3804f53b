package com.icarus.docker.controller.base;

import cn.hutool.json.JSONObject;
import com.icarus.common.rest.BaseResponse;
import com.icarus.docker.cli.DefaultDockerPluginImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@Slf4j
@RequiredArgsConstructor
public abstract class BaseDockerSwarmInfoController extends BaseDockerController{

    private final DefaultDockerPluginImpl defaultDockerPlugin;

    @PostMapping(value = "node-list", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<List<JSONObject>> nodeList(
            @RequestParam String id,
            @RequestParam(required = false) String nodeId,
            @RequestParam(required = false) String nodeName, String nodeRole) throws Exception {
        //
        Map<String, Object> map = this.toDockerParameter(id);
        map.put("id", nodeId);
        map.put("name", nodeName);
        map.put("role", nodeRole);
        List<JSONObject> listSwarmNodes = (List<JSONObject>) defaultDockerPlugin.execute("listSwarmNodes", map);
        return BaseResponse.ok(listSwarmNodes);
    }

    /**
     * 修改节点信息
     *
     * @param id 集群ID
     * @return json
     */
    @PostMapping(value = "update", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<String> update(@RequestParam String id,
                                       @RequestParam String nodeId,
                                       @RequestParam String availability,
                                       @RequestParam String role) throws Exception {
        //
        Map<String, Object> map = this.toDockerParameter(id);
        map.put("nodeId", nodeId);
        map.put("availability", availability);
        map.put("role", role);
        defaultDockerPlugin.execute("updateSwarmNode", map);
        return BaseResponse.ok("修改成功");
    }
}
