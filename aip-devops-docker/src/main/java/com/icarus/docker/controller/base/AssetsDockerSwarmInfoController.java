package com.icarus.docker.controller.base;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.icarus.common.rest.BaseResponse;
import com.icarus.docker.cli.DefaultDockerPluginImpl;
import com.icarus.docker.entity.DockerInfo;
import com.icarus.docker.entity.MachineDockerInfo;
import com.icarus.docker.service.DockerInfoService;
import com.icarus.docker.service.MachineDockerInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping(value = "/aip/assets/docker/swarm")
public class AssetsDockerSwarmInfoController extends BaseDockerSwarmInfoController{
    @Autowired
    private DockerInfoService dockerInfoService;
    @Autowired
    private MachineDockerInfoService machineDockerInfoService;

    public AssetsDockerSwarmInfoController(DefaultDockerPluginImpl defaultDockerPlugin) {
        super(defaultDockerPlugin);
    }

    /**
     * 根据参数 id 获取 docker 信息
     *
     * @param id id
     * @return docker 信息
     */
    @Override
    protected Map<String, Object> toDockerParameter(String id) {
        DockerInfo dockerInfoModel = dockerInfoService.getById(id);
        Assert.notNull(dockerInfoModel, "dockerInfoModel不能为空");
        MachineDockerInfo machineDockerModel = machineDockerInfoService.getById(dockerInfoModel.getMachineDockerId());
        Assert.notNull(machineDockerModel, "machineDockerModel不能为空");
        return machineDockerInfoService.toParameter(machineDockerModel);
    }

    /**
     * @return json
     */
    @GetMapping(value = "list-all", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<List<MachineDockerInfo>> listAll() {
        MachineDockerInfo machineDockerModel = new MachineDockerInfo();
        machineDockerModel.setSwarmControlAvailable(true);
        LambdaQueryWrapper<MachineDockerInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(MachineDockerInfo::getSwarmControlAvailable, true);
        // load list with all
        List<MachineDockerInfo> swarmInfoModes = machineDockerInfoService.list(lambdaQueryWrapper);
        return BaseResponse.ok(swarmInfoModes);
    }
}
