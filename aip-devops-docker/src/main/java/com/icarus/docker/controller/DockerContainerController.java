package com.icarus.docker.controller;

import com.icarus.docker.cli.DefaultDockerCheckPluginImpl;
import com.icarus.docker.cli.DefaultDockerPluginImpl;
import com.icarus.docker.controller.base.BaseDockerContainerController;
import com.icarus.docker.entity.DockerInfo;
import com.icarus.docker.entity.MachineDockerInfo;
import com.icarus.docker.service.DockerInfoService;
import com.icarus.docker.service.MachineDockerInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;


@RestController
@RequestMapping(value = "/aip/docker/container")
public class DockerContainerController extends BaseDockerContainerController {
    @Autowired
    private DockerInfoService dockerInfoService;
    @Autowired
    private MachineDockerInfoService machineDockerInfoService;

    public DockerContainerController(DefaultDockerCheckPluginImpl defaultDockerCheckPlugin,
                                     DefaultDockerPluginImpl defaultDockerPlugin) {
        super(defaultDockerCheckPlugin, defaultDockerPlugin);
    }

    @Override
    protected Map<String, Object> toDockerParameter(String id) {
        DockerInfo dockerInfoModel = dockerInfoService.getById(id);
        Assert.notNull(dockerInfoModel, "dockerInfoModel不能为空");
        MachineDockerInfo machineDockerModel = machineDockerInfoService.getById(dockerInfoModel.getMachineDockerId());
        Assert.notNull(machineDockerModel, "machineDockerModel不能为空");
        return machineDockerInfoService.toParameter(machineDockerModel);
    }
}
