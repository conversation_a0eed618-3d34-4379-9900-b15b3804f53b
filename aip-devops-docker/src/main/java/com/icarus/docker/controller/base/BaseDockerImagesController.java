package com.icarus.docker.controller.base;

import cn.hutool.core.exceptions.UtilException;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.Tuple;
import cn.hutool.core.util.*;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.json.JSONObject;
import com.icarus.common.rest.BaseResponse;
import com.icarus.docker.cli.DefaultDockerPluginImpl;
import com.icarus.docker.utils.FileUtils;
import com.icarus.docker.utils.LogRecorder;
import com.icarus.utils.AuthUtil;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.Writer;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.function.Consumer;

@Slf4j
@RequiredArgsConstructor
public abstract class BaseDockerImagesController extends BaseDockerController{

    private final DefaultDockerPluginImpl defaultDockerPlugin;
    private final Executor flowExecutorPool;

    /**
     * @return json
     */
    @PostMapping(value = "list")
    public BaseResponse<List<JSONObject>> list(@RequestParam String id,
                                               @RequestParam(required = false) String name,
                                               @RequestParam(required = false) String showAll,
                                               @RequestParam(required = false) String dangling) throws Exception {
        Map<String, Object> parameter = this.toDockerParameter(id);
        parameter.put("name", name);
        parameter.put("showAll", showAll);
        parameter.put("dangling", dangling);
        parameter.put("workspaceId", "1");
        List<JSONObject> listContainer = (List<JSONObject>) defaultDockerPlugin.execute("listImages", parameter);
        return BaseResponse.ok(listContainer);
    }

    /**
     * @return json
     */
    @GetMapping(value = "remove", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<Object> del(@RequestParam String id,@RequestParam(required = false) String imageId) throws Exception {
        Map<String, Object> parameter = this.toDockerParameter(id);
        parameter.put("imageId", imageId);
        parameter.put("workspaceId", "1");
        defaultDockerPlugin.execute("removeImage", parameter);
        return BaseResponse.ok("执行成功");
    }

    /**
     * @return json
     */
    @GetMapping(value = "batchRemove", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<Object> batchRemove(@RequestParam String id,@RequestParam(required = false) String[] imagesIds) throws Exception {
        Map<String, Object> parameter = this.toDockerParameter(id);
        parameter.put("imagesIds", imagesIds);
        parameter.put("workspaceId", "1");
        defaultDockerPlugin.execute("batchRemove", parameter);
        return BaseResponse.ok("执行成功");
    }

    /**
     * @return json
     */
    @GetMapping(value = "inspect", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<JSONObject> inspect(@RequestParam String id,@RequestParam(required = false) String imageId) throws Exception {
        Map<String, Object> parameter = this.toDockerParameter(id);
        parameter.put("imageId", imageId);
        parameter.put("workspaceId", "1");
        JSONObject inspectImage = (JSONObject) defaultDockerPlugin.execute("inspectImage", parameter);
        return BaseResponse.ok(inspectImage);
    }

    /**
     * @return json
     */
    @GetMapping(value = "pull-image", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<String> pullImage(@RequestParam String id,@RequestParam(required = false) String repository) {
        Map<String, Object> parameter = this.toDockerParameter(id);
        parameter.put("repository", repository);
        parameter.put("workspaceId", "1");
        //
        String uuid = IdUtil.fastSimpleUUID();
        File file = FileUtil.file(FileUtil.getTmpDir(), AuthUtil.getUserCode(),"docker-log", uuid + ".log");
        LogRecorder logRecorder = LogRecorder.builder().file(file).build();
        logRecorder.system("start pull {}", repository);
        Consumer<String> logConsumer = logRecorder::info;
        parameter.put("logConsumer", logConsumer);
        flowExecutorPool.execute(() -> {
            try {
                defaultDockerPlugin.execute("pullImage", parameter);
                logRecorder.system("pull end");
            } catch (Exception e) {
                logRecorder.error("pull 异常", e);
            } finally {
                IoUtil.close(logRecorder);
            }

        });
        return BaseResponse.ok("pull start", uuid);
    }

    /**
     *
     */
    @GetMapping(value = "save-image", produces = MediaType.APPLICATION_JSON_VALUE)
    public void saveImage(@RequestParam String id,@RequestParam(required = false) String imageId, HttpServletResponse response) {
        Map<String, Object> parameter = this.toDockerParameter(id);
        parameter.put("imageId", imageId);
        //
        try {
            Tuple saveImage = (Tuple) defaultDockerPlugin.execute("saveImage", parameter);
            if (saveImage == null) {
                response.setContentType(MediaType.APPLICATION_JSON_VALUE);
                Writer writer = null;
                try {
                    writer = response.getWriter();
                    writer.write("镜像不存在");
                    writer.flush();
                } catch (IOException var8) {
                    IOException e = var8;
                    throw new UtilException(e);
                } finally {
                    IoUtil.close(writer);
                }
                return;
            }
            InputStream inputStream = saveImage.get(0);
            String name = saveImage.get(1);
            String charset = (String) ObjectUtil.defaultIfNull(response.getCharacterEncoding(), "UTF-8");
            String encodeText = URLUtil.encodeAll(name, CharsetUtil.charset(charset));
            response.setHeader("Content-Disposition", StrUtil.format("attachment;filename=\"{}\";filename*={}''{}", new Object[]{encodeText, charset, encodeText}));
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            ServletOutputStream out = null;
            try {
                out = response.getOutputStream();
                IoUtil.copy(inputStream, out, 8192);
            } catch (IOException var8) {
                IOException e = var8;
                throw new UtilException(e);
            } finally {
                IoUtil.close(out);
                IoUtil.close(inputStream);
            }
        } catch (Exception e) {
            log.error("保存镜像失败", e);
            response.setContentType(MediaType.APPLICATION_JSON_VALUE);
            Writer writer = null;
            try {
                writer = response.getWriter();
                writer.write("保存镜像失败");
                writer.flush();
            } catch (IOException var8) {
                throw new UtilException(var8);
            } finally {
                IoUtil.close(writer);
            }
        }
    }

    /**
     *
     */
    @PostMapping(value = "load-image", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<String> loadImage(@RequestParam String id,
                                          MultipartFile file) throws Exception {
        String originalFilename = file.getOriginalFilename();
        String extName = FileUtil.extName(originalFilename);
        boolean expression = StrUtil.equalsIgnoreCase(extName, "tar");
        Assert.state(expression, "该文件不是tar压缩包");
        Map<String, Object> parameter = this.toDockerParameter(id);
        parameter.put("stream", file.getInputStream());
        //
        defaultDockerPlugin.execute("loadImage", parameter);
        return BaseResponse.ok("加载成功");
    }

    /**
     * 获取拉取的日志
     *
     * @param id   id
     * @param line 需要获取的行号
     * @return json
     */
    @GetMapping(value = "pull-image-log", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<JSONObject> getNowLog(@RequestParam String id,
                                              @RequestParam int line) {
        File file = FileUtil.file(FileUtil.getTmpDir(), AuthUtil.getUserCode(),"docker-log", id + ".log");
        if (!file.exists()) {
            return BaseResponse.serviceError("日志文件不存在");
        }
        JSONObject data = FileUtils.readLogFile(file, line);
        return BaseResponse.ok(data);
    }

    /**
     * @return json
     */
    @PostMapping(value = "create-container", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<Object> createContainer(@RequestBody JSONObject jsonObject) throws Exception {
        String id = jsonObject.getStr("id");
        Assert.hasText(id, "主键不能为空");
        String imageId = jsonObject.getStr("imageId");
        Assert.hasText(imageId, "imageId不能为空");
        String name = jsonObject.getStr("name");
        Assert.hasText(name, "名称不能为空");

        Map<String, Object> parameter = this.toDockerParameter(id);
        parameter.putAll(jsonObject);
        parameter.put("workspaceId", "1");
        defaultDockerPlugin.execute("createContainer", parameter);
        return BaseResponse.ok("创建成功");
    }
}
