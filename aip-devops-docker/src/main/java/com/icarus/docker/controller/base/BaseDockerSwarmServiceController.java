package com.icarus.docker.controller.base;

import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.UtilException;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.*;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.json.JSONObject;
import com.icarus.common.rest.BaseResponse;
import com.icarus.docker.cli.DefaultDockerPluginImpl;
import com.icarus.docker.utils.FileUtils;
import com.icarus.docker.utils.LogRecorder;
import com.icarus.utils.AuthUtil;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.IOException;
import java.io.Writer;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.function.Consumer;

@Slf4j
public abstract class BaseDockerSwarmServiceController extends BaseDockerController{
    private static final TimedCache<String, Set<String>> LOG_CACHE = new TimedCache<>(30 * 1000);
    private final DefaultDockerPluginImpl defaultDockerPlugin;
    private final Executor flowExecutorPool;

    public BaseDockerSwarmServiceController(DefaultDockerPluginImpl defaultDockerPlugin,
                                            Executor flowExecutorPool) {
        this.defaultDockerPlugin = defaultDockerPlugin;
        this.flowExecutorPool = flowExecutorPool;
        // 30 秒检查一次
        LOG_CACHE.schedulePrune(30 * 1000);
        // 监控过期
        LOG_CACHE.setListener((key, userIds) -> {
            try {
                log.debug("async_resource_expired: {}, {}", key, userIds);
                Map<String, Object> map = MapUtil.of("uuid", key);
                defaultDockerPlugin.execute("closeAsyncResource", map);
                //
                for (String userId : userIds) {
                    File file = FileUtil.file(FileUtil.getTmpDir(), userId, "docker-swarm-log", key + ".log");
                    FileUtil.del(file);
                }
            } catch (Exception e) {
                log.error("close_resource_failure", e);
            }
        });
    }

    @PostMapping(value = "list", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<List<JSONObject>> list(
            @RequestParam String id,
            @RequestParam(required = false) String serviceId,
            @RequestParam(required = false) String serviceName) throws Exception {
        //
        Map<String, Object> map = this.toDockerParameter(id);
        map.put("id", serviceId);
        map.put("name", serviceName);
        List<JSONObject> listSwarmNodes = (List<JSONObject>) defaultDockerPlugin.execute("listServices", map);
        return BaseResponse.ok(listSwarmNodes);
    }

    @PostMapping(value = "task-list", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<List<JSONObject>> taskList(
            @RequestParam String id,
            @RequestParam(required = false) String serviceId,
            @RequestParam(required = false) String taskId,
            @RequestParam(required = false) String taskName,
            @RequestParam(required = false) String taskNode,
            @RequestParam(required = false) String taskState) throws Exception {
        //
        Map<String, Object> map = this.toDockerParameter(id);
        map.put("id", taskId);
        map.put("serviceId", serviceId);
        map.put("name", taskName);
        map.put("node", taskNode);
        map.put("state", taskState);
        List<JSONObject> listSwarmNodes = (List<JSONObject>) defaultDockerPlugin.execute("listTasks", map);
        return BaseResponse.ok(listSwarmNodes);
    }

    @GetMapping(value = "del", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<String> del(@RequestParam String id, @RequestParam String serviceId) throws Exception {
        //
        Map<String, Object> map = this.toDockerParameter(id);
        map.put("serviceId", serviceId);
        defaultDockerPlugin.execute("removeService", map);
        return BaseResponse.ok("删除成功");
    }

    @PostMapping(value = "edit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<String> edit(@RequestBody JSONObject jsonObject) throws Exception {
        //
        String id = jsonObject.getStr("id");
        Map<String, Object> map = this.toDockerParameter(id);
        map.putAll(jsonObject);
        defaultDockerPlugin.execute("updateService", map);
        return BaseResponse.ok("修改成功");
    }

    /**
     * @return json
     */
    @GetMapping(value = "start-log", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<String> pullImage(@RequestParam String id,
                                          @RequestParam String type,
                                          @RequestParam String dataId,
                                          @RequestParam(required = false) Integer tail,
                                          @RequestParam(required = false) String since,
                                          @RequestParam(required = false) Boolean timestamps) {
        Map<String, Object> parameter = this.toDockerParameter(id);
        parameter.put(StrUtil.equalsIgnoreCase(type, "service") ? "serviceId" : "taskId", dataId);
        //
        String uuid = IdUtil.fastSimpleUUID();
        File file = FileUtil.file(FileUtil.getTmpDir(), AuthUtil.getUserCode(), "docker-swarm-log", uuid + ".log");
        LogRecorder logRecorder = LogRecorder.builder().file(file).build();

        logRecorder.system("start pull {}", dataId);
        logRecorder.info("");
        Consumer<String> logConsumer = logRecorder::append;
        parameter.put("charset", CharsetUtil.CHARSET_UTF_8);
        parameter.put("consumer", logConsumer);
        //
        tail = ObjectUtil.defaultIfNull(tail, 50);
        tail = Math.max(tail, 1);
        parameter.put("tail", tail);
        //parameter.put("since", since);
        parameter.put("timestamps", timestamps);
        // 操作id
        parameter.put("uuid", uuid);
        flowExecutorPool.execute(() -> {
            try {
                defaultDockerPlugin.execute(StrUtil.equalsIgnoreCase(type, "service") ? "logService" : "logTask", parameter);
                logRecorder.system("pull end");
            } catch (Exception e) {
                logRecorder.error("pull_log_exception", e);
            } finally {
                IoUtil.close(logRecorder);
            }
        });
        // 添加到缓存中
        LOG_CACHE.put(uuid, CollUtil.newHashSet(AuthUtil.getUserCode()));
        return BaseResponse.ok(uuid);
    }

    /**
     * 获取拉取的日志
     *
     * @param id   id
     * @param line 需要获取的行号
     * @return json
     */
    @GetMapping(value = "pull-log", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<JSONObject> getNowLog(@RequestParam String id,
                                              @RequestParam int line) {
        File file = FileUtil.file(FileUtil.getTmpDir(),AuthUtil.getUserCode(), "docker-swarm-log", id + ".log");
        if (!file.exists()) {
            return BaseResponse.serviceError("日志文件不存在");
        }
        JSONObject data = FileUtils.readLogFile(file, line);
        // 更新缓存，避免超时被清空
        synchronized (BaseDockerSwarmServiceController.class) {
            Set<String> userIds = ObjectUtil.defaultIfNull(LOG_CACHE.get(id), new HashSet<>());
            userIds.add(AuthUtil.getUserCode());
            LOG_CACHE.put(id, userIds);
        }
        return BaseResponse.ok(data);
    }

    /**
     * 下载拉取的日志
     *
     * @param id id
     */
    @GetMapping(value = "download-log")
    public void downloadLog(@RequestParam String id,
                            HttpServletResponse response) {
        File file = FileUtil.file(FileUtil.getTmpDir(), AuthUtil.getUserCode(), "docker-swarm-log", id + ".log");
        if (!file.exists()) {
            response.setContentType(MediaType.APPLICATION_JSON_VALUE);
            Writer writer = null;
            try {
                writer = response.getWriter();
                writer.write("日志文件不存在");
                writer.flush();
            } catch (IOException var8) {
                IOException e = var8;
                throw new UtilException(e);
            } finally {
                IoUtil.close(writer);
            }
            return;
        }
        String fileName = file.getName();
        String contentType = ObjectUtil.defaultIfNull(FileUtil.getMimeType(fileName), "application/octet-stream");
        BufferedInputStream in = null;

        try {
            in = FileUtil.getInputStream(file);
            String charset = ObjectUtil.defaultIfNull(response.getCharacterEncoding(), "UTF-8");
            String encodeText = URLUtil.encodeAll(fileName, CharsetUtil.charset(charset));
            response.setHeader("Content-Disposition", StrUtil.format("attachment;filename=\"{}\";filename*={}''{}", new Object[]{encodeText, charset, encodeText}));
            response.setContentType(contentType);
            ServletOutputStream out = null;
            try {
                out = response.getOutputStream();
                IoUtil.copy(in, out, 8192);
            } catch (IOException var8) {
                IOException e = var8;
                throw new UtilException(e);
            } finally {
                IoUtil.close(out);
                IoUtil.close(in);
            }
        } finally {
            IoUtil.close(in);
        }
    }
}
