package com.icarus.docker.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.common.rest.BaseResponse;
import com.icarus.docker.entity.MachineDockerInfo;
import com.icarus.docker.service.MachineDockerInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 机器Docker信息控制器
 */
@Slf4j
@RestController
@RequestMapping("/aip/docker/machine")
@RequiredArgsConstructor
public class MachineDockerInfoController {

    private final MachineDockerInfoService machineDockerInfoService;

    /**
     * 分页查询机器Docker信息
     *
     * @param current 当前页
     * @param size 每页大小
     * @param host 地址
     * @param status 状态
     * @return 分页结果
     */
    @GetMapping("/page")
    public BaseResponse<Page<MachineDockerInfo>> page(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String host,
            @RequestParam(required = false) Integer status) {
        try {
            Page<MachineDockerInfo> page = new Page<>(current, size);
            LambdaQueryWrapper<MachineDockerInfo> wrapper = new LambdaQueryWrapper<>();
            wrapper.like(host != null, MachineDockerInfo::getHost, host)
                  .eq(status != null, MachineDockerInfo::getStatus, status);
            Page<MachineDockerInfo> result = machineDockerInfoService.page(page, wrapper);
            return BaseResponse.ok(result);
        } catch (Exception e) {
            log.error("查询机器Docker信息失败", e);
            return BaseResponse.serviceError("查询机器Docker信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取机器Docker信息详情
     *
     * @param id 机器Docker信息ID
     * @return 机器Docker信息
     */
    @GetMapping("/{id}")
    public BaseResponse<MachineDockerInfo> getById(@PathVariable String id) {
        try {
            MachineDockerInfo machineDockerInfo = machineDockerInfoService.getMachineDockerInfo(id);
            return BaseResponse.ok(machineDockerInfo);
        } catch (Exception e) {
            log.error("获取机器Docker信息失败", e);
            return BaseResponse.serviceError("获取机器Docker信息失败: " + e.getMessage());
        }
    }

    /**
     * 创建机器Docker信息
     *
     * @param machineDockerInfo 机器Docker信息
     * @return 操作结果
     */
    @PostMapping
    public BaseResponse<Boolean> create(@RequestBody MachineDockerInfo machineDockerInfo) {
        try {
            boolean success = machineDockerInfoService.createMachineDockerInfo(machineDockerInfo);
            return BaseResponse.ok(success);
        } catch (Exception e) {
            log.error("创建机器Docker信息失败", e);
            return BaseResponse.serviceError("创建机器Docker信息失败: " + e.getMessage());
        }
    }

    /**
     * 更新机器Docker信息
     *
     * @param machineDockerInfo 机器Docker信息
     * @return 操作结果
     */
    @PutMapping
    public BaseResponse<Boolean> update(@RequestBody MachineDockerInfo machineDockerInfo) {
        try {
            boolean success = machineDockerInfoService.updateMachineDockerInfo(machineDockerInfo);
            return BaseResponse.ok(success);
        } catch (Exception e) {
            log.error("更新机器Docker信息失败", e);
            return BaseResponse.serviceError("更新机器Docker信息失败: " + e.getMessage());
        }
    }

    /**
     * 删除机器Docker信息
     *
     * @param id 机器Docker信息ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public BaseResponse<Boolean> delete(@PathVariable String id) {
        try {
            boolean success = machineDockerInfoService.deleteMachineDockerInfo(id);
            return BaseResponse.ok(success);
        } catch (Exception e) {
            log.error("删除机器Docker信息失败", e);
            return BaseResponse.serviceError("删除机器Docker信息失败: " + e.getMessage());
        }
    }

    /**
     * 更新心跳时间
     *
     * @param id 机器Docker信息ID
     * @return 操作结果
     */
    @PutMapping("/heartbeat/{id}")
    public BaseResponse<Boolean> updateHeartbeat(@PathVariable String id) {
        try {
            boolean success = machineDockerInfoService.updateHeartbeat(id);
            return BaseResponse.ok(success);
        } catch (Exception e) {
            log.error("更新心跳时间失败", e);
            return BaseResponse.serviceError("更新心跳时间失败: " + e.getMessage());
        }
    }

    /**
     * 更新状态
     *
     * @param id 机器Docker信息ID
     * @param status 状态 0 异常离线 1 正常
     * @param failureMsg 错误消息
     * @return 操作结果
     */
    @PutMapping("/status/{id}")
    public BaseResponse<Boolean> updateStatus(
            @PathVariable String id,
            @RequestParam Integer status,
            @RequestParam(required = false) String failureMsg) {
        try {
            boolean success = machineDockerInfoService.updateStatus(id, status, failureMsg);
            return BaseResponse.ok(success);
        } catch (Exception e) {
            log.error("更新状态失败", e);
            return BaseResponse.serviceError("更新状态失败: " + e.getMessage());
        }
    }
} 