package com.icarus.docker.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.common.rest.BaseResponse;
import com.icarus.docker.entity.DockerInfo;
import com.icarus.docker.service.DockerInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * Docker信息控制器
 */
@Slf4j
@RestController
@RequestMapping("/aip/docker/info")
@RequiredArgsConstructor
public class DockerInfoController {

    private final DockerInfoService dockerInfoService;

    /**
     * 分页查询Docker信息
     *
     * @param current 当前页
     * @param size 每页大小
     * @param name Docker名称
     * @return 分页结果
     */
    @GetMapping("/page")
    public BaseResponse<Page<DockerInfo>> page(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String name) {
        try {
            Page<DockerInfo> page = new Page<>(current, size);
            LambdaQueryWrapper<DockerInfo> wrapper = new LambdaQueryWrapper<>();
            wrapper.like(name != null, DockerInfo::getName, name);
            Page<DockerInfo> result = dockerInfoService.page(page, wrapper);
            return BaseResponse.ok(result);
        } catch (Exception e) {
            log.error("查询Docker信息失败", e);
            return BaseResponse.serviceError("查询Docker信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取Docker信息详情
     *
     * @param id Docker信息ID
     * @return Docker信息
     */
    @GetMapping("/{id}")
    public BaseResponse<DockerInfo> getById(@PathVariable String id) {
        try {
            DockerInfo dockerInfo = dockerInfoService.getDockerInfo(id);
            return BaseResponse.ok(dockerInfo);
        } catch (Exception e) {
            log.error("获取Docker信息失败", e);
            return BaseResponse.serviceError("获取Docker信息失败: " + e.getMessage());
        }
    }

    /**
     * 创建Docker信息
     *
     * @param dockerInfo Docker信息
     * @return 操作结果
     */
    @PostMapping
    public BaseResponse<Boolean> create(@RequestBody DockerInfo dockerInfo) {
        try {
            boolean success = dockerInfoService.createDockerInfo(dockerInfo);
            return BaseResponse.ok(success);
        } catch (Exception e) {
            log.error("创建Docker信息失败", e);
            return BaseResponse.serviceError("创建Docker信息失败: " + e.getMessage());
        }
    }

    /**
     * 更新Docker信息
     *
     * @param dockerInfo Docker信息
     * @return 操作结果
     */
    @PutMapping
    public BaseResponse<Boolean> update(@RequestBody DockerInfo dockerInfo) {
        try {
            boolean success = dockerInfoService.updateDockerInfo(dockerInfo);
            return BaseResponse.ok(success);
        } catch (Exception e) {
            log.error("更新Docker信息失败", e);
            return BaseResponse.serviceError("更新Docker信息失败: " + e.getMessage());
        }
    }

    /**
     * 删除Docker信息
     *
     * @param id Docker信息ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public BaseResponse<Boolean> delete(@PathVariable String id) {
        try {
            boolean success = dockerInfoService.deleteDockerInfo(id);
            return BaseResponse.ok(success);
        } catch (Exception e) {
            log.error("删除Docker信息失败", e);
            return BaseResponse.serviceError("删除Docker信息失败: " + e.getMessage());
        }
    }

    /**
     * 更新心跳时间
     *
     * @param id Docker信息ID
     * @return 操作结果
     */
    @PutMapping("/heartbeat/{id}")
    public BaseResponse<Boolean> updateHeartbeat(@PathVariable String id) {
        try {
            boolean success = dockerInfoService.updateHeartbeat(id);
            return BaseResponse.ok(success);
        } catch (Exception e) {
            log.error("更新心跳时间失败", e);
            return BaseResponse.serviceError("更新心跳时间失败: " + e.getMessage());
        }
    }
} 