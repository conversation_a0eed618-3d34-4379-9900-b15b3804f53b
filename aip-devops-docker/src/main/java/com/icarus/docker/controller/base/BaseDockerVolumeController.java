package com.icarus.docker.controller.base;

import cn.hutool.json.JSONObject;
import com.icarus.common.rest.BaseResponse;
import com.icarus.docker.cli.DefaultDockerPluginImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@Slf4j
@RequiredArgsConstructor
public abstract class BaseDockerVolumeController extends BaseDockerController{

    private final DefaultDockerPluginImpl defaultDockerPlugin;

    /**
     * @return json
     */
    @PostMapping(value = "list", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<List<JSONObject>> list(@RequestParam String id,
                                               @RequestParam(required = false) String name,
                                               @RequestParam(required = false) String dangling) throws Exception {
        Map<String, Object> parameter = this.toDockerParameter(id);
        parameter.put("name", name);
        parameter.put("dangling", dangling);
        List<JSONObject> listContainer = (List<JSONObject>) defaultDockerPlugin.execute("listVolumes", parameter);
        return BaseResponse.ok(listContainer);
    }

    /**
     * @return json
     */
    @GetMapping(value = "remove", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<Object> del(@RequestParam String id,@RequestParam(required = false) String volumeName) throws Exception {
        Map<String, Object> parameter = this.toDockerParameter(id);
        parameter.put("volumeName", volumeName);
        defaultDockerPlugin.execute("removeVolume", parameter);
        return BaseResponse.ok("执行成功");
    }
}
