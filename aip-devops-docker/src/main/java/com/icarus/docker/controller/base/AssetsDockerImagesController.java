package com.icarus.docker.controller.base;

import com.icarus.docker.cli.DefaultDockerPluginImpl;
import com.icarus.docker.entity.DockerInfo;
import com.icarus.docker.entity.MachineDockerInfo;
import com.icarus.docker.service.DockerInfoService;
import com.icarus.docker.service.MachineDockerInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.Map;
import java.util.concurrent.Executor;

@RestController
@RequestMapping(value = "/aip/assets/docker/images")
public class AssetsDockerImagesController extends BaseDockerImagesController{
    @Autowired
    private DockerInfoService dockerInfoService;
    @Autowired
    private MachineDockerInfoService machineDockerInfoService;

    public AssetsDockerImagesController(DefaultDockerPluginImpl defaultDockerPlugin, Executor flowExecutorPool) {
        super(defaultDockerPlugin, flowExecutorPool);
    }

    /**
     * 根据参数 id 获取 docker 信息
     *
     * @param id id
     * @return docker 信息
     */
    @Override
    protected Map<String, Object> toDockerParameter(String id) {
        MachineDockerInfo dockerInfoModel = machineDockerInfoService.getById(id);
        Assert.notNull(dockerInfoModel, "machineDockerModel不能为空");
        return machineDockerInfoService.toParameter(dockerInfoModel);
    }
}
