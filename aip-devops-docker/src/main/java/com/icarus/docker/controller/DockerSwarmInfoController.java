package com.icarus.docker.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.icarus.common.rest.BaseResponse;
import com.icarus.docker.cli.DefaultDockerPluginImpl;
import com.icarus.docker.controller.base.BaseDockerSwarmInfoController;
import com.icarus.docker.entity.DockerInfo;
import com.icarus.docker.entity.DockerSwarmInfo;
import com.icarus.docker.entity.MachineDockerInfo;
import com.icarus.docker.service.DockerInfoService;
import com.icarus.docker.service.DockerSwarmInfoService;
import com.icarus.docker.service.MachineDockerInfoService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;


@RestController
@RequestMapping(value = "/aip/docker/swarm")
@Slf4j
public class DockerSwarmInfoController extends BaseDockerSwarmInfoController {
    @Autowired
    private DockerInfoService dockerInfoService;
    @Autowired
    private MachineDockerInfoService machineDockerInfoService;
    @Autowired
    private DockerSwarmInfoService dockerSwarmInfoService;

    public DockerSwarmInfoController(DefaultDockerPluginImpl defaultDockerPlugin) {
        super(defaultDockerPlugin);
    }

    /**
     * 根据参数 id 获取 docker 信息
     *
     * @param id id
     * @return docker 信息
     */
    @Override
    protected Map<String, Object> toDockerParameter(String id) {
        DockerInfo dockerInfoModel = dockerInfoService.getById(id);
        Assert.notNull(dockerInfoModel, "dockerInfoModel不能为空");
        MachineDockerInfo machineDockerModel = machineDockerInfoService.getById(dockerInfoModel.getMachineDockerId());
        Assert.notNull(machineDockerModel, "machineDockerModel不能为空");
        return machineDockerInfoService.toParameter(machineDockerModel);
    }

    @PostMapping(value = "list", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<PageDTO<DockerSwarmInfo>> list(HttpServletRequest request,
                                                       @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                       @RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {
        LambdaQueryWrapper<DockerSwarmInfo> wrapper = getQueryWrapper(request);
        IPage<DockerSwarmInfo> page = new Page<>(pageNo, pageSize);
        IPage<DockerSwarmInfo> pageList = dockerSwarmInfoService.page(page, wrapper);
        List<DockerSwarmInfo> records = pageList.getRecords();
        records.forEach(dockerSwarmInfoMode -> {
            String swarmId = dockerSwarmInfoMode.getSwarmId();
            MachineDockerInfo machineDocker = machineDockerInfoService.tryMachineDockerBySwarmId(swarmId);
            dockerSwarmInfoMode.setMachineDocker(machineDocker);
        });
        PageDTO<DockerSwarmInfo> pageDTO = new PageDTO<>();
        pageDTO.setRecords(records);
        pageDTO.setTotal(pageList.getTotal());
        pageDTO.setCurrent(pageNo);
        return BaseResponse.ok(pageDTO);
    }

    private static @NotNull LambdaQueryWrapper<DockerSwarmInfo> getQueryWrapper(HttpServletRequest request) {
        String id = request.getParameter("id");
        String name = request.getParameter("name");
        String swarmIdParam = request.getParameter("swarmId");
        String tag = request.getParameter("tag");
        LambdaQueryWrapper<DockerSwarmInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StrUtil.isNotEmpty(id), DockerSwarmInfo::getId, id);
        wrapper.eq(StrUtil.isNotEmpty(name), DockerSwarmInfo::getName, name);
        wrapper.eq(StrUtil.isNotEmpty(swarmIdParam), DockerSwarmInfo::getSwarmId, swarmIdParam);
        wrapper.eq(StrUtil.isNotEmpty(tag), DockerSwarmInfo::getTag, tag);
        return wrapper;
    }

    /**
     * @return json
     */
    @GetMapping(value = "list-all", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<List<DockerSwarmInfo>> listAll(HttpServletRequest request) {
        // load list with all
        LambdaQueryWrapper<DockerSwarmInfo> wrapper = getQueryWrapper(request);
        List<DockerSwarmInfo> swarmInfoModes = dockerSwarmInfoService.list(wrapper);
        return BaseResponse.ok(swarmInfoModes);
    }

    @PostMapping(value = "edit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<Object> edit(@RequestParam String id,
                                     @RequestParam String name,
                                     @RequestParam String tag,
                                     HttpServletRequest request) throws Exception {
        DockerSwarmInfo dockerSwarmInfoMode1 = dockerSwarmInfoService.getById(id);
        Assert.notNull(dockerSwarmInfoMode1, "cluster_not_exist");
        // 更新集群信息
        DockerSwarmInfo dockerSwarmInfoMode = new DockerSwarmInfo();
        dockerSwarmInfoMode.setId(id);
        dockerSwarmInfoMode.setName(name);
        dockerSwarmInfoMode.setTag(tag);
        dockerSwarmInfoService.updateById(dockerSwarmInfoMode);
        // 更新集群关联的 docker 工作空间的 tag
        DockerSwarmInfo dockerModel = new DockerSwarmInfo();
        dockerModel.setSwarmId(dockerSwarmInfoMode1.getSwarmId());
        LambdaQueryWrapper<DockerSwarmInfo> queryWrapper = getQueryWrapper(request);
        List<DockerSwarmInfo> machineDockerModels = dockerSwarmInfoService.list(queryWrapper);
        Assert.notEmpty(machineDockerModels, "docker_info_not_found");
        return BaseResponse.ok("修改成功");
    }

    @GetMapping(value = "del", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<Object> del(@RequestParam String id, HttpServletRequest request) throws Exception {
        dockerSwarmInfoService.deleteDockerSwarmInfo(id);
        return BaseResponse.ok("删除成功");
    }
}
