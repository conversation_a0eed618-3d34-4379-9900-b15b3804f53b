package com.icarus.docker.controller.base;

import com.icarus.docker.cli.DefaultDockerPluginImpl;
import com.icarus.docker.entity.DockerInfo;
import com.icarus.docker.entity.MachineDockerInfo;
import com.icarus.docker.service.DockerInfoService;
import com.icarus.docker.service.MachineDockerInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.Map;

@RestController
@RequestMapping(value = "/aip/assets/docker/volumes")
public class AssetsDockerVolumeController extends BaseDockerVolumeController{
    @Autowired
    private DockerInfoService dockerInfoService;
    @Autowired
    private MachineDockerInfoService machineDockerInfoService;

    public AssetsDockerVolumeController(DefaultDockerPluginImpl defaultDockerPlugin) {
        super(defaultDockerPlugin);
    }

    /**
     * 根据参数 id 获取 docker 信息
     *
     * @param id id
     * @return docker 信息
     */
    @Override
    protected Map<String, Object> toDockerParameter(String id) {
        MachineDockerInfo machineDockerModel = machineDockerInfoService.getById(id);
        Assert.notNull(machineDockerModel, "machineDockerModel不能为空");
        return machineDockerInfoService.toParameter(machineDockerModel);
    }
}
