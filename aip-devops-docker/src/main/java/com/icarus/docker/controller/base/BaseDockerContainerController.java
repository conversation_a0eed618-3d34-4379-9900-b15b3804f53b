package com.icarus.docker.controller.base;

import cn.hutool.core.exceptions.UtilException;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.*;
import cn.hutool.json.JSONObject;
import com.icarus.common.rest.BaseResponse;
import com.icarus.docker.cli.DefaultDockerCheckPluginImpl;
import com.icarus.docker.cli.DefaultDockerPluginImpl;
import com.icarus.docker.entity.MachineDockerInfo;
import com.icarus.docker.service.MachineDockerInfoService;
import com.icarus.docker.utils.LogRecorder;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.IOException;
import java.io.Writer;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

@Slf4j
@RequiredArgsConstructor
public abstract class BaseDockerContainerController extends BaseDockerController {
    private final DefaultDockerCheckPluginImpl defaultDockerCheckPlugin;
    private final DefaultDockerPluginImpl defaultDockerPlugin;
    @Autowired
    private MachineDockerInfoService machineDockerInfoService;
    @Autowired
    private ThreadPoolExecutor flowExecutorPool;

    @GetMapping(value = "info", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<JSONObject> info(@RequestParam("id") String id) throws Exception {
        Map<String, Object> parameter = this.toDockerParameter(id);
        JSONObject info = defaultDockerCheckPlugin.execute("info", parameter, JSONObject.class);
        return BaseResponse.ok(info);
    }

    @PostMapping(value = "prune", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<Object> prune(@RequestParam String id, @RequestParam(required = false) String pruneType,
                                      @RequestParam(required = false) String labels, @RequestParam(required = false) String until,
                                      @RequestParam(required = false) String dangling) throws Exception {
        Map<String, Object> parameter = this.toDockerParameter(id);
        parameter.put("pruneType", pruneType);
        parameter.put("labels", labels);
        parameter.put("until", until);
        parameter.put("dangling", dangling);
        //
        Long spaceReclaimed = defaultDockerPlugin.execute("prune", parameter, Long.class);
        spaceReclaimed = ObjectUtil.defaultIfNull(spaceReclaimed, 0L);
        return BaseResponse.ok(FileUtil.readableFileSize(spaceReclaimed));
    }

    /**
     * @return json
     */
    @PostMapping(value = "list", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<List<JSONObject>> list(@RequestParam String id,
                                               @RequestParam(required = false) String name,
                                               @RequestParam(required = false) String containerId,
                                               @RequestParam(required = false) String imageId,
                                               @RequestParam(required = false) String showAll) throws Exception {
        Map<String, Object> parameter = this.toDockerParameter(id);
        parameter.put("name", name);
        parameter.put("containerId", containerId);
        parameter.put("imageId", imageId);
        parameter.put("showAll", showAll);
        List<JSONObject> listContainer = (List<JSONObject>) defaultDockerPlugin.execute("listContainer", parameter);
        return BaseResponse.ok(listContainer);
    }

    /**
     * @return json
     */
    @GetMapping(value = "remove", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<Object> del(@RequestParam String id, @RequestParam(required = false) String containerId) throws Exception {
        Map<String, Object> parameter = this.toDockerParameter(id);
        parameter.put("containerId", containerId);
        defaultDockerPlugin.execute("removeContainer", parameter);
        return BaseResponse.ok("执行成功");
    }

    /**
     * @return json
     */
    @GetMapping(value = "start")
    public BaseResponse<Object> start(@RequestParam String id, @RequestParam(required = false) String containerId) throws Exception {
        Map<String, Object> parameter = this.toDockerParameter(id);
        parameter.put("containerId", containerId);
        defaultDockerPlugin.execute("startContainer", parameter);
        return BaseResponse.ok("执行成功");
    }

    /**
     * @return json
     */
    @GetMapping(value = "stop")
    public BaseResponse<Object> stop(@RequestParam String id, @RequestParam(required = false) String containerId) throws Exception {
        Map<String, Object> parameter = this.toDockerParameter(id);
        parameter.put("containerId", containerId);
        defaultDockerPlugin.execute("stopContainer", parameter);
        return BaseResponse.ok("执行成功");
    }

    /**
     * @return json
     */
    @GetMapping(value = "restart")
    public BaseResponse<Object> restart(@RequestParam String id, @RequestParam(required = false) String containerId) throws Exception {
        Map<String, Object> parameter = this.toDockerParameter(id);
        parameter.put("containerId", containerId);
        defaultDockerPlugin.execute("restartContainer", parameter);
        return BaseResponse.ok("执行成功");
    }

    /**
     * @return json
     */
    @GetMapping(value = "stats")
    public BaseResponse<Map<String, JSONObject>> stats(@RequestParam String id, @RequestParam(required = false) String containerId) throws Exception {
        Map<String, Object> parameter = this.toDockerParameter(id);
        parameter.put("containerId", containerId);
        Map<String, JSONObject> stats = (Map<String, JSONObject>) defaultDockerPlugin.execute("stats", parameter);
        return BaseResponse.ok(stats);
    }

    /**
     * @return json
     */
    @GetMapping(value = "inspect-container")
    public BaseResponse<JSONObject> inspectContainer(@RequestParam String id,
                                                     @RequestParam String containerId) throws Exception {
        Map<String, Object> parameter = this.toDockerParameter(id);
        parameter.put("containerId", containerId);
        JSONObject results = (JSONObject) defaultDockerPlugin.execute("inspectContainer", parameter);
        return BaseResponse.ok(results);
    }

    /**
     * 修改容器配置
     * NanoCpus：以 10-9 (十億分之一) 個 CPU 為單位的 CPU 配額。 例如，250000000 nanocpus = 0.25 CPU。
     *
     * @return json
     */
    @PostMapping(value = "update-container")
    public BaseResponse<JSONObject> updateContainer(@RequestBody JSONObject jsonObject) throws Exception {
        // @ValidatorItem String id, String containerId
        String id = jsonObject.getStr("id");
        Assert.hasText(id, "主键不能为空");
        jsonObject.remove("id");
        Map<String, Object> parameter = this.toDockerParameter(id);
        parameter.putAll(jsonObject);
        JSONObject results = (JSONObject) defaultDockerPlugin.execute("updateContainer", parameter);
        return BaseResponse.ok(results);
    }

    /**
     * drop old container and create new container
     *
     * @return json
     */
    @PostMapping(value = "rebuild-container")
    public BaseResponse<Object> reBuildContainer(@RequestBody JSONObject jsonObject) throws Exception {
        String id = jsonObject.getStr("id");
        String containerId = jsonObject.getStr("containerId");
        Assert.hasText(id, "主键不能为空");
        String imageId = jsonObject.getStr("imageId");
        Assert.hasText(imageId, "imageId不能为空");
        String name = jsonObject.getStr("name");
        Assert.hasText(name, "名称不能为空");

        Map<String, Object> parameter = this.toDockerParameter(id);

        // drop old container
        if (StrUtil.isNotEmpty(containerId)) {
            parameter.put("containerId", containerId);
            try {
                defaultDockerPlugin.execute("removeContainer", parameter);
            } catch (com.github.dockerjava.api.exception.NotFoundException notFoundException) {
                log.warn(notFoundException.getMessage());
            }
        }

        // create new container
        parameter.putAll(jsonObject);
        defaultDockerPlugin.execute("createContainer", parameter);
        return BaseResponse.ok("rebuild成功");
    }

    /**
     * 下载拉取的日志
     *
     * @param id id
     */
    @GetMapping(value = "download-log")
    public void downloadLog(@RequestParam(required = false) String id,
                            @RequestParam(required = false) String containerId,
                            HttpServletResponse response) {
        MachineDockerInfo machineDockerInfo = machineDockerInfoService.getMachineDockerInfo(id);
        Map<String, Object> parameter = machineDockerInfoService.toParameter(machineDockerInfo);
        parameter.put("containerId", containerId);
        parameter.put("charset", StandardCharsets.UTF_8);
        parameter.put("tail", 5000);
        parameter.put("uuid", IdUtil.getSnowflakeNextIdStr());
        parameter.put("timestamps", true);
        File file = FileUtil.createTempFile(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), ".log", true);
        LogRecorder logRecorder = LogRecorder.builder().file(file).charset(StandardCharsets.UTF_8).build();
        Consumer<String> logConsumer = logRecorder::append;
        parameter.put("consumer", logConsumer);
        flowExecutorPool.execute(() -> {
            try {
                defaultDockerPlugin.execute("logContainer", parameter);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        try {
            TimeUnit.SECONDS.sleep(10);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        String fileName = file.getName();
        String contentType = ObjectUtil.defaultIfNull(FileUtil.getMimeType(fileName), "application/octet-stream");
        BufferedInputStream in = null;

        try {
            in = FileUtil.getInputStream(file);
            String charset = ObjectUtil.defaultIfNull(response.getCharacterEncoding(), "UTF-8");
            String encodeText = URLUtil.encodeAll(fileName, CharsetUtil.charset(charset));
            response.setHeader("Content-Disposition", StrUtil.format("attachment;filename=\"{}\";filename*={}''{}", new Object[]{encodeText, charset, encodeText}));
            response.setContentType(contentType);
            ServletOutputStream out = null;
            try {
                out = response.getOutputStream();
                IoUtil.copy(in, out, 8192);
            } catch (IOException var8) {
                IOException e = var8;
                throw new UtilException(e);
            } finally {
                IoUtil.close(out);
                IoUtil.close(in);
            }
        } finally {
            IoUtil.close(in);
            if (file.exists()) {
                file.delete();
            }
        }
    }

    /**
     * @return json
     */
    @PostMapping(value = "list-compose")
    public BaseResponse<List<JSONObject>> listCompose(@RequestParam String id,
                                                      @RequestParam(required = false) String name,
                                                      @RequestParam(required = false) String containerId,
                                                      @RequestParam(required = false) String imageId,
                                                      @RequestParam(required = false) Boolean showAll) throws Exception {
        Map<String, Object> parameter = this.toDockerParameter(id);
        parameter.put("name", name);
        parameter.put("containerId", containerId);
        parameter.put("imageId", imageId);
        parameter.put("showAll", showAll);
        List<JSONObject> listContainer = (List<JSONObject>) defaultDockerPlugin.execute("listComposeContainer", parameter);
        return BaseResponse.ok(listContainer);
    }
}
