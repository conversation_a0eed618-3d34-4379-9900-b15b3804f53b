package com.icarus.docker.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Entity;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.icarus.common.rest.BaseResponse;
import com.icarus.docker.cli.DefaultDockerCheckPluginImpl;
import com.icarus.docker.dto.PageDTO;
import com.icarus.docker.entity.MachineDockerInfo;
import com.icarus.docker.service.DockerInfoService;
import com.icarus.docker.service.MachineDockerInfoService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import com.icarus.docker.entity.DockerInfo;

import java.util.HashMap;
import java.util.Optional;

@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/aip/assets/docker")
@Slf4j
public class MachineDockerController {
    @Autowired
    private DockerInfoService dockerInfoService;
    @Autowired
    private MachineDockerInfoService machineDockerInfoService;
    @Autowired
    private DefaultDockerCheckPluginImpl defaultDockerCheckPlugin;


    @PostMapping(value = "list-data", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<PageDTO<MachineDockerInfo>> listJson(HttpServletRequest request,
                                                             @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                             @RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {
        PageDTO<MachineDockerInfo> pageResultDto = machineDockerInfoService.listPage(request, pageNo, pageSize);
        return BaseResponse.ok(pageResultDto);
    }

    @PostMapping(value = "edit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<Object> edit(@RequestBody MachineDockerInfo machineDockerInfo) throws Exception {
        String id = machineDockerInfo.getId();
        MachineDockerInfo dockerInfoModel = machineDockerInfoService.takeOverModel(machineDockerInfo);
        if (StrUtil.isEmpty(id)) {
            // 创建
            machineDockerInfoService.check(dockerInfoModel);
            // 默认正常
            dockerInfoModel.setStatus(1);
            // 获取容器版本

            machineDockerInfoService.save(dockerInfoModel);
        } else {
            machineDockerInfoService.check(dockerInfoModel);
            machineDockerInfoService.updateById(dockerInfoModel);
        }
        //
        return BaseResponse.ok("编辑成功");
    }

    @GetMapping(value = "try-local-docker", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<String> tryLocalDocker(HttpServletRequest request) {
        try {
            String dockerHost = (String) defaultDockerCheckPlugin.execute("testLocal", new HashMap<>(1));
            LambdaQueryWrapper<MachineDockerInfo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MachineDockerInfo::getHost, dockerHost);
            boolean exists = machineDockerInfoService.count(wrapper) > 0;
            if (exists) {
                return BaseResponse.serviceError("已重复添加: " + dockerHost);
            }
            MachineDockerInfo dockerModel = new MachineDockerInfo();
            dockerModel.setHost(dockerHost);
            dockerModel.setName("localhost");
            machineDockerInfoService.check(dockerModel);
            dockerModel.setStatus(1);
            machineDockerInfoService.save(dockerModel);
            return BaseResponse.ok("创建成功: " + dockerHost);
        } catch (Throwable e) {
            log.error("detect_local_docker_exception", e);
            return BaseResponse.serviceError("识别本地容器失败: {}" + e.getMessage());
        }
    }

    @GetMapping(value = "del", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<Object> del(@RequestParam String id) throws Exception {
        //
        {
            DockerInfo dockerInfoModel = new DockerInfo();
            dockerInfoModel.setMachineDockerId(id);
            long count = dockerInfoService.countByMachineId(id);
            Assert.state(count <= 0, "docker_associated_workspaces_message, count: " + count);
        }
        MachineDockerInfo infoModel = machineDockerInfoService.getById(id);
        Optional.ofNullable(infoModel).ifPresent(machineDockerModel -> {
            if (StrUtil.isNotEmpty(machineDockerModel.getSwarmId())) {
                // 判断集群
//                DockerSwarmInfoMode dockerInfoModel = new DockerSwarmInfoMode();
//                dockerInfoModel.setSwarmId(machineDockerModel.getSwarmId());
//                long count = dockerSwarmInfoService.count(dockerInfoModel);
//                Assert.state(count <= 0, StrUtil.format(I18nMessageUtil.get("i18n.docker_cluster_associated_workspaces_message.5520"), count));
            }
        });
        machineDockerInfoService.removeById(id);
        return BaseResponse.ok("删除成功");
    }
}
