package com.icarus.docker.utils;

import java.util.concurrent.ConcurrentLinkedDeque;

/**
 * 定长队列
 */
public class LimitQueue<E> extends ConcurrentLinkedDeque<E> {
    private final int limit;

    public LimitQueue(int limit) {
        this.limit = Math.max(limit, 0);
    }

    @Override
    public boolean offerFirst(E s) {
        if (full()) {
            // 删除最后一个
            pollLast();
        }
        return super.offerFirst(s);
    }

    @Override
    public boolean offerLast(E s) {
        if (full()) {
            // 删除第一个
            pollFirst();
        }
        return super.offerLast(s);
    }

    public boolean full() {
        return size() > limit;
    }
}
