package com.icarus.docker.dto;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;

import java.util.List;

@Data
public class PageDTO<T> {
    private List<T> records;      // 数据列表
    private long pageSize;        // 每页大小
    private long current;         // 当前页
    private long total;          // 总记录数

    public static <T> PageDTO<T> from(Page<T> page) {
        PageDTO<T> pageDTO = new PageDTO<>();
        pageDTO.setRecords(page.getRecords());
        pageDTO.setPageSize(page.getSize());
        pageDTO.setCurrent(page.getCurrent());
        pageDTO.setTotal(page.getTotal());
        return pageDTO;
    }
} 