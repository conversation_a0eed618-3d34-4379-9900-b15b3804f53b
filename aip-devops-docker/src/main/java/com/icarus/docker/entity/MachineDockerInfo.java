package com.icarus.docker.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 机器Docker信息实体类
 */
@Data
@TableName("DESIGN_DEVOPS_MACHINE_DOCKER_INFO")
public class MachineDockerInfo {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 名称
     */
    private String name;

    /**
     * 地址
     */
    private String host;

    /**
     * 开启 tls 验证
     */
    private Boolean tlsVerify;

    /**
     * 证书信息
     */
    private String certInfo;

    /**
     * 存在证书
     */
    private Boolean certExist;

    /**
     * 状态 0 异常离线 1 正常
     */
    private Integer status;

    /**
     * 错误消息
     */
    private String failureMsg;

    /**
     * docker 版本
     */
    private String dockerVersion;

    /**
     * 集群节点ID
     */
    private String swarmNodeId;

    /**
     * 最后心跳时间
     */
    private LocalDateTime lastHeartbeatTime;

    /**
     * 超时时间，单位 秒
     */
    private Integer heartbeatTimeout;

    /**
     * 集群ID
     */
    private String swarmId;

    /**
     * 仓库账号
     */
    private String registryUsername;

    /**
     * 仓库密码
     */
    private String registryPassword;

    /**
     * 仓库邮箱
     */
    private String registryEmail;

    /**
     * 仓库地址
     */
    private String registryUrl;

    /**
     * 集群的创建时间
     */
    private LocalDateTime swarmCreatedAt;

    /**
     * 集群的更新时间
     */
    private LocalDateTime swarmUpdatedAt;

    /**
     * 节点地址
     */
    private String swarmNodeAddr;

    /**
     * 集群是否能管理
     */
    private Boolean swarmControlAvailable;

    /**
     * 开启SSH访问
     */
    private Boolean enableSsh;

    /**
     * SSH Id
     */
    private String machineSshId;

    /**
     * 是否使用 sudo 执行命令
     */
    private Boolean sshUseSudo;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 