package com.icarus.docker.entity;

import cn.hutool.core.annotation.PropIgnore;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Docker集群信息实体类
 */
@Data
@TableName("DESIGN_DEVOPS_DOCKER_SWARM_INFO")
public class DockerSwarmInfo {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 集群名称
     */
    private String name;

    /**
     * 集群ID
     */
    private String swarmId;

    /**
     * 集群容器标签
     */
    private String tag;

    @TableField(exist = false)
    private MachineDockerInfo machineDocker;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 