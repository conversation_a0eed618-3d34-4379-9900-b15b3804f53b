package com.icarus.docker.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Docker信息实体类
 */
@Data
@TableName("DESIGN_DEVOPS_DOCKER_INFO")
public class DockerInfo {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * docker名称
     */
    private String name;

    /**
     * 地址
     */
    private String host;

    /**
     * 开启 tls 验证
     */
    private Boolean tlsVerify;

    /**
     * 存在证书
     */
    private Boolean certExist;

    /**
     * 集群节点ID
     */
    private String swarmNodeId;

    /**
     * 最后心跳时间
     */
    private LocalDateTime lastHeartbeatTime;

    /**
     * 超时时间，单位 秒
     */
    private Integer heartbeatTimeout;

    /**
     * 集群ID
     */
    private String swarmId;

    /**
     * 仓库账号
     */
    private String registryUsername;

    /**
     * 仓库密码
     */
    private String registryPassword;

    /**
     * 仓库邮箱
     */
    private String registryEmail;

    /**
     * 仓库地址
     */
    private String registryUrl;

    /**
     * 机器 docker id
     */
    private String machineDockerId;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 