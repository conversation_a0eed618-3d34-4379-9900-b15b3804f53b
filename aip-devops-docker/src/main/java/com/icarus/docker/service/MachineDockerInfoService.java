package com.icarus.docker.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.icarus.docker.dto.PageDTO;
import com.icarus.docker.entity.MachineDockerInfo;
import jakarta.servlet.http.HttpServletRequest;

import java.util.Map;

/**
 * 机器Docker信息服务接口
 */
public interface MachineDockerInfoService extends IService<MachineDockerInfo> {

    /**
     * 根据ID获取机器Docker信息
     * @param id 机器Docker信息ID
     * @return 机器Docker信息
     */
    MachineDockerInfo getMachineDockerInfo(String id);

    /**
     * 创建机器Docker信息
     * @param machineDockerInfo 机器Docker信息
     * @return 是否成功
     */
    boolean createMachineDockerInfo(MachineDockerInfo machineDockerInfo);

    /**
     * 更新机器Docker信息
     * @param machineDockerInfo 机器Docker信息
     * @return 是否成功
     */
    boolean updateMachineDockerInfo(MachineDockerInfo machineDockerInfo);

    /**
     * 删除机器Docker信息
     * @param id 机器Docker信息ID
     * @return 是否成功
     */
    boolean deleteMachineDockerInfo(String id);

    /**
     * 更新心跳时间
     * @param id 机器Docker信息ID
     * @return 是否成功
     */
    boolean updateHeartbeat(String id);

    /**
     * 更新状态
     * @param id 机器Docker信息ID
     * @param status 状态 0 异常离线 1 正常
     * @param failureMsg 错误消息
     * @return 是否成功
     */
    boolean updateStatus(String id, Integer status, String failureMsg);

    Map<String, Object> toParameter(MachineDockerInfo machineDockerModel);

    MachineDockerInfo tryMachineDockerBySwarmId(String swarmId);

    PageDTO<MachineDockerInfo> listPage(HttpServletRequest request, Integer pageNo, Integer pageSize);

    MachineDockerInfo takeOverModel(MachineDockerInfo machineDockerInfo);

    void check(MachineDockerInfo dockerInfoModel);
}