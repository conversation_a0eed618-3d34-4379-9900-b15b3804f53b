package com.icarus.docker.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.docker.entity.DockerInfo;
import com.icarus.docker.mapper.DockerInfoMapper;
import com.icarus.docker.service.DockerInfoService;
import com.icarus.utils.AuthUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * Docker信息服务实现类
 */
@Slf4j
@Service
public class DockerInfoServiceImpl extends ServiceImpl<DockerInfoMapper, DockerInfo> implements DockerInfoService {

    @Override
    public DockerInfo getDockerInfo(String id) {
        return this.getById(id);
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createDockerInfo(DockerInfo dockerInfo) {
        dockerInfo.setCreateTime(LocalDateTime.now());
        dockerInfo.setCreateBy(AuthUtil.getUsername());
        return this.save(dockerInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDockerInfo(DockerInfo dockerInfo) {
        dockerInfo.setUpdateTime(LocalDateTime.now());
        dockerInfo.setUpdateBy(AuthUtil.getUsername());
        return this.updateById(dockerInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDockerInfo(String id) {
        return this.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateHeartbeat(String id) {
        DockerInfo dockerInfo = new DockerInfo();
        dockerInfo.setId(id);
        dockerInfo.setLastHeartbeatTime(LocalDateTime.now());
        return this.updateById(dockerInfo);
    }

    @Override
    public long countByMachineId(String machineDockerId) {
        LambdaQueryWrapper<DockerInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DockerInfo::getMachineDockerId, machineDockerId);
        return this.count(wrapper);
    }
} 