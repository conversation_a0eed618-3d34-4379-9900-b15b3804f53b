package com.icarus.docker.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.comparator.CompareUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Entity;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.docker.cli.DefaultDockerCheckPluginImpl;
import com.icarus.docker.dto.PageDTO;
import com.icarus.docker.entity.MachineDockerInfo;
import com.icarus.docker.mapper.MachineDockerInfoMapper;
import com.icarus.docker.service.MachineDockerInfoService;
import com.icarus.utils.AuthUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.io.File;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 机器Docker信息服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MachineDockerInfoServiceImpl extends ServiceImpl<MachineDockerInfoMapper, MachineDockerInfo> implements MachineDockerInfoService {
    private final DefaultDockerCheckPluginImpl defaultDockerCheckPlugin;

    @Override
    public MachineDockerInfo getMachineDockerInfo(String id) {
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createMachineDockerInfo(MachineDockerInfo machineDockerInfo) {
        machineDockerInfo.setCreateTime(LocalDateTime.now());
        machineDockerInfo.setCreateBy(AuthUtil.getUsername());
        return this.save(machineDockerInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMachineDockerInfo(MachineDockerInfo machineDockerInfo) {
        machineDockerInfo.setUpdateTime(LocalDateTime.now());
        machineDockerInfo.setUpdateBy(AuthUtil.getUsername());
        return this.updateById(machineDockerInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMachineDockerInfo(String id) {
        return this.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateHeartbeat(String id) {
        MachineDockerInfo machineDockerInfo = new MachineDockerInfo();
        machineDockerInfo.setId(id);
        machineDockerInfo.setLastHeartbeatTime(LocalDateTime.now());
        return this.updateById(machineDockerInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(String id, Integer status, String failureMsg) {
        MachineDockerInfo machineDockerInfo = new MachineDockerInfo();
        machineDockerInfo.setId(id);
        machineDockerInfo.setStatus(status);
        machineDockerInfo.setFailureMsg(failureMsg);
        machineDockerInfo.setUpdateTime(LocalDateTime.now());
        machineDockerInfo.setUpdateBy(AuthUtil.getUsername());
        return this.updateById(machineDockerInfo);
    }

    /**
     * 插件 插件参数 map
     * @return 插件需要使用到到参数
     */
    @Override
    public Map<String, Object> toParameter(MachineDockerInfo machineDockerModel) {
        Map<String, Object> parameter = new HashMap<>(10);
        parameter.put("dockerHost", machineDockerModel.getHost());
        parameter.put("name", machineDockerModel.getName());
        parameter.put("registryUsername", machineDockerModel.getRegistryUsername());
        parameter.put("registryPassword", machineDockerModel.getRegistryPassword());
        parameter.put("registryEmail", machineDockerModel.getRegistryEmail());
        parameter.put("registryUrl", machineDockerModel.getRegistryUrl());
        parameter.put("timeout", machineDockerModel.getHeartbeatTimeout());
        // TODO 暂时不支持开启 TLS
//        if (machineDockerModel.getTlsVerify()) {
//            File filePath = certificateInfoService.getFilePath(machineDockerModel.getCertInfo());
//            Assert.notNull(filePath, I18nMessageUtil.get("i18n.docker_certificate_file_missing.ad46"));
//            parameter.put("dockerCertPath", filePath.getAbsolutePath());
//        }
//        if (Boolean.TRUE.equals(machineDockerModel.getEnableSsh())
//                && StrUtil.isNotEmpty(machineDockerModel.getMachineSshId())) {
//            // 添加SSH的操作Session
//            MachineSshModel sshModel = machineSshServer.getByKey(machineDockerModel.getMachineSshId());
//            Assert.notNull(sshModel, I18nMessageUtil.get("i18n.ssh_info_does_not_exist.5ed0"));
//            // 需要关闭之前的连接，避免阻塞
//            parameter.put("closeBefore", true);
//            parameter.put("sshUseSudo", machineDockerModel.getSshUseSudo());
//            parameter.put("session", (Supplier<Session>) () -> machineSshServer.getSessionByModel(sshModel));
//        }
        return parameter;
    }

    @Override
    public MachineDockerInfo tryMachineDockerBySwarmId(String swarmId) {
        if (StrUtil.isEmpty(swarmId)) {
            return null;
        }
        //
        MachineDockerInfo dockerInfoModel = new MachineDockerInfo();
        dockerInfoModel.setSwarmId(swarmId);
        dockerInfoModel.setSwarmControlAvailable(true);
        LambdaQueryWrapper<MachineDockerInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MachineDockerInfo::getSwarmId, swarmId);
        wrapper.eq(MachineDockerInfo::getSwarmControlAvailable, true);
        List<MachineDockerInfo> machineDockerModels = this.list(wrapper);
        if (machineDockerModels == null) {
            return null;
        }
        // 跟进在线情况排序
        machineDockerModels.sort((o1, o2) -> CompareUtil.compare(o2.getStatus(), o1.getStatus()));
        return CollUtil.getFirst(machineDockerModels);
    }

    @Override
    public PageDTO<MachineDockerInfo> listPage(HttpServletRequest request,
                                               Integer pageNo, Integer pageSize) {
        Page<MachineDockerInfo> page = new Page<>(pageNo, pageSize);
        String id = request.getParameter("id");
        String name = request.getParameter("name");
        String host = request.getParameter("host");
        LambdaQueryWrapper<MachineDockerInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StrUtil.isNotEmpty(id), MachineDockerInfo::getId, id);
        wrapper.eq(StrUtil.isNotEmpty(name), MachineDockerInfo::getName, name);
        wrapper.eq(StrUtil.isNotEmpty(host), MachineDockerInfo::getHost, host);
        Page<MachineDockerInfo> infoPage = this.page(page, wrapper);
        PageDTO<MachineDockerInfo> pageDTO = new PageDTO<>();
        pageDTO.setRecords(infoPage.getRecords());
        pageDTO.setTotal(infoPage.getTotal());
        return pageDTO;
    }

    @Override
    public MachineDockerInfo takeOverModel(MachineDockerInfo machineDockerInfo) {
        String name = machineDockerInfo.getName();
        Assert.hasText(name, "please_fill_in_name");
        String host = machineDockerInfo.getHost();
        String id = machineDockerInfo.getId();
        Boolean tlsVerifyStr = machineDockerInfo.getTlsVerify();
        String registryUrl = machineDockerInfo.getRegistryUrl();
        String registryUsername = machineDockerInfo.getRegistryUsername();
        String registryPassword = machineDockerInfo.getRegistryPassword();
        String registryEmail = machineDockerInfo.getRegistryEmail();
        int heartbeatTimeout = Integer.parseInt(Objects.toString(machineDockerInfo.getHeartbeatTimeout(), "-1"));
        String certInfo = machineDockerInfo.getCertInfo();
        Boolean enableSshStr = machineDockerInfo.getEnableSsh();
        boolean enableSsh = Convert.toBool(enableSshStr, false);
        String machineSshId = machineDockerInfo.getMachineSshId();
        boolean sshUseSudo = Convert.toBool(machineDockerInfo.getSshUseSudo(), false);
        // TODO 暂时不支持SSH
//        if (enableSsh) {
//            MachineSshModel model = machineSshServer.getByKey(machineSshId);
//            host = "ssh://" + model.getHost() + ":" + (model.getPort() == null ? 22 : model.getPort());
//        }
        boolean tlsVerify = Convert.toBool(tlsVerifyStr, false);
        // 暂时不支持 TLS
        if (tlsVerify) {
//            File filePath = certificateInfoService.getFilePath(certInfo);
//            Assert.notNull(filePath, I18nMessageUtil.get("i18n.incorrect_certificate_info.aee1"));
//            // 验证证书文件是否正确
//            boolean ok = (boolean) plugin.execute("certPath", "certPath", filePath.getAbsolutePath());
//            Assert.state(ok, I18nMessageUtil.get("i18n.certificate_info_incorrect.a950"));
        } else {
            certInfo = StrUtil.emptyToDefault(certInfo, StrUtil.EMPTY);
        }
        boolean ok = false;
        try {
            ok = (boolean) defaultDockerCheckPlugin.execute("host", "host", host);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        Assert.state(ok, "host无法连接或者错误");
        // 验证重复
//        Entity entity = Entity.create();
//        entity.set("host", host);
//        if (StrUtil.isNotEmpty(id)) {
//            entity.set("id", StrUtil.format(" <> {}", id));
//        }
        LambdaQueryWrapper<MachineDockerInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MachineDockerInfo::getHost, host);
        wrapper.ne(StrUtil.isNotEmpty(id), MachineDockerInfo::getId, id);
        boolean exists = this.count(wrapper) > 0;
        Assert.state(!exists, "docker_already_exists");
        //
        MachineDockerInfo machineDockerModel = new MachineDockerInfo();
        machineDockerModel.setHeartbeatTimeout(heartbeatTimeout);
        machineDockerModel.setHost(host);
        machineDockerModel.setEnableSsh(enableSsh);
        machineDockerModel.setMachineSshId(machineSshId);
        // 保存是会验证证书一定存在
        machineDockerModel.setCertExist(tlsVerify);
        machineDockerModel.setName(name);
        machineDockerModel.setCertInfo(certInfo);
        machineDockerModel.setTlsVerify(tlsVerify);
        machineDockerModel.setRegistryUrl(registryUrl);
        machineDockerModel.setRegistryUsername(registryUsername);
        machineDockerModel.setRegistryPassword(registryPassword);
        machineDockerModel.setRegistryEmail(registryEmail);
        machineDockerModel.setSshUseSudo(sshUseSudo);
        //
        machineDockerModel.setId(id);
        return machineDockerModel;
    }

    @Override
    public void check(MachineDockerInfo dockerInfoModel) {
        Map<String, Object> parameter = this.toParameter(dockerInfoModel);
        parameter.put("closeBefore", true);
        String errorReason = (String) defaultDockerCheckPlugin.execute("ping", parameter);
        Assert.isNull(errorReason, "unable_to_connect_to_docker" + errorReason);
        // 检查授权
        String registryUrl = dockerInfoModel.getRegistryUrl();
        if (StrUtil.isNotEmpty(registryUrl)) {
            MachineDockerInfo oldInfoModel = this.getById(dockerInfoModel.getId());
            String registryPassword = Optional.ofNullable(dockerInfoModel.getRegistryPassword())
                    .orElseGet(() -> Optional.ofNullable(oldInfoModel).map(MachineDockerInfo::getRegistryPassword).orElse(null));
            Assert.hasText(registryPassword, "repository_password_cannot_be_empty");
            parameter.put("closeBefore", true);
            parameter.put("registryPassword", registryPassword);
            try {
                JSONObject jsonObject = (JSONObject) defaultDockerCheckPlugin.execute("testAuth", parameter);
                log.info("{}", jsonObject);
            } catch (Exception e) {
                log.warn("repository_authorization_error", e);
                throw new IllegalArgumentException("incorrect_repository_credentials" + e.getMessage());
            }
        }
        // 修改状态为在线
        dockerInfoModel.setStatus(1);
        JSONObject info = defaultDockerCheckPlugin.execute("info", parameter, JSONObject.class);
        dockerInfoModel.setDockerVersion(info.getStr("serverVersion"));
    }
} 