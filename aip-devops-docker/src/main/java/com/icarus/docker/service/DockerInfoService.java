package com.icarus.docker.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.icarus.docker.entity.DockerInfo;

/**
 * Docker信息服务接口
 */
public interface DockerInfoService extends IService<DockerInfo> {

    /**
     * 根据ID获取Docker信息
     * @param id Docker信息ID
     * @return Docker信息
     */
    DockerInfo getDockerInfo(String id);

    /**
     * 创建Docker信息
     * @param dockerInfo Docker信息
     * @return 是否成功
     */
    boolean createDockerInfo(DockerInfo dockerInfo);

    /**
     * 更新Docker信息
     * @param dockerInfo Docker信息
     * @return 是否成功
     */
    boolean updateDockerInfo(DockerInfo dockerInfo);

    /**
     * 删除Docker信息
     * @param id Docker信息ID
     * @return 是否成功
     */
    boolean deleteDockerInfo(String id);

    /**
     * 更新心跳时间
     * @param id Docker信息ID
     * @return 是否成功
     */
    boolean updateHeartbeat(String id);

    long countByMachineId(String machineDockerId);
}