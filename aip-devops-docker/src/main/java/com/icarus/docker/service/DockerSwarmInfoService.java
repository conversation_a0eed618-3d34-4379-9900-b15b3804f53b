package com.icarus.docker.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.icarus.docker.entity.DockerSwarmInfo;
import jakarta.servlet.http.HttpServletRequest;

/**
 * Docker集群信息服务接口
 */
public interface DockerSwarmInfoService extends IService<DockerSwarmInfo> {

    /**
     * 根据ID获取Docker集群信息
     * @param id Docker集群信息ID
     * @return Docker集群信息
     */
    DockerSwarmInfo getDockerSwarmInfo(String id);

    /**
     * 创建Docker集群信息
     * @param dockerSwarmInfo Docker集群信息
     * @return 是否成功
     */
    boolean createDockerSwarmInfo(DockerSwarmInfo dockerSwarmInfo);

    /**
     * 更新Docker集群信息
     * @param dockerSwarmInfo Docker集群信息
     * @return 是否成功
     */
    boolean updateDockerSwarmInfo(DockerSwarmInfo dockerSwarmInfo);

    /**
     * 删除Docker集群信息
     * @param id Docker集群信息ID
     * @return 是否成功
     */
    boolean deleteDockerSwarmInfo(String id);

    /**
     * 根据集群ID获取Docker集群信息
     * @param swarmId 集群ID
     * @return Docker集群信息
     */
    DockerSwarmInfo getBySwarmId(String swarmId);

}