package com.icarus.docker.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.docker.entity.DockerSwarmInfo;
import com.icarus.docker.mapper.DockerSwarmInfoMapper;
import com.icarus.docker.service.DockerSwarmInfoService;
import com.icarus.utils.AuthUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * Docker集群信息服务实现类
 */
@Slf4j
@Service
public class DockerSwarmInfoServiceImpl extends ServiceImpl<DockerSwarmInfoMapper, DockerSwarmInfo> implements DockerSwarmInfoService {

    @Override
    public DockerSwarmInfo getDockerSwarmInfo(String id) {
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createDockerSwarmInfo(DockerSwarmInfo dockerSwarmInfo) {
        dockerSwarmInfo.setCreateTime(LocalDateTime.now());
        dockerSwarmInfo.setCreateBy(AuthUtil.getUsername());
        return this.save(dockerSwarmInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDockerSwarmInfo(DockerSwarmInfo dockerSwarmInfo) {
        dockerSwarmInfo.setUpdateTime(LocalDateTime.now());
        dockerSwarmInfo.setUpdateBy(AuthUtil.getUsername());
        return this.updateById(dockerSwarmInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDockerSwarmInfo(String id) {
        return this.removeById(id);
    }

    @Override
    public DockerSwarmInfo getBySwarmId(String swarmId) {
        LambdaQueryWrapper<DockerSwarmInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DockerSwarmInfo::getSwarmId, swarmId);
        return this.getOne(wrapper);
    }

} 