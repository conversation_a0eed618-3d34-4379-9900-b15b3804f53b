DROP TABLE IF EXISTS DESIGN_DEVOPS_DOCKER_INFO;
CREATE TABLE DESIGN_DEVOPS_DOCKER_INFO(
                                          ID VARCHAR(255) NOT NULL,
                                          NAME VARCHAR(255),
                                          HOST VARCHAR(255),
                                          TLS_VERIFY BOOLEAN,
                                          CERT_EXIST BOOLEAN,
                                          SWARM_NODE_ID VARCHAR(255),
                                          LAST_HEARTBEAT_TIME TIMESTAMP,
                                          HEA<PERSON><PERSON>AT_TIMEOUT INTEGER,
                                          SWARM_ID VARCHAR(255),
                                          REGISTRY_USERNAME VARCHAR(255),
                                          REGISTRY_PASSWORD VARCHAR(255),
                                          REGISTRY_EMAIL VARCHAR(255),
                                          R<PERSON><PERSON><PERSON>Y_URL VARCHAR(255),
                                          MACHINE_DOCKER_ID VARCHAR(255),
                                          CREATE_BY VARCHAR(32),
                                          CREATE_TIME TIMESTAMP,
                                          UPDATE_BY VARCHAR(32),
                                          UPDATE_TIME TIMESTAMP,
                                          PRIMARY KEY (ID)
);

COMMENT ON TABLE DESIGN_DEVOPS_DOCKER_INFO IS 'docker信息';
COMMENT ON COLUMN DESIGN_DEVOPS_DOCKER_INFO.ID IS '主键';
COMMENT ON COLUMN DESIGN_DEVOPS_DOCKER_INFO.NAME IS 'docker名称';
COMMENT ON COLUMN DESIGN_DEVOPS_DOCKER_INFO.HOST IS '地址';
COMMENT ON COLUMN DESIGN_DEVOPS_DOCKER_INFO.TLS_VERIFY IS '开启 tls 验证';
COMMENT ON COLUMN DESIGN_DEVOPS_DOCKER_INFO.CERT_EXIST IS '存在证书';
COMMENT ON COLUMN DESIGN_DEVOPS_DOCKER_INFO.SWARM_NODE_ID IS '集群节点ID';
COMMENT ON COLUMN DESIGN_DEVOPS_DOCKER_INFO.LAST_HEARTBEAT_TIME IS '最后心跳时间';
COMMENT ON COLUMN DESIGN_DEVOPS_DOCKER_INFO.HEARTBEAT_TIMEOUT IS '超时时间，单位 秒';
COMMENT ON COLUMN DESIGN_DEVOPS_DOCKER_INFO.SWARM_ID IS '集群ID';
COMMENT ON COLUMN DESIGN_DEVOPS_DOCKER_INFO.REGISTRY_USERNAME IS '仓库账号';
COMMENT ON COLUMN DESIGN_DEVOPS_DOCKER_INFO.REGISTRY_PASSWORD IS '仓库密码';
COMMENT ON COLUMN DESIGN_DEVOPS_DOCKER_INFO.REGISTRY_EMAIL IS '仓库邮箱';
COMMENT ON COLUMN DESIGN_DEVOPS_DOCKER_INFO.REGISTRY_URL IS '仓库地址';
COMMENT ON COLUMN DESIGN_DEVOPS_DOCKER_INFO.MACHINE_DOCKER_ID IS '机器 docker id';
COMMENT ON COLUMN DESIGN_DEVOPS_DOCKER_INFO.CREATE_BY IS '创建人';
COMMENT ON COLUMN DESIGN_DEVOPS_DOCKER_INFO.CREATE_TIME IS '创建日期';
COMMENT ON COLUMN DESIGN_DEVOPS_DOCKER_INFO.UPDATE_BY IS '更新人';
COMMENT ON COLUMN DESIGN_DEVOPS_DOCKER_INFO.UPDATE_TIME IS '更新日期';




DROP TABLE IF EXISTS DESIGN_DEVOPS_MACHINE_DOCKER_INFO;
CREATE TABLE DESIGN_DEVOPS_MACHINE_DOCKER_INFO(
                                                  ID VARCHAR(255) NOT NULL,
                                                  NAME VARCHAR(255),
                                                  HOST VARCHAR(255),
                                                  TLS_VERIFY BOOLEAN,
                                                  CERT_INFO VARCHAR(255),
                                                  CERT_EXIST BOOLEAN,
                                                  STATUS VARCHAR(1),
                                                  FAILURE_MSG VARCHAR(1000),
                                                  DOCKER_VERSION VARCHAR(255),
                                                  SWARM_NODE_ID VARCHAR(255),
                                                  LAST_HEARTBEAT_TIME TIMESTAMP,
                                                  HEARTBEAT_TIMEOUT INTEGER,
                                                  SWARM_ID VARCHAR(255),
                                                  REGISTRY_USERNAME VARCHAR(255),
                                                  REGISTRY_PASSWORD VARCHAR(255),
                                                  REGISTRY_EMAIL VARCHAR(255),
                                                  REGISTRY_URL VARCHAR(255),
                                                  SWARM_CREATED_AT TIMESTAMP,
                                                  SWARM_UPDATED_AT TIMESTAMP,
                                                  SWARM_NODE_ADDR VARCHAR(255),
                                                  SWARM_CONTROL_AVAILABLE BOOLEAN,
                                                  ENABLE_SSH BOOLEAN,
                                                  MACHINE_SSH_ID VARCHAR(255),
                                                  SSH_USE_SUDO BOOLEAN,
                                                  CREATE_BY VARCHAR(32),
                                                  CREATE_TIME TIMESTAMP,
                                                  UPDATE_BY VARCHAR(32),
                                                  UPDATE_TIME TIMESTAMP,
                                                  PRIMARY KEY (ID)
);

COMMENT ON TABLE DESIGN_DEVOPS_MACHINE_DOCKER_INFO IS '机器docker信息';
COMMENT ON COLUMN DESIGN_DEVOPS_MACHINE_DOCKER_INFO.ID IS '主键';
COMMENT ON COLUMN DESIGN_DEVOPS_MACHINE_DOCKER_INFO.NAME IS 'docker名称';
COMMENT ON COLUMN DESIGN_DEVOPS_MACHINE_DOCKER_INFO.HOST IS '地址';
COMMENT ON COLUMN DESIGN_DEVOPS_MACHINE_DOCKER_INFO.TLS_VERIFY IS '开启 tls 验证';
COMMENT ON COLUMN DESIGN_DEVOPS_MACHINE_DOCKER_INFO.CERT_INFO IS '证书信息';
COMMENT ON COLUMN DESIGN_DEVOPS_MACHINE_DOCKER_INFO.CERT_EXIST IS '存在证书';
COMMENT ON COLUMN DESIGN_DEVOPS_MACHINE_DOCKER_INFO.STATUS IS '状态 0 , 异常离线 1 正常';
COMMENT ON COLUMN DESIGN_DEVOPS_MACHINE_DOCKER_INFO.FAILURE_MSG IS '错误消息';
COMMENT ON COLUMN DESIGN_DEVOPS_MACHINE_DOCKER_INFO.DOCKER_VERSION IS 'docker 版本';
COMMENT ON COLUMN DESIGN_DEVOPS_MACHINE_DOCKER_INFO.SWARM_NODE_ID IS '集群节点ID';
COMMENT ON COLUMN DESIGN_DEVOPS_MACHINE_DOCKER_INFO.LAST_HEARTBEAT_TIME IS '最后心跳时间';
COMMENT ON COLUMN DESIGN_DEVOPS_MACHINE_DOCKER_INFO.HEARTBEAT_TIMEOUT IS '超时时间，单位 秒';
COMMENT ON COLUMN DESIGN_DEVOPS_MACHINE_DOCKER_INFO.SWARM_ID IS '集群ID';
COMMENT ON COLUMN DESIGN_DEVOPS_MACHINE_DOCKER_INFO.REGISTRY_USERNAME IS '仓库账号';
COMMENT ON COLUMN DESIGN_DEVOPS_MACHINE_DOCKER_INFO.REGISTRY_PASSWORD IS '仓库密码';
COMMENT ON COLUMN DESIGN_DEVOPS_MACHINE_DOCKER_INFO.REGISTRY_EMAIL IS '仓库邮箱';
COMMENT ON COLUMN DESIGN_DEVOPS_MACHINE_DOCKER_INFO.REGISTRY_URL IS '仓库地址';
COMMENT ON COLUMN DESIGN_DEVOPS_MACHINE_DOCKER_INFO.SWARM_CREATED_AT IS '集群的创建时间';
COMMENT ON COLUMN DESIGN_DEVOPS_MACHINE_DOCKER_INFO.SWARM_UPDATED_AT IS '集群的更新时间';
COMMENT ON COLUMN DESIGN_DEVOPS_MACHINE_DOCKER_INFO.SWARM_NODE_ADDR IS '节点地址';
COMMENT ON COLUMN DESIGN_DEVOPS_MACHINE_DOCKER_INFO.SWARM_CONTROL_AVAILABLE IS '集群是否能管理';
COMMENT ON COLUMN DESIGN_DEVOPS_MACHINE_DOCKER_INFO.ENABLE_SSH IS '开启SSH访问';
COMMENT ON COLUMN DESIGN_DEVOPS_MACHINE_DOCKER_INFO.MACHINE_SSH_ID IS 'SSH Id';
COMMENT ON COLUMN DESIGN_DEVOPS_MACHINE_DOCKER_INFO.SSH_USE_SUDO IS '是否使用 sudo 执行命令';
COMMENT ON COLUMN DESIGN_DEVOPS_MACHINE_DOCKER_INFO.CREATE_BY IS '创建人';
COMMENT ON COLUMN DESIGN_DEVOPS_MACHINE_DOCKER_INFO.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN DESIGN_DEVOPS_MACHINE_DOCKER_INFO.UPDATE_BY IS '更新人';
COMMENT ON COLUMN DESIGN_DEVOPS_MACHINE_DOCKER_INFO.UPDATE_TIME IS '更新时间';



DROP TABLE IF EXISTS DESIGN_DEVOPS_DOCKER_SWARM_INFO;
CREATE TABLE DESIGN_DEVOPS_DOCKER_SWARM_INFO(
                                                ID VARCHAR(255) NOT NULL,
                                                NAME VARCHAR(255),
                                                SWARM_ID VARCHAR(255),
                                                TAG VARCHAR(255),
                                                CREATE_BY VARCHAR(32),
                                                CREATE_TIME TIMESTAMP,
                                                UPDATE_BY VARCHAR(32),
                                                UPDATE_TIME TIMESTAMP,
                                                PRIMARY KEY (ID)
);

COMMENT ON TABLE DESIGN_DEVOPS_DOCKER_SWARM_INFO IS 'Docker集群信息';
COMMENT ON COLUMN DESIGN_DEVOPS_DOCKER_SWARM_INFO.ID IS '主键';
COMMENT ON COLUMN DESIGN_DEVOPS_DOCKER_SWARM_INFO.NAME IS '集群名称';
COMMENT ON COLUMN DESIGN_DEVOPS_DOCKER_SWARM_INFO.SWARM_ID IS '集群ID';
COMMENT ON COLUMN DESIGN_DEVOPS_DOCKER_SWARM_INFO.TAG IS '集群容器标签';
COMMENT ON COLUMN DESIGN_DEVOPS_DOCKER_SWARM_INFO.CREATE_BY IS '创建人';
COMMENT ON COLUMN DESIGN_DEVOPS_DOCKER_SWARM_INFO.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN DESIGN_DEVOPS_DOCKER_SWARM_INFO.UPDATE_BY IS '更新人';
COMMENT ON COLUMN DESIGN_DEVOPS_DOCKER_SWARM_INFO.UPDATE_TIME IS '更新时间';

