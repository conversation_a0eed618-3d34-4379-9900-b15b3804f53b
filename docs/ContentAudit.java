package com.mt.aicore.cmtt.core.service;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.mt.aicore.cmtt.common.utils.HttpClient4Utils;
import com.mt.aicore.cmtt.common.utils.SignatureUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Consts;
import org.apache.http.client.HttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

@Slf4j
@Service
public class ContentAudit {

    @Value("${huaweicloud.key.ak}")
    String ak;
    @Value("${huaweicloud.key.sk}")
    String sk;
    private HttpClient httpClient = HttpClient4Utils.createHttpClient(100, 20, 2000, 2000, 2000);

    // /*
    // * 文本内容审核
    // * */
    // public RunTextModerationResponse textAudit(String text) {
    //
    // ICredential auth = new BasicCredentials()
    // .withAk(ak)
    // .withSk(sk);
    //
    // ModerationClient client = ModerationClient.newBuilder()
    // .withCredential(auth)
    // .withRegion(ModerationRegion.valueOf("cn-north-4"))
    // .build();
    // RunTextModerationRequest request = new RunTextModerationRequest();
    // TextDetectionReq body = new TextDetectionReq();
    // TextDetectionDataReq databody = new TextDetectionDataReq();
    // databody.withText(text)
    // .withLanguage(TextDetectionDataReq.LanguageEnum.fromValue("zh"));
    // body.withData(databody);
    // body.withEventType("search");
    // request.withBody(body);
    // RunTextModerationResponse response = null;
    // try {
    // response = client.runTextModeration(request);
    // if(!response.getResult().getSuggestion().equals("pass")){
    // log.info("文本内容审核未通过：{}", text);
    // }
    //
    // } catch (ConnectionException | RequestTimeoutException |
    // ServiceResponseException e) {
    // e.printStackTrace();
    // log.info(e.getMessage());
    // }
    // return response;
    // }
    /**
     * 产品密钥ID，产品标识
     */
    @Value("${yidun.text.secret-id}")
    private String secretId;

    @Value("${yidun.text.secret-key}")
    private String secretKey;

    @Value("${yidun.text.business-id}")
    private String businessId;

    @Value("${yidun.text.api-url}")
    private String apiUrl;

    /**
     * 文本内容审核
     * @param text
     * @return
     */
    public Boolean textAudit(String text) {
        Map<String, String> params = new HashMap<String, String>();
        // 1.设置公共参数
        params.put("secretId", secretId);
        params.put("businessId", businessId);
        params.put("version", "v5.3");
        params.put("timestamp", String.valueOf(System.currentTimeMillis()));
        params.put("nonce", String.valueOf(new Random().nextInt()));
        // MD5, SM3, SHA1, SHA256
        params.put("signatureMethod", "MD5");

        // 2.设置私有参数
        params.put("dataId", "ebfcad1c-dba1-490c-b4de-e784c2691768");
        params.put("content", text);

        // 预处理参数
        params = pretreatmentParams(params);

        // 3.生成签名信息
        String signature = SignatureUtils.genSignature(secretKey, params);
        params.put("signature", signature);

        // 4.发送HTTP请求，这里使用的是HttpClient工具包，产品可自行选择自己熟悉的工具包发送请求
        String response = HttpClient4Utils.sendPost(httpClient, apiUrl, params, Consts.UTF_8);

        // 5.解析接口返回值
        JSONObject responseObj = JSONUtil.parseObj(response);
        int code = responseObj.getInt("code");
        String msg = responseObj.getStr("msg");

        if (code != 200) {
            log.error("审核请求失败: code={}, msg={}", code, msg);
            return false;
        }

        // 获取审核结果
        JSONObject antispam = responseObj.getJSONObject("result").getJSONObject("antispam");
        String taskId = antispam.getStr("taskId");
        String dataId = antispam.getStr("dataId");
        int suggestion = antispam.getInt("suggestion");
        long censorTime = antispam.getLong("censorTime");
        JSONArray labels = antispam.getJSONArray("labels");

        // resultType 为 1 时表示检测成功，继续处理 suggestion
        switch (suggestion) {
            case 0:
                log.info("文本审核通过 - taskId: {}, 审核时间: {}", taskId, censorTime);
                return true;

            case 1:
                log.info("文本需人工复审 - taskId: {}, 审核时间: {}, 分类信息: {}",
                        taskId, censorTime, labels);
                return false;
            case 2:
                log.info("文本审核不通过 - taskId: {}, 审核时间: {}, 分类信息: {}",
                        taskId, censorTime, labels);
                return false;
            default:
                log.info("未知的审核结果 - suggestion: {}", suggestion);
                return false;
        }
    }

    /**
     * 预处理参数
     *
     * @param params
     * @return
     */
    public Map<String, String> pretreatmentParams(Map<String, String> params) {
        Map<String, String> result = new HashMap<>(params.size());
        for (Map.Entry<String, String> entry : params.entrySet()) {
            // 过滤空值与原始参数中的signature，避免签名串与实际请求参数不一致导致签名校验失败
            if (entry.getValue() != null && !"signature".equals(entry.getKey())) {
                result.put(entry.getKey(), entry.getValue());
            }
        }
        return result;
    }
}
