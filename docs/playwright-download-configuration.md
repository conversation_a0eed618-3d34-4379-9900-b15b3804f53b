# Playwright 下载路径配置文档

## 概述

本文档介绍如何配置 Playwright 浏览器的下载路径和临时文件存储路径。

## 配置项说明

### 1. 配置文件位置

配置项位于各环境的 `application-{env}.yml` 文件中：

```yaml
# 爬虫配置
crawler:
  playwright:
    idle-timeout: 5  # 浏览器空闲超时时间（分钟）
    check-interval: 2  # 定时检查间隔（分钟）
    download-path: ${java.io.tmpdir}/playwright-downloads  # Playwright下载路径
    temp-path: ${java.io.tmpdir}/crawler_files  # 临时文件存储路径
```

### 2. 配置项详解

| 配置项 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `crawler.playwright.download-path` | Playwright 浏览器的下载目录 | `${java.io.tmpdir}/playwright-downloads` | `/home/<USER>/file/playwright-downloads` |
| `crawler.playwright.temp-path` | 爬虫临时文件存储目录 | `${java.io.tmpdir}/crawler_files` | `/home/<USER>/file/crawler_files` |

### 3. 环境配置示例

#### 开发环境 (application-dev.yml)
```yaml
crawler:
  playwright:
    download-path: ${java.io.tmpdir}/playwright-downloads
    temp-path: ${java.io.tmpdir}/crawler_files
```

#### 测试环境 (application-test.yml)
```yaml
crawler:
  playwright:
    download-path: ${java.io.tmpdir}/playwright-downloads
    temp-path: ${java.io.tmpdir}/crawler_files
```

#### 生产环境 (application-uat.yml)
```yaml
crawler:
  playwright:
    download-path: /home/<USER>/file/playwright-downloads
    temp-path: /home/<USER>/file/crawler_files
```

## 功能特性

### 1. 自动目录创建

系统会自动创建配置的目录：
- 如果下载目录不存在，会自动创建
- 如果临时文件目录不存在，会自动创建
- 创建失败时会记录警告日志并使用默认路径

### 2. 路径验证

- 启动时会验证路径的可读写性
- 不可写的路径会记录错误日志
- 支持相对路径和绝对路径

### 3. 兼容性处理

- 对于不支持 `setDownloadsPath` 的 Playwright 版本，会优雅降级
- 使用反射机制尝试设置下载路径
- 设置失败时会记录调试日志并继续运行

## 使用方法

### 1. 在代码中获取配置路径

```java
@Autowired
private PlaywrightBrowserService playwrightBrowserService;

// 获取下载路径
String downloadPath = playwrightBrowserService.getDownloadPath();

// 获取临时文件路径
String tempPath = playwrightBrowserService.getTempPath();
```

### 2. 自定义路径配置

可以通过以下方式自定义路径：

#### 方式一：修改配置文件
```yaml
crawler:
  playwright:
    download-path: /custom/download/path
    temp-path: /custom/temp/path
```

#### 方式二：环境变量
```bash
export CRAWLER_PLAYWRIGHT_DOWNLOAD_PATH=/custom/download/path
export CRAWLER_PLAYWRIGHT_TEMP_PATH=/custom/temp/path
```

#### 方式三：JVM 参数
```bash
-Dcrawler.playwright.download-path=/custom/download/path
-Dcrawler.playwright.temp-path=/custom/temp/path
```

## 注意事项

### 1. 权限要求

- 确保应用程序对配置的目录有读写权限
- Linux/Unix 系统需要适当的文件权限设置

### 2. 磁盘空间

- 下载目录需要足够的磁盘空间
- 临时文件会在处理完成后自动清理

### 3. 路径格式

- Windows 系统使用反斜杠或正斜杠都可以
- Linux/Unix 系统使用正斜杠
- 支持环境变量替换，如 `${java.io.tmpdir}`

### 4. 安全考虑

- 不要将下载路径设置在系统关键目录
- 建议使用专门的数据目录
- 定期清理临时文件

## 故障排除

### 1. 目录创建失败

**问题**：日志显示"创建下载目录失败"

**解决方案**：
- 检查路径权限
- 确保父目录存在
- 检查磁盘空间

### 2. Playwright 版本兼容性

**问题**：日志显示"当前Playwright版本不支持setDownloadsPath"

**解决方案**：
- 这是正常情况，系统会自动降级处理
- 可以升级 Playwright 版本以获得完整支持

### 3. 配置不生效

**问题**：配置的路径没有生效

**解决方案**：
- 检查配置文件语法
- 确认配置文件被正确加载
- 重启应用程序

## 测试验证

运行测试类验证配置：

```bash
mvn test -Dtest=PlaywrightDownloadPathTest
```

测试会验证：
- 配置是否正确读取
- 目录是否可以创建
- 路径是否可读写
