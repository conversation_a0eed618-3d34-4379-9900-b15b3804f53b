package com.icarus.sdk.mcp.server.service.aip;

import cn.hutool.json.JSONObject;
import com.icarus.sdk.mcp.annotation.MCP;
import com.icarus.sdk.mcp.annotation.McpTool;
import com.microsoft.playwright.*;
import com.microsoft.playwright.options.LoadState;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@MCP
@Service
public class aip_mcp_browser {
	private final Playwright playwright;
	private final Browser browser;
	private final Map<String, BrowserContext> contexts;
	private final Map<String, Page> pages;

	public aip_mcp_browser() {
		this.playwright = Playwright.create();
		this.browser = playwright.chromium().launch(new BrowserType.LaunchOptions()
						.setHeadless(true)
//				.setExecutablePath(Paths.get("/Applications/Chromium.app/Contents/MacOS/Chromium"))
		);
		this.contexts = new HashMap<>();
		this.pages = new HashMap<>();
	}

	/**
	 * Navigate to a specified URL, supporting opening in a new tab.
	 *
	 * @param params JSON object containing the parameters
	 * @return Result of the operation
	 * @throws Exception If an error occurs
	 */
	@McpTool(groovyFile = "com.icarus.sdk.mcp/aip-mcp-browser.groovy")
	public String browser_navigate(JSONObject params) throws Exception {
		String url = (String) params.getStr("url");
		boolean newTab = (Boolean) params.getOrDefault("new_tab", false);

		if (url == null || url.isEmpty()) {
			return "错误：必须提供URL作为参数";
		}

		if (newTab) {
			// 新建标签页
			String contextId = "context_" + System.currentTimeMillis();
			BrowserContext context = browser.newContext((new Browser.NewContextOptions()
					.setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"))
					.setExtraHTTPHeaders((Map<String, String>) new HashMap<>().put("accept-language", "zh-CN,zh;q=0.9"))
			);
			contexts.put(contextId, context);

			String pageId = "page_" + System.currentTimeMillis();
			Page page = context.newPage();
			pages.put(pageId, page);

			// 导航到指定URL
			page.navigate(url);

			return String.format("新标签页已打开，ID：%s，页面标题：%s", pageId, page.title());
		} else {
			// 使用默认标签页
			String defaultPageId = "default_page";
			if (!pages.containsKey(defaultPageId)) {
				pages.put(defaultPageId, browser.newPage());
			}

			Page defaultPage = pages.get(defaultPageId);
			defaultPage.navigate(url);

			return String.format("已导航到URL：%s，页面标题：%s", url, defaultPage.title());
		}
	}

	/**
	 * Return to the previous page.
	 *
	 * @param params JSON object (not used in this method)
	 * @return Result of the operation
	 * @throws Exception If an error occurs
	 */
	@McpTool(groovyFile = "com.icarus.sdk.mcp/aip-mcp-browser.groovy")
	public String browser_go_back(JSONObject params) throws Exception {
		String defaultPageId = "default_page";
		if (pages.containsKey(defaultPageId)) {
			Page page = pages.get(defaultPageId);
			page.goBack();
			return String.format("已返回上一页，当前URL：%s", page.url());
		} else {
			return "错误：没有页面可以返回";
		}
	}

	/**
	 * Scroll the page.
	 *
	 * @param params JSON object containing the parameters
	 * @return Result of the operation
	 * @throws Exception If an error occurs
	 */
	@McpTool(groovyFile = "com.icarus.sdk.mcp/aip-mcp-browser.groovy")
	public String browser_scroll(JSONObject params) throws Exception {
		String defaultPageId = "default_page";
		if (pages.containsKey(defaultPageId)) {
			Page page = pages.get(defaultPageId);

			String direction = (String) params.getOrDefault("direction", "down");

			if ("down".equalsIgnoreCase(direction)) {
				// 向下滚动
				page.evaluate("window.scrollBy(0, window.innerHeight)");
			} else if ("up".equalsIgnoreCase(direction)) {
				// 向上滚动
				page.evaluate("window.scrollBy(0, -window.innerHeight)");
			} else {
				return "错误：滚动方向必须是up或down";
			}

			return String.format("页面已%s滚动", direction);
		} else {
			return "错误：没有打开的页面可以滚动";
		}
	}

	/**
	 * Click on a page element.
	 *
	 * @param params JSON object containing the parameters
	 * @return Result of the operation
	 * @throws Exception If an error occurs
	 */
	@McpTool(groovyFile = "com.icarus.sdk.mcp/aip-mcp-browser.groovy")
	public String browser_click(JSONObject params) throws Exception {
		int index = (Integer) params.getOrDefault("index", 0);
		boolean newTab = (Boolean) params.getOrDefault("new_tab", false);
		String selector = (String) params.getOrDefault("selector", null);

		String defaultPageId = "default_page";
		if (pages.containsKey(defaultPageId)) {
			Page page = pages.get(defaultPageId);

			// 获取所有可点击元素
			ElementHandle[] elements = page.querySelectorAll("a, button, input[type='button'], input[type='submit']").toArray(new ElementHandle[0]);

			// 如果提供了选择器，则使用选择器定位元素
			ElementHandle element = null;
			if (selector != null && !selector.isEmpty()) {
				element = page.querySelector(selector);
			} else if (index >= 0 && index < elements.length) {
				element = elements[index];
			}

			if (element != null) {
				// 检查元素是否可见
				if (!element.isVisible()) {
					return "错误：目标元素不可见，无法点击";
				}

				// 检查元素是否可点击
				if (element.getAttribute("disabled") != null) {
					return "错误：目标元素不可点击（已禁用）";
				}

				log.info("正在点击元素，索引：{}，选择器：{}", index, selector);

				if (newTab) {
					// 在新标签页中打开
					String contextId = "context_" + System.currentTimeMillis();
					BrowserContext context = browser.newContext();
					contexts.put(contextId, context);

					String pageId = "page_" + System.currentTimeMillis();
					Page newPage = context.newPage();
					pages.put(pageId, newPage);

					// 点击元素并等待新页面加载
					element.click();

					return String.format("元素已点击并在新标签页中打开，ID：%s，页面标题：%s", pageId, newPage.title());
				} else {
					// 在当前页面点击
					element.click();

					// 等待页面加载完成
					page.waitForLoadState(LoadState.DOMCONTENTLOADED);

					return String.format("元素已点击，当前页面标题：%s，URL：%s", page.title(), page.url());
				}
			} else {
				return "错误：无效的元素索引或选择器未找到元素";
			}
		} else {
			return "错误：没有打开的页面可以点击元素";
		}
	}

	/**
	 * Enter text in the input box.
	 *
	 * @param params JSON object containing the parameters
	 * @return Result of the operation
	 * @throws Exception If an error occurs
	 */
	@McpTool(groovyFile = "com.icarus.sdk.mcp/aip-mcp-browser.groovy")
	public String browser_type(JSONObject params) throws Exception {
		int index = (Integer) params.getOrDefault("index", 0);
		String text = (String) params.get("text");
		String selector = (String) params.getOrDefault("selector", null);

		if (text == null) {
			return "错误：必须提供要输入的文本";
		}

		// 限制文本长度为10000字符
		if (text.length() > 10000) {
			return "错误：输入文本长度不能超过10000个字符";
		}

		String defaultPageId = "default_page";
		if (pages.containsKey(defaultPageId)) {
			Page page = pages.get(defaultPageId);

			// 获取所有输入框
			ElementHandle[] elements = page.querySelectorAll("input[type='text'], input[type='password'], textarea").toArray(new ElementHandle[0]);

			// 如果提供了选择器，则使用选择器定位元素
			ElementHandle element = null;
			if (selector != null && !selector.isEmpty()) {
				element = page.querySelector(selector);
			} else if (index >= 0 && index < elements.length) {
				element = elements[index];
			}

			if (element != null) {
				// 检查输入框是否可编辑
				if (element.getAttribute("readonly") != null) {
					return "错误：目标输入框是只读的，无法输入文本";
				}

				// 检查输入框是否可见
				if (!element.isVisible()) {
					return "错误：目标输入框不可见，无法输入文本";
				}

				// 清除现有内容并输入新文本
				if (index > 0) {
					log.info("正在向输入框索引{}输入文本: {}", index, text);
				} else {
					log.info("正在向输入定位器{}输入文本: {}", selector, text);
				}
				element.fill(text);

				if (index > 0) {
					return String.format("文本已输入到索引%d输入框，内容：%s", index, text);
				} else {
					return String.format("文本已输入到定位器%s输入框，内容：%s", selector, text);
				}

			} else {
				return "错误：无效的输入框索引或选择器未找到元素";
			}
		} else {
			return "错误：没有打开的页面可以输入文本";
		}
	}

	/**
	 * Get the current page state (title, URL, list of interactive elements, etc.).
	 *
	 * @param params JSON object containing the parameters
	 * @return Result of the operation
	 * @throws Exception If an error occurs
	 */
	@McpTool(groovyFile = "com.icarus.sdk.mcp/aip-mcp-browser.groovy")
	public String browser_get_state(JSONObject params) throws Exception {
		boolean includeScreenshot = (Boolean) params.getOrDefault("include_screenshot", false);

		String defaultPageId = "default_page";
		if (pages.containsKey(defaultPageId)) {
			Page page = pages.get(defaultPageId);

			StringBuilder result = new StringBuilder();
			result.append("当前页面状态：\n");
			result.append(String.format("标题：%s\n", page.title()));
			result.append(String.format("URL：%s\n", page.url()));

			// 获取所有可点击元素
			ElementHandle[] clickElements = page.querySelectorAll("a, button, input[type='button'], input[type='submit']").toArray(new ElementHandle[0]);

			// 获取所有输入框
			ElementHandle[] inputElements = page.querySelectorAll("input[type='text'], input[type='password'], textarea").toArray(new ElementHandle[0]);

			result.append(String.format("可点击元素数量：%d\n", clickElements.length));
			result.append(String.format("输入框数量：%d\n", inputElements.length));

			if (includeScreenshot) {
				String screenshotPath = String.format("page_screenshot_%d.png", System.currentTimeMillis());
				page.screenshot(new Page.ScreenshotOptions().setPath(Paths.get(screenshotPath)));
				result.append(String.format("截图已保存：%s\n", screenshotPath));
			}

			return result.toString();
		} else {
			return "错误：没有打开的页面可以获取状态";
		}
	}

	/**
	 * Extract structured content from the page based on the query.
	 *
	 * @param params JSON object containing the parameters
	 * @return Result of the operation
	 * @throws Exception If an error occurs
	 */
	@McpTool(groovyFile = "com.icarus.sdk.mcp/aip-mcp-browser.groovy")
	public String browser_extract_content(JSONObject params) throws Exception {
		String query = (String) params.get("query");
		boolean extractLinks = (Boolean) params.getOrDefault("extract_links", false);

		if (query == null || query.isEmpty()) {
			return "错误：必须提供提取要求作为参数";
		}

		String defaultPageId = "default_page";
		if (pages.containsKey(defaultPageId)) {
			Page page = pages.get(defaultPageId);

			// 这里只是一个简单的实现，实际应用中可能需要更复杂的逻辑
			// 获取页面内容
			String content = page.content();

			// 根据查询要求提取内容
			// 实际应用中可以使用正则表达式或DOM解析来提取特定内容
			String extractedContent = String.format("根据查询要求提取的内容（模拟数据）：%s\n", query);

			if (extractLinks) {
				// 提取链接
				ElementHandle[] links = page.querySelectorAll("a").toArray(new ElementHandle[0]);
				extractedContent += String.format("提取到的链接数量：%d\n", links.length);
			}

			return extractedContent;
		} else {
			return "错误：没有打开的页面可以提取内容";
		}
	}

	/**
	 * List all open tabs.
	 *
	 * @param params JSON object (not used in this method)
	 * @return Result of the operation
	 * @throws Exception If an error occurs
	 */
	@McpTool(groovyFile = "com.icarus.sdk.mcp/aip-mcp-browser.groovy")
	public String browser_list_tabs(JSONObject params) throws Exception {
		StringBuilder result = new StringBuilder();
		result.append("打开的标签页：\n");

		for (Map.Entry<String, Page> entry : pages.entrySet()) {
			result.append(String.format("ID：%s，标题：%s，URL：%s\n", entry.getKey(), entry.getValue().title(), entry.getValue().url()));
		}

		return result.toString();
	}

	/**
	 * Switch to a specified tab.
	 *
	 * @param params JSON object containing the parameters
	 * @return Result of the operation
	 * @throws Exception If an error occurs
	 */
	@McpTool(groovyFile = "com.icarus.sdk.mcp/aip-mcp-browser.groovy")
	public String browser_switch_tab(JSONObject params) throws Exception {
		String tabIndex = (String) params.get("tab_index");

		if (tabIndex == null || tabIndex.isEmpty()) {
			return "错误：必须提供要切换到的标签页索引";
		}

		// 实际应用中需要实现标签页切换逻辑
		// 这里只是一个模拟实现
		if (pages.containsKey(tabIndex)) {
			return String.format("已切换到标签页，ID：%s，标题：%s，URL：%s", tabIndex, pages.get(tabIndex).title(), pages.get(tabIndex).url());
		} else {
			return "错误：指定的标签页不存在";
		}
	}

	/**
	 * Close a specified tab.
	 *
	 * @param params JSON object containing the parameters
	 * @return Result of the operation
	 * @throws Exception If an error occurs
	 */
	@McpTool(groovyFile = "com.icarus.sdk.mcp/aip-mcp-browser.groovy")
	public String browser_close_tab(JSONObject params) throws Exception {
		String tabIndex = (String) params.get("tab_index");

		if (tabIndex == null || tabIndex.isEmpty()) {
			return "错误：必须提供要关闭的标签页索引";
		}

		// 实际应用中需要实现标签页关闭逻辑
		// 这里只是一个模拟实现
		if (pages.containsKey(tabIndex)) {
			Page page = pages.remove(tabIndex);
			return String.format("已关闭标签页，ID：%s，标题：%s", tabIndex, page.title());
		} else {
			return "错误：指定的标签页不存在";
		}
	}
}