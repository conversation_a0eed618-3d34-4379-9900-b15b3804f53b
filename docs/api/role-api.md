# 角色管理 API

## 接口列表

### 1. 创建角色
- **接口**: POST /aip/role/create
- **描述**: 创建新的角色
- **请求参数**: 
  | 参数名 | 类型 | 必填 | 描述 | 示例值 |
  |--------|------|------|------|--------|
  | roleName | String | 是 | 角色名称 | 管理员 |
  | roleCode | String | 否 | 角色编码(为空时自动生成) | admin |
  | description | String | 否 | 角色描述 | 系统管理员 |
  | createBy | String | 否 | 创建人 | admin |
  | tenantId | Integer | 否 | 租户ID | 1 |

- **返回参数**:
  | 参数名 | 类型 | 描述 |
  |--------|------|------|
  | code | Integer | 状态码 |
  | message | String | 提示信息 |
  | data | Object | 角色信息 |
  | data.id | String | 角色ID |
  | data.roleName | String | 角色名称 |
  | data.roleCode | String | 角色编码 |
  | data.description | String | 描述 |
  | data.createBy | String | 创建人 |
  | data.createTime | String | 创建时间 |
  | data.updateBy | String | 更新人 |
  | data.updateTime | String | 更新时间 |
  | data.tenantId | Integer | 租户ID |

### 2. 更新角色
- **接口**: POST /aip/role/update
- **描述**: 更新角色信息
- **请求参数**: 同创建角色，但id字段必填
  | 参数名 | 类型 | 必填 | 描述 |
  |--------|------|------|------|
  | id | String | 是 | 角色ID |
  | ... | ... | ... | 其他字段同创建角色 |

- **返回参数**: 同创建角色接口

### 3. 删除角色
- **接口**: DELETE /aip/role/{id}
- **描述**: 删除指定角色
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 |
  |--------|------|------|------|
  | id | String | 是 | 角色ID |

- **返回参数**:
  | 参数名 | 类型 | 描述 |
  |--------|------|------|
  | code | Integer | 状态码 |
  | message | String | 提示信息 |
  | data | Boolean | 删除结果 |

### 4. 获取角色详情
- **接口**: GET /aip/role/{id}
- **描述**: 获取指定角色的详细信息
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 |
  |--------|------|------|------|
  | id | String | 是 | 角色ID |

- **返回参数**: 同创建角色接口

### 5. 分页查询角色列表
- **接口**: GET /aip/role/list
- **描述**: 分页查询角色列表
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 | 默认值 |
  |--------|------|------|------|--------|
  | pageNo | Integer | 否 | 页码 | 1 |
  | pageSize | Integer | 否 | 每页大小 | 10 |
  | roleName | String | 否 | 角色名称(模糊查询) | - |
  | roleCode | String | 否 | 角色编码(模糊查询) | - |

- **返回参数**:
  | 参数名 | 类型 | 描述 |
  |--------|------|------|
  | code | Integer | 状态码 |
  | message | String | 提示信息 |
  | data | Object | 分页数据 |
  | data.records | Array | 角色列表 |
  | data.total | Long | 总记录数 |
  | data.size | Integer | 每页大小 |
  | data.current | Integer | 当前页码 |
  | data.pages | Integer | 总页数 |

### 6. 查询角色
- **接口**: GET /aip/role/query
- **描述**: 根据条件查询角色列表
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 |
  |--------|------|------|------|
  | roleName | String | 否 | 角色名称(模糊查询) |
  | roleCode | String | 否 | 角色编码(模糊查询) |

- **返回参数**:
  | 参数名 | 类型 | 描述 |
  |--------|------|------|
  | code | Integer | 状态码 |
  | message | String | 提示信息 |
  | data | Array | 角色列表 |
  | data[].id | String | 角色ID |
  | data[].roleName | String | 角色名称 |
  | data[].roleCode | String | 角色编码 |
  | data[].description | String | 描述 |
  | data[].createTime | String | 创建时间 |
  | data[].updateTime | String | 更新时间 |

## 错误码说明
- 200: 操作成功
- 400: 请求参数错误
- 500: 服务器内部错误

## 注意事项
1. 创建角色时，如果未指定roleCode且开启了多租户隔离，系统会自动生成10位随机字符串作为roleCode
2. 更新角色时必须提供角色ID
3. 角色编码在同一租户下必须唯一
4. 分页查询默认按创建时间降序排序
5. 查询接口支持角色名称和编码的模糊匹配 