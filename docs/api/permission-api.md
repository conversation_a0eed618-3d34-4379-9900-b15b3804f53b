# 菜单权限管理 API

## 接口列表

### 分页查询菜单列表
- **接口**: GET /aip/permission/list
- **描述**: 分页查询菜单列表，支持按菜单名称模糊查询，返回树形结构
- **请求参数**: 
  | 参数名 | 类型 | 必填 | 描述 | 示例值 |
  |--------|------|------|------|--------|
  | name | String | 否 | 菜单名称(模糊查询) | 系统管理 |
  | column | String | 否 | 排序字段 | createTime |
  | order | String | 否 | 排序方式(asc/desc) | desc |
  | _t | Long | 否 | 时间戳 | 1740661119180 |

- **请求示例**:
  ```
  GET /aip/permission/list?name=系统管理&column=createTime&order=desc&_t=1740661119180
  ```

- **返回示例**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": [
      {
        "id": "1",
        "name": "系统管理",
        "parentId": "0",
        "isLeaf": 0,
        "children": [
          {
            "id": "11",
            "name": "用户管理",
            "parentId": "1",
            "isLeaf": 1,
            "children": []
          }
        ]
      }
    ]
  }
  ```

- **处理逻辑**:
  1. 如果传入name参数，进行模糊查询并返回平铺结构
  2. 如果未传入name参数，返回完整的树形结构
  3. 默认按sortNo升序排序
  4. 只返回未删除的菜单(delFlag=0)

- **响应字段说明**:
  | 字段名 | 类型 | 描述 |
  |--------|------|------|
  | id | String | 菜单ID |
  | name | String | 菜单名称 |
  | parentId | String | 父菜单ID |
  | isLeaf | Integer | 是否叶子节点(0否1是) |
  | children | Array | 子菜单列表 |

### 创建菜单
- **接口**: POST /aip/permission/add
- **描述**: 创建新的菜单
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 |
  |--------|------|------|------|
  | name | String | 是 | 菜单名称 |
  | parentId | String | 否 | 父菜单ID |
  | url | String | 否 | 菜单路径 |
  | component | String | 否 | 组件路径 |
  | componentName | String | 否 | 组件名称 |
  | redirect | String | 否 | 重定向地址 |
  | menuType | Integer | 是 | 菜单类型(0一级菜单1子菜单2按钮) |
  | perms | String | 否 | 权限标识 |
  | sortNo | BigDecimal | 否 | 排序号 |
  | icon | String | 否 | 菜单图标 |
  | isLeaf | Integer | 否 | 是否叶子节点(0否1是) |

### 更新菜单
- **接口**: POST /aip/permission/update
- **描述**: 更新现有菜单信息
- **请求参数**: 同创建菜单，但id字段必填

### 删除菜单
- **接口**: DELETE /aip/permission/{id}
- **描述**: 删除指定菜单
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 |
  |--------|------|------|------|
  | id | String | 是 | 菜单ID |

### 获取菜单详情
- **接口**: GET /aip/permission/{id}
- **描述**: 获取指定菜单的详细信息
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 |
  |--------|------|------|------|
  | id | String | 是 | 菜单ID |

### 获取菜单树
- **接口**: GET /aip/permission/tree
- **描述**: 获取完整的菜单树结构

### 获取子菜单
- **接口**: GET /aip/permission/children/{parentId}
- **描述**: 获取指定父菜单的直接子菜单
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 |
  |--------|------|------|------|
  | parentId | String | 是 | 父菜单ID |

- **错误码**:
  - 200: 操作成功
  - 500: 服务器内部错误