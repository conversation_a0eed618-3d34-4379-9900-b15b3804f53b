# 部门管理 API

## 接口列表

### 1. 创建部门
- **接口**: POST /aip/depart/create
- **描述**: 创建新的部门
- **请求参数**: 
  | 参数名 | 类型 | 必填 | 描述 | 示例值 |
  |--------|------|------|------|--------|
  | departName | String | 是 | 部门名称 | 技术部 |
  | parentId | String | 否 | 父部门ID | 1001 |
  | departNameEn | String | 否 | 英文名称 | Tech Department |
  | departNameAbbr | String | 否 | 部门简称 | Tech |
  | departOrder | Integer | 否 | 排序号 | 1 |
  | description | String | 否 | 描述 | 技术研发部门 |
  | orgCategory | String | 否 | 机构类别(1公司,2组织机构,3岗位) | 1 |
  | orgType | String | 否 | 机构类型(1一级部门,2子部门) | 2 |
  | orgCode | String | 否 | 机构编码 | A01 |
  | mobile | String | 否 | 手机号 | 13800138000 |
  | fax | String | 否 | 传真 | 010-12345678 |
  | address | String | 否 | 地址 | 北京市朝阳区 |
  | status | String | 否 | 状态(1启用,0不启用) | 1 |

- **返回参数**:
  | 参数名 | 类型 | 描述 |
  |--------|------|------|
  | code | Integer | 状态码 |
  | message | String | 提示信息 |
  | data | Object | 部门信息 |
  | data.id | String | 部门ID |
  | data.departName | String | 部门名称 |
  | data.parentId | String | 父部门ID |
  | data.departNameEn | String | 英文名称 |
  | data.departNameAbbr | String | 部门简称 |
  | data.departOrder | Integer | 排序号 |
  | data.description | String | 描述 |
  | data.orgCategory | String | 机构类别 |
  | data.orgType | String | 机构类型 |
  | data.orgCode | String | 机构编码 |
  | data.mobile | String | 手机号 |
  | data.fax | String | 传真 |
  | data.address | String | 地址 |
  | data.status | String | 状态 |
  | data.createTime | String | 创建时间 |
  | data.updateTime | String | 更新时间 |

### 2. 更新部门
- **接口**: POST /aip/depart/update
- **描述**: 更新部门信息
- **请求参数**: 同创建部门，但id字段必填
  | 参数名 | 类型 | 必填 | 描述 |
  |--------|------|------|------|
  | id | String | 是 | 部门ID |
  | ... | ... | ... | 其他字段同创建部门 |

- **返回参数**: 同创建部门接口

### 3. 删除部门
- **接口**: DELETE /aip/depart/{id}
- **描述**: 删除指定部门，如果存在子部门则不允许删除
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 |
  |--------|------|------|------|
  | id | String | 是 | 部门ID |

- **返回参数**:
  | 参数名 | 类型 | 描述 |
  |--------|------|------|
  | code | Integer | 状态码 |
  | message | String | 提示信息 |
  | data | Boolean | 删除结果 |

### 4. 获取部门详情
- **接口**: GET /aip/depart/{id}
- **描述**: 获取指定部门的详细信息
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 |
  |--------|------|------|------|
  | id | String | 是 | 部门ID |

- **返回参数**: 同创建部门接口的返回参数

### 5. 获取部门树
- **接口**: GET /aip/depart/tree
- **描述**: 获取完整的部门树结构
- **请求参数**: 无

- **返回参数**:
  | 参数名 | 类型 | 描述 |
  |--------|------|------|
  | code | Integer | 状态码 |
  | message | String | 提示信息 |
  | data | Array | 部门树列表 |
  | data[].id | String | 部门ID |
  | data[].departName | String | 部门名称 |
  | data[].parentId | String | 父部门ID |
  | data[].children | Array | 子部门列表 |
  | data[].izLeaf | Integer | 是否叶子节点(1是0否) |
  | ... | ... | 其他字段同部门详情 |

### 6. 获取子部门
- **接口**: GET /aip/depart/children/{parentId}
- **描述**: 获取指定父部门的直接子部门列表
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 |
  |--------|------|------|------|
  | parentId | String | 是 | 父部门ID |

- **返回参数**:
  | 参数名 | 类型 | 描述 |
  |--------|------|------|
  | code | Integer | 状态码 |
  | message | String | 提示信息 |
  | data | Array | 子部门列表 |
  | data[].id | String | 部门ID |
  | data[].departName | String | 部门名称 |
  | ... | ... | 其他字段同部门详情 |

### 7. 分页查询部门列表
- **接口**: GET /aip/depart/list
- **描述**: 分页查询部门列表，默认按departOrder升序排序
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 | 默认值 |
  |--------|------|------|------|--------|
  | pageNo | Integer | 否 | 页码 | 1 |
  | pageSize | Integer | 否 | 每页大小 | 10 |

- **返回参数**:
  | 参数名 | 类型 | 描述 |
  |--------|------|------|
  | code | Integer | 状态码 |
  | message | String | 提示信息 |
  | data | Object | 分页数据 |
  | data.records | Array | 部门列表 |
  | data.total | Long | 总记录数 |
  | data.size | Integer | 每页大小 |
  | data.current | Integer | 当前页码 |
  | data.pages | Integer | 总页数 |

## 错误码说明
- 200: 操作成功
- 400: 请求参数错误
- 500: 服务器内部错误

## 注意事项
1. 创建部门时departName为必填项
2. 更新部门时id为必填项
3. 删除部门时会检查是否存在子部门，如存在则不允许删除
4. 部门树接口返回的是完整的树形结构，包含所有层级的部门信息
5. 分页查询默认按departOrder字段升序排序 