# 许可证管理接口文档

## 基础信息
- 基础路径: `/aip/license`
- 控制器类: `LicenseController`
- 响应格式: JSON
- 时间格式: `yyyy-MM-dd HH:mm:ss`
- 时区: GMT+8

## 许可证状态说明
- 1: 生效
- 2: 失效
- 3: 未生效

## 接口列表

### 1. 创建许可证
- **接口**: POST `/aip/license`
- **描述**: 创建新的许可证
- **请求头**:
  ```
  Content-Type: application/json
  ```
- **请求参数**: 
  | 参数名 | 类型 | 必填 | 描述 | 示例值 |
  |--------|------|------|------|--------|
  | projectName | String | 是 | 项目名称 | 示例项目 |
  | productName | String | 是 | 产品名称 | 示例产品 |
  | applicantName | String | 是 | 申请人姓名 | 张三 |
  | issuedTime | String | 是 | 生效时间 | 2024-03-15 00:00:00 |
  | expiryTime | String | 是 | 失效时间 | 2025-03-15 00:00:00 |
  | content | String | 是 | 原始内容 | {"key": "value"} |
  | remarks | String | 否 | 备注信息 | 测试用许可证 |

- **响应参数**:
  | 参数名 | 类型 | 描述 | 示例值 |
  |--------|------|------|--------|
  | code | Integer | 响应码 | 200 |
  | message | String | 响应消息 | 操作成功 |
  | data | Object | 许可证信息 | 见示例 |

- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "id": "**********",
      "licenseNumber": "LIC202403151428001",
      "projectName": "示例项目",
      "productName": "示例产品",
      "applicantName": "张三",
      "issuedTime": "2024-03-15 00:00:00",
      "expiryTime": "2025-03-15 00:00:00",
      "licenseStatus": "1",
      "content": "{\"key\": \"value\"}",
      "remarks": "测试用许可证"
    }
  }
  ```

### 2. 更新许可证
- **接口**: PUT `/aip/license`
- **描述**: 更新现有许可证信息
- **请求头**:
  ```
  Content-Type: application/json
  ```
- **请求参数**: 
  | 参数名 | 类型 | 必填 | 描述 | 示例值 |
  |--------|------|------|------|--------|
  | id | String | 是 | 许可证ID | ********** |
  | projectName | String | 否 | 项目名称 | 示例项目 |
  | productName | String | 否 | 产品名称 | 示例产品 |
  | applicantName | String | 否 | 申请人姓名 | 张三 |
  | content | String | 否 | 原始内容 | {"key": "value"} |
  | remarks | String | 否 | 备注信息 | 测试用许可证 |

- **响应格式**: 同创建许可证

### 3. 续期许可证
- **接口**: POST `/aip/license/renew`
- **描述**: 延长许可证的有效期
- **请求头**:
  ```
  Content-Type: application/json
  ```
- **请求参数**: 
  | 参数名 | 类型 | 必填 | 描述 | 示例值 |
  |--------|------|------|------|--------|
  | id | String | 是 | 许可证ID | ********** |
  | expiryTime | String | 是 | 新的过期时间 | 2025-03-15 00:00:00 |

- **请求示例**:
  ```json
  {
    "id": "**********",
    "expiryTime": "2025-03-15 00:00:00"
  }
  ```

- **响应格式**: 同创建许可证

### 4. 使许可证失效
- **接口**: PUT `/aip/license/invalidate/{id}`
- **描述**: 使指定许可证失效
- **路径参数**:
  | 参数名 | 类型 | 描述 | 示例值 |
  |--------|------|------|--------|
  | id | String | 许可证ID | ********** |

- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": true
  }
  ```

### 5. 下载许可证
- **接口**: GET `/aip/license/download/{id}`
- **描述**: 下载指定许可证文件
- **路径参数**:
  | 参数名 | 类型 | 描述 | 示例值 |
  |--------|------|------|--------|
  | id | String | 许可证ID | ********** |

- **响应格式**: 文件下载
- **响应头**:
  ```
  Content-Type: application/octet-stream
  Content-Disposition: attachment; filename=license.lic
  ```

### 6. 分页查询许可证
- **接口**: GET `/aip/license/list`
- **描述**: 分页查询许可证列表
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 | 默认值 |
  |--------|------|------|------|--------|
  | pageNo | Integer | 否 | 页码 | 1 |
  | pageSize | Integer | 否 | 每页大小 | 10 |
  | licenseNumber | String | 否 | 许可证编号 | - |
  | projectName | String | 否 | 项目名称 | - |
  | productName | String | 否 | 产品名称 | - |
  | applicantName | String | 否 | 申请人姓名 | - |
  | licenseStatus | String | 否 | 许可证状态 | - |

- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "records": [{
        "id": "**********",
        "licenseNumber": "LIC202403151428001",
        "projectName": "示例项目",
        "productName": "示例产品",
        "applicantName": "张三",
        "issuedTime": "2024-03-15 00:00:00",
        "expiryTime": "2025-03-15 00:00:00",
        "licenseStatus": "1",
        "content": "{\"key\": \"value\"}",
        "remarks": "测试用许可证"
      }],
      "total": 1,
      "size": 10,
      "current": 1,
      "pages": 1
    }
  }
  ```

### 7. 验证许可证
- **接口**: POST `/aip/license/verify`
- **描述**: 验证系统中的许可证，从 META-INF/license/license.lic 读取许可证文件进行验证
- **请求头**:
  ```
  Content-Type: application/json
  ```
- **请求参数**: 无

- **响应参数**:
  | 参数名 | 类型 | 描述 | 示例值 |
  |--------|------|------|--------|
  | valid | Boolean | 是否有效 | true |
  | licenseNumber | String | 许可证编号 | LIC202403151428001 |
  | projectName | String | 项目名称 | 示例项目 |
  | productName | String | 产品名称 | 示例产品 |
  | applicantName | String | 申请人姓名 | 张三 |
  | issuedTime | String | 生效时间 | 2024-03-15 00:00:00 |
  | expiryTime | String | 失效时间 | 2025-03-15 00:00:00 |
  | status | String | 许可证状态 | 1 |
  | message | String | 验证消息 | 许可证有效 |
  | content | Object | 许可证内容 | {"url": [...]} |

- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "valid": true,
      "licenseNumber": "LIC202403151428001",
      "projectName": "示例项目",
      "productName": "示例产品",
      "applicantName": "张三",
      "issuedTime": "2024-03-15 00:00:00",
      "expiryTime": "2025-03-15 00:00:00",
      "status": "1",
      "message": "许可证有效",
      "content": {
        "url": ["/api/example/*", "/api/test/**"]
      }
    }
  }
  ```

## 错误码说明
- 200: 操作成功
- 500: 服务器内部错误

## 注意事项
1. 所有时间字段均采用 `yyyy-MM-dd HH:mm:ss` 格式
2. 许可证状态说明：
   - 1: 生效 - 当前时间在生效时间和失效时间之间
   - 2: 失效 - 当前时间超过失效时间
   - 3: 未生效 - 当前时间早于生效时间
3. 许可证编号自动生成，格式为：LIC + 年月日时分秒 + 3位序列号
4. 创建和更新许可证时，生效时间必须早于失效时间
5. 下载接口返回的是文本文件，文件名格式为：license.lic
6. 分页查询默认按创建时间降序排序
7. 验证接口会自动从项目的 META-INF/license/license.lic 目录读取许可证文件
8. content 字段中的 url 属性用于指定许可证允许访问的 URL 路径列表，支持 Ant 风格的路径匹配 