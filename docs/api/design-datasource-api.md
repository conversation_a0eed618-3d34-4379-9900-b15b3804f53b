# 数据源管理 API 文档

## 基础信息

- 基础路径: `/aip/dataSource`
- 功能说明: 提供数据源的增删改查、连接测试、数据库表同步等功能

## API 列表

### 1. 保存数据源

创建新的数据源配置。

- **接口路径**: `/aip/dataSource/save`
- **请求方法**: POST
- **请求头**:
  - Content-Type: application/json
  - username: 当前用户名

#### 请求参数

```json
{
  "code": "string",         // 数据源编码（必填）
  "name": "string",         // 数据源名称（必填）
  "dbType": "string",       // 数据库类型（必填）
  "dbDriver": "string",     // 数据库驱动类（可选，会根据dbType自动设置）
  "dbUrl": "string",        // 数据库连接URL（必填）
  "dbUsername": "string",   // 数据库用户名（必填）
  "dbPassword": "string",   // 数据库密码（必填）
  "dbName": "string"        // 数据库名称（必填）
}
```

#### 响应结果

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": "string",
    "code": "string",
    "name": "string",
    "dbType": "string",
    "dbDriver": "string",
    "dbUrl": "string",
    "dbUsername": "string",
    "dbPassword": "string",
    "dbName": "string",
    "createBy": "string",
    "createTime": "2024-03-10T10:00:00",
    "updateBy": "string",
    "updateTime": "2024-03-10T10:00:00"
  }
}
```

### 2. 更新数据源

更新现有数据源的配置信息。

- **接口路径**: `/aip/dataSource/update`
- **请求方法**: PUT
- **请求头**:
  - Content-Type: application/json
  - username: 当前用户名

#### 请求参数

```json
{
  "id": "string",          // 数据源ID（必填）
  "code": "string",        // 数据源编码（必填）
  "name": "string",        // 数据源名称（必填）
  "dbType": "string",      // 数据库类型（必填）
  "dbDriver": "string",    // 数据库驱动类
  "dbUrl": "string",       // 数据库连接URL（必填）
  "dbUsername": "string",  // 数据库用户名（必填）
  "dbPassword": "string",  // 数据库密码（必填）
  "dbName": "string"       // 数据库名称（必填）
}
```

#### 响应结果

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": "string",
    "code": "string",
    "name": "string",
    "dbType": "string",
    "dbDriver": "string",
    "dbUrl": "string",
    "dbUsername": "string",
    "dbPassword": "string",
    "dbName": "string",
    "createBy": "string",
    "createTime": "2024-03-10T10:00:00",
    "updateBy": "string",
    "updateTime": "2024-03-10T10:00:00"
  }
}
```

### 3. 测试数据库连接

测试数据源配置的连接是否可用。

- **接口路径**: `/aip/dataSource/testConnection`
- **请求方法**: POST
- **请求头**:
  - Content-Type: application/json

#### 请求参数

```json
{
  "dbType": "string",      // 数据库类型（必填）
  "dbDriver": "string",    // 数据库驱动类
  "dbUrl": "string",       // 数据库连接URL（必填）
  "dbUsername": "string",  // 数据库用户名（必填）
  "dbPassword": "string"   // 数据库密码（必填）
}
```

#### 响应结果

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": true  // true表示连接成功，false表示连接失败
}
```

### 4. 分页查询数据源

分页查询数据源列表，支持多个查询条件。

- **接口路径**: `/aip/dataSource/list`
- **请求方法**: GET
- **请求参数**:
  - pageNo: 页码，默认1（必填）
  - pageSize: 每页大小，默认10（必填）
  - code: 数据源编码（可选）
  - name: 数据源名称（可选）
  - dbType: 数据库类型（可选）

#### 响应结果

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "total": 100,
    "records": [
      {
        "id": "string",
        "code": "string",
        "name": "string",
        "dbType": "string",
        "dbDriver": "string",
        "dbUrl": "string",
        "dbUsername": "string",
        "dbName": "string",
        "createBy": "string",
        "createTime": "2024-03-10T10:00:00",
        "updateBy": "string",
        "updateTime": "2024-03-10T10:00:00"
      }
    ]
  }
}
```

### 5. 获取数据源详情

根据ID获取数据源的详细信息。

- **接口路径**: `/aip/dataSource/get/{id}`
- **请求方法**: GET
- **路径参数**:
  - id: 数据源ID（必填）

#### 响应结果

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": "string",
    "code": "string",
    "name": "string",
    "dbType": "string",
    "dbDriver": "string",
    "dbUrl": "string",
    "dbUsername": "string",
    "dbName": "string",
    "createBy": "string",
    "createTime": "2024-03-10T10:00:00",
    "updateBy": "string",
    "updateTime": "2024-03-10T10:00:00"
  }
}
```

### 6. 同步数据库表信息

同步指定数据源的数据库表结构信息。

- **接口路径**: `/aip/dataSource/sync/{dataSourceId}`
- **请求方法**: POST
- **请求头**:
  - username: 当前用户名
- **路径参数**:
  - dataSourceId: 数据源ID（必填）

#### 响应结果

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功"
}
```

### 7. 获取数据源的表信息

获取指定数据源下所有的表和字段信息。

- **接口路径**: `/aip/dataSource/tables/{dataSourceCode}`
- **请求方法**: GET
- **路径参数**:
  - dataSourceCode: 数据源编码（必填）

#### 响应结果

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "tableName": "string",
      "tableCode": "string",
      "tableDescription": "string",
      "columns": [
        {
          "columnName": "string",
          "columnCode": "string",
          "columnDescription": "string",
          "dataType": "string",
          "isNullable": "string",
          "isPrimary": "string",
          "columnOrder": 1
        }
      ]
    }
  ]
}
```

### 8. 获取数据库类型列表

获取系统支持的所有数据库类型。

- **接口路径**: `/aip/dataSource/dbTypes`
- **请求方法**: GET

#### 响应结果

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "code": "string",
      "driverClass": "string",
      "description": "string"
    }
  ]
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200    | 操作成功 |
| 500    | 系统错误 |
| 400    | 请求参数错误 |
| 401    | 未授权 |
| 403    | 禁止访问 |
| 404    | 资源不存在 |

## 注意事项

1. 所有请求都需要进行用户认证
2. 数据源密码在传输和存储时需要进行加密处理
3. 建议在测试数据库连接成功后再保存数据源配置
4. 同步数据库表信息可能需要较长时间，建议异步处理
5. 获取表信息接口可能返回大量数据，建议合理使用分页或限制查询范围 