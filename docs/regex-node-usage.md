# 正则匹配节点使用文档

## 1. 节点介绍

正则匹配节点（RegexNode）用于对输入文本进行正则表达式匹配，支持配置多个正则表达式，按优先级顺序进行匹配。该节点可以提取文本中符合特定模式的内容，如日期、金额、方向、期限等信息。

## 2. 节点功能

- **多正则配置**：支持配置多个正则表达式，每个正则表达式可以有不同的名称、备注和优先级
- **优先级匹配**：按优先级顺序尝试匹配，优先级数值越小越优先
- **条件跳出**：可以设置跳出条件，当满足条件时停止后续匹配
- **结果聚合**：将所有匹配结果聚合到一个结构化对象中返回
- **按名称访问**：支持通过正则配置的名称直接访问对应的匹配结果
- **全局匹配**：支持查找文本中所有符合正则表达式的内容，而不仅是第一个匹配项
- **多结果处理**：当一个正则表达式匹配到多个结果时，提供多种方式访问这些结果

## 3. 配置参数说明

### 属性配置

| 参数名 | 类型 | 必填 | 说明 |
| ----- | ---- | ---- | ---- |
| inputText | String | 是 | 需要进行正则匹配的输入文本 |
| regexConfigs | Array | 是 | 正则表达式配置列表 |

### 正则配置项（RegexConfig）

| 参数名 | 类型 | 必填 | 说明 |
| ----- | ---- | ---- | ---- |
| name | String | 是 | 正则表达式名称，用于标识匹配结果 |
| comment | String | 否 | 正则表达式的备注说明 |
| regexExpression | String | 是 | 正则表达式内容 |
| priority | Integer | 是 | 优先级，数值越小优先级越高 |
| exitCondition | String | 否 | 跳出条件，目前支持"结果非空" |
| globalMatch | Boolean | 否 | 是否启用全局匹配，默认为true |

### 输出参数

| 参数名 | 类型 | 说明 |
| ----- | ---- | ---- |
| result | Object | 包含所有匹配结果的对象 |
| hasMatch | Boolean | 是否有任何正则匹配成功 |
| totalMatches | Number | 所有正则表达式匹配到的结果总数 |
| [name] | String/Array | 每个正则配置的名称会作为单独的输出参数，值为对应的匹配结果。如果匹配到多个结果，则为数组类型 |
| [name_n] | String | 当一个正则匹配到多个结果时，可通过name_1, name_2等方式访问具体的第n个匹配结果 |

## 4. 结果格式

```json
{
  "matches": [
    {
      "name": "方向",
      "result": "卖出",
      "comment": "交易方向",
      "priority": 1,
      "index": 1
    },
    {
      "name": "金额",
      "result": "1.3亿",
      "comment": "交易金额",
      "priority": 2,
      "index": 1
    },
    {
      "name": "金额",
      "result": "9KW",
      "comment": "交易金额",
      "priority": 2,
      "index": 2
    }
  ],
  "hasMatch": true,
  "byName": {
    "方向": "卖出",
    "金额": ["1.3亿", "9KW"],
    "金额_1": "1.3亿",
    "金额_2": "9KW"
  },
  "totalMatches": 3,
  "uniquePatterns": 2
}
```

## 5. 如何访问匹配结果

### 方法一：通过result对象访问

```
// 访问完整的结果对象
{result}

// 访问是否有匹配结果
{hasMatch}

// 访问匹配结果总数
{totalMatches}

// 访问所有匹配结果数组
{result.matches}

// 访问第一个匹配结果
{result.matches[0].result}

// 通过byName对象按名称访问
{result.byName.方向}

// 访问多个匹配结果
{result.byName.金额[0]} // 第一个匹配结果
{result.byName.金额[1]} // 第二个匹配结果
```

### 方法二：直接通过名称访问（推荐）

```
// 直接访问名为"方向"的匹配结果
{方向}

// 直接访问名为"金额"的所有匹配结果（如果有多个）
{金额}

// 直接访问名为"金额"的第一个匹配结果
{金额_1}

// 直接访问名为"金额"的第二个匹配结果
{金额_2}
```

## 6. 前端配置示例

```javascript
// 正则节点前端配置示例
const regexNodeConfig = {
  id: "regex_node_1",
  type: "regexNode",
  position: { x: 300, y: 200 },
  data: {
    properties: {
      inputText: "{input.text}",  // 使用上游节点输出的text字段作为输入
      regexConfigs: {
        columns: [
          { title: "名称", key: "name", dataIndex: "name" },
          { title: "正则表达式", key: "regexExpression", dataIndex: "regexExpression" },
          { title: "备注", key: "comment", dataIndex: "comment" },
          { title: "优先级", key: "priority", dataIndex: "priority" },
          { title: "跳出条件", key: "exitCondition", dataIndex: "exitCondition" },
          { title: "全局匹配", key: "globalMatch", dataIndex: "globalMatch" }
        ],
        tableData: [
          {
            name: "direction",
            comment: "交易方向",
            regexExpression: "买入|卖出",
            priority: 1,
            exitCondition: "结果非空",
            globalMatch: true
          },
          {
            name: "amount",
            comment: "交易金额",
            regexExpression: "\\d+(\\.\\d+)?亿|\\d+KW",
            priority: 2,
            exitCondition: "",
            globalMatch: true
          }
        ]
      }
    },
    inputs: {
      text: ""  // 定义输入参数
    },
    outputs: {
      result: "",  // 定义输出参数
      hasMatch: false,
      totalMatches: 0,
      direction: "",  // 每个正则名称都会作为输出参数
      amount: []  // 可能有多个匹配结果的参数定义为数组
    }
  }
};
```

## 7. 使用场景示例

### 场景一：提取文本中的关键信息

假设有一段文本描述："客户要求在2023-06-15买入100股苹果公司股票，期限为30天"，需要提取其中的日期、操作方向、数量和期限信息。

配置如下：
```json
{
  "inputText": "客户要求在2023-06-15买入100股苹果公司股票，期限为30天",
  "regexConfigs": [
    {
      "name": "date",
      "comment": "交易日期",
      "regexExpression": "\\d{4}-\\d{2}-\\d{2}",
      "priority": 1,
      "exitCondition": "结果非空"
    },
    {
      "name": "direction",
      "comment": "交易方向",
      "regexExpression": "买入|卖出",
      "priority": 2,
      "exitCondition": "结果非空"
    },
    {
      "name": "quantity",
      "comment": "交易数量",
      "regexExpression": "\\d+(?=股)",
      "priority": 3,
      "exitCondition": "结果非空"
    },
    {
      "name": "period",
      "comment": "交易期限",
      "regexExpression": "\\d+(?=天)",
      "priority": 4,
      "exitCondition": "结果非空"
    }
  ]
}
```

在下游节点中，可以直接使用 `{date}`、`{direction}`、`{quantity}` 和 `{period}` 来引用对应的匹配结果。

### 场景二：提取多个匹配项

假设有一段文本包含多个金额："本次交易涉及1.3亿和9KW两笔资金"，需要提取所有金额信息。

配置如下：
```json
{
  "inputText": "本次交易涉及1.3亿和9KW两笔资金",
  "regexConfigs": [
    {
      "name": "amount",
      "comment": "交易金额",
      "regexExpression": "\\d+(\\.\\d+)?亿|\\d+KW",
      "priority": 1,
      "globalMatch": true
    }
  ]
}
```

在下游节点中，可以通过以下方式访问结果：
- `{amount}` - 获取所有匹配到的金额列表 ["1.3亿", "9KW"]
- `{amount_1}` - 获取第一个匹配到的金额 "1.3亿"
- `{amount_2}` - 获取第二个匹配到的金额 "9KW"
- `{totalMatches}` - 获取匹配到的总数量 2

### 场景三：表单验证

验证用户输入的电子邮箱、手机号码和密码是否符合规范。

配置如下：
```json
{
  "inputText": "{form.value}",
  "regexConfigs": [
    {
      "name": "email",
      "comment": "电子邮箱验证",
      "regexExpression": "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$",
      "priority": 1,
      "exitCondition": "结果非空"
    },
    {
      "name": "mobile",
      "comment": "手机号码验证",
      "regexExpression": "^1[3-9]\\d{9}$",
      "priority": 2,
      "exitCondition": "结果非空"
    },
    {
      "name": "password",
      "comment": "密码强度验证",
      "regexExpression": "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d]{8,}$",
      "priority": 3,
      "exitCondition": "结果非空"
    }
  ]
}
```

## 8. 注意事项

1. 正则表达式需要遵循Java正则表达式语法规则
2. 优先级数值越小，匹配优先级越高
3. 当设置跳出条件为"结果非空"且匹配结果非空时，将不再执行后续正则匹配
4. 如果所有正则表达式都未能匹配，result.matches将为空数组，hasMatch为false
5. 正则表达式执行失败时会记录错误日志，但不会中断节点执行
6. 正则配置的名称（name）建议使用英文，且不要使用与节点内置属性（如result、hasMatch）相同的名称
7. 当一个正则表达式匹配到多个结果时，可以通过name_1、name_2等方式访问具体的匹配结果
8. 默认启用全局匹配(globalMatch=true)，会查找所有匹配项；如果只需要第一个匹配项，可设置globalMatch=false
9. totalMatches字段表示所有正则表达式匹配到的结果总数，可用于判断匹配情况 