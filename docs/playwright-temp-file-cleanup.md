# Playwright 临时文件清理功能

## 问题描述

Playwright 在运行时会在系统临时目录创建大量的临时文件夹，格式为 `playwright-java-{随机数字}`，每个文件夹大约 70MB。如果不及时清理，这些文件会：

1. **占用大量磁盘空间**：每个临时目录约 70MB，多次运行后会累积大量文件
2. **影响服务器性能**：在 Linux 服务器上尤其明显
3. **导致磁盘空间不足**：长期运行可能导致磁盘空间耗尽

## 解决方案

### 1. 自动清理功能

#### 配置启用
```yaml
crawler:
  playwright:
    cleanup-temp-files: true  # 启用临时文件清理（默认：true）
```

#### 清理时机
- **浏览器初始化时**：清理之前遗留的临时文件
- **浏览器资源释放时**：清理当前会话的临时文件
- **应用关闭时**：Spring 容器销毁时自动清理

### 2. 手动清理功能

#### API 端点
```http
POST /api/crawler-settings/cleanup-temp-files
```

#### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": "临时文件清理完成"
}
```

#### 使用方法
```bash
# 使用 curl 调用
curl -X POST http://localhost:8080/api/crawler-settings/cleanup-temp-files

# 或在代码中调用
playwrightBrowserService.manualCleanupTempFiles();
```

### 3. 浏览器状态监控

#### API 端点
```http
GET /api/crawler-settings/browser-status
```

#### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "initialized": true,
    "pageCount": 2,
    "contextCount": 1,
    "idleTimeoutMinutes": 5,
    "checkIntervalMinutes": 2,
    "idleTimeMinutes": 1,
    "shouldRelease": false,
    "lastUsedTime": "2025-07-28T14:30:00.000+00:00"
  }
}
```

## 清理日志示例

### 初始化时清理
```
2025-07-28 14:30:00.123 [main] INFO  PlaywrightBrowserService - 开始清理 3 个 Playwright 临时文件夹
2025-07-28 14:30:00.456 [main] INFO  PlaywrightBrowserService - 清理完成：删除了 3 个临时目录，释放空间 216 MB
```

### 资源释放时清理
```
2025-07-28 14:35:00.123 [scheduler] INFO  PlaywrightBrowserService - 开始释放 Playwright 浏览器资源...
2025-07-28 14:35:00.234 [scheduler] INFO  PlaywrightBrowserService - 开始清理 1 个 Playwright 临时文件夹
2025-07-28 14:35:00.345 [scheduler] INFO  PlaywrightBrowserService - 清理完成：删除了 1 个临时目录，释放空间 72 MB
```

### 手动清理
```
2025-07-28 14:40:00.123 [http-nio-8080-exec-1] INFO  CrawlerSettingsController - 收到清理临时文件请求
2025-07-28 14:40:00.234 [http-nio-8080-exec-1] INFO  PlaywrightBrowserService - 手动触发 Playwright 临时文件清理
2025-07-28 14:40:00.345 [http-nio-8080-exec-1] INFO  PlaywrightBrowserService - 清理完成：删除了 2 个临时目录，释放空间 144 MB
```

## 配置选项

### 完整配置示例
```yaml
crawler:
  playwright:
    idle-timeout: 5  # 浏览器空闲超时时间（分钟）
    check-interval: 2  # 定时检查间隔（分钟）
    download-path: C:\playwright-downloads  # 下载路径
    temp-path: C:\crawler-temp  # 临时文件路径
    cleanup-temp-files: true  # 启用临时文件清理
    browsers-path: C:\playwright-browsers  # 浏览器安装目录
```

### 环境特定配置

#### 开发环境（application-dev.yml）
```yaml
crawler:
  playwright:
    cleanup-temp-files: true  # 开发时启用清理
```

#### 生产环境（application-prod.yml）
```yaml
crawler:
  playwright:
    cleanup-temp-files: true  # 生产环境必须启用
```

#### 测试环境（application-test.yml）
```yaml
crawler:
  playwright:
    cleanup-temp-files: false  # 测试时可能需要保留文件用于调试
```

## 最佳实践

### 1. 生产环境建议
- **启用自动清理**：设置 `cleanup-temp-files: true`
- **定期监控**：通过 API 监控浏览器状态
- **磁盘监控**：监控临时目录的磁盘使用情况
- **定时清理**：可以设置系统级定时任务作为备份清理机制

### 2. 开发环境建议
- **启用清理**：避免开发机磁盘空间不足
- **调试时禁用**：如果需要检查临时文件，可以临时禁用清理

### 3. 容器环境建议
```dockerfile
# 在 Dockerfile 中设置临时目录清理
ENV CRAWLER_PLAYWRIGHT_CLEANUP_TEMP_FILES=true

# 或者添加定时清理脚本
RUN echo "0 */6 * * * find /tmp -name 'playwright-java-*' -type d -mtime +1 -exec rm -rf {} \;" | crontab -
```

## 故障排除

### 1. 清理失败
**问题**：临时文件清理失败
**原因**：权限不足或文件被占用
**解决**：
```bash
# 检查权限
ls -la /tmp/playwright-java-*

# 手动清理（Linux）
sudo rm -rf /tmp/playwright-java-*

# 手动清理（Windows）
rmdir /s /q C:\Users\<USER>\AppData\Local\Temp\playwright-java-*
```

### 2. 配置不生效
**问题**：清理功能没有启用
**检查**：
1. 确认配置文件中 `cleanup-temp-files: true`
2. 检查日志中是否有清理相关信息
3. 调用 `/browser-status` API 查看状态

### 3. 磁盘空间仍然不足
**问题**：清理后磁盘空间仍然不足
**排查**：
1. 检查浏览器安装目录大小
2. 检查下载目录大小
3. 检查其他临时文件

## 监控和告警

### 1. 磁盘空间监控
```bash
# Linux 监控脚本
#!/bin/bash
TEMP_SIZE=$(du -sh /tmp/playwright-java-* 2>/dev/null | awk '{sum+=$1} END {print sum}')
if [ "$TEMP_SIZE" -gt 1000 ]; then
    echo "警告：Playwright 临时文件占用空间超过 1GB"
fi
```

### 2. API 监控
```bash
# 定期检查浏览器状态
curl -s http://localhost:8080/api/crawler-settings/browser-status | jq '.data'
```

### 3. 日志监控
监控关键日志：
- `清理完成：删除了 X 个临时目录`
- `清理失败`
- `权限不足`

## 测试验证

运行测试验证清理功能：
```bash
mvn test -Dtest=PlaywrightTempFileCleanupTest
```

测试会：
1. 创建模拟的临时文件
2. 调用清理功能
3. 验证文件是否被正确删除
