# Playwright 下载路径优化

## 🎯 优化目标

解决浏览器下载位置配置问题：
1. **确保下载到指定位置**，而不是使用默认位置
2. **让环境变量设置变为可选**，而不是必须的
3. **提供更好的错误提示和配置验证**

## 🔧 优化内容

### 1. **下载路径优化**

#### **原有问题**
- 配置了下载路径但实际下载到默认位置
- 下载目录创建失败时回退到默认路径
- 缺少详细的错误信息

#### **优化后逻辑**
```java
// 确保下载到指定位置，而不是默认位置
private void ensureDownloadDirectoryExists() {
    // 1. 验证配置
    if (StrUtil.isEmpty(downloadPath)) {
        downloadPath = System.getProperty("java.io.tmpdir") + "/playwright-downloads";
    }
    
    // 2. 创建目录
    Path downloadDir = Paths.get(downloadPath);
    if (!Files.exists(downloadDir)) {
        Files.createDirectories(downloadDir);
    }
    
    // 3. 验证权限
    if (!Files.isWritable(downloadDir)) {
        throw new RuntimeException("下载目录没有写入权限: " + downloadPath);
    }
    
    // 4. 不回退到默认路径，确保用户知道配置问题
}
```

### 2. **浏览器路径优化**

#### **原有问题**
- 强制设置环境变量，即使目录为空
- 缺少灵活的配置选项

#### **优化后逻辑**
```java
private void setBrowsersPath() {
    if (browsersPath != null && !browsersPath.trim().isEmpty()) {
        // 只有在强制模式或目录中有浏览器时才设置环境变量
        if (forceBrowsersPath || !isBrowserInstallationNeeded(browsersDir)) {
            System.setProperty("PLAYWRIGHT_BROWSERS_PATH", browsersPath);
        } else {
            log.info("浏览器目录为空且未强制设置，使用默认浏览器路径");
        }
    }
}
```

### 3. **新增配置选项**

```yaml
crawler:
  playwright:
    download-path: /your/download/path  # 下载路径（必须有效）
    browsers-path:   # 浏览器路径（可选，留空使用默认）
    force-browsers-path: false  # 是否强制设置浏览器路径环境变量
```

## 📋 配置说明

### **下载路径配置**

#### **必须配置**
```yaml
crawler:
  playwright:
    download-path: /aip-home/home/<USER>
```

#### **行为**
- ✅ **确保下载到指定位置**
- ✅ **自动创建目录**（如果不存在）
- ✅ **验证目录权限**
- ❌ **不回退到默认路径**（确保用户知道配置问题）

#### **错误处理**
```
❌ 创建下载目录失败: /invalid/path
RuntimeException: 无法创建或访问下载目录: /invalid/path
```

### **浏览器路径配置**

#### **可选配置**
```yaml
crawler:
  playwright:
    browsers-path:   # 留空使用默认路径
    force-browsers-path: false
```

#### **三种模式**

##### **1. 默认模式（推荐）**
```yaml
browsers-path:   # 留空
force-browsers-path: false
```
- 使用 Playwright 默认浏览器路径
- 无需手动安装浏览器
- 最简单的配置

##### **2. 自定义路径 + 自动模式**
```yaml
browsers-path: /custom/browser/path
force-browsers-path: false
```
- 只有在目录中有浏览器时才使用自定义路径
- 目录为空时自动回退到默认路径
- 灵活的配置

##### **3. 自定义路径 + 强制模式**
```yaml
browsers-path: /custom/browser/path
force-browsers-path: true
```
- 强制使用自定义路径
- 需要手动安装浏览器到指定目录
- 完全控制浏览器位置

## 🚀 使用指南

### **快速开始（推荐）**

```yaml
crawler:
  playwright:
    download-path: /your/download/folder  # 只需配置下载路径
    # 其他配置使用默认值
```

### **自定义浏览器路径**

如果需要自定义浏览器路径：

1. **配置路径**
```yaml
crawler:
  playwright:
    browsers-path: /custom/browser/path
    force-browsers-path: true
```

2. **安装浏览器**
```bash
PLAYWRIGHT_BROWSERS_PATH=/custom/browser/path mvn exec:java -e -D exec.mainClass=com.microsoft.playwright.CLI -D exec.args="install"
```

### **验证配置**

运行测试验证配置：
```bash
mvn test -Dtest=DownloadPathConfigTest
```

## 🔍 故障排除

### **下载路径问题**

#### **问题**: 下载目录创建失败
```
❌ 创建下载目录失败: /invalid/path
```

#### **解决方案**:
1. 检查路径是否有效
2. 检查父目录是否存在
3. 检查目录权限
4. 使用绝对路径而不是相对路径

#### **问题**: 下载目录没有写入权限
```
❌ 下载目录没有写入权限: /path/to/downloads
```

#### **解决方案**:
```bash
# Linux/Mac
chmod 755 /path/to/downloads

# Windows
# 右键 -> 属性 -> 安全 -> 编辑权限
```

### **浏览器路径问题**

#### **问题**: 浏览器目录为空
```
⚠️ 检测到浏览器安装目录为空或不完整: /custom/path
```

#### **解决方案**:
1. **选项1**: 安装浏览器到自定义路径
```bash
PLAYWRIGHT_BROWSERS_PATH=/custom/path mvn exec:java -e -D exec.mainClass=com.microsoft.playwright.CLI -D exec.args="install"
```

2. **选项2**: 使用默认路径
```yaml
browsers-path:   # 留空
force-browsers-path: false
```

## 📊 配置对比

| 配置项 | 优化前 | 优化后 |
|--------|--------|--------|
| **下载路径** | 可能下载到默认位置 | ✅ 确保下载到指定位置 |
| **目录创建** | 失败时使用默认路径 | ✅ 失败时抛出异常 |
| **浏览器路径** | 强制设置环境变量 | ✅ 可选设置 |
| **错误提示** | 简单日志 | ✅ 详细错误信息 |
| **配置验证** | 运行时发现问题 | ✅ 启动时验证 |

## 🎉 优势

### **1. 可靠性**
- 确保下载到指定位置
- 不会静默回退到默认路径
- 详细的错误信息

### **2. 灵活性**
- 环境变量设置变为可选
- 支持多种配置模式
- 向后兼容

### **3. 易用性**
- 最小配置即可使用
- 清晰的错误提示
- 完整的文档说明

### **4. 可维护性**
- 配置验证测试
- 详细的日志输出
- 故障排除指南
