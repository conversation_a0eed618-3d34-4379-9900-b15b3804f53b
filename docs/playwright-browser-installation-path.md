# Playwright 浏览器安装目录配置

## 概述

本文档介绍如何自定义 Playwright 浏览器的安装目录，避免使用默认的用户目录，特别适用于服务器环境或需要统一管理浏览器的场景。

## 配置方法

### 1. 通过配置文件配置（推荐）

在 `application-{env}.yml` 中配置：

```yaml
crawler:
  playwright:
    browsers-path: C:\playwright-browsers  # Windows
    # 或
    browsers-path: /opt/playwright-browsers  # Linux
```

### 2. 通过环境变量配置

```bash
# Windows
set PLAYWRIGHT_BROWSERS_PATH=C:\playwright-browsers

# Linux/Mac
export PLAYWRIGHT_BROWSERS_PATH=/opt/playwright-browsers
```

### 3. 通过 JVM 参数配置

```bash
-Dcrawler.playwright.browsers-path=C:\playwright-browsers
```

## 配置示例

### Windows 开发环境
```yaml
crawler:
  playwright:
    browsers-path: C:\playwright-browsers
    download-path: C:\playwright-browsers\downloads
    temp-path: C:\crawler-temp
```

### Linux 生产环境
```yaml
crawler:
  playwright:
    browsers-path: /opt/playwright-browsers
    download-path: /opt/playwright-browsers/downloads
    temp-path: /tmp/crawler-temp
```

## 安装浏览器到自定义目录

### 方法1：使用配置的路径安装

配置好路径后，运行以下命令：

```bash
# 安装所有浏览器
mvn exec:java -e -D exec.mainClass=com.microsoft.playwright.CLI -D exec.args="install"

# 只安装 Chromium（推荐）
mvn exec:java -e -D exec.mainClass=com.microsoft.playwright.CLI -D exec.args="install chromium"
```

### 方法2：指定路径安装

```bash
# Windows
set PLAYWRIGHT_BROWSERS_PATH=C:\playwright-browsers
mvn exec:java -e -D exec.mainClass=com.microsoft.playwright.CLI -D exec.args="install"

# Linux
PLAYWRIGHT_BROWSERS_PATH=/opt/playwright-browsers mvn exec:java -e -D exec.mainClass=com.microsoft.playwright.CLI -D exec.args="install"
```

## 目录结构

安装完成后，目录结构如下：

```
C:\playwright-browsers\  (或 /opt/playwright-browsers/)
├── chromium-1234567\
│   ├── chrome.exe (或 chrome)
│   └── ... (其他文件)
├── firefox-1234567\
│   ├── firefox.exe (或 firefox)
│   └── ... (其他文件)
└── webkit-1234567\
    ├── Playwright.exe (或 Playwright)
    └── ... (其他文件)
```

## 优势

### 1. 统一管理
- 所有环境使用相同的浏览器版本
- 便于备份和迁移
- 减少重复下载

### 2. 权限控制
- 可以设置特定的目录权限
- 避免用户目录权限问题
- 适合容器化部署

### 3. 磁盘管理
- 可以选择有足够空间的磁盘
- 避免用户目录空间不足
- 便于监控磁盘使用情况

## 注意事项

### 1. 权限要求
- 确保应用有读写指定目录的权限
- Linux 系统可能需要 sudo 权限创建 /opt 目录

### 2. 磁盘空间
- 每个浏览器约需要 200-300MB 空间
- 建议预留至少 1GB 空间

### 3. 网络要求
- 首次安装需要下载浏览器
- 确保网络连接稳定

## 故障排除

### 1. 权限问题
```bash
# Linux 创建目录并设置权限
sudo mkdir -p /opt/playwright-browsers
sudo chown -R $USER:$USER /opt/playwright-browsers
```

### 2. 安装失败
检查日志中的错误信息：
- 网络连接问题
- 磁盘空间不足
- 权限不足

### 3. 路径不生效
确认配置优先级：
1. JVM 参数
2. 环境变量
3. 配置文件

## 验证安装

运行应用后，查看日志输出：

```
设置 Playwright 浏览器安装路径: C:\playwright-browsers
找到 Playwright 浏览器安装目录: C:\playwright-browsers
  - chromium-1234567 (大小: 245 MB)
```

## Docker 环境配置

在 Dockerfile 中：

```dockerfile
# 创建浏览器目录
RUN mkdir -p /opt/playwright-browsers

# 设置环境变量
ENV PLAYWRIGHT_BROWSERS_PATH=/opt/playwright-browsers

# 安装浏览器
RUN mvn exec:java -e -D exec.mainClass=com.microsoft.playwright.CLI -D exec.args="install chromium"
```

## 最佳实践

1. **生产环境**：使用系统级目录如 `/opt/playwright-browsers`
2. **开发环境**：可以使用用户目录如 `C:\playwright-browsers`
3. **容器环境**：使用容器内固定路径
4. **定期清理**：清理旧版本浏览器释放空间
5. **监控空间**：定期检查浏览器目录大小
