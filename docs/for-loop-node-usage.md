# FOR循环节点与循环控制节点使用文档

## 1. 节点介绍

FOR循环节点（ForLoopNode）用于实现标准的Java风格for循环控制流程，完全遵循`for(初始化表达式; 条件表达式; 更新表达式) { 循环体 }`的语法结构。循环控制节点（LoopBreakNode）用于在满足特定条件时控制循环的执行流程，类似于编程语言中的break和continue关键字。

### 1.1 循环节点的画布连接方式

FOR循环节点在画布上有四个连接端口：
- **in**：循环的入口，从其他节点到循环节点的常规连接
- **out**：循环的出口，循环完成后连接到后续节点
- **loopIn**：循环体的入口，连接到循环体内的第一个节点
- **loopOut**：循环体的出口，循环体内的最后一个节点连回此端口

系统会自动识别连接到 loopIn 和 loopOut 端口的节点及其相关连接，将它们视为循环体内容。

## 2. 节点功能

### 2.1 FOR循环节点功能

- **标准语法**：完全对应Java中的`for(初始化表达式; 条件表达式; 更新表达式)`语法
- **循环变量**：固定使用"i"作为循环变量名
- **条件判断**：支持多种比较运算符的条件表达式
- **灵活更新**：支持多种更新方式（自增、自减、复合赋值等）
- **迭代限制**：内置最大迭代次数10000，防止无限循环
- **结果收集**：自动收集每次迭代的循环变量值到结果数组中
- **循环对象**：支持在循环过程中读写共享对象
- **循环内容配置**：支持通过 loopContent 配置循环体内容，也可通过端口连接自动识别
- **嵌套循环控制**：支持多层嵌套循环，可通过循环控制节点控制特定层级的循环

### 2.2 循环控制节点功能

- **循环中断**：相当于编程中的break，立即终止整个循环
- **迭代跳过**：相当于编程中的continue，跳过当前迭代的结果收集
- **简单配置**：只需选择控制类型（break/continue）即可
- **目标循环级别**：支持指定要控制的循环层级，可以从内层循环控制外层循环

## 3. 配置参数说明

### 3.1 FOR循环节点属性配置

| 参数名 | 类型 | 必填 | 说明 |
| ----- | ---- | ---- | ---- |
| initialization | String | 是 | 初始化表达式，例如: "i = 0" |
| condition | String | 是 | 条件表达式，例如: "i < 10" |
| update | String | 是 | 更新表达式，例如: "i++" |
| loopContent | Object | 否 | 循环体内容配置，用于定义循环体的子画布 |

### 3.2 循环内容配置说明

循环内容(loopContent)主要包含以下属性：

| 参数名 | 类型 | 说明 |
| ----- | ---- | ---- |
| canvas | Object | 子画布配置，定义循环体内容的节点和连接关系 |

**注意**：虽然可以通过 loopContent 配置循环体内容，但更常见的方式是通过画布上的 loopIn 和 loopOut 端口连接来自动识别循环体。

### 3.3 循环控制节点属性配置

| 参数名 | 类型 | 必填 | 说明 |
| ----- | ---- | ---- | ---- |
| type | String | 是 | 控制类型："break"（终止循环）或"continue"（跳过当前迭代） |
| targetLoopLevel | Integer | 否 | 目标循环层级：0表示当前循环(默认)，1表示父循环 |

### 3.4 FOR循环节点输出参数

| 参数名 | 类型 | 说明 |
| ----- | ---- | ---- |
| result | Array | 包含所有迭代循环变量值的数组 |
| currentIndex | Integer | 最后一次迭代的索引 |
| currentItem | Object | 最后一次迭代的循环变量值 |
| loopObject | Object | 循环过程中可读写的对象 |

## 4. 循环上下文变量

在使用FOR循环节点时，以下变量可供其他节点使用：

| 变量名 | 类型 | 说明 |
| ----- | ---- | ---- |
| loop.index | Integer | 当前循环的索引值（从1开始） |
| loop.item | Object | 当前循环的循环变量值 |
| loop.object | Object | 循环中可读写的对象，用于在迭代之间传递数据 |
| loop.result | Array | 当前已收集的所有迭代结果 |
| current_loop_id | String | 当前循环的唯一ID（用于嵌套循环控制）|
| parent_loop_id | String | 父循环的唯一ID（用于嵌套循环控制）|

## 5. 结果格式

```json
{
  "result": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
  "currentIndex": 10,
  "currentItem": 9,
  "loopObject": {"sum": 45}
}
```

## 6. 如何访问循环结果

### 方法一：通过输出参数访问

```
// 访问所有迭代结果
{result}

// 访问特定的迭代结果
{result[0]}
{result[1]}

// 访问最后一次迭代的索引
{currentIndex}

// 访问最后一次迭代的循环变量值
{currentItem}

// 访问循环对象
{loopObject}

// 访问循环对象中的特定属性
{loopObject.sum}
```

### 方法二：通过上下文变量访问

在其他节点中，可以使用以下方式访问循环变量：

```
// 访问当前索引
{loop.index}

// 访问当前循环变量值
{loop.item}

// 访问或更新循环对象
{loop.object}

// 访问循环结果数组
{loop.result}

// 嵌套循环控制时使用
{current_loop_id}  // 当前循环的唯一ID
{parent_loop_id}   // 父循环的唯一ID
```

## 7. 画布配置示例

### 7.1 FOR循环节点的画布连接配置

FOR循环节点在画布中需要按照以下方式连接，以定义循环体内容：

1. 将前置节点连接到FOR循环节点的 **in** 端口
2. 将FOR循环节点的 **loopIn** 端口连接到循环体的第一个节点
3. 将循环体的最后一个节点连接到FOR循环节点的 **loopOut** 端口
4. 将FOR循环节点的 **out** 端口连接到循环后的下一个节点

```
[前置节点] --> (in) [FOR循环节点] (out) --> [循环后节点]
                    |         ^
                    |         |
                (loopIn)    (loopOut)
                    |         |
                    v         |
               [循环体第一个节点]
                    |
                    v
               [循环体节点...]
                    |
                    v
               [循环体最后节点]
```

### 7.2 FOR循环节点配置示例

```javascript
// FOR循环节点配置示例 - 模拟 for(int i = 0; i < 10; i++)
const forLoopNodeConfig = {
  id: "for_loop_node_1",
  type: "forLoopNode",
  position: { x: 300, y: 200 },
  data: {
    properties: {
      initialization: "i = 0",    // 初始化表达式
      condition: "i < 10",        // 条件表达式
      update: "i++",             // 更新表达式
    },
    outputs: {
      result: [],
      currentIndex: 0,
      currentItem: 0,
      loopObject: {}
    }
  }
};
```

### 7.3 循环控制节点配置示例

```javascript
// 循环控制节点前端配置示例 - 控制当前循环
const loopBreakNodeConfig = {
  id: "loop_break_node_1",
  type: "loopBreakNode",
  position: { x: 300, y: 300 },
  data: {
    properties: {
      type: "break",  // 或者 "continue"
      targetLoopLevel: 0 // 0表示当前循环(默认)
    }
  }
};

// 循环控制节点前端配置示例 - 控制父循环
const parentLoopBreakNodeConfig = {
  id: "parent_loop_break_node_1",
  type: "loopBreakNode",
  position: { x: 300, y: 400 },
  data: {
    properties: {
      type: "break",  // 或者 "continue"
      targetLoopLevel: 1 // 1表示父循环
    }
  }
};
```

## 8. 使用场景示例

### 场景一：累加计算（基本for循环）

模拟标准for循环: `for(int i = 1; i <= 100; i++)`，计算1到100的和。

配置和处理流程：

1. FOR循环节点配置：
```json
{
  "initialization": "i = 1",
  "condition": "i <= 100",
  "update": "i++",
  "loopContent": {}
}
```

2. 循环体内更新循环对象：
```javascript
const currentValue = context['loop.item']; // 当前i值
const loopObject = context['loop.object'] || {};

// 初始化循环对象（如果是第一次迭代）
if (context['loop.index'] === 1) {
  loopObject.sum = 0;
}

// 累加当前值
loopObject.sum += currentValue;

// 更新回上下文
context['loop.object'] = loopObject;
```

3. 循环执行完成后，可以通过以下方式访问结果：
   - 所有循环值：`{result}` (数组[1,2,3,...,100])
   - 累加和：`{loopObject.sum}` (值为5050)

### 场景二：遍历字符（步长递增）

模拟步长为2的for循环: `for(int i = 0; i < 10; i += 2)`，生成偶数列表。

配置和处理流程：

1. FOR循环节点配置：
```json
{
  "initialization": "i = 0",
  "condition": "i < 10",
  "update": "i += 2",
  "loopContent": {}
}
```

2. 循环体内处理：
```javascript
const currentValue = context['loop.item']; // 当前i值
const loopObject = context['loop.object'] || {};

// 初始化循环对象（如果是第一次迭代）
if (context['loop.index'] === 1) {
  loopObject.evens = [];
}

// 添加当前偶数
loopObject.evens.push(currentValue);

// 更新回上下文
context['loop.object'] = loopObject;
```

3. 循环执行完成后：
   - result 数组为 [0, 2, 4, 6, 8]
   - loopObject.evens 也为 [0, 2, 4, 6, 8]

### 场景三：条件中断循环

模拟带break的for循环，找到第一个能被13整除的数。

配置和处理流程：

1. FOR循环节点配置：
```json
{
  "initialization": "i = 100",
  "condition": "i < 200",
  "update": "i++",
  "loopContent": {}
}
```

2. 循环体内添加条件判断节点：
```javascript
const currentValue = context['loop.item']; // 当前i值
const loopObject = context['loop.object'] || {};

// 检查当前值是否能被13整除
if (currentValue % 13 === 0) {
  // 记录找到的值
  loopObject.found = currentValue;
  // 设置break标志终止循环
  context['loop-break-flag'] = true;
}

// 更新回上下文
context['loop.object'] = loopObject;
```

3. 循环执行完成后：
   - loopObject.found 的值为104（100到200之间第一个能被13整除的数）

### 场景四：构建斐波那契数列

使用for循环生成斐波那契数列的前20个数。

配置和处理流程：

1. FOR循环节点配置：
```json
{
  "initialization": "i = 0",
  "condition": "i < 20",
  "update": "i++",
  "loopContent": {}
}
```

2. 循环体内计算斐波那契数并存储：
```javascript
const index = context['loop.item'];  // 当前i值
const loopObject = context['loop.object'] || {};

// 初始化斐波那契数列数组
if (!loopObject.fibonacci) {
  loopObject.fibonacci = [0, 1];
}

// 计算下一个斐波那契数
if (index >= 2) {
  const nextFib = loopObject.fibonacci[index - 1] + loopObject.fibonacci[index - 2];
  loopObject.fibonacci.push(nextFib);
}

// 更新回上下文
context['loop.object'] = loopObject;
```

3. 循环执行完成后，可以通过以下方式访问结果：
   - 斐波那契数列：`{loopObject.fibonacci}`

### 场景五：遍历数组元素

需要遍历输入的用户数组，处理每个用户的数据。

配置和处理流程：

1. FOR循环节点配置（假设输入有一个userArray数组）：
```json
{
  "initialization": "i = 0",
  "condition": "i < {input.userArray.length}",
  "update": "i++",
  "loopContent": {}
}
```

2. 循环体内处理数组元素：
```javascript
const i = context['loop.item'];  // 当前索引值
const loopObject = context['loop.object'] || {};
const userArray = context.input.userArray;  // 输入的用户数组

// 获取当前用户
const currentUser = userArray[i];

// 初始化结果数组
if (context['loop.index'] === 1) {
  loopObject.processedUsers = [];
  loopObject.totalAge = 0;
}

// 处理用户数据
if (currentUser) {
  // 例如，添加一个计算后的字段
  const processedUser = {
    ...currentUser,
    isAdult: currentUser.age >= 18,
    greeting: `你好，${currentUser.name}！`
  };
  
  // 累计年龄
  loopObject.totalAge += currentUser.age;
  
  // 添加到处理结果中
  loopObject.processedUsers.push(processedUser);
}

// 更新回上下文
context['loop.object'] = loopObject;
```

3. 循环执行完成后：
   - loopObject.processedUsers 包含处理后的所有用户数据
   - loopObject.totalAge 包含所有用户年龄之和

### 场景六：处理二维数组

需要处理一个二维矩阵，计算每行的和与总和。

配置和处理流程：

1. 外层FOR循环节点配置（行遍历）：
```json
{
  "initialization": "row = 0",
  "condition": "row < {input.matrix.length}",
  "update": "row++",
  "loopContent": {}
}
```

2. 外层循环体内嵌套内层循环（列遍历）：
```json
{
  "initialization": "col = 0",
  "condition": "col < {input.matrix[row].length}",
  "update": "col++",
  "loopContent": {}
}
```

3. 内层循环体内进行处理：
```javascript
const row = context.parent['loop.item'];  // 当前行索引
const col = context['loop.item'];         // 当前列索引
const matrix = context.input.matrix;      // 输入的二维矩阵
const loopObject = context['loop.object'] || {};
const parentLoopObject = context.parent['loop.object'] || {};

// 获取当前元素值
const value = matrix[row][col];

// 初始化当前行的和
if (context['loop.index'] === 1) {
  loopObject.rowSum = 0;
}

// 累加当前元素到行和
loopObject.rowSum += value;

// 如果是最后一列，记录行和
if (col === matrix[row].length - 1) {
  // 初始化行和数组和总和（只在第一行时）
  if (context.parent['loop.index'] === 1) {
    parentLoopObject.rowSums = [];
    parentLoopObject.totalSum = 0;
  }
  
  // 添加当前行的和到行和数组
  parentLoopObject.rowSums.push(loopObject.rowSum);
  parentLoopObject.totalSum += loopObject.rowSum;
  
  // 更新父循环上下文
  context.parent['loop.object'] = parentLoopObject;
}

// 更新当前循环上下文
context['loop.object'] = loopObject;
```

4. 循环执行完成后：
   - parentLoopObject.rowSums 包含每行的和
   - parentLoopObject.totalSum 包含整个矩阵的总和

### 场景七：嵌套循环控制

模拟嵌套循环结构，并使用循环控制节点控制不同层级的循环。

配置和处理流程：

1. 外层FOR循环节点配置：
```json
{
  "initialization": "i = 0",
  "condition": "i < 5",
  "update": "i++"
}
```

2. 内层FOR循环节点配置：
```json
{
  "initialization": "j = 0",
  "condition": "j < 3",
  "update": "j++"
}
```

3. 在内层循环内添加条件判断和循环控制节点：
```javascript
const i = context.parent['loop.item']; // 外层循环索引
const j = context['loop.item'];        // 内层循环索引
const loopObject = context['loop.object'] || {};

// 初始化
if (context['loop.index'] === 1) {
  loopObject.matrix = [];
  for (let row = 0; row < 5; row++) {
    loopObject.matrix[row] = [];
  }
}

// 记录当前位置的值
loopObject.matrix[i][j] = `(${i},${j})`;

// 特殊情况：当达到特定条件时，跳出内层循环
if (j === 1 && i === 2) {
  // 中断当前(内层)循环，targetLoopLevel=0(默认)
  context['loop-break-flag-' + context['current_loop_id']] = true;
}

// 特殊情况：当达到特定条件时，跳出外层循环
if (j === 2 && i === 3) {
  // 中断父(外层)循环，targetLoopLevel=1
  context['loop-break-flag-' + context['parent_loop_id']] = true;
}

// 更新循环对象
context['loop.object'] = loopObject;
```

4. 使用循环控制节点（更推荐的方式）：
```json
// 内层循环控制节点配置（控制当前循环）
{
  "type": "break",
  "targetLoopLevel": 0
}

// 外层循环控制节点配置（控制父循环）
{
  "type": "break",
  "targetLoopLevel": 1
}
```

5. 循环执行完成后，loopObject.matrix将包含部分填充的矩阵，反映了循环控制的效果。

## 9. 支持的表达式语法

### 9.1 初始化表达式

初始化表达式用于设置循环变量的初始值，通常是一个赋值表达式。

支持的格式：
- `变量名 = 值`，例如：`i = 0`、`count = 1`、`index = 10`

### 9.2 条件表达式

条件表达式用于判断是否继续执行循环，当条件为false时循环结束。

支持的比较运算符：
- 小于：`<`，例如：`i < 10`
- 大于：`>`，例如：`i > 0`
- 小于等于：`<=`，例如：`i <= 100`
- 大于等于：`>=`，例如：`i >= 1`
- 等于：`==`，例如：`i == 10`
- 不等于：`!=`，例如：`i != 0`

### 9.3 更新表达式

更新表达式用于在每次循环结束后更新循环变量的值。

支持的格式：
- 自增：`++`或`变量名++`，例如：`i++`
- 自减：`--`或`变量名--`，例如：`i--`
- 加法赋值：`变量名 += 值`，例如：`i += 2`
- 减法赋值：`变量名 -= 值`，例如：`i -= 1`
- 赋值加法：`变量名 = 变量名 + 值`，例如：`i = i + 1`
- 赋值减法：`变量名 = 变量名 - 值`，例如：`i = i - 1`

## 10. 如何遍历数组

### 10.1 基本数组遍历方法

使用FOR循环节点遍历数组，需要将循环条件设置为数组长度，然后在循环体内通过索引访问数组元素：

1. **配置FOR循环节点**：
```json
{
  "initialization": "i = 0",
  "condition": "i < {input.myArray.length}",
  "update": "i++",
  "loopContent": {}
}
```

2. **在循环体内访问数组元素**：
```javascript
// 获取当前索引和数组
const i = context['loop.item'];
const myArray = context.input.myArray;

// 获取当前数组元素
const currentElement = myArray[i];

// 处理当前元素
// ...
```

### 10.2 使用循环对象存储数组访问结果

```javascript
const i = context['loop.item'];
const myArray = context.input.myArray;
const loopObject = context['loop.object'] || {};

// 初始化
if (context['loop.index'] === 1) {
  loopObject.processedData = [];
}

// 处理当前元素
const currentElement = myArray[i];
const processedElement = someProcessingFunction(currentElement);

// 保存处理结果
loopObject.processedData.push(processedElement);

// 更新循环对象
context['loop.object'] = loopObject;
```

### 10.3 处理不同类型的数组

1. **数字数组示例**：
```javascript
const i = context['loop.item'];
const numbers = context.input.numbers;
const loopObject = context['loop.object'] || {};

// 初始化
if (context['loop.index'] === 1) {
  loopObject.sum = 0;
  loopObject.max = Number.NEGATIVE_INFINITY;
  loopObject.min = Number.POSITIVE_INFINITY;
}

const currentNumber = numbers[i];

// 更新统计信息
loopObject.sum += currentNumber;
loopObject.max = Math.max(loopObject.max, currentNumber);
loopObject.min = Math.min(loopObject.min, currentNumber);

// 更新循环对象
context['loop.object'] = loopObject;
```

2. **对象数组示例**：
```javascript
const i = context['loop.item'];
const users = context.input.users;
const loopObject = context['loop.object'] || {};

// 初始化
if (context['loop.index'] === 1) {
  loopObject.adults = [];
  loopObject.minors = [];
  loopObject.totalAge = 0;
}

const user = users[i];

// 根据年龄分类用户
if (user.age >= 18) {
  loopObject.adults.push(user);
} else {
  loopObject.minors.push(user);
}

// 累计总年龄
loopObject.totalAge += user.age;

// 更新循环对象
context['loop.object'] = loopObject;
```

3. **字符串数组示例**：
```javascript
const i = context['loop.item'];
const words = context.input.words;
const loopObject = context['loop.object'] || {};

// 初始化
if (context['loop.index'] === 1) {
  loopObject.concatenated = '';
  loopObject.wordCount = words.length;
  loopObject.totalChars = 0;
}

const word = words[i];

// 拼接字符串
loopObject.concatenated += (i > 0 ? ' ' : '') + word;
loopObject.totalChars += word.length;

// 更新循环对象
context['loop.object'] = loopObject;
```

### 10.4 数组遍历与过滤

结合条件判断和循环控制，实现数组元素的过滤：

```javascript
const i = context['loop.item'];
const numbers = context.input.numbers;
const loopObject = context['loop.object'] || {};

// 初始化
if (context['loop.index'] === 1) {
  loopObject.evenNumbers = [];
  loopObject.oddNumbers = [];
}

const currentNumber = numbers[i];

// 根据奇偶性分类
if (currentNumber % 2 === 0) {
  loopObject.evenNumbers.push(currentNumber);
} else {
  loopObject.oddNumbers.push(currentNumber);
}

// 如果找到大于100的数字，中断循环
if (currentNumber > 100) {
  context['loop-break-flag'] = true;
}

// 更新循环对象
context['loop.object'] = loopObject;
```

## 11. 注意事项

1. 循环变量名固定为"i"，代码中已内置此设置
2. 初始化表达式必须是形如"变量名 = 值"的赋值表达式
3. 条件表达式目前支持基本的比较运算符：<, >, <=, >=, ==, !=
4. 条件表达式支持数组长度比较，形如 "i < array.length" 或 "i < array.size()"
5. 更新表达式支持自增、自减、加法赋值、减法赋值等常见的更新方式
6. 为避免无限循环，系统内置了最大迭代次数10000
7. 循环变量（loop.index、loop.item等）只在循环执行期间有效
8. loop.object可用于在迭代之间传递和累积数据，是跨迭代共享数据的主要方式
9. 每次循环迭代都会将当前循环变量值添加到result数组，除非使用了continue标志
10. 循环控制节点(break)会立即终止目标循环，不再执行后续迭代
11. 循环控制节点(continue)会跳过目标循环当前迭代的结果收集，直接进入下一次迭代
12. 循环控制节点可以通过targetLoopLevel参数指定要控制的循环层级：0表示当前循环，1表示父循环
13. 循环执行完毕后，可以通过result、currentIndex、currentItem和loopObject访问循环结果
14. FOR循环节点支持嵌套使用，并提供了嵌套循环间的控制机制
15. 遍历数组时，可以使用`i < array.length`或`i < array.size()`这样的条件表达式
16. 通过索引访问数组元素时，使用`myArray[i]`的方式获取当前元素
17. 每个循环节点都有唯一的循环ID，通过current_loop_id和parent_loop_id在上下文中可见
18. 嵌套循环的控制标记使用循环ID进行区分，确保只影响目标循环
19. 循环控制标记会在被处理后自动清除，不会影响后续执行
20. 增强的日志记录功能可以帮助调试复杂的循环场景 