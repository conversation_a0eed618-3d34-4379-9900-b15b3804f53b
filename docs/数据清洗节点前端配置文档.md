# 数据清洗节点前端配置文档

## 节点概述

数据清洗节点用于对输入数据进行清洗、转换、验证和高级处理，支持多种数据格式和处理操作。节点可以处理文件数据或直接处理输入参数中的数据。

## 枚举常量说明

前端开发时需要使用以下枚举常量，这些常量已存储在数据库的`design_enum`表中：

### 清洗类型枚举 (clearNodeType)

用于指定清洗步骤的类型：

| 值 | 说明 |
| --- | --- |
| BASIC | 基础清洗，包括空格处理、去重等基础操作 |
| CONVERT | 数据转换，包括类型转换、日期格式化等 |
| VALIDATE | 数据验证，包括非空验证、范围检查等 |
| ADVANCED | 高级处理，包括正则表达式、自定义函数等 |

### 基础清洗操作枚举 (clearBasicOperations)

当清洗类型为`BASIC`时，可选的操作类型：

| 值 | 说明 |
| --- | --- |
| REMOVE_SPACE | 删除空值，可配置删除方式 |
| REMOVE_DUPLICATE | 删除重复行，可选保留首次或末次出现 |
| REPLACE_VALUE | 替换特定值，支持正则表达式 |
| TRIM_WHITESPACE | 修剪空白字符，可选修剪方式 |

### 数据转换操作枚举 (clearConvertOperations)

当清洗类型为`CONVERT`时，可选的操作类型：

| 值 | 说明 |
| --- | --- |
| TYPE_CONVERT | 类型转换，如字符串转数字 |
| DATE_FORMAT | 日期格式化，转换日期格式 |
| DATA_NORMALIZE | 数据归一化，包括单位转换和归一化处理 |
| STRING_PROCESS | 字符串处理，如大小写转换、截取等 |

### 数据验证操作枚举 (clearValidateOperations)

当清洗类型为`VALIDATE`时，可选的操作类型：

| 值 | 说明 |
| --- | --- |
| NOT_NULL | 非空验证，检查值是否为空 |
| RANGE_CHECK | 范围检查，验证值是否在指定范围内 |
| FORMAT_CHECK | 格式验证，检查值是否符合特定格式 |
| CUSTOM_RULE | 自定义规则验证，使用自定义规则验证值 |

### 高级处理操作枚举 (clearAdvancedOperations)

当清洗类型为`ADVANCED`时，可选的操作类型：

| 值 | 说明 |
| --- | --- |
| REGEX_PATTERN | 正则表达式处理，提取、匹配或替换 |
| CUSTOM_FUNCTION | 自定义函数，应用预定义函数处理数据 |
| CONDITION_FILTER | 条件过滤，根据条件筛选数据 |
| DATA_AGGREGATION | 数据聚合，聚合计算数据 |

### 数据类型枚举 (clearDataTypes)

用于指定数据预览配置中的数据类型：

| 值 | 说明 |
| --- | --- |
| CSV | CSV格式数据，逗号分隔值 |
| JSON | JSON格式数据 |
| XML | XML格式数据 |
| TEXT | 纯文本格式数据 |

### 空格处理类型枚举 (clearTrimTypes)

用于指定空格处理的方式：

| 值 | 说明 |
| --- | --- |
| all | 删除所有空格 |
| left | 删除左侧空格 |
| right | 删除右侧空格 |
| both | 删除两侧空格 |
| collapse | 将连续空格合并为一个空格 |

### 数据类型转换选项枚举 (clearTypeConvertOptions)

用于指定类型转换的目标类型：

| 值 | 说明 |
| --- | --- |
| string | 字符串类型 |
| number | 数字类型（双精度浮点数） |
| integer | 整数类型 |
| double | 双精度浮点数类型 |
| boolean | 布尔类型 |
| date | 日期类型 |

### 日期格式选项枚举 (clearDateFormats)

用于指定日期格式化的格式：

| 值 | 说明 |
| --- | --- |
| yyyy-MM-dd | 年-月-日格式，如2023-01-01 |
| yyyy/MM/dd | 年/月/日格式，如2023/01/01 |
| MM/dd/yyyy | 月/日/年格式，如01/01/2023 |
| dd/MM/yyyy | 日/月/年格式，如01/01/2023 |
| yyyy-MM-dd HH:mm:ss | 年-月-日 时:分:秒格式，如2023-01-01 12:30:45 |
| yyyy/MM/dd HH:mm:ss | 年/月/日 时:分:秒格式，如2023/01/01 12:30:45 |
| HH:mm:ss | 时:分:秒格式，如12:30:45 |

### 单位转换选项枚举 (clearUnitOptions)

用于指定单位转换的单位：

| 值 | 说明 |
| --- | --- |
| mm | 毫米 |
| cm | 厘米 |
| m | 米 |
| km | 千米 |
| g | 克 |
| kg | 千克 |
| C | 摄氏度 |
| F | 华氏度 |

### 大小写转换选项枚举 (clearCaseConversion)

用于指定字符串大小写转换的方式：

| 值 | 说明 |
| --- | --- |
| upper | 全部转为大写 |
| lower | 全部转为小写 |
| capitalize | 首字母大写，其余小写 |

### 正则表达式模式枚举 (clearRegexModes)

用于指定正则表达式的处理模式：

| 值 | 说明 |
| --- | --- |
| extract | 提取匹配的内容 |
| match | 检查是否匹配 |
| replace | 替换匹配的内容 |

### 自定义函数类型枚举 (clearFunctionTypes)

用于指定自定义函数的类型：

| 值 | 说明 |
| --- | --- |
| abs | 绝对值函数 |
| sqrt | 平方根函数 |
| round | 四舍五入函数 |
| length | 计算字符串长度函数 |
| reverse | 字符串反转函数 |

### 条件操作符枚举 (clearConditionOperators)

用于指定条件过滤的操作符：

| 值 | 说明 |
| --- | --- |
| equals | 等于 |
| notEquals | 不等于 |
| contains | 包含 |
| startsWith | 以...开始 |
| endsWith | 以...结束 |
| greaterThan | 大于 |
| lessThan | 小于 |
| isNull | 为空 |
| isNotNull | 不为空 |
| matches | 匹配正则表达式 |

### 聚合类型枚举 (clearAggregationTypes)

用于指定数据聚合的类型：

| 值 | 说明 |
| --- | --- |
| count | 计数 |
| sum | 求和 |
| avg | 平均值 |
| max | 最大值 |
| min | 最小值 |

### 格式验证类型枚举 (clearFormatTypes)

用于指定格式验证的类型：

| 值 | 说明 |
| --- | --- |
| email | 电子邮件格式 |
| phone | 电话号码格式 |
| date | 日期格式 |
| number | 数字格式 |
| url | URL格式 |
| ip | IP地址格式 |
| custom | 自定义格式（需提供正则表达式） |

## 节点属性配置

### 1. 数据预览配置

数据预览配置用于设置数据源和预览参数：

| 属性名 | 类型 | 必填 | 说明 |
| ----- | ---- | ---- | ---- |
| filePath | String | 否 | 文件路径，如不填则从输入参数获取数据 |
| dataType | String | 是 | 数据类型，支持CSV/JSON/XML/TEXT |
| rows | Integer | 否 | 预览行数，默认为10 |
| columns | Integer | 否 | 预览列数，仅对CSV等表格数据有效 |

### 2. 清洗步骤配置

清洗步骤配置是一个数组，每个步骤包含以下属性：

| 属性名 | 类型 | 必填 | 说明 |
| ----- | ---- | ---- | ---- |
| type | String | 是 | 清洗类型：BASIC/CONVERT/VALIDATE/ADVANCED |
| operation | String | 是 | 具体操作，根据type不同而不同 |
| config | Object | 是 | 操作的具体配置参数 |

## 清洗操作类型及配置

### 1. 基础清洗 (BASIC)

#### 1.1 删除空值 (REMOVE_SPACE)

```json
{
  "type": "BASIC",
  "operation": "REMOVE_SPACE",
  "config": {
    "trimType": "all",  // 空格处理类型：all/left/right/both
    "removeEmpty": true,  // 是否删除空值
    "removeEmptyRows": true  // 是否删除全空行
  }
}
```

#### 1.2 删除重复行 (REMOVE_DUPLICATE)

```json
{
  "type": "BASIC",
  "operation": "REMOVE_DUPLICATE",
  "config": {
    "keepFirst": true  // true保留第一个出现的数据，false保留最后一个
  }
}
```

#### 1.3 替换特定值 (REPLACE_VALUE)

```json
{
  "type": "BASIC",
  "operation": "REPLACE_VALUE",
  "config": {
    "oldValue": "旧值",  // 要替换的值，可以是正则表达式，格式为"regex:表达式"
    "newValue": "新值"  // 替换后的值
  }
}
```

#### 1.4 修剪空白字符 (TRIM_WHITESPACE)

```json
{
  "type": "BASIC",
  "operation": "TRIM_WHITESPACE",
  "config": {
    "trimType": "both"  // 空格处理类型：all/collapse/both
  }
}
```

### 2. 数据转换 (CONVERT)

#### 2.1 类型转换 (TYPE_CONVERT)

```json
{
  "type": "CONVERT",
  "operation": "TYPE_CONVERT",
  "config": {
    "sourceType": "string",  // 源类型
    "targetType": "number"  // 目标类型：string/number/integer/double/boolean/date
  }
}
```

#### 2.2 日期格式化 (DATE_FORMAT)

```json
{
  "type": "CONVERT",
  "operation": "DATE_FORMAT",
  "config": {
    "sourceFormat": "yyyy-MM-dd",  // 源日期格式
    "targetFormat": "dd/MM/yyyy"  // 目标日期格式
  }
}
```

#### 2.3 数据归一化 (DATA_NORMALIZE)

```json
{
  "type": "CONVERT",
  "operation": "DATA_NORMALIZE",
  "config": {
    "minValue": 0,  // 最小值
    "maxValue": 100,  // 最大值
    "decimalPlaces": 2,  // 小数位数
    "sourceUnit": "cm",  // 源单位
    "targetUnit": "m"  // 目标单位
  }
}
```

#### 2.4 字符串处理 (STRING_PROCESS)

```json
{
  "type": "CONVERT",
  "operation": "STRING_PROCESS",
  "config": {
    "caseConversion": "upper",  // 大小写转换：upper/lower/capitalize
    "startIndex": 0,  // 截取起始索引
    "endIndex": 10,  // 截取结束索引
    "padChar": "0",  // 填充字符
    "padLength": 10,  // 填充长度
    "padDirection": "left"  // 填充方向：left/right
  }
}
```

### 3. 数据验证 (VALIDATE)

#### 3.1 非空验证 (NOT_NULL)

```json
{
  "type": "VALIDATE",
  "operation": "NOT_NULL",
  "config": {
    "removeNulls": true,  // 是否移除空值
    "errorMessage": "值不能为空"  // 错误信息
  }
}
```

#### 3.2 范围检查 (RANGE_CHECK)

```json
{
  "type": "VALIDATE",
  "operation": "RANGE_CHECK",
  "config": {
    "minValue": 0,  // 最小值
    "maxValue": 100,  // 最大值
    "removeInvalid": true,  // 是否移除无效值
    "errorMessage": "值不在有效范围内"  // 错误信息
  }
}
```

#### 3.3 格式验证 (FORMAT_CHECK)

```json
{
  "type": "VALIDATE",
  "operation": "FORMAT_CHECK",
  "config": {
    "format": "email",  // 格式类型：email/phone/date/number/url/ip/custom
    "pattern": "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$",  // 自定义正则表达式
    "removeInvalid": true,  // 是否移除无效值
    "errorMessage": "格式无效"  // 错误信息
  }
}
```

#### 3.4 自定义规则验证 (CUSTOM_RULE)

```json
{
  "type": "VALIDATE",
  "operation": "CUSTOM_RULE",
  "config": {
    "rule": "length > 5",  // 自定义规则
    "removeInvalid": true,  // 是否移除无效值
    "errorMessage": "不符合自定义规则"  // 错误信息
  }
}
```

### 4. 高级处理 (ADVANCED)

#### 4.1 正则表达式 (REGEX_PATTERN)

```json
{
  "type": "ADVANCED",
  "operation": "REGEX_PATTERN",
  "config": {
    "pattern": "\\d+",  // 正则表达式
    "regexMode": "extract",  // 正则模式：extract/match/replace
    "regexGroup": 0,  // 提取的分组索引
    "newValue": "替换值"  // 替换值（仅在replace模式下使用）
  }
}
```

#### 4.2 自定义函数 (CUSTOM_FUNCTION)

```json
{
  "type": "ADVANCED",
  "operation": "CUSTOM_FUNCTION",
  "config": {
    "functionType": "abs",  // 函数类型：abs/sqrt/round/length/reverse
    "functionParams": ""  // 函数参数
  }
}
```

#### 4.3 条件过滤 (CONDITION_FILTER)

```json
{
  "type": "ADVANCED",
  "operation": "CONDITION_FILTER",
  "config": {
    "field": "0",  // 字段名或索引
    "operator": "contains",  // 操作符
    "filterValue": "test"  // 过滤值
  }
}
```

#### 4.4 数据聚合 (DATA_AGGREGATION)

```json
{
  "type": "ADVANCED",
  "operation": "DATA_AGGREGATION",
  "config": {
    "aggregationType": "sum",  // 聚合类型：count/sum/avg/max/min
    "groupByField": "0"  // 分组字段
  }
}
```

## 完整配置示例

```json
{
  "dataPreview": {
    "filePath": "/path/to/data.csv",
    "dataType": "CSV",
    "rows": 100,
    "columns": 10
  },
  "cleanSteps": [
    {
      "type": "BASIC",
      "operation": "REMOVE_SPACE",
      "config": {
        "trimType": "all",
        "removeEmpty": true
      }
    },
    {
      "type": "CONVERT",
      "operation": "TYPE_CONVERT",
      "config": {
        "sourceType": "string",
        "targetType": "number"
      }
    },
    {
      "type": "VALIDATE",
      "operation": "RANGE_CHECK",
      "config": {
        "minValue": 0,
        "maxValue": 100,
        "removeInvalid": true
      }
    },
    {
      "type": "ADVANCED",
      "operation": "DATA_AGGREGATION",
      "config": {
        "aggregationType": "avg"
      }
    }
  ]
}
```

## 节点输出

节点执行完成后，会将清洗后的数据作为结果输出到`result`属性中，可以在后续节点中通过`${清洗节点ID.result}`来引用。 