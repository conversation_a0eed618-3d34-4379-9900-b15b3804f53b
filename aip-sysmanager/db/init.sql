create table design_sys_depart_permission
(
    id            varchar(32) not null
        primary key,
    depart_id     varchar(32),
    permission_id varchar(32),
    data_rule_ids varchar(1000)
);

comment on table design_sys_depart_permission is '部门权限表';

comment on column design_sys_depart_permission.depart_id is '部门id';

comment on column design_sys_depart_permission.permission_id is '权限id';

comment on column design_sys_depart_permission.data_rule_ids is '数据规则id';

create table design_sys_depart_role
(
    id          varchar(32) not null
        primary key,
    depart_id   varchar(32),
    role_name   varchar(200),
    role_code   varchar(100),
    description varchar(255),
    create_by   varchar(32),
    create_time timestamp,
    update_by   varchar(32),
    update_time timestamp
);

comment on table design_sys_depart_role is '部门角色表';

comment on column design_sys_depart_role.depart_id is '部门id';

comment on column design_sys_depart_role.role_name is '部门角色名称';

comment on column design_sys_depart_role.role_code is '部门角色编码';

comment on column design_sys_depart_role.description is '描述';

comment on column design_sys_depart_role.create_by is '创建人';

comment on column design_sys_depart_role.create_time is '创建时间';

comment on column design_sys_depart_role.update_by is '更新人';

comment on column design_sys_depart_role.update_time is '更新时间';

create table design_sys_depart_role_permission
(
    id            varchar(32) not null
        primary key,
    depart_id     varchar(32),
    role_id       varchar(32),
    permission_id varchar(32),
    data_rule_ids varchar(1000),
    operate_date  timestamp,
    operate_ip    varchar(20)
);

comment on table design_sys_depart_role_permission is '部门角色权限表';

comment on column design_sys_depart_role_permission.depart_id is '部门id';

comment on column design_sys_depart_role_permission.role_id is '角色id';

comment on column design_sys_depart_role_permission.permission_id is '权限id';

comment on column design_sys_depart_role_permission.data_rule_ids is '数据权限ids';

comment on column design_sys_depart_role_permission.operate_date is '操作时间';

comment on column design_sys_depart_role_permission.operate_ip is '操作ip';

create table design_sys_depart_role_user
(
    id       varchar(32) not null
        primary key,
    user_id  varchar(32),
    drole_id varchar(32)
);

comment on table design_sys_depart_role_user is '部门角色用户表';

comment on column design_sys_depart_role_user.id is '主键id';

comment on column design_sys_depart_role_user.user_id is '用户id';

comment on column design_sys_depart_role_user.drole_id is '角色id';

create table design_sys_permission
(
    id                   varchar(32) not null
        primary key,
    parent_id            varchar(32),
    name                 varchar(255),
    url                  varchar(255),
    component            varchar(255),
    is_route             integer     default 1,
    component_name       varchar(255),
    redirect             varchar(255),
    menu_type            integer,
    perms                varchar(255),
    perms_type           varchar(10) default '0'::character varying,
    sort_no              NUMERIC(8,2),
    always_show          integer,
    icon                 varchar(255),
    is_leaf              integer,
    keep_alive           integer,
    hidden               integer     default 0,
    hide_tab             integer,
    description          varchar(255),
    create_by            varchar(255),
    create_time          timestamp,
    update_by            varchar(255),
    update_time          timestamp,
    del_flag             integer     default 0,
    rule_flag            integer     default 0,
    status               varchar(2),
    internal_or_external integer
);

comment on table design_sys_permission is '菜单权限表';

comment on column design_sys_permission.id is '主键id';

comment on column design_sys_permission.parent_id is '父id';

comment on column design_sys_permission.name is '菜单标题';

comment on column design_sys_permission.url is '路径';

comment on column design_sys_permission.component is '组件';

comment on column design_sys_permission.is_route is '是否路由菜单: 0:不是  1:是（默认值1）';

comment on column design_sys_permission.component_name is '组件名字';

comment on column design_sys_permission.redirect is '一级菜单跳转地址';

comment on column design_sys_permission.menu_type is '菜单类型(0:一级菜单; 1:子菜单:2:按钮权限)';

comment on column design_sys_permission.perms is '菜单权限编码';

comment on column design_sys_permission.perms_type is '权限策略1显示2禁用';

comment on column design_sys_permission.sort_no is '菜单排序';

comment on column design_sys_permission.always_show is '聚合子路由: 1是0否';

comment on column design_sys_permission.icon is '菜单图标';

comment on column design_sys_permission.is_leaf is '是否叶子节点:    1是0否';

comment on column design_sys_permission.keep_alive is '是否缓存该页面:    1:是   0:不是';

comment on column design_sys_permission.hidden is '是否隐藏路由: 0否,1是';

comment on column design_sys_permission.hide_tab is '是否隐藏tab: 0否,1是';

comment on column design_sys_permission.description is '描述';

comment on column design_sys_permission.create_by is '创建人';

comment on column design_sys_permission.create_time is '创建时间';

comment on column design_sys_permission.update_by is '更新人';

comment on column design_sys_permission.update_time is '更新时间';

comment on column design_sys_permission.del_flag is '删除状态 0正常 1已删除';

comment on column design_sys_permission.rule_flag is '是否添加数据权限1是0否';

comment on column design_sys_permission.status is '按钮权限状态(0无效1有效)';

comment on column design_sys_permission.internal_or_external is '外链菜单打开方式 0/内部打开 1/外部打开';

create table design_sys_role
(
    id          varchar(32)  not null
        primary key,
    role_name   varchar(200),
    role_code   varchar(100) not null,
    description varchar(255),
    create_by   varchar(32),
    create_time timestamp,
    update_by   varchar(32),
    update_time timestamp,
    tenant_id   integer default 0
);

comment on table design_sys_role is '角色表';

comment on column design_sys_role.id is '主键id';

comment on column design_sys_role.role_name is '角色名称';

comment on column design_sys_role.role_code is '角色编码';

comment on column design_sys_role.description is '描述';

comment on column design_sys_role.create_by is '创建人';

comment on column design_sys_role.create_time is '创建时间';

comment on column design_sys_role.update_by is '更新人';

comment on column design_sys_role.update_time is '更新时间';

comment on column design_sys_role.tenant_id is '租户ID';

create table design_sys_role_permission
(
    id            varchar(32) not null
        primary key,
    role_id       varchar(32),
    permission_id varchar(32),
    data_rule_ids varchar(1000),
    operate_date  timestamp,
    operate_ip    varchar(100)
);

comment on table design_sys_role_permission is '角色权限表';

comment on column design_sys_role_permission.role_id is '角色id';

comment on column design_sys_role_permission.permission_id is '权限id';

comment on column design_sys_role_permission.data_rule_ids is '数据权限ids';

comment on column design_sys_role_permission.operate_date is '操作时间';

comment on column design_sys_role_permission.operate_ip is '操作ip';

create table design_sys_tenant_pack
(
    id          varchar(32) not null
        primary key,
    tenant_id   integer,
    pack_name   varchar(20),
    status      varchar(1),
    remarks     varchar(100),
    create_by   varchar(32),
    create_time date,
    update_by   varchar(32),
    update_time date,
    pack_code   varchar(50),
    pack_type   varchar(10) default 'custom'::character varying
);

comment on table design_sys_tenant_pack is '租户产品包';

comment on column design_sys_tenant_pack.id is '主键id';

comment on column design_sys_tenant_pack.tenant_id is '租户id';

comment on column design_sys_tenant_pack.pack_name is '产品包名';

comment on column design_sys_tenant_pack.status is '开启状态(0 未开启 1开启)';

comment on column design_sys_tenant_pack.remarks is '备注';

comment on column design_sys_tenant_pack.create_by is '创建人';

comment on column design_sys_tenant_pack.create_time is '创建时间';

comment on column design_sys_tenant_pack.update_by is '更新人';

comment on column design_sys_tenant_pack.update_time is '更新时间';

comment on column design_sys_tenant_pack.pack_code is '编码,默认添加的三个管理员需要设置编码';

comment on column design_sys_tenant_pack.pack_type is '产品包类型(default 默认产品包 custom 自定义产品包)';

create table design_sys_tenant_pack_perms
(
    id            varchar(32) not null
        primary key,
    pack_id       varchar(32),
    permission_id varchar(32),
    create_by     varchar(32),
    create_time   date,
    update_by     varchar(32),
    update_time   date
);

comment on table design_sys_tenant_pack_perms is '租户产品包和菜单关系表';

comment on column design_sys_tenant_pack_perms.id is '主键编号';

comment on column design_sys_tenant_pack_perms.pack_id is '租户产品包名称';

comment on column design_sys_tenant_pack_perms.permission_id is '菜单id';

comment on column design_sys_tenant_pack_perms.create_by is '创建人';

comment on column design_sys_tenant_pack_perms.create_time is '创建时间';

comment on column design_sys_tenant_pack_perms.update_by is '更新人';

comment on column design_sys_tenant_pack_perms.update_time is '更新时间';


create table design_sys_tenant_pack_user
(
    id          varchar(32) not null
        primary key,
    pack_id     varchar(32),
    user_id     varchar(32),
    tenant_id   integer,
    create_by   varchar(50),
    create_time timestamp,
    update_by   varchar(50),
    update_time timestamp,
    status      integer
);

comment on table design_sys_tenant_pack_user is '租户套餐人员表';

comment on column design_sys_tenant_pack_user.pack_id is '租户产品包ID';

comment on column design_sys_tenant_pack_user.user_id is '用户ID';

comment on column design_sys_tenant_pack_user.tenant_id is '租户ID';

comment on column design_sys_tenant_pack_user.create_by is '创建人';

comment on column design_sys_tenant_pack_user.create_time is '创建时间';

comment on column design_sys_tenant_pack_user.update_by is '更新人';

comment on column design_sys_tenant_pack_user.update_time is '更新时间';

comment on column design_sys_tenant_pack_user.status is '状态 正常状态1 申请状态0';

create table design_sys_user
(
    id              varchar(32) not null
        primary key,
    username        varchar(100),
    realname        varchar(100),
    password        varchar(255),
    salt            varchar(45),
    avatar          varchar(255),
    birthday        date,
    sex             integer,
    email           varchar(45),
    phone           varchar(45),
    org_code        varchar(64),
    status          integer,
    del_flag        integer,
    third_id        varchar(100),
    third_type      varchar(100),
    activiti_sync   integer,
    work_no         varchar(100),
    telephone       varchar(45),
    create_by       varchar(32),
    create_time     timestamp,
    update_by       varchar(32),
    update_time     timestamp,
    user_identity   integer,
    depart_ids      varchar(1000),
    client_id       varchar(64),
    login_tenant_id integer,
    bpm_status      varchar(2)
);

comment on table design_sys_user is '用户表';

comment on column design_sys_user.id is '主键id';

comment on column design_sys_user.username is '登录账号';

comment on column design_sys_user.realname is '真实姓名';

comment on column design_sys_user.password is '密码';

comment on column design_sys_user.salt is 'md5密码盐';

comment on column design_sys_user.avatar is '头像';

comment on column design_sys_user.birthday is '生日';

comment on column design_sys_user.sex is '性别(0-默认未知,1-男,2-女)';

comment on column design_sys_user.email is '电子邮件';

comment on column design_sys_user.phone is '电话';

comment on column design_sys_user.org_code is '登录会话的机构编码';

comment on column design_sys_user.status is '性别(1-正常,2-冻结)';

comment on column design_sys_user.del_flag is '删除状态(0-正常,1-已删除)';

comment on column design_sys_user.third_id is '第三方登录的唯一标识';

comment on column design_sys_user.third_type is '第三方类型';

comment on column design_sys_user.activiti_sync is '同步工作流引擎(1-同步,0-不同步)';

comment on column design_sys_user.work_no is '工号，唯一键';

comment on column design_sys_user.telephone is '座机号';

comment on column design_sys_user.create_by is '创建人';

comment on column design_sys_user.create_time is '创建时间';

comment on column design_sys_user.update_by is '更新人';

comment on column design_sys_user.update_time is '更新时间';

comment on column design_sys_user.user_identity is '身份（1普通成员 2上级）';

comment on column design_sys_user.depart_ids is '负责部门';

comment on column design_sys_user.client_id is '设备ID';

comment on column design_sys_user.login_tenant_id is '上次登录选择租户ID';

comment on column design_sys_user.bpm_status is '流程入职离职状态';


create table design_sys_user_depart
(
    id      varchar(32) not null
        primary key,
    user_id varchar(32),
    dep_id  varchar(32)
);

comment on table design_sys_user_depart is '用户部门';

comment on column design_sys_user_depart.id is 'id';

comment on column design_sys_user_depart.user_id is '用户id';

comment on column design_sys_user_depart.dep_id is '部门id';

create table design_sys_user_role
(
    id        varchar(32) not null
        primary key,
    user_id   varchar(32),
    role_id   varchar(32),
    tenant_id integer default 0
);

comment on table design_sys_user_role is '用户角色表';

comment on column design_sys_user_role.id is '主键id';

comment on column design_sys_user_role.user_id is '用户id';

comment on column design_sys_user_role.role_id is '角色id';

comment on column design_sys_user_role.tenant_id is '租户ID';

create table design_sys_user_tenant
(
    id          varchar(32) not null
        primary key,
    user_id     varchar(32),
    tenant_id   integer,
    status      varchar(1),
    create_by   varchar(32),
    create_time timestamp,
    update_by   varchar(32),
    update_time timestamp
);

comment on table design_sys_user_tenant is '用户租户关系表';

comment on column design_sys_user_tenant.id is '主键id';

comment on column design_sys_user_tenant.user_id is '用户id';

comment on column design_sys_user_tenant.tenant_id is '租户id';

comment on column design_sys_user_tenant.status is '状态(1 正常 2 离职 3 待审核 4 拒绝 5 邀请加入)';

comment on column design_sys_user_tenant.create_by is '创建人登录名称';

comment on column design_sys_user_tenant.create_time is '创建日期';

comment on column design_sys_user_tenant.update_by is '更新人登录名称';

comment on column design_sys_user_tenant.update_time is '更新日期';


create table design_sys_depart
(
    id               varchar(32)                                not null
        primary key,
    parent_id        varchar(32),
    depart_name      varchar(100)                               not null,
    depart_name_en   varchar(500),
    depart_name_abbr varchar(500),
    depart_order     integer     default 0,
    description      varchar(500),
    org_category     varchar(10) default '1'::character varying not null,
    org_type         varchar(10),
    org_code         varchar(64)                                not null,
    mobile           varchar(32),
    fax              varchar(32),
    address          varchar(100),
    memo             varchar(500),
    status           varchar(1),
    del_flag         varchar(1),
    qywx_identifier  varchar(100),
    ding_identifier  varchar(100),
    create_by        varchar(32),
    create_time      timestamp,
    update_by        varchar(32),
    update_time      timestamp,
    tenant_id        integer     default 0,
    iz_leaf          integer     default 0
);

comment on table design_sys_depart is '部门表';

comment on column design_sys_depart.id is 'ID';

comment on column design_sys_depart.parent_id is '父机构ID';

comment on column design_sys_depart.depart_name is '机构/部门名称';

comment on column design_sys_depart.depart_name_en is '英文名';

comment on column design_sys_depart.depart_name_abbr is '缩写';

comment on column design_sys_depart.depart_order is '排序';

comment on column design_sys_depart.description is '描述';

comment on column design_sys_depart.org_category is '机构类别 1公司，2组织机构，3岗位';

comment on column design_sys_depart.org_type is '机构类型 1一级部门 2子部门';

comment on column design_sys_depart.org_code is '机构编码';

comment on column design_sys_depart.mobile is '手机号';

comment on column design_sys_depart.fax is '传真';

comment on column design_sys_depart.address is '地址';

comment on column design_sys_depart.memo is '备注';

comment on column design_sys_depart.status is '状态（1启用，0不启用）';

comment on column design_sys_depart.del_flag is '删除状态（0，正常，1已删除）';

comment on column design_sys_depart.qywx_identifier is '对接企业微信的ID';

comment on column design_sys_depart.ding_identifier is '对接钉钉部门的ID';

comment on column design_sys_depart.create_by is '创建人';

comment on column design_sys_depart.create_time is '创建日期';

comment on column design_sys_depart.update_by is '更新人';

comment on column design_sys_depart.update_time is '更新日期';

comment on column design_sys_depart.tenant_id is '租户ID';

comment on column design_sys_depart.iz_leaf is '是否有叶子节点: 1是0否';



DROP TABLE IF EXISTS DESIGN_DEVOPS_LICENSE;
CREATE TABLE DESIGN_DEVOPS_LICENSE(
                                      ID VARCHAR(255) NOT NULL,
                                      LICENSE_NUMBER VARCHAR(255) NOT NULL,
                                      PROJECT_NAME VARCHAR(255),
                                      PRODUCT_NAME VARCHAR(255),
                                      APPLICANT_NAME VARCHAR(255),
                                      ISSUED_TIME TIMESTAMP,
                                      EXPIRY_TIME TIMESTAMP,
                                      LICENSE_STATUS VARCHAR(1),
                                      REMARKS VARCHAR(1000),
                                      LICENSE_CONTENT TEXT,
                                      CONTENT TEXT COMMENT '原始内容',
                                      CREATE_BY VARCHAR(32),
                                      CREATE_TIME TIMESTAMP,
                                      UPDATE_BY VARCHAR(32),
                                      UPDATE_TIME TIMESTAMP,
                                      PUBLIC_KEY TEXT COMMENT 'RSA公钥',
                                      PRIVATE_KEY TEXT COMMENT 'RSA私钥',
                                      PRIMARY KEY (ID)
);

COMMENT ON TABLE DESIGN_DEVOPS_LICENSE IS '许可证信息';
COMMENT ON COLUMN DESIGN_DEVOPS_LICENSE.ID IS '主键';
COMMENT ON COLUMN DESIGN_DEVOPS_LICENSE.LICENSE_NUMBER IS '许可证编号';
COMMENT ON COLUMN DESIGN_DEVOPS_LICENSE.PROJECT_NAME IS '项目名称';
COMMENT ON COLUMN DESIGN_DEVOPS_LICENSE.PRODUCT_NAME IS '产品名称';
COMMENT ON COLUMN DESIGN_DEVOPS_LICENSE.APPLICANT_NAME IS '申请人名称';
COMMENT ON COLUMN DESIGN_DEVOPS_LICENSE.ISSUED_TIME IS '证书生效时间';
COMMENT ON COLUMN DESIGN_DEVOPS_LICENSE.EXPIRY_TIME IS '证书失效时间';
COMMENT ON COLUMN DESIGN_DEVOPS_LICENSE.LICENSE_STATUS IS '许可证状态;1. 生效 2. 失效';
COMMENT ON COLUMN DESIGN_DEVOPS_LICENSE.REMARKS IS '备注';
COMMENT ON COLUMN DESIGN_DEVOPS_LICENSE.LICENSE_CONTENT IS '许可证内容';
COMMENT ON COLUMN DESIGN_DEVOPS_LICENSE.CONTENT IS '原始内容';
COMMENT ON COLUMN DESIGN_DEVOPS_LICENSE.CREATE_BY IS '创建人';
COMMENT ON COLUMN DESIGN_DEVOPS_LICENSE.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN DESIGN_DEVOPS_LICENSE.UPDATE_BY IS '更新人';
COMMENT ON COLUMN DESIGN_DEVOPS_LICENSE.UPDATE_TIME IS '更新时间';



DROP TABLE IF EXISTS DESIGN_DATA_SOURCE;
CREATE TABLE DESIGN_DATA_SOURCE(
                                   ID VARCHAR(32) NOT NULL,
                                   CODE VARCHAR(255),
                                   NAME VARCHAR(255),
                                   REMARK VARCHAR(255),
                                   DB_TYPE VARCHAR(255),
                                   DB_DRIVER VARCHAR(255),
                                   DB_URL VARCHAR(255),
                                   DB_NAME VARCHAR(255),
                                   DB_USERNAME VARCHAR(255),
                                   DB_PASSWORD VARCHAR(255),
                                   CREATE_BY VARCHAR(255),
                                   CREATE_TIME TIMESTAMP,
                                   UPDATE_BY VARCHAR(255),
                                   UPDATE_TIME TIMESTAMP,
                                   PRIMARY KEY (ID)
);

COMMENT ON TABLE DESIGN_DATA_SOURCE IS '数据库数据源';
COMMENT ON COLUMN DESIGN_DATA_SOURCE.ID IS '主键';
COMMENT ON COLUMN DESIGN_DATA_SOURCE.CODE IS '数据源编码';
COMMENT ON COLUMN DESIGN_DATA_SOURCE.NAME IS '数据源名称';
COMMENT ON COLUMN DESIGN_DATA_SOURCE.REMARK IS '备注';
COMMENT ON COLUMN DESIGN_DATA_SOURCE.DB_TYPE IS '数据库类型';
COMMENT ON COLUMN DESIGN_DATA_SOURCE.DB_DRIVER IS '驱动类';
COMMENT ON COLUMN DESIGN_DATA_SOURCE.DB_URL IS '数据源地址';
COMMENT ON COLUMN DESIGN_DATA_SOURCE.DB_NAME IS '数据库名称';
COMMENT ON COLUMN DESIGN_DATA_SOURCE.DB_USERNAME IS '用户名';
COMMENT ON COLUMN DESIGN_DATA_SOURCE.DB_PASSWORD IS '密码';
COMMENT ON COLUMN DESIGN_DATA_SOURCE.CREATE_BY IS '创建人';
COMMENT ON COLUMN DESIGN_DATA_SOURCE.CREATE_TIME IS '创建日期';
COMMENT ON COLUMN DESIGN_DATA_SOURCE.UPDATE_BY IS '更新人';
COMMENT ON COLUMN DESIGN_DATA_SOURCE.UPDATE_TIME IS '更新日期';


DROP TABLE IF EXISTS DESIGN_DB_INFO;
CREATE TABLE DESIGN_DB_INFO(
                               ID VARCHAR(255) NOT NULL,
                               DATA_SOURCE_CODE VARCHAR(255) NOT NULL,
                               TABLE_NAME VARCHAR(255),
                               TABLE_CODE VARCHAR(255),
                               TABLE_DESCRIPTION VARCHAR(255),
                               COLUMN_NAME VARCHAR(255),
                               COLUMN_CODE VARCHAR(255),
                               COLUMN_DESCRIPTION VARCHAR(1000),
                               DATA_TYPE VARCHAR(1000),
                               IS_PRIMARY VARCHAR(1) DEFAULT  0,
                               IS_NULLABLE VARCHAR(255) DEFAULT  1,
                               COLUMN_ORDER INTEGER,
                               CREATE_BY VARCHAR(32),
                               CREATE_TIME TIMESTAMP,
                               UPDATE_BY VARCHAR(32),
                               UPDATE_TIME TIMESTAMP,
                               PRIMARY KEY (ID)
);

COMMENT ON TABLE DESIGN_DB_INFO IS '数据库信息';
COMMENT ON COLUMN DESIGN_DB_INFO.ID IS '主键';
COMMENT ON COLUMN DESIGN_DB_INFO.DATA_SOURCE_CODE IS '数据源代码';
COMMENT ON COLUMN DESIGN_DB_INFO.TABLE_NAME IS '表名称';
COMMENT ON COLUMN DESIGN_DB_INFO.TABLE_CODE IS '表代码';
COMMENT ON COLUMN DESIGN_DB_INFO.TABLE_DESCRIPTION IS '表描述';
COMMENT ON COLUMN DESIGN_DB_INFO.COLUMN_NAME IS '字段名称';
COMMENT ON COLUMN DESIGN_DB_INFO.COLUMN_CODE IS '字段代码';
COMMENT ON COLUMN DESIGN_DB_INFO.COLUMN_DESCRIPTION IS '字段描述';
COMMENT ON COLUMN DESIGN_DB_INFO.DATA_TYPE IS '字段数据类型';
COMMENT ON COLUMN DESIGN_DB_INFO.IS_PRIMARY IS '是否主键：0-否，1-是';
COMMENT ON COLUMN DESIGN_DB_INFO.IS_NULLABLE IS '是否可为空：0-否，1-是';
COMMENT ON COLUMN DESIGN_DB_INFO.COLUMN_ORDER IS '字段顺序';
COMMENT ON COLUMN DESIGN_DB_INFO.CREATE_BY IS '创建人';
COMMENT ON COLUMN DESIGN_DB_INFO.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN DESIGN_DB_INFO.UPDATE_BY IS '更新人';
COMMENT ON COLUMN DESIGN_DB_INFO.UPDATE_TIME IS '更新时间';
