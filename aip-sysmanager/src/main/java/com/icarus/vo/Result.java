package com.icarus.vo;

import lombok.Data;

/**
 * 统一API返回结果封装
 */
@Data
public class Result<T> {
    /**
     * 成功标志
     */
    private boolean success = true;

    /**
     * 返回处理消息
     */
    private String message = "操作成功";

    /**
     * 返回代码
     */
    private Integer code = 0;

    /**
     * 返回数据对象
     */
    private T result;

    /**
     * 时间戳
     */
    private long timestamp = System.currentTimeMillis();

    public Result() {
    }

    /**
     * 成功结果
     */
    public static <T> Result<T> OK() {
        Result<T> r = new Result<>();
        r.setSuccess(true);
        r.setCode(200);
        r.setMessage("成功");
        return r;
    }

    /**
     * 成功结果
     */
    public static <T> Result<T> OK(T data) {
        Result<T> r = new Result<>();
        r.setSuccess(true);
        r.setCode(200);
        r.setResult(data);
        r.setMessage("成功");
        return r;
    }

    /**
     * 失败结果
     */
    public static <T> Result<T> error(String msg) {
        Result<T> r = new Result<>();
        r.setSuccess(false);
        r.setCode(500);
        r.setMessage(msg);
        return r;
    }

    /**
     * 失败结果
     */
    public static <T> Result<T> error(int code, String msg) {
        Result<T> r = new Result<>();
        r.setSuccess(false);
        r.setCode(code);
        r.setMessage(msg);
        return r;
    }
} 