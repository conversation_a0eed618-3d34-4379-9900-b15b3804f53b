package com.icarus.vo;

import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * @ClassName ForgetPwdVO
 * <AUTHOR>
 * @Date 2025/3/3
 * @Version 1.0
 **/
@Data
public class ForgetPwdVO {

    /**
     * 新密码
     */
    @NotBlank(message = "新密码不能为空")
    private String newPwd;

    /**
     * 输两遍新密码
     */
    @NotBlank(message = "确认密码不能为空")
    private String confirmPwd;

    /**
     * TOTP动态时间码
     */
    private String totpCode;

    /**
     * 要修改的用户
     */
    @Pattern(regexp = "^(?=.*[a-zA-Z])[a-zA-Z0-9]{4,20}$",
            message = "用户名需4-20位字母和数字组合，且必须包含至少一个英文字母")
    private String userName;

    @AssertTrue(message = "新密码与确认密码不一致")
    public boolean isPasswordMatch() {
        return newPwd != null && newPwd.equals(confirmPwd);
    }


}
