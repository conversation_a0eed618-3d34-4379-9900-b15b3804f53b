package com.icarus.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;


/**
 * @ClassName UserVO
 * <AUTHOR>
 * @Date 2025/3/1
 * @Version 1.0
 **/
@Data
public class UserVO {

    /**
     * 登录账号
     */
    @Pattern(regexp = "^(?=.*[a-zA-Z])[a-zA-Z0-9]{4,20}$",
            message = "用户名需4-20位字母和数字组合，且必须包含至少一个英文字母")
    private String username;

    /**
     * 真实姓名
     */
    private String realname;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    private String password;

    /**
     * 性别(0-默认未知,1-男,2-女)
     */
    private Integer sex;

    /**
     * 电子邮件
     */
    private String email;

    /**
     * 电话
     */
    private String phone;

    /**
     * 负责部门 多个用逗号分隔
     */
    @NotBlank(message = "部门不能为空")
    private String departIds;

    /**
     * 上次登录选择租户ID
     */
    private Integer loginTenantId;

    /**
     * 角色ID集合 多个用逗号分隔
     */
    @NotBlank(message = "角色不能为空")
    private String roleIds;

    @JsonIgnore
    private String totpSecret;

    @JsonIgnore
    private String qrCode;

}
