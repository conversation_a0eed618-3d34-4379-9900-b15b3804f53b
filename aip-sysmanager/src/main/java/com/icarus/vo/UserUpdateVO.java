package com.icarus.vo;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;


/**
 * @ClassName UserUpdateVO
 * <AUTHOR>
 * @Date 2025/3/1
 * @Version 1.0
 **/
@Data
public class UserUpdateVO {

    /**
     * 用户id
     */
    private String id;

    /**
     *
     */
    private String username;

    /**
     * 真实姓名
     */
    private String realname;

    private String password;

    /**
     * 性别(0-默认未知,1-男,2-女)
     */
    private Integer sex;

    /**
     * 电子邮件
     */
    private String email;

    /**
     * 电话
     */
    private String phone;

    /**
     * 负责部门 多个用逗号分隔
     */
    @NotBlank(message = "部门不能为空")
    private String departIds;

    /**
     * 角色ID集合 多个用逗号分隔
     */
    @NotBlank(message = "角色不能为空")
    private String roleIds;


}
