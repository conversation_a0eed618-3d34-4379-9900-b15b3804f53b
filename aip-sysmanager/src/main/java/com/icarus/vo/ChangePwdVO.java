package com.icarus.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import jakarta.validation.constraints.AssertTrue;

/**
 * @ClassName ChangePwdVO
 * <AUTHOR>
 * @Date 2025/2/28
 * @Version 1.0
 **/
@Data
public class ChangePwdVO {

    /**
     * 旧密码
     */
    @NotBlank(message = "旧密码不能为空")
    private String oldPwd;

    /**
     * 新密码
     */
    @NotBlank(message = "新密码不能为空")
//    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).+$", message = "密码必须包含大小写字母和数字")
    private String newPwd;

    /**
     * 输两遍新密码
     */
    @NotBlank(message = "确认密码不能为空")
    private String confirmPwd;

    @JsonIgnore
    private String totpCode;

    @JsonIgnore
    private String totpSecret;

    private Integer type = 0;

    /**
     * 要修改的用户
     */
    @Pattern(regexp = "^(?=.*[a-zA-Z])[a-zA-Z0-9]{4,20}$",
            message = "用户名需4-20位字母和数字组合，且必须包含至少一个英文字母")
    private String userName;

    @AssertTrue(message = "新密码与确认密码不一致")
    public boolean isPasswordMatch() {
        return newPwd != null && newPwd.equals(confirmPwd);
    }

    @AssertTrue(message = "新密码与旧密码不能相同")
    public boolean isPasswordDuplicated() {
        return !newPwd.equals(oldPwd);
    }

}
