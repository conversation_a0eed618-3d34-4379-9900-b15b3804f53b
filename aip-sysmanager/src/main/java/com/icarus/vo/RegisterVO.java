package com.icarus.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;


/**
 * @ClassName RegisterVO
 * <AUTHOR>
 * @Date 2025/3/2
 * @Version 1.0
 **/
@Data
public class RegisterVO {

    /**
     * 登录账号
     */
    @Pattern(regexp = "^(?=.*[a-zA-Z])[a-zA-Z0-9]{4,20}$",
            message = "用户名需4-20位字母和数字组合，且必须包含至少一个英文字母")
    private String username;

    /**
     * 真实姓名
     */
    private String realname;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    private String password;

    @NotBlank(message = "确认密码不能为空")
    private String confirmPwd;

    /**
     * totp验证码
     */
    private String totpCode;

    @JsonIgnore
    private String secret;

    @JsonIgnore
    private String qrCode;

    /**
     * 性别(0-默认未知,1-男,2-女)
     */
    private Integer sex;

    /**
     * 负责部门 多个用逗号分隔
     */
    @NotBlank(message = "部门不能为空")
    private String departIds;

    @AssertTrue(message = "两次输入的密码不一致")
    public boolean isPasswordMatch() {
        return password != null && password.equals(confirmPwd);
    }


}
