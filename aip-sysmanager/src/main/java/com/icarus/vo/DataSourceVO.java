package com.icarus.vo;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 数据源VO类
 */
@Data
public class DataSourceVO {
    /**
     * 主键ID（更新时使用）
     */
    private String id;

    /**
     * 数据源编码
     */
    @NotBlank(message = "数据源编码不能为空")
    private String code;

    /**
     * 数据源名称
     */
    @NotBlank(message = "数据源名称不能为空")
    private String name;

    /**
     * 备注
     */
    private String remark;

    /**
     * 数据库类型
     */
    @NotBlank(message = "数据库类型不能为空")
    private String dbType;

    /**
     * 驱动类
     */
    @NotBlank(message = "驱动类不能为空")
    private String dbDriver;

    /**
     * 数据源地址
     */
    @NotBlank(message = "数据源地址不能为空")
    private String dbUrl;

    /**
     * 数据库名称
     */
    @NotBlank(message = "数据库名称不能为空")
    private String dbName;

    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    private String dbUsername;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    private String dbPassword;
}