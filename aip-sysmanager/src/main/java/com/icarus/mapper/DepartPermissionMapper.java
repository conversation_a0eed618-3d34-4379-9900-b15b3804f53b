package com.icarus.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.icarus.entity.DepartPermission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Select;
import java.util.List;

/**
 * 部门权限表 Mapper 接口
 */
@Mapper
public interface DepartPermissionMapper extends BaseMapper<DepartPermission> {
    
    /**
     * 根据部门ID获取权限ID列表
     */
    @Select("select permission_id from design_sys_depart_permission where depart_id = #{departId}")
    List<String> getPermissionIdsByDepartId(@Param("departId") String departId);
    
    /**
     * 根据部门ID删除部门权限关系
     */
    @Delete("delete from design_sys_depart_permission where depart_id = #{departId}")
    int deleteByDepartId(@Param("departId") String departId);
    
    /**
     * 根据权限ID删除部门权限关系
     */
    @Delete("delete from design_sys_depart_permission where permission_id = #{permissionId}")
    int deleteByPermissionId(@Param("permissionId") String permissionId);
} 