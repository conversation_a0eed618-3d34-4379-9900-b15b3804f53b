package com.icarus.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.icarus.entity.DesignSysUserRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户角色关系表数据库访问层
 * @ClassName DesignSysUserRoleMapper
 * <AUTHOR>
 * @Date 2025/3/01
 * @Version 1.0
 **/
@Mapper
public interface DesignSysUserRoleMapper  extends BaseMapper<DesignSysUserRole>{
    /** 
     * 分页查询指定行数据
     *
     * @param page 分页参数
     * @param wrapper 动态查询条件
     * @return 分页对象列表
     */
    IPage<DesignSysUserRole> selectByPage(IPage<DesignSysUserRole> page , @Param(Constants.WRAPPER) Wrapper<DesignSysUserRole> wrapper);
}