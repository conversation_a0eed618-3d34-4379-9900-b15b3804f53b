package com.icarus.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.icarus.entity.Permission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface PermissionMapper extends BaseMapper<Permission> {

    /**
     * 修改菜单状态字段： 是否子节点
     * @param id 菜单id
     * @param leaf 叶子节点
     * @return int
     */
    @Update("update design_sys_permission set is_leaf=#{leaf} where id = #{id}")
    public int setMenuLeaf(@Param("id") String id, @Param("leaf") int leaf);
} 