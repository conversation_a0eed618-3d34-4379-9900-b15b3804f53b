package com.icarus.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.entity.Depart;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.data.repository.query.Param;

import java.util.List;

@Mapper
public interface DepartMapper extends BaseMapper<Depart> {

    /**
     * 根据用户ID查询部门集合
     * @param userId 用户id
     * @return List<SysDepart>
     */
    public List<Depart> queryUserDeparts(@Param("userId") String userId);

    /**
     * 根据用户名查询部门
     *
     * @param username
     * @return
     */
    public List<Depart> queryDepartsByUsername(@Param("username") String username);

    /**
     * 根据用户名查询部门
     *
     * @param userId
     * @return
     */
    public List<String> queryDepartsByUserId(@Param("userId") String userId);

    /**
     * 通过部门编码获取部门id
     * @param orgCode 部门编码
     * @return String
     */
    @Select("select id from design_sys_depart where org_code=#{orgCode}")
    public String queryDepartIdByOrgCode(@Param("orgCode") String orgCode);

    /**
     * 通过部门id，查询部门下的用户的账号
     * @param departIds 部门ID集合
     * @return String
     */
    public List<String> queryUserAccountByDepartIds(@Param("departIds") List<String> departIds);

    /**
     * 通过部门id 查询部门id,父id
     * @param departId 部门id
     * @return
     */
    @Select("select id,parent_id from design_sys_depart where id=#{departId}")
    public Depart getParentDepartId(@Param("departId") String departId);

    /**
     *  根据部门Id查询,当前和下级所有部门IDS
     * @param departId
     * @return
     */
    List<String> getSubDepIdsByDepId(@Param("departId") String departId);

    /**
     * 根据部门编码获取部门下所有IDS
     * @param orgCodes
     * @return
     */
    List<String> getSubDepIdsByOrgCodes(@org.apache.ibatis.annotations.Param("orgCodes") String[] orgCodes);

    /**
     * 根据parent_id查询下级部门
     * @param parentId 父id
     * @return List<SysDepart>
     */
    List<Depart> queryTreeListByPid(@Param("parentId") String parentId);
    /**
     * 根据id下级部门数量
     * @param parentId
     * @return
     */
    @Select("SELECT count(*) FROM design_sys_depart where del_flag ='0' AND parent_id = #{parentId,jdbcType=VARCHAR}")
    Integer queryCountByPid(@Param("parentId")String parentId);
    /**
     * 根据OrgCod查询所属公司信息
     * @param orgCode
     * @return
     */
    Depart queryCompByOrgCode(@Param("orgCode")String orgCode);
    /**
     * 根据id下级部门
     * @param parentId
     * @return
     */
    @Select("SELECT * FROM design_sys_depart where del_flag ='0' AND parent_id = #{parentId,jdbcType=VARCHAR}")
    List<Depart> queryDeptByPid(@Param("parentId")String parentId);

    /**
     * 通过父级id和租户id查询部门
     * @param parentId
     * @param tenantId
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<Depart> queryBookDepTreeSync(@Param("parentId") String parentId, @Param("tenantId") Integer tenantId, @Param("departName") String departName);

    @InterceptorIgnore(tenantLine = "true")
    @Select("SELECT * FROM design_sys_depart where id = #{id,jdbcType=VARCHAR}")
    Depart getDepartById(@Param("id") String id);

    @InterceptorIgnore(tenantLine = "true")
    List<Depart> getMaxCodeDepart(@Param("page") Page<Depart> page, @Param("parentId") String parentId);

    /**
     * 修改部门状态字段： 是否子节点
     * @param id 部门id
     * @param leaf 叶子节点
     * @return int
     */
    @Update("UPDATE design_sys_depart SET iz_leaf=#{leaf} WHERE id = #{id}")
    int setMainLeaf(@Param("id") String id, @Param("leaf") Integer leaf);



    /**
     * 根据部门名称和租户id获取部门数据
     * @param departName
     * @param tenantId
     * @return
     */
    List<Depart> getDepartByName(@Param("departName")String departName, @Param("tenantId")Integer tenantId,@Param("parentId") String parentId);

    /**
     * 根据部门名称和租户id获取分页部门数据
     * @param page
     * @param departName
     * @param tenantId
     * @param parentId
     * @return
     */
    List<Depart> getDepartPageByName(@Param("page") Page<Depart> page, @Param("departName") String departName, @Param("tenantId") Integer tenantId, @Param("parentId") String parentId);

} 