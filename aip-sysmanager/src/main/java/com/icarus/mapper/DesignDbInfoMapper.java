package com.icarus.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.icarus.entity.DesignDbInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据库表信息Mapper接口
 */
@Mapper
public interface DesignDbInfoMapper extends BaseMapper<DesignDbInfo> {
    /**
     * 批量删除指定数据源的表信息
     * @param dataSourceCode 数据源编码
     * @return 影响行数
     */
    int deleteByDataSourceCode(@Param("dataSourceCode") String dataSourceCode);
} 