package com.icarus.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.icarus.entity.UserDepart;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户部门关系表 Mapper 接口
 */
@Mapper
public interface UserDepartMapper extends BaseMapper<UserDepart> {
    
    /**
     * 通过用户id查询部门id列表
     */
    @Select("SELECT dep_id FROM design_sys_user_depart WHERE user_id = #{userId}")
    List<String> getDepIdsByUserId(@Param("userId") String userId);

    /**
     * 通过部门id查询用户id列表
     */
    @Select("SELECT user_id FROM design_sys_user_depart WHERE dep_id = #{depId}")
    List<String> getUserIdsByDepId(@Param("depId") String depId);
    
    /**
     * 通过部门id列表查询用户id列表
     */
    List<String> getUserIdsByDepIds(@Param("depIds") List<String> depIds);

    List<UserDepart> getUserDepartByUid(String userId);
}