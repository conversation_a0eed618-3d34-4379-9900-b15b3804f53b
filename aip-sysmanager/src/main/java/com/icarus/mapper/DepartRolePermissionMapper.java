package com.icarus.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.icarus.entity.DepartRolePermission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Select;
import java.util.List;

/**
 * 部门角色权限表 Mapper 接口
 */
@Mapper
public interface DepartRolePermissionMapper extends BaseMapper<DepartRolePermission> {
    
    /**
     * 根据部门角色ID获取权限ID列表
     */
    @Select("select permission_id from design_sys_depart_role_permission where depart_id = #{departId} and role_id = #{roleId}")
    List<String> getPermissionIds(@Param("departId") String departId, @Param("roleId") String roleId);
    
    /**
     * 根据部门ID删除权限关系
     */
    @Delete("delete from design_sys_depart_role_permission where depart_id = #{departId}")
    int deleteByDepartId(@Param("departId") String departId);
    
    /**
     * 根据角色ID删除权限关系
     */
    @Delete("delete from design_sys_depart_role_permission where role_id = #{roleId}")
    int deleteByRoleId(@Param("roleId") String roleId);
    
    /**
     * 根据权限ID删除权限关系
     */
    @Delete("delete from design_sys_depart_role_permission where permission_id = #{permissionId}")
    int deleteByPermissionId(@Param("permissionId") String permissionId);
    
    /**
     * 根据部门ID和角色ID删除权限关系
     */
    @Delete("delete from design_sys_depart_role_permission where depart_id = #{departId} and role_id = #{roleId}")
    int deleteByDepartIdAndRoleId(@Param("departId") String departId, @Param("roleId") String roleId);
} 