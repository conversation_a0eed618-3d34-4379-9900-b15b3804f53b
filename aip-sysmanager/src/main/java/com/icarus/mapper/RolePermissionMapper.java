package com.icarus.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.icarus.entity.RolePermission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Select;
import java.util.List;

/**
 * 角色权限表 Mapper 接口
 */
@Mapper
public interface RolePermissionMapper extends BaseMapper<RolePermission> {
    
    /**
     * 根据角色ID获取权限ID列表
     */
    @Select("select permission_id from design_sys_role_permission where role_id = #{roleId}")
    List<String> getPermissionIdsByRoleId(@Param("roleId") String roleId);
    
    /**
     * 根据角色ID删除角色权限关系
     */
    @Delete("delete from design_sys_role_permission where role_id = #{roleId}")
    int deleteByRoleId(@Param("roleId") String roleId);
    
    /**
     * 根据权限ID删除角色权限关系
     */
    @Delete("delete from design_sys_role_permission where permission_id = #{permissionId}")
    int deleteByPermissionId(@Param("permissionId") String permissionId);
} 