package com.icarus.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.icarus.entity.DesignSysUserDepart;

/**
 * 用户部门关系表
 * @ClassName DesignSysUserDepart
 * <AUTHOR>
 * @Date 2025/3/1
 * @Version 1.0
 **/
@Mapper
public interface DesignSysUserDepartMapper  extends BaseMapper<DesignSysUserDepart>{
    /** 
     * 分页查询指定行数据
     *
     * @param page 分页参数
     * @param wrapper 动态查询条件
     * @return 分页对象列表
     */
    IPage<DesignSysUserDepart> selectByPage(IPage<DesignSysUserDepart> page , @Param(Constants.WRAPPER) Wrapper<DesignSysUserDepart> wrapper);
}