package com.icarus.totp;

import dev.samstevens.totp.code.*;
import dev.samstevens.totp.exceptions.QrGenerationException;
import dev.samstevens.totp.qr.QrData;
import dev.samstevens.totp.qr.ZxingPngQrGenerator;
import dev.samstevens.totp.secret.DefaultSecretGenerator;
import dev.samstevens.totp.secret.SecretGenerator;
import dev.samstevens.totp.time.SystemTimeProvider;
import dev.samstevens.totp.time.TimeProvider;
import org.springframework.stereotype.Component;

import static dev.samstevens.totp.util.Utils.getDataUriForImage;

/**
 * @ClassName TotpUtils
 * <AUTHOR>
 * @Date 2025/3/2
 * @Version 1.0
 **/
@Component
public class TotpUtils {

    private final TotpConfig totpConfig;

    public TotpUtils(TotpConfig totpConfig) {
        this.totpConfig = totpConfig;
    }

    /**
     * 生成共享密钥
     * @return
     */
    public String createSecretKey() {
        SecretGenerator secretGenerator = new DefaultSecretGenerator();
        return secretGenerator.generate();
    }

    /**
     * 生成二维码(格式 data:image/png;base64)
     * @param secretKey
     * @param userName
     * @return
     * @throws QrGenerationException
     */
    public String createQrCode(String userName, String secretKey) throws QrGenerationException {
        // 生成二维码
        QrData data = new QrData.Builder()
                .label(userName)
                .secret(secretKey)
                .issuer(totpConfig.getIssuer())
                .algorithm(HashingAlgorithm.SHA1)
                .digits(totpConfig.getDigits())
                .period(totpConfig.getPeriod())
                .build();
        ZxingPngQrGenerator generator = new ZxingPngQrGenerator();
        generator.setImageSize(totpConfig.getImageSize());
        byte[] imageData = generator.generate(data);
        // 获取生成的数据的MIME类型
        String mimeType = generator.getImageMimeType();
        return getDataUriForImage(imageData, mimeType);
    }

    /**
     * 验证动态时间码
     * @param secretKey
     * @param code
     * @return
     */
    public boolean verify(String secretKey, String code) {
        TimeProvider timeProvider = new SystemTimeProvider();
        CodeGenerator codeGenerator = new DefaultCodeGenerator();
        DefaultCodeVerifier verifier = new DefaultCodeVerifier(codeGenerator, timeProvider);
        // 将代码有效期设置，单位为秒
        verifier.setTimePeriod(totpConfig.getPeriod());
        // 允许前后各 x 个时间间隔内有效的代码通过验证
        verifier.setAllowedTimePeriodDiscrepancy(totpConfig.getAllowedDiscrepancy());
        return verifier.isValidCode(secretKey, code);
    }

    /**
     * 是否开启双因素验证
     * @return
     */
    public boolean getAuthenticationSign() {
        return totpConfig.isAuthentication();
    }

}
