package com.icarus.totp;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName TotpConfig
 * <AUTHOR>
 * @Date 2025/3/2
 * @Version 1.0
 **/
@Data
@Configuration
@ConfigurationProperties(prefix = "sysmanager.totp")
public class TotpConfig {

    /**
     * 是否开启TOTP认证
     */
    private boolean authentication;

    /**
     * 签发者名称（显示在验证器应用中）
     */
    private String issuer ;

    /**
     * 验证码位数（默认6位，Currently, on Android and Blackberry the digits parameter is ignored by the Google Authenticator implementation.）
     */
    private int digits = 6;

    /**
     * @link https://github.com/google/google-authenticator/wiki/Key-Uri-Format
     * 验证码有效期（秒）（Currently, the period parameter is ignored by the Google Authenticator implementations.）
     */
    private int period = 30;

    /**
     * 二维码大小
     */
    private int imageSize = 200;

    /**
     * 允许的时间窗口偏差（单位：个）
     */
    private int allowedDiscrepancy = 2;

}
