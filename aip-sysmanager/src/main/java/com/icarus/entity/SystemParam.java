package com.icarus.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("design_sys_param")
public class SystemParam {

    @TableId(type = IdType.ASSIGN_ID)
    private Integer id;
    /**
     * 系统参数名称
     */
    private String paramKey;
    /**
     * 系统参数值
     */
    private String paramValue;
    /**
     * 系统参数描述
     */
    private String description;
}
