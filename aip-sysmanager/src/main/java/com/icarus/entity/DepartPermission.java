package com.icarus.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 部门权限表
 */
@Data
@TableName("design_sys_depart_permission")
public class DepartPermission {
    
    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    
    /**
     * 部门id
     */
    private String departId;
    
    /**
     * 权限id
     */
    private String permissionId;
    
    /**
     * 数据规则id
     */
    private String dataRuleIds;
} 