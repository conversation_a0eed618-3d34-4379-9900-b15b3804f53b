package com.icarus.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * @ClassName DesignSysUser
 * <AUTHOR>
 * @Date 2025/2/27
 * @Version 1.0
 **/
@Data
@TableName("design_sys_user")
public class DesignSysUser {
    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 登录账号
     */
    private String username;

    /**
     * 真实姓名
     */
    private String realname;

    /**
     * 密码
     */
    private String password;

    /**
     * 是否管理员
     */
    @TableField(exist = false)
    private boolean adminFlag;

    /**
     * md5密码盐
     */
    private String salt;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 生日
     */
    private Date birthday;

    /**
     * 性别(0-默认未知,1-男,2-女)
     */
    private Integer sex;

    /**
     * 电子邮件
     */
    private String email;

    /**
     * 电话
     */
    private String phone;

    /**
     * 登录会话的机构编码
     */
    private String orgCode;

    /**
     * 状态(1-正常,2-冻结)
     */
    private Integer status;

    /**
     * totp验证码密钥
     */
    private String totpSecret;

    /**
     * 二维码
     */
    private String qrCode;

    /**
     * 删除状态(0-正常,1-已删除)
     */
    @TableLogic
    private Integer deleted;

    /**
     * 第三方登录的唯一标识
     */
    private String thirdId;

    /**
     * 第三方类型
     */
    private String thirdType;

    /**
     * 同步工作流引擎(1-同步,0-不同步)
     */
    private Integer activitiSync;

    /**
     * 工号，唯一键
     */
    private String workNo;

    /**
     * 座机号
     */
    private String telephone;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 身份（1普通成员 2上级）
     */
    private Integer userIdentity;

    /**
     * 负责部门 多个用逗号分隔
     */
    private String departIds;

    /**
     * 负责部门 多个用逗号分隔
     */
    @TableField(exist = false)
    private String departNames;

    /**
     * 设备ID
     */
    private String clientId;

    /**
     * 上次登录选择租户ID
     */
    private String loginTenantId;

    /**
     * 流程入职离职状态
     */
    private String bpmStatus;

    /**
     * 角色ID集合 多个用逗号分隔
     */
    @TableField(exist = false)
    private String roleIds;

    @TableField(exist = false)
    private String roleNames;

    /**
     * 租户ID
     */
    @TableField(exist = false)
    private String relTenantId;

    @TableField(exist = false)
    private String updateFromPage;

}
