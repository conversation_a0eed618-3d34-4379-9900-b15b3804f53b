package com.icarus.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.util.List;
import com.icarus.enums.MenuType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.icarus.config.BooleanToIntegerDeserializer;

/**
 * 菜单权限表
 */
@Data
@TableName("design_sys_permission")
public class Permission {
    
    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    @TableField(exist = false)
    private String key;

    @TableField(exist = false)
    private String title;

    /**
     * 父id
     */
    private String parentId;
    
    /**
     * 菜单标题
     */
    private String name;
    
    /**
     * 路径
     */
    private String url;
    
    /**
     * 组件
     */
    private String component;
    
    /**
     * 是否路由菜单: 0:不是  1:是（默认值1）
     */
    @JsonDeserialize(using = BooleanToIntegerDeserializer.class)
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Integer isRoute;
    
    /**
     * 组件名字
     */
    private String componentName;
    
    /**
     * 一级菜单跳转地址
     */
    private String redirect;
    
    /**
     * 菜单类型(0:一级菜单; 1:子菜单:2:按钮权限)
     */
    @TableField(value = "menu_type")
    private Integer menuType;
    
    /**
     * 获取菜单类型枚举
     */
    @TableField(exist = false)
    private String menuTypeName;
    
    public String getMenuTypeName() {
        return MenuType.getByCode(this.menuType).getDesc();
    }
    
    /**
     * 菜单权限编码
     */
    private String perms;
    
    /**
     * 权限策略1显示2禁用
     */
    private String permsType;
    
    /**
     * 菜单排序
     */
    private BigDecimal sortNo;

    /**
     * 所属区域
     */
    private String region;
    
    /**
     * 聚合子路由: 1是0否
     */
    @JsonDeserialize(using = BooleanToIntegerDeserializer.class)
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Integer alwaysShow;
    
    /**
     * 菜单图标
     */
    private String icon;
    
    /**
     * 是否叶子节点: 1是0否
     */
    @JsonDeserialize(using = BooleanToIntegerDeserializer.class)
    private Integer isLeaf;
    
    /**
     * 是否缓存该页面: 1:是 0:不是
     */
    @JsonDeserialize(using = BooleanToIntegerDeserializer.class)
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Integer keepAlive;
    
    /**
     * 是否隐藏路由: 0否,1是
     */
    @JsonDeserialize(using = BooleanToIntegerDeserializer.class)
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Integer hidden;
    
    /**
     * 是否隐藏tab: 0否,1是
     */
    @JsonDeserialize(using = BooleanToIntegerDeserializer.class)
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Integer hideTab;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    
    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;
    
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
    
    /**
     * 删除状态 0正常 1已删除
     */
    private Integer delFlag;
    
    /**
     * 是否添加数据权限1是0否
     */
    private Integer ruleFlag;
    
    /**
     * 按钮权限状态(0无效1有效)
     */
    private String status;
    
    /**
     * 外链菜单打开方式 0/内部打开 1/外部打开
     */
    @JsonDeserialize(using = BooleanToIntegerDeserializer.class)
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Integer internalOrExternal;
    
    /**
     * 子菜单/按钮
     */
    @TableField(exist = false)
    private List<Permission> children;
} 