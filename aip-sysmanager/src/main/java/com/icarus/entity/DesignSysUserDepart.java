package com.icarus.entity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户部门关系表
 * @ClassName DesignSysUserDepart
 * <AUTHOR>
 * @Date 2025/3/1
 * @Version 1.0
 **/
 @Data
@TableName("design_sys_user_depart")
public class DesignSysUserDepart implements Serializable,Cloneable{
    /** id */
    @TableId
    private String id ;
    /** 用户id */
    private String userId ;
    /** 部门id */
    private String depId ;

}