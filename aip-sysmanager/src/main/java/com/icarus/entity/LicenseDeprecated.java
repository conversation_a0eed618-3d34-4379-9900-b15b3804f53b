package com.icarus.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 许可证信息实体类
 */
@Deprecated
@Data
@TableName("DESIGN_DEVOPS_LICENSE")
public class LicenseDeprecated {
    
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    
    /**
     * 许可证编号
     */
    private String licenseNumber;
    
    /**
     * 项目名称
     */
    private String projectName;
    
    /**
     * 产品名称
     */
    private String productName;
    
    /**
     * 申请人名称
     */
    private String applicantName;
    
    /**
     * 证书生效时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime issuedTime;
    
    /**
     * 证书失效时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime expiryTime;

    /**
     * 有效期(天)
     */
    @TableField(exist = false)
    private Long expiration;
    
    /**
     * 许可证状态(1:生效 2:失效)
     */
    private String licenseStatus;
    
    /**
     * 备注
     */
    private String remarks;
    
    /**
     * 许可证内容
     */
    private String licenseContent;
    
    /**
     * 原始内容
     */
    private String content;
    
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    
    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;
    
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
    
    /**
     * RSA公钥
     */
    private String publicKey;
    
    /**
     * RSA私钥
     */
    private String privateKey;
} 