package com.icarus.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户角色关系表
 * @ClassName DesignSysUserRole
 * <AUTHOR>
 * @Date 2025/3/01
 * @Version 1.0
 **/
@Data
@TableName("design_sys_user_role")
public class DesignSysUserRole implements Serializable,Cloneable{

    /** 主键id */
    @TableId
    private String id ;

    /** 用户id */
    private String userId ;

    /** 角色id */
    private String roleId ;

    /** 租户ID */
    private String tenantId ;

}