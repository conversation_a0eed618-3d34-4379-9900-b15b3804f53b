package com.icarus.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.NoArgsConstructor;

/**
 * 角色权限表
 */
@Data
@TableName("design_sys_role_permission")
@NoArgsConstructor
public class RolePermission {
    
    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    
    /**
     * 角色id
     */
    private String roleId;
    
    /**
     * 权限id
     */
    private String permissionId;
    
    /**
     * 数据权限ids
     */
    private String dataRuleIds;
    
    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime operateDate;
    
    /**
     * 操作ip
     */
    private String operateIp;

    public RolePermission(String roleId, String p) {
        this.roleId = roleId;
        this.permissionId = p;
    }
}