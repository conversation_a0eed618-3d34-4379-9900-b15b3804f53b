package com.icarus.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 部门表
 */
@Data
@TableName("design_sys_depart")
public class Depart {
    
    /**
     * ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    
    /**
     * 父机构ID
     */
    private String parentId;
    
    /**
     * 机构/部门名称
     */
    private String departName;
    
    /**
     * 英文名
     */
    private String departNameEn;
    
    /**
     * 缩写
     */
    private String departNameAbbr;
    
    /**
     * 排序
     */
    private Integer departOrder;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 机构类别 1公司，2组织机构，3岗位
     */
    private String orgCategory;
    
    /**
     * 机构类型 1一级部门 2子部门
     */
    private String orgType;
    
    /**
     * 机构编码
     */
    private String orgCode;
    
    /**
     * 手机号
     */
    private String mobile;
    
    /**
     * 传真
     */
    private String fax;
    
    /**
     * 地址
     */
    private String address;
    
    /**
     * 备注
     */
    private String memo;
    
    /**
     * 状态（1启用，0不启用）
     */
    private String status;
    
    /**
     * 删除状态（0，正常，1已删除）
     */
    private String delFlag;
    
    /**
     * 对接企业微信的ID
     */
    private String qywxIdentifier;
    
    /**
     * 对接钉钉部门的ID
     */
    private String dingIdentifier;

    /**部门负责人的ids*/
    @TableField(exist = false)
    private String directorUserIds;
    /**旧的部门负责人的ids(用于比较删除和新增)*/
    @TableField(exist = false)
    private String oldDirectorUserIds;
    
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    
    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;
    
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
    
    /**
     * 租户ID
     */
    private Integer tenantId;
    
    /**
     * 是否有叶子节点: 1是0否
     */
    private Integer izLeaf;
    
    /**
     * 子部门列表
     */
    @TableField(exist = false)
    private List<Depart> children;
} 