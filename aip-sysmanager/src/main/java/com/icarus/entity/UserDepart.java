package com.icarus.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 用户部门关系表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("design_sys_user_depart")
public class UserDepart {
    
    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    
    /**
     * 用户id
     */
    private String userId;
    
    /**
     * 部门id
     */
    private String depId;

    /**
     * 构造函数 - 快速创建用户部门关系
     */
    public UserDepart(String userId, String depId) {
        this.userId = userId;
        this.depId = depId;
    }
} 