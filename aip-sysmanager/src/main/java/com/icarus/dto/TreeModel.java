package com.icarus.dto;

import com.icarus.entity.Permission;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 树形列表用到
 */
@Data
public class TreeModel implements Serializable {

    private static final long serialVersionUID = 4013193970046502756L;

    private String key;

    private String title;

    private String slotTitle;

    private Integer isLeaf;

    private String icon;

    private Integer ruleFlag;

    private Map<String, String> scopedSlots;


    private List<TreeModel> children;

    public TreeModel() {

    }

    public TreeModel(Permission permission) {
        this.key = permission.getId();
        this.icon = permission.getIcon();
        this.parentId = permission.getParentId();
        this.title = permission.getName();
        this.slotTitle = permission.getName();
        this.value = permission.getId();
        this.isLeaf = permission.getIsLeaf();
        this.label = permission.getName();
        if (permission.getIsLeaf() != 1) {
            this.children = new ArrayList<TreeModel>();
        }
    }

    public TreeModel(String key, String parentId, String slotTitle, Integer ruleFlag, Integer isLeaf) {
        this.key = key;
        this.parentId = parentId;
        this.ruleFlag = ruleFlag;
        this.slotTitle = slotTitle;
        Map<String, String> map = new HashMap(5);
        map.put("title", "hasDatarule");
        this.scopedSlots = map;
        this.isLeaf = isLeaf;
        this.value = key;
        if (isLeaf != 1) {
            this.children = new ArrayList<TreeModel>();
        }
    }

    private String parentId;

    private String label;

    private String value;

}
