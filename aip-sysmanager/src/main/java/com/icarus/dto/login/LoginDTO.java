package com.icarus.dto.login;

import com.icarus.dto.PermissionResultDTO;
import com.icarus.entity.Depart;
import com.icarus.entity.DesignSysUser;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @ClassName LoginDTO
 * <AUTHOR>
 * @Date 2025/2/28
 * @Version 1.0
 **/
@Data
public class LoginDTO {

    /**
     * 跳转页面
     *  1-登录后跳转首页
     *  2-TOTP初次二维码页面
     *  3-TOTP填验证码页面
     */
    private Integer type;

    /**
     * TOTP 二维码
     */
    private String qrCode;

    /**
     * TOTP 密钥
     */
    private String totpSecret;

    /**
     * token
     */
    private String token;

    /**
     * 用户信息
     */
    private DesignSysUser user;

    /**
     * 用户部门信息
     */
    private List<Depart> departs;

    /**
     * 用户菜单信息
     */
    private Map<String, List<PermissionResultDTO>> menuTree;
}
