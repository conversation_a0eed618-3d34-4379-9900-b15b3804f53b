package com.icarus.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 数据库表信息DTO类
 */
@Data
public class DbInfoDTO {
    /**
     * 主键
     */
    private String id;

    /**
     * 数据源编码
     */
    private String dataSourceCode;

    /**
     * 表名称
     */
    private String tableName;

    /**
     * 表编码
     */
    private String tableCode;

    /**
     * 表描述
     */
    private String tableDescription;

    /**
     * 列名称
     */
    private String columnName;

    /**
     * 列编码
     */
    private String columnCode;

    /**
     * 列描述
     */
    private String columnDescription;

    /**
     * 数据类型
     */
    private String dataType;

    /**
     * 是否主键 (0-否, 1-是)
     */
    private String isPrimary;

    /**
     * 是否可为空 (0-否, 1-是)
     */
    private String isNullable;

    /**
     * 列顺序
     */
    private Integer columnOrder;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
} 