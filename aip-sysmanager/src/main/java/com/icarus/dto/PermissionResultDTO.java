package com.icarus.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * <p>
 * 权限结果
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-01
 */
@Data
public class PermissionResultDTO implements Serializable {

    /**
     * 菜单标题
     */
    private String title;

    /**
     * key
     */
    private String key;

    /**
     * icon
     */
    private String icon;

    /**
     * 菜单路径
     */
    private String path;

    /**
     * 子集
     */
    private List<PermissionResultDTO> children = new ArrayList<>();
}
