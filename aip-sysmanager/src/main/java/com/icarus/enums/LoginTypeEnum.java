package com.icarus.enums;

import lombok.Getter;

/**
 * 登录跳转枚举
 * @ClassName LoginTypeEnum
 * <AUTHOR>
 * @Date 2025/3/3
 * @Version 1.0
 **/
@Getter
public enum LoginTypeEnum {

    /**
     * 登录
     */
    LOGIN_TYPE(1, "登录"),

    /**
     * TOTP 双因素认证 二维码生成
     */
    TOTP_QR_CODE_TYPE(2, "TOTP初次二维码"),

    /**
     * TOTP 双因素认证 时间验证码
     */
    TOTP_TIME_CODE_TYPE(3, "TOTP时间验证码"),
    ;

    private final Integer code;
    private final String desc;

    LoginTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


}
