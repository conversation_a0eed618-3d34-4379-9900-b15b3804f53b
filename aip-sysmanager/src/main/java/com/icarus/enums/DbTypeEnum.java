package com.icarus.enums;

import lombok.Getter;

/**
 * 数据库类型枚举
 */
@Getter
public enum DbTypeEnum {
    
    MYSQL55("mysql5.5", "com.mysql.jdbc.Driver", "MySQL 5.5"),
    MYSQL57("mysql5.7+", "com.mysql.cj.jdbc.Driver", "MySQL 5.7及以上"),
    ORACLE("oracle", "oracle.jdbc.driver.OracleDriver", "Oracle数据库"),
    POSTGRESQL("postgresql", "org.postgresql.Driver", "PostgreSQL数据库");

    /**
     * 数据库类型编码
     */
    private final String code;

    /**
     * 驱动类名
     */
    private final String driverClass;

    /**
     * 描述
     */
    private final String description;

    DbTypeEnum(String code, String driverClass, String description) {
        this.code = code;
        this.driverClass = driverClass;
        this.description = description;
    }

    /**
     * 根据类型编码获取驱动类名
     * @param code 类型编码
     * @return 驱动类名
     */
    public static String getDriverClass(String code) {
        for (DbTypeEnum dbType : values()) {
            if (dbType.getCode().equals(code)) {
                return dbType.getDriverClass();
            }
        }
        throw new IllegalArgumentException("Unsupported database type: " + code);
    }
} 