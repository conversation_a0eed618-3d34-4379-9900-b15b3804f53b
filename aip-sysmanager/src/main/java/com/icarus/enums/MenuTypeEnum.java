package com.icarus.enums;

import lombok.Getter;

/**
 * 导航菜单类型枚举
 */
@Getter
public enum MenuTypeEnum {

    /**
     * 固定菜单
     */
    FIXED_MENU("fixedMenu", "固定菜单", 1),

    /**
     * 业务模块
     */
    BUSINESS_MENU("businessMenu", "业务模块", 2),

    /**
     * 用户设置
     */
    USER_CONFIG("userConfig", "用户设置", 3);

    /**
     * 菜单编码
     */
    private final String code;

    /**
     * 菜单名称
     */
    private final String name;

    /**
     * 菜单类型
     */
    private final Integer type;

    /**
     * 构造方法
     *
     * @param code 菜单编码
     * @param name 菜单名称
     * @param type 菜单类型
     */
    MenuTypeEnum(String code, String name, Integer type) {
        this.code = code;
        this.name = name;
        this.type = type;
    }

    /**
     * 根据编码获取枚举
     *
     * @param code 菜单编码
     * @return 菜单枚举
     */
    public static MenuTypeEnum getByCode(String code) {
        for (MenuTypeEnum value : MenuTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据类型获取枚举
     *
     * @param type 菜单类型
     * @return 菜单枚举
     */
    public static MenuTypeEnum getByType(Integer type) {
        for (MenuTypeEnum value : MenuTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 判断是否为固定菜单
     *
     * @param code 菜单编码
     * @return true/false
     */
    public static boolean isFixedMenu(String code) {
        return FIXED_MENU.getCode().equals(code);
    }

    /**
     * 判断是否为业务模块
     *
     * @param code 菜单编码
     * @return true/false
     */
    public static boolean isBusinessMenu(String code) {
        return BUSINESS_MENU.getCode().equals(code);
    }

    /**
     * 判断是否为用户设置
     *
     * @param code 菜单编码
     * @return true/false
     */
    public static boolean isUserConfig(String code) {
        return USER_CONFIG.getCode().equals(code);
    }
} 