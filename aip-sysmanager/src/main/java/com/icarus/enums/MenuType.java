package com.icarus.enums;

import lombok.Getter;

/**
 * 菜单类型枚举
 */
@Getter
public enum MenuType {
    
    /**
     * 一级菜单
     */
    PRIMARY(0, "一级菜单"),
    
    /**
     * 子菜单
     */
    SUB_MENU(1, "子菜单");
    
    private final Integer code;
    private final String desc;
    
    MenuType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public static MenuType getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (MenuType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
} 