package com.icarus.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.common.rest.BaseResponse;
import com.icarus.dto.PageDTO;
import com.icarus.dto.user.UserDTO;
import com.icarus.entity.DesignSysUser;
import com.icarus.service.DesignSysUserService;
import com.icarus.vo.ChangePwdVO;
import com.icarus.vo.UserVO;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 用户管理
 * @ClassName DesignSysUserController
 * <AUTHOR>
 * @Date 2025/2/27
 * @Version 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/aip/sys/user")
@RequiredArgsConstructor
public class DesignSysUserController {

    private final DesignSysUserService designSysUserService;

    /**
     * 创建用户
     * @param user
     * @param request
     * @return
     */
    @PostMapping
    public BaseResponse create(@Valid @RequestBody UserVO user, HttpServletRequest request) {
        try {
            designSysUserService.saveUser(user, request);
            return BaseResponse.ok("创建成功", null);
        } catch (Exception e) {
            log.error("创建用户失败: {}", e);
            return BaseResponse.serviceError("创建用户失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户
     * @param user
     * @return
     */
    @PutMapping
    public BaseResponse update(@RequestBody DesignSysUser user, HttpServletRequest request) {
        try {
            designSysUserService.editUser(user, request);
            return BaseResponse.ok("修改成功", null);
        } catch (Exception e) {
            log.error("更新用户失败: {}", e);
            return BaseResponse.serviceError("更新用户失败: " + e.getMessage());
        }
    }

    /**
     * 删除用户
     * @param id
     * @return
     */
    @DeleteMapping("/del/{id}")
    public BaseResponse<Void> delete(@PathVariable String id) {
        try {
            designSysUserService.removeById(id);
            return BaseResponse.ok("删除成功",null);
        } catch (Exception e) {
            log.error("删除用户失败: {}", e);
            return BaseResponse.serviceError("删除用户失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户信息
     * @param id
     * @return
     */
    @GetMapping("/get/{id}")
    public BaseResponse<UserDTO> getById(@PathVariable String id) {
        try {
            DesignSysUser user = designSysUserService.getUserById(id);
            if (user == null) {
                return BaseResponse.badRequest("用户不存在");
            }
            UserDTO dto = new UserDTO();
            BeanUtils.copyProperties(user, dto);
            return BaseResponse.ok(dto);
        } catch (Exception e) {
            log.error("获取用户失败: {}", e);
            return BaseResponse.serviceError("获取用户失败: " + e.getMessage());
        }
    }

    /**
     * 修改密码
     * @param vo
     * @return
     */
    @PostMapping("/change")
    public BaseResponse changePwd(@Valid @RequestBody ChangePwdVO vo, HttpServletRequest request) {
        try {
            log.info("修改密码入参: {}", vo);
            designSysUserService.changePwd(vo, request);
            return BaseResponse.ok("密码修改成功", null);
        } catch (Exception e) {
            log.error("密码更新失败: {}", e);
            return BaseResponse.serviceError("密码更新失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户列表
     * @return
     */
    @GetMapping("/list")
    public BaseResponse<PageDTO<UserDTO>> list(@RequestParam(defaultValue = "1") Integer current,
                                                  @RequestParam(defaultValue = "10") Integer pageSize,
                                                  @RequestParam(required = false) String userName,
                                                  @RequestParam(required = false) String realName,
                                               @RequestParam(required = false) String phone,
                                               @RequestParam(required = false) Integer status,
                                                  @RequestParam(required = false) Integer sex) {
        try {
            Page<DesignSysUser> page = new Page<>(current, pageSize);
            LambdaQueryWrapper<DesignSysUser> wrapper = new LambdaQueryWrapper<>();
            wrapper.like(userName != null, DesignSysUser::getUsername, userName)
                    .like(realName != null, DesignSysUser::getRealname, realName)
                    .eq(status != null, DesignSysUser::getStatus, status)
                    .like(phone != null, DesignSysUser::getPhone, phone)
                    .eq(sex != null, DesignSysUser::getSex, sex);
            return BaseResponse.ok(designSysUserService.userPage(page, wrapper));
        } catch (Exception e) {
            log.error("获取用户列表失败: {}", e);
            return BaseResponse.serviceError("获取用户列表失败: " + e.getMessage());
        }
    }

    /**
     * 启用/禁用
     * @param status 1-启用 2-禁用
     * @return
     */
    @GetMapping("/enable/{userId}/{status}")
    public BaseResponse<UserDTO> isEnabled(@PathVariable String userId,@PathVariable Integer status, HttpServletRequest request) {
        try {
            designSysUserService.enable(userId, status, request);
            return BaseResponse.ok("操作成功", null);
        } catch (Exception e) {
            log.error("操作失败: {}" , e);
            return BaseResponse.serviceError("操作失败: " + e.getMessage());
        }
    }

}
