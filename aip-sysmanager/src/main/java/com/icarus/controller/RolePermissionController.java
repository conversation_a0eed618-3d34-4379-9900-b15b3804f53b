package com.icarus.controller;

import com.icarus.common.rest.BaseResponse;
import com.icarus.service.RolePermissionService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 角色权限关系管理
 */
@Slf4j
@RestController
@RequestMapping("/aip/role-permission")
@RequiredArgsConstructor
public class RolePermissionController {
    
    private final RolePermissionService rolePermissionService;
    
    /**
     * 保存角色权限关系
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     * @param request HTTP请求
     * @return 操作结果
     */
    @PostMapping("/save/{roleId}")
    public BaseResponse<Boolean> saveRolePermissions(
            @PathVariable String roleId,
            @RequestBody List<String> permissionIds,
            HttpServletRequest request) {
        try {
            String operateIp = request.getRemoteAddr();
            boolean success = rolePermissionService.saveRolePermissions(roleId, permissionIds, operateIp);
            return BaseResponse.ok(success);
        } catch (Exception e) {
            log.error("保存角色权限关系失败", e);
            return BaseResponse.serviceError("保存角色权限关系失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取角色拥有的权限ID列表
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    @GetMapping("/list/{roleId}")
    public BaseResponse<List<String>> getPermissionIds(@PathVariable String roleId) {
        try {
            List<String> permissionIds = rolePermissionService.getPermissionIdsByRoleId(roleId);
            return BaseResponse.ok(permissionIds);
        } catch (Exception e) {
            log.error("获取角色权限列表失败", e);
            return BaseResponse.serviceError("获取角色权限列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除角色的所有权限
     * @param roleId 角色ID
     * @return 操作结果
     */
    @DeleteMapping("/role/{roleId}")
    public BaseResponse<Boolean> deleteByRoleId(@PathVariable String roleId) {
        try {
            boolean success = rolePermissionService.deleteByRoleId(roleId);
            return BaseResponse.ok(success);
        } catch (Exception e) {
            log.error("删除角色权限关系失败", e);
            return BaseResponse.serviceError("删除角色权限关系失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除某个权限关联的所有角色关系
     * @param permissionId 权限ID
     * @return 操作结果
     */
    @DeleteMapping("/permission/{permissionId}")
    public BaseResponse<Boolean> deleteByPermissionId(@PathVariable String permissionId) {
        try {
            boolean success = rolePermissionService.deleteByPermissionId(permissionId);
            return BaseResponse.ok(success);
        } catch (Exception e) {
            log.error("删除权限相关的角色关系失败", e);
            return BaseResponse.serviceError("删除权限相关的角色关系失败: " + e.getMessage());
        }
    }
} 