package com.icarus.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.dto.DataSourceDTO;
import com.icarus.dto.DbInfoDTO;
import com.icarus.dto.PageDTO;
import com.icarus.dto.TableInfoDTO;
import com.icarus.entity.DesignDataSource;
import com.icarus.service.DesignDataSourceService;
import com.icarus.vo.DataSourceVO;
import com.icarus.vo.DbTypeVO;
import com.icarus.vo.Result;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 数据源管理控制器
 */
@RestController
@RequestMapping("/aip/dataSource")
@RequiredArgsConstructor
public class DesignDataSourceController {

    private final DesignDataSourceService designDataSourceService;

    /**
     * 保存数据源
     * @param dataSourceVO 数据源VO
     * @param request HTTP请求
     * @return 保存结果
     */
    @PostMapping("/save")
    public Result<DesignDataSource> saveDataSource(@RequestBody @Valid DataSourceVO dataSourceVO, HttpServletRequest request) {
        DesignDataSource dataSource = designDataSourceService.saveDataSource(dataSourceVO, request);
        return Result.OK(dataSource);
    }

    /**
     * 更新数据源
     * @param dataSourceVO 数据源VO
     * @param request HTTP请求
     * @return 更新结果
     */
    @PutMapping("/update")
    public Result<DesignDataSource> updateDataSource(@RequestBody @Valid DataSourceVO dataSourceVO, HttpServletRequest request) {
        if (dataSourceVO.getId() == null) {
            return Result.error("数据源ID不能为空");
        }
        DesignDataSource dataSource = designDataSourceService.updateDataSource(dataSourceVO, request);
        return Result.OK(dataSource);
    }

    /**
     * 测试数据库连接
     * @param dataSourceVO 数据源VO
     * @return 测试结果
     */
    @PostMapping("/testConnection")
    public Result<Boolean> testConnection(@RequestBody @Valid DataSourceVO dataSourceVO) {
        boolean success = designDataSourceService.testConnection(dataSourceVO);
        return Result.OK(success);
    }

    /**
     * 分页查询数据源
     * @param pageNo 页码
     * @param pageSize 每页大小
     * @param code 数据源编码
     * @param name 数据源名称
     * @param dbType 数据库类型
     * @return 分页结果
     */
    @GetMapping("/list")
    public Result<PageDTO<DataSourceDTO>> pageDataSource(
            @RequestParam(defaultValue = "1") Integer pageNo,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String code,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String dbType) {
        Page<DesignDataSource> page = new Page<>(pageNo, pageSize);
        PageDTO<DataSourceDTO> result = designDataSourceService.pageDataSource(page, code, name, dbType);
        return Result.OK(result);
    }

    /**
     * 根据ID获取数据源
     * @param id 数据源ID
     * @return 数据源信息
     */
    @GetMapping("/get/{id}")
    public Result<DataSourceDTO> getDataSourceById(@PathVariable String id) {
        DataSourceDTO dataSource = designDataSourceService.getDataSourceById(id);
        if (dataSource == null) {
            return Result.error("数据源不存在");
        }
        return Result.OK(dataSource);
    }

    /**
     * 同步数据库表信息
     * @param dataSourceId 数据源ID
     * @param request HTTP请求
     * @return 同步结果
     */
    @PostMapping("/sync/{dataSourceId}")
    public Result<Void> syncDatabaseInfo(@PathVariable String dataSourceId, HttpServletRequest request) {
        designDataSourceService.syncDatabaseInfo(dataSourceId, request);
        return Result.OK();
    }

    /**
     * 获取数据源下所有表信息
     * @param dataSourceCode 数据源编码
     * @return 表信息列表（包含字段信息）
     */
    @GetMapping("/tables/{dataSourceCode}")
    public Result<List<TableInfoDTO>> getTablesByDataSourceCode(@PathVariable String dataSourceCode) {
        List<TableInfoDTO> tables = designDataSourceService.getTablesByDataSourceCode(dataSourceCode);
        return Result.OK(tables);
    }

    /**
     * 获取所有数据库类型
     * @return 数据库类型列表
     */
    @GetMapping("/dbTypes")
    public Result<List<DbTypeVO>> listDbTypes() {
        List<DbTypeVO> dbTypes = designDataSourceService.listDbTypes();
        return Result.OK(dbTypes);
    }
} 