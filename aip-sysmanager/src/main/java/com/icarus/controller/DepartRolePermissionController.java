package com.icarus.controller;

import com.icarus.common.rest.BaseResponse;
import com.icarus.service.DepartRolePermissionService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 部门角色权限关系管理
 */
@Slf4j
@RestController
@RequestMapping("/aip/depart-role-permission")
@RequiredArgsConstructor
public class DepartRolePermissionController {
    
    private final DepartRolePermissionService departRolePermissionService;
    
    /**
     * 保存部门角色权限关系
     * @param departId 部门ID
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     * @param request HTTP请求
     * @return 操作结果
     */
    @PostMapping("/save/{departId}/{roleId}")
    public BaseResponse<Boolean> saveDepartRolePermissions(
            @PathVariable String departId,
            @PathVariable String roleId,
            @RequestBody List<String> permissionIds,
            HttpServletRequest request) {
        try {
            String operateIp = request.getRemoteAddr();
            boolean success = departRolePermissionService.saveDepartRolePermissions(departId, roleId, permissionIds, operateIp);
            return BaseResponse.ok(success);
        } catch (Exception e) {
            log.error("保存部门角色权限关系失败", e);
            return BaseResponse.serviceError("保存部门角色权限关系失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取部门角色拥有的权限ID列表
     * @param departId 部门ID
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    @GetMapping("/list/{departId}/{roleId}")
    public BaseResponse<List<String>> getPermissionIds(
            @PathVariable String departId,
            @PathVariable String roleId) {
        try {
            List<String> permissionIds = departRolePermissionService.getPermissionIds(departId, roleId);
            return BaseResponse.ok(permissionIds);
        } catch (Exception e) {
            log.error("获取部门角色权限列表失败", e);
            return BaseResponse.serviceError("获取部门角色权限列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除部门的所有权限关系
     * @param departId 部门ID
     * @return 操作结果
     */
    @DeleteMapping("/depart/{departId}")
    public BaseResponse<Boolean> deleteByDepartId(@PathVariable String departId) {
        try {
            boolean success = departRolePermissionService.deleteByDepartId(departId);
            return BaseResponse.ok(success);
        } catch (Exception e) {
            log.error("删除部门权限关系失败", e);
            return BaseResponse.serviceError("删除部门权限关系失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除角色的所有权限关系
     * @param roleId 角色ID
     * @return 操作结果
     */
    @DeleteMapping("/role/{roleId}")
    public BaseResponse<Boolean> deleteByRoleId(@PathVariable String roleId) {
        try {
            boolean success = departRolePermissionService.deleteByRoleId(roleId);
            return BaseResponse.ok(success);
        } catch (Exception e) {
            log.error("删除角色权限关系失败", e);
            return BaseResponse.serviceError("删除角色权限关系失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除某个权限关联的所有关系
     * @param permissionId 权限ID
     * @return 操作结果
     */
    @DeleteMapping("/permission/{permissionId}")
    public BaseResponse<Boolean> deleteByPermissionId(@PathVariable String permissionId) {
        try {
            boolean success = departRolePermissionService.deleteByPermissionId(permissionId);
            return BaseResponse.ok(success);
        } catch (Exception e) {
            log.error("删除权限关联的关系失败", e);
            return BaseResponse.serviceError("删除权限关联的关系失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除部门角色的所有权限关系
     * @param departId 部门ID
     * @param roleId 角色ID
     * @return 操作结果
     */
    @DeleteMapping("/{departId}/{roleId}")
    public BaseResponse<Boolean> deleteByDepartIdAndRoleId(
            @PathVariable String departId,
            @PathVariable String roleId) {
        try {
            boolean success = departRolePermissionService.deleteByDepartIdAndRoleId(departId, roleId);
            return BaseResponse.ok(success);
        } catch (Exception e) {
            log.error("删除部门角色权限关系失败", e);
            return BaseResponse.serviceError("删除部门角色权限关系失败: " + e.getMessage());
        }
    }
} 