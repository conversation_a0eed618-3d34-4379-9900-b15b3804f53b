package com.icarus.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.icarus.common.redis.RedisUtil;
import com.icarus.common.rest.BaseResponse;
import com.icarus.constant.CommonConstant;
import com.icarus.dto.SysDepartTreeModel;
import com.icarus.dto.login.ForgetPwdDTO;
import com.icarus.dto.login.LoginDTO;
import com.icarus.entity.DesignSysUser;
import com.icarus.service.DesignSysUserService;
import com.icarus.service.impl.DepartServiceImpl;
import com.icarus.totp.TotpUtils;
import com.icarus.utils.JwtUtil;
import com.icarus.vo.ChangePwdVO;
import com.icarus.vo.ForgetPwdVO;
import com.icarus.vo.RegisterVO;
import dev.samstevens.totp.exceptions.QrGenerationException;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;


/**
 * 免登陆相关接口控制器
 * @ClassName CheckFreeController
 * <AUTHOR>
 * @Date 2025/3/1
 * @Version 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/aip/free")
@RequiredArgsConstructor
public class CheckFreeController {

    private final DesignSysUserService designSysUserService;
    private final DepartServiceImpl departService;
    private final TotpUtils totpUtils;
    private final RedisUtil redisUtil;

    /**
     * 用户注册
     * @param user
     * @return
     */
    @PostMapping("/register")
    public BaseResponse register(@Valid @RequestBody RegisterVO user, HttpServletRequest request) {
        try {
            log.info("用户注册入参: {}", user);
            Object obj = redisUtil.get(CommonConstant.PREFIX_USER_TOTP_SECRET + user.getUsername());
            if(obj==null){
                throw new RuntimeException("页面停留时间过长或获取验证数据失败，请刷新后重试");
            }
            String secret = obj.toString();
            // 验证时间验证码
            if(!totpUtils.verify(secret,user.getTotpCode())){
                return BaseResponse.badRequest("验证码错误");
            }
            user.setSecret(secret);
            user.setQrCode((String) redisUtil.get(CommonConstant.PREFIX_USER_TOTP_QR_CODE + user.getUsername()));
            designSysUserService.register(user, request);
            return BaseResponse.ok("注册成功", null);
        } catch (Exception e) {
            log.error("用户注册失败: {}", e);
            return BaseResponse.serviceError("用户注册失败: " + e.getMessage());
        }
    }

    /**
     * 注册时生成 TOTP 二维码
     * @param user
     * @return
     */
    @PostMapping("/register/code")
    public BaseResponse<LoginDTO> createCode(@Valid @RequestBody RegisterVO user) {
        try {
            log.info("注册前生成二维码入参: {}", user);
            DesignSysUser one = designSysUserService.getOne(new QueryWrapper<DesignSysUser>().lambda().eq(DesignSysUser::getUsername, user.getUsername()));
            if(!Objects.isNull(one)){
                throw new RuntimeException("用户名已存在");
            }
            return BaseResponse.ok("二维码生成成功", register(user));
        } catch (Exception e) {
            log.error("二维码生成失败: {}", e);
            return BaseResponse.serviceError("二维码生成失败: " + e.getMessage());
        }
    }

    /**
     * 忘记密码
     * @param vo
     * @param request
     * @return
     */
    @PostMapping("/forgot")
    public BaseResponse<ForgetPwdDTO> forgot(@Valid @RequestBody ForgetPwdVO vo, HttpServletRequest request) {
        try {
            log.info("忘记密码入参: {}", vo);
            ChangePwdVO changePwdVO = new ChangePwdVO();
            BeanUtils.copyProperties(vo, changePwdVO);
            LambdaQueryWrapper<DesignSysUser> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DesignSysUser::getUsername,vo.getUserName());
            DesignSysUser sysUser = designSysUserService.getOne(queryWrapper);
            BaseResponse status = designSysUserService.checkUserIsEffective(sysUser);
            if(status.getCode() != HttpStatus.OK.value()) {
                return status;
            }
            if(totpUtils.getAuthenticationSign()) {
                log.info("===========================系统开启双因素认证===========================");
                // 校验用户是否存在且有效
                if(StringUtils.isEmpty(vo.getTotpCode())){
                    // 没有验证码时， 引导前端打开验证码输入框
                    return BaseResponse.ok("验证成功", new ForgetPwdDTO(1));
                }else{
                    // 有验证码，进行验证
                    if(!totpUtils.verify(sysUser.getTotpSecret(), vo.getTotpCode())) {
                        return BaseResponse.badRequest("验证码错误");
                    }
                }
            }
            changePwdVO.setTotpSecret(sysUser.getTotpSecret());
            changePwdVO.setType(1);
            designSysUserService.changePwd(changePwdVO, request);
            return BaseResponse.ok("修改成功，请重新登录", new ForgetPwdDTO(2));
        } catch (Exception e) {
            log.error("修改失败: {}", e);
            return BaseResponse.serviceError("修改失败: " + e.getMessage());
        }
    }

    /**
     * 免登录查询部门list
     * @param parentId
     * @param ids
     * @param primaryKey
     * @return
     */
    @GetMapping("/depart/list")
    public BaseResponse<List<SysDepartTreeModel>> queryDepartTree(@RequestParam(name = "pid", required = false) String parentId,
                                                                      @RequestParam(name = "ids", required = false) String ids,
                                                                      @RequestParam(name = "primaryKey", required = false) String primaryKey) {
        try {
            List<SysDepartTreeModel> list = departService.queryTreeListByPid(parentId,ids, primaryKey);
            return BaseResponse.ok(list);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return BaseResponse.serviceError("查询部门列表失败");
        }
    }


    /**
     * 注册生成二维码
     * @return
     * @throws QrGenerationException
     */
    public LoginDTO register(RegisterVO user) throws QrGenerationException {
        LoginDTO login = new LoginDTO();
        String secretKey = totpUtils.createSecretKey();
        String qrCode = totpUtils.createQrCode(user.getUsername(), secretKey);
        login.setQrCode(qrCode);
        login.setTotpSecret(secretKey);
        // 设置二维码绑定有效期
        redisUtil.set(CommonConstant.PREFIX_USER_TOTP_QR_CODE + user.getUsername(), qrCode, JwtUtil.EXPIRE_TIME / 2 / 1000);
        redisUtil.set(CommonConstant.PREFIX_USER_TOTP_SECRET + user.getUsername(), secretKey, JwtUtil.EXPIRE_TIME / 2 / 1000);
        log.info("用户[{}]注册，生成密钥和二维码完成", user.getUsername());
        return login;
    }


}
