package com.icarus.controller;

import com.icarus.common.rest.BaseResponse;
import com.icarus.service.DepartPermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 部门权限关系管理
 */
@Slf4j
@RestController
@RequestMapping("/aip/depart-permission")
@RequiredArgsConstructor
public class DepartPermissionController {
    
    private final DepartPermissionService departPermissionService;
    
    /**
     * 保存部门权限关系
     * @param departId 部门ID
     * @param permissionIds 权限ID列表
     * @return 操作结果
     */
    @PostMapping("/save/{departId}")
    public BaseResponse<Boolean> saveDepartPermissions(
            @PathVariable String departId,
            @RequestBody List<String> permissionIds) {
        try {
            boolean success = departPermissionService.saveDepartPermissions(departId, permissionIds);
            return BaseResponse.ok(success);
        } catch (Exception e) {
            log.error("保存部门权限关系失败", e);
            return BaseResponse.serviceError("保存部门权限关系失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取部门拥有的权限ID列表
     * @param departId 部门ID
     * @return 权限ID列表
     */
    @GetMapping("/list/{departId}")
    public BaseResponse<List<String>> getPermissionIds(@PathVariable String departId) {
        try {
            List<String> permissionIds = departPermissionService.getPermissionIdsByDepartId(departId);
            return BaseResponse.ok(permissionIds);
        } catch (Exception e) {
            log.error("获取部门权限列表失败", e);
            return BaseResponse.serviceError("获取部门权限列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除部门的所有权限
     * @param departId 部门ID
     * @return 操作结果
     */
    @DeleteMapping("/depart/{departId}")
    public BaseResponse<Boolean> deleteByDepartId(@PathVariable String departId) {
        try {
            boolean success = departPermissionService.deleteByDepartId(departId);
            return BaseResponse.ok(success);
        } catch (Exception e) {
            log.error("删除部门权限关系失败", e);
            return BaseResponse.serviceError("删除部门权限关系失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除某个权限关联的所有部门关系
     * @param permissionId 权限ID
     * @return 操作结果
     */
    @DeleteMapping("/permission/{permissionId}")
    public BaseResponse<Boolean> deleteByPermissionId(@PathVariable String permissionId) {
        try {
            boolean success = departPermissionService.deleteByPermissionId(permissionId);
            return BaseResponse.ok(success);
        } catch (Exception e) {
            log.error("删除权限相关的部门关系失败", e);
            return BaseResponse.serviceError("删除权限相关的部门关系失败: " + e.getMessage());
        }
    }
} 