package com.icarus.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.icarus.common.rest.BaseResponse;
import com.icarus.entity.SystemParam;
import com.icarus.service.SystemParamService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 系统参数控制器
 * 提供系统参数的查询功能
 */
@Slf4j
@RestController
@RequestMapping("/aip/system/param")
@RequiredArgsConstructor
public class SystemParamController {
    @Resource
    private final SystemParamService systemParamService;


    /**
     * 根据ID获取目录信息
     *
     * @param id 目录ID
     * @return 目录信息
     */

    @GetMapping("/get/{key}")
    public BaseResponse<SystemParam> getByKey(@PathVariable String key) {
        try {
            LambdaQueryWrapper<SystemParam> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SystemParam::getParamKey, key);
            SystemParam systemParam = systemParamService.getOne(wrapper);
            if (systemParam == null) {
                return BaseResponse.badRequest("参数不存在");
            }
            return BaseResponse.ok(systemParam);
        } catch (Exception e) {
            log.error("获取系统参数失败", e);
            return BaseResponse.serviceError("获取系统参数失败: " + e.getMessage());
        }
    }
} 