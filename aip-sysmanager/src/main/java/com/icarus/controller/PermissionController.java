package com.icarus.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.icarus.common.rest.BaseResponse;
import com.icarus.constant.CommonConstant;
import com.icarus.dto.SysPermissionTree;
import com.icarus.entity.Permission;
import com.icarus.entity.RolePermission;
import com.icarus.service.PermissionService;
import com.icarus.service.RolePermissionService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 菜单权限管理
 */
@Slf4j
@RestController
@RequestMapping("/aip/permission")
@RequiredArgsConstructor
public class PermissionController {
    
    private final PermissionService permissionService;
    private final RolePermissionService sysRolePermissionService;
    
    /**
     * 创建菜单
     * @param permission 菜单信息
     * @return 创建后的菜单信息
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public BaseResponse<Void> add(@RequestBody Permission permission) {
        try {
            permissionService.addPermission(permission);
            return BaseResponse.ok(null);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return BaseResponse.serviceError("操作失败");
        }
    }
    
    /**
     * 更新菜单
     * @param permission 菜单信息
     * @return 更新后的菜单信息
     */
    @PostMapping("/update")
    public BaseResponse<Permission> update(@RequestBody Permission permission) {
        try {
            if (permission.getId() == null) {
                return BaseResponse.badRequest("菜单ID不能为空");
            }
            permissionService.editPermission(permission);
            return BaseResponse.ok(permission);
        } catch (Exception e) {
            log.error("更新菜单失败", e);
            return BaseResponse.serviceError("更新菜单失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除菜单
     * @param id 菜单ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public BaseResponse<Boolean> delete(@PathVariable String id) {
        try {
            permissionService.deletePermission(id);
            boolean success = permissionService.removeById(id);
            return BaseResponse.ok(success);
        } catch (Exception e) {
            log.error("删除菜单失败", e);
            return BaseResponse.serviceError("删除菜单失败: " + e.getMessage());
        }
    }

    /**
     * 查询角色授权
     *
     * @return
     */
    @RequestMapping(value = "/queryRolePermission", method = RequestMethod.GET)
    public BaseResponse<List<String>> queryRolePermission(@RequestParam(name = "roleId") String roleId) {
        try {
            List<RolePermission> list = sysRolePermissionService.list(new QueryWrapper<RolePermission>().lambda().eq(RolePermission::getRoleId, roleId));
            return BaseResponse.ok(list.stream().map(sysRolePermission -> String.valueOf(sysRolePermission.getPermissionId())).collect(Collectors.toList()));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return BaseResponse.serviceError("查询角色授权失败");
        }
    }

    /**
     * 获取菜单详情
     * @param id 菜单ID
     * @return 菜单详情
     */
    @GetMapping("/{id}")
    public BaseResponse<Permission> getById(@PathVariable String id) {
        try {
            Permission permission = permissionService.getById(id);
            if (permission == null) {
                return BaseResponse.badRequest("菜单不存在");
            }
            return BaseResponse.ok(permission);
        } catch (Exception e) {
            log.error("获取菜单详情失败", e);
            return BaseResponse.serviceError("获取菜单详情失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询菜单列表
     * 请求参数: column=createTime&order=desc&name=%E7%A7%9F%E6%88%B7%E7%AE%A1%E7%90%86&_t=1740661119180
     * @return 分页结果
     */
    @GetMapping("/list")
    public BaseResponse<List<SysPermissionTree>> list(Permission sysPermission, HttpServletRequest req) {
        long start = System.currentTimeMillis();
        try {
            LambdaQueryWrapper<Permission> query = new LambdaQueryWrapper<Permission>();
            query.eq(Permission::getDelFlag, CommonConstant.DEL_FLAG_0);
            query.orderByAsc(Permission::getSortNo);

            //支持通过菜单名字，模糊查询
            if(StrUtil.isNotEmpty(sysPermission.getName())){
                query.like(Permission::getName, sysPermission.getName());
            }
            List<Permission> list = permissionService.list(query);
            List<SysPermissionTree> treeList = new ArrayList<>();

            //如果有菜单名查询条件，则平铺数据 不做上下级
            if(StrUtil.isNotEmpty(sysPermission.getName())){
                if(list!=null && !list.isEmpty()){
                    treeList = list.stream().map(e -> {
                        e.setIsLeaf(1);
                        return new SysPermissionTree(e);
                    }).collect(Collectors.toList());
                }
            }else{
                permissionService.buildTreeList(treeList, list, null);
            }
            log.info("======获取全部菜单数据=====耗时: {}" , (System.currentTimeMillis() - start) + "毫秒");
            return BaseResponse.ok(treeList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return BaseResponse.serviceError("查询菜单失败: " + e.getMessage());
        }
    }

    /**
     * 保存角色授权
     *
     * @return
     */
    @RequestMapping(value = "/saveRolePermission", method = RequestMethod.POST)
    public BaseResponse<String> saveRolePermission(@RequestBody JSONObject json) {
        long start = System.currentTimeMillis();
        try {
            String roleId = json.getStr("roleId");
            String permissionIds = json.getStr("permissionIds");
            String lastPermissionIds = json.getStr("lastpermissionIds");
            this.sysRolePermissionService.saveRolePermission(roleId, permissionIds, lastPermissionIds);
            log.info("======角色授权成功=====耗时:" + (System.currentTimeMillis() - start) + "毫秒");
            return BaseResponse.ok("授权成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return BaseResponse.serviceError("授权失败");
        }
    }
} 