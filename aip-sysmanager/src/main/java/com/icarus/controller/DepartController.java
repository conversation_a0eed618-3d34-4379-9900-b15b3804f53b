package com.icarus.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.common.rest.BaseResponse;
import com.icarus.dto.SysDepartTreeModel;
import com.icarus.entity.Depart;
import com.icarus.service.DepartService;
import com.icarus.utils.AuthUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 部门管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/aip/depart")
@RequiredArgsConstructor
public class DepartController {
    
    private final DepartService departService;
    
    /**
     * 创建部门
     * @param depart 部门信息
     * @return 创建后的部门信息
     */
    @PostMapping("/create")
    public BaseResponse<Depart> create(@RequestBody Depart depart) {
        try {
            if (depart.getDepartName() == null) {
                return BaseResponse.badRequest("部门名称不能为空");
            }
            departService.saveDepartData(depart, AuthUtil.getUsername());
            return BaseResponse.ok(depart);
        } catch (Exception e) {
            log.error("创建部门失败", e);
            return BaseResponse.serviceError("创建部门失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新部门
     * @param depart 部门信息
     * @return 更新后的部门信息
     */
    @PostMapping("/update")
    public BaseResponse<Depart> update(@RequestBody Depart depart) {
        try {
            if (depart.getId() == null) {
                return BaseResponse.badRequest("部门ID不能为空");
            }
            boolean success = departService.updateDepartInfo(depart);
            return success ? BaseResponse.ok(depart) : BaseResponse.badRequest("更新失败");
        } catch (Exception e) {
            log.error("更新部门失败", e);
            return BaseResponse.serviceError("更新部门失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除部门
     * @param id 部门ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public BaseResponse<Boolean> delete(@PathVariable String id) {
        try {
            departService.deleteDepart(id);
            return BaseResponse.ok(true);
        } catch (Exception e) {
            log.error("删除部门失败", e);
            return BaseResponse.serviceError("删除部门失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取部门详情
     * @param id 部门ID
     * @return 部门详情
     */
    @GetMapping("/{id}")
    public BaseResponse<Depart> getById(@PathVariable String id) {
        try {
            Depart depart = departService.getById(id);
            if (depart == null) {
                return BaseResponse.badRequest("部门不存在");
            }
            return BaseResponse.ok(depart);
        } catch (Exception e) {
            log.error("获取部门详情失败", e);
            return BaseResponse.serviceError("获取部门详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取部门树
     * @return 部门树结构
     */
    @GetMapping("/tree")
    public BaseResponse<List<Depart>> getDepartTree() {
        try {
            List<Depart> tree = departService.getDepartTree();
            return BaseResponse.ok(tree);
        } catch (Exception e) {
            log.error("获取部门树失败", e);
            return BaseResponse.serviceError("获取部门树失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取子部门
     * @param parentId 父部门ID
     * @return 子部门列表
     */
    @GetMapping("/children/{parentId}")
    public BaseResponse<List<Depart>> getChildren(@PathVariable String parentId) {
        try {
            List<Depart> children = departService.getChildren(parentId);
            return BaseResponse.ok(children);
        } catch (Exception e) {
            log.error("获取子部门失败", e);
            return BaseResponse.serviceError("获取子部门失败: " + e.getMessage());
        }
    }
    
    /**
     * 分页查询部门列表
     * @param pageNo 页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    @GetMapping("/list")
    public BaseResponse<Page<Depart>> list(
            @RequestParam(defaultValue = "1") Integer pageNo,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            Page<Depart> page = new Page<>(pageNo, pageSize);
            LambdaQueryWrapper<Depart> wrapper = new LambdaQueryWrapper<>();
            wrapper.orderByAsc(Depart::getDepartOrder);
            Page<Depart> result = departService.page(page, wrapper);
            return BaseResponse.ok(result);
        } catch (Exception e) {
            log.error("分页查询部门失败", e);
            return BaseResponse.serviceError("分页查询部门失败: " + e.getMessage());
        }
    }

    /**
     * 异步查询部门list
     * @param parentId 父节点 异步加载时传递
     * @param ids 前端回显是传递
     * @param primaryKey 主键字段（id或者orgCode）
     * @return
     */
    @RequestMapping(value = "/queryDepartTreeSync", method = RequestMethod.GET)
    public BaseResponse<List<SysDepartTreeModel>> queryDepartTreeSync(@RequestParam(name = "pid", required = false) String parentId,
                                                                @RequestParam(name = "ids", required = false) String ids,
                                                                @RequestParam(name = "primaryKey", required = false) String primaryKey) {
        try {
            List<SysDepartTreeModel> list = departService.queryTreeListByPid(parentId,ids, primaryKey);
            return BaseResponse.ok(list);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return BaseResponse.serviceError("查询部门列表失败");
        }
    }

    /**
     * <p>
     * 部门搜索功能方法,根据关键字模糊搜索相关部门
     * </p>
     *
     * @param keyWord
     * @return
     */
    @RequestMapping(value = "/searchBy", method = RequestMethod.GET)
    public BaseResponse<List<SysDepartTreeModel>> searchBy(@RequestParam(name = "keyWord", required = false) String keyWord,
                                                     @RequestParam(name = "myDeptSearch", required = false) String myDeptSearch) {
        //部门查询，myDeptSearch为1时为我的部门查询，登录用户为上级时查只查负责部门下数据
        String departIds = null;
        List<SysDepartTreeModel> treeList = this.departService.searchByKeyWord(keyWord,myDeptSearch,departIds);
        return BaseResponse.ok(treeList);
    }
} 