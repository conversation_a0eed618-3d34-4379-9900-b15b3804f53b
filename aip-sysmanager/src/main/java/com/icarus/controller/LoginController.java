package com.icarus.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.icarus.common.redis.RedisUtil;
import com.icarus.common.rest.BaseResponse;
import com.icarus.constant.CommonConstant;
import com.icarus.dto.PermissionResultDTO;
import com.icarus.dto.login.LoginDTO;
import com.icarus.entity.*;
import com.icarus.enums.LoginTypeEnum;
import com.icarus.model.SysLoginModel;
import com.icarus.service.DesignSysUserService;
import com.icarus.service.PermissionService;
import com.icarus.service.SystemParamService;
import com.icarus.service.impl.DepartServiceImpl;
import com.icarus.service.impl.DesignSysUserDepartServiceImpl;
import com.icarus.service.impl.DesignSysUserRoleServiceImpl;
import com.icarus.service.impl.RoleServiceImpl;
import com.icarus.totp.TotpUtils;
import com.icarus.utils.JsonUtils;
import com.icarus.utils.JwtUtil;
import com.icarus.utils.PasswordUtil;
import dev.samstevens.totp.exceptions.QrGenerationException;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;


/**
 * 登录控制器
 * @ClassName LoginController
 * <AUTHOR>
 * @Date 2025/2/27
 * @Version 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/aip")
@RequiredArgsConstructor
public class LoginController {

    @Value("${sysmanager.jasypt.secret}")
    private String secret;
    private final DesignSysUserService designSysUserService;
    private final DesignSysUserDepartServiceImpl designSysUserDepartService;
    private final DepartServiceImpl departService;
    private final RoleServiceImpl roleService;
    private final DesignSysUserRoleServiceImpl designSysUserRoleService;
    private final PermissionService permissionService;
    private final SystemParamService systemParamService;
    private final RedisUtil redisUtil;
    private final TotpUtils totpUtils;

    /**
     * 登录
     * @param sysLoginModel
     * @param request
     * @return
     */
    @PostMapping( "/login")
    @ResponseBody
    public BaseResponse<LoginDTO> login(@RequestBody SysLoginModel sysLoginModel, HttpServletRequest request) throws QrGenerationException {
        log.info("===========================登录操作{}===========================", sysLoginModel);
        // 生成当前请求的唯一标识
        String requestId = UUID.randomUUID().toString();
        BaseResponse response = verify(sysLoginModel, requestId);
        if(response.getCode() != HttpStatus.OK.value()) {
            return response;
        }
        Object json = redisUtil.get(CommonConstant.PREFIX_USER_TMP_LOGIN + requestId);
        if(Objects.isNull(json)){
            return BaseResponse.badRequest("系统异常，请稍候重试");
        }
        DesignSysUser sysUser = JsonUtils.jsonToObject(JsonUtils.objectToJson(json), DesignSysUser.class);

        // 这里判断是否开启双因素认证
        LambdaQueryWrapper<SystemParam> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SystemParam::getParamKey, "verificationMethod");
        SystemParam systemParam = systemParamService.getOne(wrapper);
        if(totpUtils.getAuthenticationSign() && !StringUtils.equals(systemParam.getParamValue(),"N")){
            log.info("===========================系统开启双因素认证===========================");
            // 开启双因素验证，判断是否已经绑定了双因素认证
            LoginDTO dto = new LoginDTO();
            if(StringUtils.isEmpty(sysUser.getTotpSecret()) && StringUtils.isEmpty(sysLoginModel.getTotpCode())){
                // 初次登录，需要生成二维码引导用户绑定
                return BaseResponse.ok("二维码获取成功", firstLogin(dto,sysUser));
            }else if (StringUtils.isEmpty(sysLoginModel.getTotpCode())){
                // 验证码为空，返回时间验证码页面
                dto.setType(LoginTypeEnum.TOTP_TIME_CODE_TYPE.getCode());
                return BaseResponse.ok("时间验证码页面跳转", dto);
            }else{
                // 验证时间验证码
                if(!totpUtils.verify(sysUser.getTotpSecret(),sysLoginModel.getTotpCode())){
                    return BaseResponse.badRequest("验证码错误");
                }
            }
        }
        // 验证成功获取用户信息
        BaseResponse result = userInfo(sysUser, request);
        // 登录成功删除登录失败信息
        redisUtil.del(CommonConstant.LOGIN_FAIL + sysLoginModel.getUsername());
        log.info("===========================用户[{}]登录成功===========================", sysLoginModel.getUsername());
        return result;
    }

    /**
     * 登出
     * @param request
     * @return
     */
    @PostMapping("/logout")
    @ResponseBody
    public BaseResponse logout(HttpServletRequest request) {
        String username= (String)request.getAttribute(CommonConstant.USER_NAME);
        DesignSysUser one = designSysUserService.getOne(new LambdaQueryWrapper<>(DesignSysUser.class).eq(DesignSysUser::getUsername, username));
        if(Objects.isNull(one)){
            return new BaseResponse(400, "退出登录异常");
        }
        // 清除token
        redisUtil.del(CommonConstant.PREFIX_USER_TOKEN + one.getId());
        // 如果有其他的,比如权限等缓存，需要同步清除
        log.info("=============用户：{} 退出登录============", username);
        return new BaseResponse<>(200, "登出成功", null);
    }

    /**
     * 绑定(二维码页面需要输入一次验证码，来验证绑定是否成功)
     * @param sysLoginModel
     * @return
     * @throws QrGenerationException
     */
    @PostMapping( "/bind")
    @ResponseBody
    public BaseResponse bind(@RequestBody SysLoginModel sysLoginModel) throws QrGenerationException {
        // 生成当前请求的唯一标识
        String requestId = UUID.randomUUID().toString();
        BaseResponse response = verify(sysLoginModel, requestId);
        if(response.getCode() != HttpStatus.OK.value()) {
            return response;
        }
        Object json = redisUtil.get(CommonConstant.PREFIX_USER_TMP_LOGIN + requestId);
        if(Objects.isNull(json)){
            return BaseResponse.badRequest("系统异常，请稍候重试");
        }
        DesignSysUser sysUser = JsonUtils.jsonToObject(JsonUtils.objectToJson(json), DesignSysUser.class);
        // 第一次登录需要绑定TOTP，验证一次时间验证码
        Object obj = redisUtil.get(CommonConstant.PREFIX_USER_TOTP_SECRET + sysUser.getId());
        if(Objects.isNull(obj)){
            return BaseResponse.badRequest("二维码已失效，请重新请求");
        }
        boolean verify = totpUtils.verify((String)obj, sysLoginModel.getTotpCode());
        if(verify){
            designSysUserService.update(Wrappers.lambdaUpdate(DesignSysUser.class)
                    .eq(DesignSysUser::getId, sysUser.getId())
                    .set(DesignSysUser::getUpdateBy, sysUser.getUsername())
                    .set(DesignSysUser::getUpdateTime, LocalDateTime.now())
                    .set(DesignSysUser::getTotpSecret, redisUtil.get(CommonConstant.PREFIX_USER_TOTP_SECRET + sysUser.getId()))
                    .set(DesignSysUser::getQrCode, redisUtil.get(CommonConstant.PREFIX_USER_TOTP_QR_CODE + sysUser.getId()))
            );
            log.info("用户[{}]绑定TOTP更新完成，{}", sysUser.getUsername());
        }else{
            return BaseResponse.badRequest("验证码错误，绑定失败");
        }
        return BaseResponse.ok("绑定成功", null);
    }


    private BaseResponse verify(SysLoginModel sysLoginModel, String requestId) {
        String username = sysLoginModel.getUsername();
        String password = sysLoginModel.getPassword();
        if(isLoginFailOvertimes(username)){
            return BaseResponse.badRequest("该用户登录失败次数过多，锁定10分钟，请10分钟后再次登录！");
        }
        // 校验用户是否存在且有效
        LambdaQueryWrapper<DesignSysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DesignSysUser::getUsername,username);
        DesignSysUser sysUser = designSysUserService.getOne(queryWrapper);
        BaseResponse status = designSysUserService.checkUserIsEffective(sysUser);
        if(status.getCode() != HttpStatus.OK.value()) {
            return status;
        }
        // 校验用户名或密码是否正确
        String userPassword = PasswordUtil.encrypt(password, CommonConstant.ENCRYPT, sysUser.getSalt());
        String sysPassword = sysUser.getPassword();
        if (!sysPassword.equals(userPassword)) {
            addLoginFailOvertimes(username);
            return BaseResponse.badRequest("用户名或密码错误");
        }
        // 设置一个短期的缓存，以便本次请求后续获取用户信息
        redisUtil.set(CommonConstant.PREFIX_USER_TMP_LOGIN + requestId, sysUser, 1);
        return BaseResponse.ok(null);
    }

    /**
     * 验证当前用户登录失败次数
     * @param username
     * @return
     */
    private boolean isLoginFailOvertimes(String username){
        String key = CommonConstant.LOGIN_FAIL + username;
        Object failTime = redisUtil.get(key);
        if(failTime!=null){
            Integer val = Integer.parseInt(failTime.toString());
            if(val >= 5){
                return true;
            }
        }
        return false;
    }

    /**
     * 记录登录失败次数
     * @param username
     */
    private void addLoginFailOvertimes(String username){
        String key = CommonConstant.LOGIN_FAIL + username;
        Object failTime = redisUtil.get(key);
        Integer val = 0;
        if(failTime!=null){
            val = Integer.parseInt(failTime.toString());
        }
        // 10分钟，一分钟为60s
        redisUtil.set(key, ++val, 600);
    }

    /**
     * 用户信息
     *
     * @param sysUser
     * @param
     * @return
     */
    private BaseResponse userInfo(DesignSysUser sysUser, HttpServletRequest request) {
        String username = sysUser.getUsername();
        LoginDTO loginDTO = new LoginDTO();

        // 生成token
        String token = JwtUtil.sign(username, sysUser.getId(), secret);
        // 设置token缓存有效时间
        String tokenKey = CommonConstant.PREFIX_USER_TOKEN + sysUser.getId();
        redisUtil.set(tokenKey, token);
        redisUtil.expire(tokenKey, JwtUtil.EXPIRE_TIME / 1000);
        loginDTO.setToken(CommonConstant.TOKEN_PREFIX + token);

        // 获取角色集合
        List<String> roleIds = designSysUserRoleService.lambdaQuery().eq(DesignSysUserRole::getUserId, sysUser.getId()).list()
                .stream().map(DesignSysUserRole::getRoleId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(roleIds)){
            return BaseResponse.badRequest("用户未分配角色");
        }

        Map<String, List<PermissionResultDTO>> myPermissionConfig =
                permissionService.getMyPermissionConfig(sysUser.getId());
        loginDTO.setMenuTree(myPermissionConfig);
        List<Role> roles = roleService.lambdaQuery().in(Role::getId, roleIds).list();
        sysUser.setRoleIds(roles.stream()
                .map(Role::getId)
                .collect(Collectors.joining(",")));
        sysUser.setRoleNames(roles.stream()
                .map(Role::getRoleName)
                .collect(Collectors.joining(",")));

        if (roleIds.contains(CommonConstant.IS_ADMIN)) {
            sysUser.setAdminFlag(true);
        }

        // 4.设置登录部门
        List<String> departIds = designSysUserDepartService.lambdaQuery().eq(DesignSysUserDepart::getUserId, sysUser.getId()).list()
                .stream().map(DesignSysUserDepart::getDepId).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(departIds)){
            List<Depart> departs = departService.lambdaQuery().in(Depart::getId, departIds).list();
            sysUser.setDepartIds(departs.stream()
                    .map(Depart::getId)
                    .collect(Collectors.joining(",")));
            sysUser.setDepartNames(departs.stream()
                    .map(Depart::getDepartName)
                    .collect(Collectors.joining(",")));
        }
        // 3.设置登录用户信息
        loginDTO.setUser(sysUser);
        loginDTO.setType(LoginTypeEnum.LOGIN_TYPE.getCode());
        return new BaseResponse<>(200,"登录成功", loginDTO);
    }

    /**
     * 初次登录绑定二维码
     * @return
     * @throws QrGenerationException
     */
    public LoginDTO firstLogin(LoginDTO dto, DesignSysUser sysUser) throws QrGenerationException {
        String secretKey = totpUtils.createSecretKey();
        String qrCode = totpUtils.createQrCode(sysUser.getUsername(), secretKey);
        dto.setQrCode(qrCode);
        dto.setTotpSecret(secretKey);
        dto.setType(LoginTypeEnum.TOTP_QR_CODE_TYPE.getCode());
        // 设置二维码绑定有效期
        redisUtil.set(CommonConstant.PREFIX_USER_TOTP_QR_CODE + sysUser.getId(), qrCode, JwtUtil.EXPIRE_TIME / 2 / 1000);
        redisUtil.set(CommonConstant.PREFIX_USER_TOTP_SECRET + sysUser.getId(), secretKey, JwtUtil.EXPIRE_TIME / 2 / 1000);
        log.info("用户[{}]初次登录，生成密钥和二维码完成", sysUser.getUsername());
        return dto;
    }

}
