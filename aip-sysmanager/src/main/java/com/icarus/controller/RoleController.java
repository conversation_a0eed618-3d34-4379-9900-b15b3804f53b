package com.icarus.controller;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.common.rest.BaseResponse;
import com.icarus.config.MybatisPlusConfig;
import com.icarus.constant.CommonConstant;
import com.icarus.dto.PageDTO;
import com.icarus.dto.TreeModel;
import com.icarus.entity.Permission;
import com.icarus.entity.Role;
import com.icarus.service.PermissionService;
import com.icarus.service.RoleService;
import com.icarus.dto.RoleQueryDTO;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 角色管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/aip/role")
@RequiredArgsConstructor
public class RoleController {
    
    private final RoleService roleService;
    private final PermissionService sysPermissionService;
    
    /**
     * 创建角色
     * @param role 角色信息
     * @return 创建后的角色信息
     */
    @PostMapping("/create")
    public BaseResponse<Role> create(@RequestBody Role role) {
        try {
            //开启多租户隔离,角色id自动生成10位
            //update-begin---author:wangshuai---date:2024-05-23---for:【TV360X-42】角色新增时设置的编码，保存后不一致---
            if(MybatisPlusConfig.OPEN_SYSTEM_TENANT_CONTROL && StrUtil.isEmpty(role.getRoleCode())){
                //update-end---author:wangshuai---date:2024-05-23---for:【TV360X-42】角色新增时设置的编码，保存后不一致---
                role.setRoleCode(RandomUtil.randomString(10));
            }
            role.setCreateTime(LocalDateTime.now());
            roleService.save(role);
            return BaseResponse.ok(role);
        } catch (Exception e) {
            log.error("创建角色失败", e);
            return BaseResponse.serviceError("创建角色失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新角色
     * @param role 角色信息
     * @return 更新后的角色信息
     */
    @PostMapping("/update")
    public BaseResponse<Role> update(@RequestBody Role role) {
        try {
            if (role.getId() == null) {
                return BaseResponse.badRequest("角色ID不能为空");
            }
            boolean success = roleService.updateRole(role);
            return success ? BaseResponse.ok(role) : BaseResponse.badRequest("更新失败");
        } catch (Exception e) {
            log.error("更新角色失败", e);
            return BaseResponse.serviceError("更新角色失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除角色
     * @param id 角色ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public BaseResponse<Boolean> delete(@PathVariable String id) {
        try {
            boolean success = roleService.deleteRole(id);
            return BaseResponse.ok(success);
        } catch (Exception e) {
            log.error("删除角色失败", e);
            return BaseResponse.serviceError("删除角色失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取角色详情
     * @param id 角色ID
     * @return 角色信息
     */
    @GetMapping("/{id}")
    public BaseResponse<Role> getById(@PathVariable String id) {
        try {
            Role role = roleService.getById(id);
            if (role == null) {
                return BaseResponse.badRequest("角色不存在");
            }
            return BaseResponse.ok(role);
        } catch (Exception e) {
            log.error("获取角色详情失败", e);
            return BaseResponse.serviceError("获取角色详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 分页查询角色列表
     * @param pageNo 页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    @GetMapping("/list")
    public BaseResponse<PageDTO<Role>> list(
            Role role,
            @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
            @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
            HttpServletRequest req) {
        try {
            Page<Role> page = new Page<>(pageNo, pageSize);
            Page<Role> pageList = roleService.listAllSysRole(page, role);
            PageDTO<Role> pageDTO = new PageDTO<>();
            pageDTO.setRecords(pageList.getRecords());
            pageDTO.setTotal(pageList.getTotal());
            pageDTO.setPageSize(pageSize);
            pageDTO.setCurrent(pageNo);
            return BaseResponse.ok(pageDTO);
        } catch (Exception e) {
            log.error("分页查询角色失败", e);
            return BaseResponse.serviceError("分页查询角色失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询角色
     * @param queryDTO 查询参数
     * @return 角色列表
     */
    @GetMapping("/query")
    public BaseResponse<List<Role>> query(RoleQueryDTO queryDTO) {
        try {
            List<Role> roles = roleService.queryRoles(queryDTO);
            return BaseResponse.ok(roles);
        } catch (Exception e) {
            log.error("查询角色失败", e);
            return BaseResponse.serviceError("查询角色失败: " + e.getMessage());
        }
    }

    /**
     * 用户角色授权功能，查询菜单权限树
     * @param request
     * @return
     */
    @RequestMapping(value = "/queryTreeList", method = RequestMethod.GET)
    public BaseResponse<Map<String,Object>> queryTreeList(HttpServletRequest request) {
        //全部权限ids
        List<String> ids = new ArrayList<>();
        try {
            LambdaQueryWrapper<Permission> query = new LambdaQueryWrapper<Permission>();
            query.eq(Permission::getDelFlag, CommonConstant.DEL_FLAG_0);
            query.orderByAsc(Permission::getSortNo);
            List<Permission> list = sysPermissionService.list(query);
            for(Permission sysPer : list) {
                ids.add(sysPer.getId());
            }
            List<TreeModel> treeList = new ArrayList<>();
            getTreeModelList(treeList, list, null);
            Map<String,Object> resMap = new HashMap<>(5);
            //全部树节点数据
            resMap.put("treeList", treeList);
            //全部树ids
            resMap.put("ids", ids);
            return BaseResponse.ok(resMap);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return BaseResponse.serviceError("用户角色授权功能失败");
        }
    }

    private void getTreeModelList(List<TreeModel> treeList, List<Permission> metaList, TreeModel temp) {
        for (Permission permission : metaList) {
            String tempPid = permission.getParentId();
            TreeModel tree = new TreeModel(permission.getId(), tempPid, permission.getName(),permission.getRuleFlag(), permission.getIsLeaf());
            if(temp==null && StrUtil.isEmpty(tempPid)) {
                treeList.add(tree);
                if(tree.getIsLeaf() != 1) {
                    getTreeModelList(treeList, metaList, tree);
                }
            }else if(temp!=null && tempPid!=null && tempPid.equals(temp.getKey())){
                temp.getChildren().add(tree);
                if(tree.getIsLeaf() != 1) {
                    getTreeModelList(treeList, metaList, tree);
                }
            }

        }
    }
} 