package com.icarus.service;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.icarus.common.rest.BaseResponse;
import com.icarus.dto.PageDTO;
import com.icarus.dto.login.LoginDTO;
import com.icarus.dto.user.UserDTO;
import com.icarus.entity.DesignSysUser;
import com.icarus.vo.ChangePwdVO;
import com.icarus.vo.RegisterVO;
import com.icarus.vo.UserUpdateVO;
import com.icarus.vo.UserVO;
import jakarta.servlet.http.HttpServletRequest;

/**
 * @ClassName DesignSysUserService
 * <AUTHOR>
 * @Date 2025/2/27
 * @Version 1.0
 **/
public interface DesignSysUserService extends IService<DesignSysUser> {

    /**
     * 根据id查询用户
     * @param id
     * @return
     */
    DesignSysUser getUserById(String id);

    /**
     * 注册
     * @param user 用户
     * @param request
     */
    void register(RegisterVO user, HttpServletRequest request);

    /**
     * 保存用户
     * @param user 用户
     * @param request
     */
    void saveUser(UserVO user, HttpServletRequest request);

    /**
     * 编辑用户
     * @param user 用户
     * @param request
     */
    void editUser(DesignSysUser user, HttpServletRequest request);

    /**
     * 校验用户是否有效
     * @param sysUser
     * @return
     */
    BaseResponse checkUserIsEffective(DesignSysUser sysUser);

    /**
     * 设置登录租户
     * @param sysUser
     * @param loginDTO
     * @param username
     * @return
     */
    BaseResponse setLoginTenant(DesignSysUser sysUser, LoginDTO loginDTO, String username);

    /**
     * 用户分页查询
     * @param page
     * @param wrapper
     * @return
     */
    PageDTO<UserDTO> userPage(Page<DesignSysUser> page, LambdaQueryWrapper<DesignSysUser> wrapper);

    /**
     * 修改密码
     * @param vo
     * @param request
     */
    void changePwd(ChangePwdVO vo, HttpServletRequest request);

    /**
     * 启用/禁用
     * @param userId
     * @param status
     * @param request
     * @return
     */
    void enable(String userId,Integer status, HttpServletRequest request);

    /**
     * 删除用户
     * @param id
     */
    void removeById(String id);

    /**
     * 角色、权限发生变化时，调用
     * @param roleId
     */
    void permissionChange(String roleId);

}
