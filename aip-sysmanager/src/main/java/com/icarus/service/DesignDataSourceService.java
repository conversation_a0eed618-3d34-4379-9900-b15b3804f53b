package com.icarus.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.icarus.dto.DataSourceDTO;
import com.icarus.dto.DbInfoDTO;
import com.icarus.dto.PageDTO;
import com.icarus.dto.TableInfoDTO;
import com.icarus.entity.DesignDataSource;
import com.icarus.vo.DataSourceVO;
import com.icarus.vo.DbTypeVO;
import jakarta.servlet.http.HttpServletRequest;

import java.util.List;

/**
 * 数据源服务接口
 */
public interface DesignDataSourceService extends IService<DesignDataSource> {
    /**
     * 保存数据源
     * @param dataSourceVO 数据源VO
     * @param request HTTP请求
     * @return 保存后的数据源
     */
    DesignDataSource saveDataSource(DataSourceVO dataSourceVO, HttpServletRequest request);

    /**
     * 更新数据源
     * @param dataSourceVO 数据源VO
     * @param request HTTP请求
     * @return 更新后的数据源
     */
    DesignDataSource updateDataSource(DataSourceVO dataSourceVO, HttpServletRequest request);

    /**
     * 测试数据库连接
     * @param dataSourceVO 数据源VO
     * @return 是否连接成功
     */
    boolean testConnection(DataSourceVO dataSourceVO);

    /**
     * 分页查询数据源
     * @param page 分页参数
     * @param code 数据源编码
     * @param name 数据源名称
     * @param dbType 数据库类型
     * @return 分页结果
     */
    PageDTO<DataSourceDTO> pageDataSource(Page<DesignDataSource> page, String code, String name, String dbType);

    /**
     * 根据ID获取数据源
     * @param id 数据源ID
     * @return 数据源DTO
     */
    DataSourceDTO getDataSourceById(String id);

    /**
     * 同步数据库表信息
     * @param dataSourceId 数据源ID
     * @param request HTTP请求
     */
    void syncDatabaseInfo(String dataSourceId, HttpServletRequest request);

    /**
     * 获取数据源下所有表信息
     * @param dataSourceCode 数据源编码
     * @return 表信息列表（包含字段信息）
     */
    List<TableInfoDTO> getTablesByDataSourceCode(String dataSourceCode);

    /**
     * 获取所有数据库类型
     * @return 数据库类型列表
     */
    List<DbTypeVO> listDbTypes();
} 