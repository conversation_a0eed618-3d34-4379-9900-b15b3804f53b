package com.icarus.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.icarus.dto.SysDepartTreeModel;
import com.icarus.entity.Depart;
import java.util.List;

public interface DepartService extends IService<Depart> {
    
    /**
     * 获取部门树结构
     * @return 部门树
     */
    List<Depart> getDepartTree();
    
    /**
     * 获取子部门
     * @param parentId 父部门ID
     * @return 子部门列表
     */
    List<Depart> getChildren(String parentId);
    
    /**
     * 更新部门信息
     * @param depart 部门信息
     * @return 是否更新成功
     */
    boolean updateDepartInfo(Depart depart);

    List<SysDepartTreeModel> queryTreeListByPid(String parentId, String ids, String primaryKey);

    void saveDepartData(Depart depart, String username);

    List<SysDepartTreeModel> searchByKeyWord(String keyWord, String myDeptSearch, String departIds);

    void deleteDepart(String id);

    Depart getDepartById(String id);
}