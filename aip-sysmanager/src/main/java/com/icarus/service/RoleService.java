package com.icarus.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.icarus.entity.Role;
import com.icarus.dto.RoleQueryDTO;
import java.util.List;

/**
 * 角色服务接口
 */
public interface RoleService extends IService<Role> {
    
    /**
     * 创建角色
     * @param role 角色信息
     * @return 是否创建成功
     */
    boolean createRole(Role role);
    
    /**
     * 更新角色
     * @param role 角色信息
     * @return 是否更新成功
     */
    boolean updateRole(Role role);
    
    /**
     * 删除角色
     * @param id 角色ID
     * @return 是否删除成功
     */
    boolean deleteRole(String id);
    
    /**
     * 查询角色
     * @param queryDTO 查询条件
     * @return 角色列表
     */
    List<Role> queryRoles(RoleQueryDTO queryDTO);

    Page<Role> listAllSysRole(Page<Role> page, Role role);
}