package com.icarus.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.icarus.entity.DepartRolePermission;
import java.util.List;

/**
 * 部门角色权限服务接口
 */
public interface DepartRolePermissionService extends IService<DepartRolePermission> {
    
    /**
     * 保存部门角色权限关系
     * @param departId 部门ID
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     * @param operateIp 操作IP
     * @return 是否保存成功
     */
    boolean saveDepartRolePermissions(String departId, String roleId, List<String> permissionIds, String operateIp);
    
    /**
     * 获取部门角色的权限ID列表
     * @param departId 部门ID
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    List<String> getPermissionIds(String departId, String roleId);
    
    /**
     * 删除部门的所有权限关系
     * @param departId 部门ID
     * @return 是否删除成功
     */
    boolean deleteByDepartId(String departId);
    
    /**
     * 删除角色的所有权限关系
     * @param roleId 角色ID
     * @return 是否删除成功
     */
    boolean deleteByRoleId(String roleId);
    
    /**
     * 删除某个权限关联的所有关系
     * @param permissionId 权限ID
     * @return 是否删除成功
     */
    boolean deleteByPermissionId(String permissionId);
    
    /**
     * 删除部门角色的所有权限关系
     * @param departId 部门ID
     * @param roleId 角色ID
     * @return 是否删除成功
     */
    boolean deleteByDepartIdAndRoleId(String departId, String roleId);
} 