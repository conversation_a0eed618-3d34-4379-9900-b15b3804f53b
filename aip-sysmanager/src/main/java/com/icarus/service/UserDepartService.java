package com.icarus.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.icarus.entity.UserDepart;

import java.util.List;

/**
 * 用户部门关系表 服务接口
 */
public interface UserDepartService extends IService<UserDepart> {
    
    /**
     * 添加用户与部门关系
     * @param userId 用户id
     * @param depIds 部门id列表
     */
    void addUserDepartRelation(String userId, List<String> depIds);
    
    /**
     * 更新用户与部门关系
     * @param userId 用户id
     * @param depIds 部门id列表
     */
    void updateUserDepartRelation(String userId, List<String> depIds);
    
    /**
     * 删除用户与部门关系
     * @param userId 用户id
     */
    void deleteUserDepartRelation(String userId);
    
    /**
     * 通过用户id查询部门id列表
     * @param userId 用户id
     * @return 部门id列表
     */
    List<String> getDepIdsByUserId(String userId);
    
    /**
     * 通过部门id查询用户id列表
     * @param depId 部门id
     * @return 用户id列表
     */
    List<String> getUserIdsByDepId(String depId);
} 