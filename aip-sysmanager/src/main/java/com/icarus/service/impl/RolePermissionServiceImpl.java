package com.icarus.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.entity.RolePermission;
import com.icarus.mapper.RolePermissionMapper;
import com.icarus.service.RolePermissionService;
import com.icarus.utils.IpUtils;
import com.icarus.utils.SpringContextHolder;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 角色权限服务实现类
 */
@Service
public class RolePermissionServiceImpl extends ServiceImpl<RolePermissionMapper, RolePermission> implements RolePermissionService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveRolePermissions(String roleId, List<String> permissionIds, String operateIp) {
        try {
            // 先删除原有的权限关系
            this.deleteByRoleId(roleId);

            // 批量保存新的权限关系
            List<RolePermission> rolePermissions = new ArrayList<>();
            for (String permissionId : permissionIds) {
                RolePermission rp = new RolePermission();
                rp.setRoleId(roleId);
                rp.setPermissionId(permissionId);
                rp.setOperateIp(operateIp);
                rp.setOperateDate(LocalDateTime.now());
                rolePermissions.add(rp);
            }

            return this.saveBatch(rolePermissions);
        } catch (Exception e) {
            log.error("保存角色权限关系失败", e);
            return false;
        }
    }

    @Override
    public List<String> getPermissionIdsByRoleId(String roleId) {
        return baseMapper.getPermissionIdsByRoleId(roleId);
    }

    @Override
    public List<String> getPermissionIdsByRoleId(List<String> roleIds) {
        LambdaQueryWrapper<RolePermission> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(RolePermission::getRoleId, roleIds);
        List<RolePermission> rolePermissions = this.list(wrapper);
        if (CollUtil.isEmpty(rolePermissions)) {
            return Collections.emptyList();
        }
        return rolePermissions.stream().map(RolePermission::getPermissionId).distinct().collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByRoleId(String roleId) {
        return baseMapper.deleteByRoleId(roleId) >= 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByPermissionId(String permissionId) {
        return baseMapper.deleteByPermissionId(permissionId) >= 0;
    }

    @Override
    public void saveRolePermission(String roleId, String permissionIds, String lastPermissionIds) {
        String ip = "";
        try {
            //获取request
            HttpServletRequest request = SpringContextHolder.getHttpServletRequest();
            //获取IP地址
            ip = IpUtils.getIpAddr(request);
        } catch (Exception e) {
            ip = "127.0.0.1";
        }
        List<String> add = getDiff(lastPermissionIds,permissionIds);
        if(add!=null && !add.isEmpty()) {
            List<RolePermission> list = new ArrayList<RolePermission>();
            for (String p : add) {
                if(StrUtil.isNotEmpty(p)) {
                    RolePermission rolepms = new RolePermission(roleId, p);
                    rolepms.setOperateDate(LocalDateTime.now());
                    rolepms.setOperateIp(ip);
                    list.add(rolepms);
                }
            }
            this.saveBatch(list);
        }

        List<String> delete = getDiff(permissionIds,lastPermissionIds);
        if(delete!=null && !delete.isEmpty()) {
            for (String permissionId : delete) {
                this.remove(new QueryWrapper<RolePermission>().lambda().eq(RolePermission::getRoleId, roleId)
                        .eq(RolePermission::getPermissionId, permissionId));
            }
        }
    }

    /**
     * 从diff中找出main中没有的元素
     * @param main
     * @param diff
     * @return
     */
    private List<String> getDiff(String main,String diff){
        if(StrUtil.isEmpty(diff)) {
            return null;
        }
        if(StrUtil.isEmpty(main)) {
            return Arrays.asList(diff.split(","));
        }

        String[] mainArr = main.split(",");
        String[] diffArr = diff.split(",");
        Map<String, Integer> map = new HashMap(5);
        for (String string : mainArr) {
            map.put(string, 1);
        }
        List<String> res = new ArrayList<String>();
        for (String key : diffArr) {
            if(StrUtil.isNotEmpty(key) && !map.containsKey(key)) {
                res.add(key);
            }
        }
        return res;
    }
} 