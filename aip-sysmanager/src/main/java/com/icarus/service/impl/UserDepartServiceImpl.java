package com.icarus.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.entity.UserDepart;
import com.icarus.mapper.UserDepartMapper;
import com.icarus.service.UserDepartService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户部门关系表 服务实现类
 */
@Service
public class UserDepartServiceImpl extends ServiceImpl<UserDepartMapper, UserDepart> implements UserDepartService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addUserDepartRelation(String userId, List<String> depIds) {
        List<UserDepart> userDeparts = depIds.stream()
                .map(depId -> new UserDepart(userId, depId))
                .collect(Collectors.toList());
        this.saveBatch(userDeparts);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserDepartRelation(String userId, List<String> depIds) {
        // 先删除原有关系
        this.deleteUserDepartRelation(userId);
        // 再添加新的关系
        this.addUserDepartRelation(userId, depIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteUserDepartRelation(String userId) {
        LambdaQueryWrapper<UserDepart> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserDepart::getUserId, userId);
        this.remove(wrapper);
    }

    @Override
    public List<String> getDepIdsByUserId(String userId) {
        return baseMapper.getDepIdsByUserId(userId);
    }

    @Override
    public List<String> getUserIdsByDepId(String depId) {
        return baseMapper.getUserIdsByDepId(depId);
    }
} 