package com.icarus.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.dto.DataSourceDTO;
import com.icarus.dto.DbInfoDTO;
import com.icarus.dto.PageDTO;
import com.icarus.dto.TableInfoDTO;
import com.icarus.entity.DesignDataSource;
import com.icarus.entity.DesignDbInfo;
import com.icarus.enums.DbTypeEnum;
import com.icarus.mapper.DesignDataSourceMapper;
import com.icarus.mapper.DesignDbInfoMapper;
import com.icarus.service.DesignDataSourceService;
import com.icarus.utils.AuthUtil;
import com.icarus.vo.DataSourceVO;
import com.icarus.vo.DbTypeVO;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.*;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 数据源服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DesignDataSourceServiceImpl extends ServiceImpl<DesignDataSourceMapper, DesignDataSource> implements DesignDataSourceService {

    private final DesignDbInfoMapper designDbInfoMapper;

    /**
     * 保存数据源
     * @param dataSourceVO 数据源VO
     * @param request HTTP请求
     * @return 保存后的数据源
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DesignDataSource saveDataSource(DataSourceVO dataSourceVO, HttpServletRequest request) {
        // 检查数据源编码是否已存在
        LambdaQueryWrapper<DesignDataSource> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DesignDataSource::getCode, dataSourceVO.getCode());
        if (this.count(wrapper) > 0) {
            throw new RuntimeException("数据源编码已存在");
        }

        // 设置驱动类
        dataSourceVO.setDbDriver(DbTypeEnum.getDriverClass(dataSourceVO.getDbType()));

        // 测试数据库连接
        if (!testConnection(dataSourceVO)) {
            throw new RuntimeException("数据库连接测试失败");
        }

        DesignDataSource dataSource = new DesignDataSource();
        BeanUtils.copyProperties(dataSourceVO, dataSource);
        
        // 设置创建信息
        String username = request.getHeader("username");
        dataSource.setCreateBy(username);
        dataSource.setCreateTime(LocalDateTime.now());
        
        this.save(dataSource);
        return dataSource;
    }

    /**
     * 更新数据源
     * @param dataSourceVO 数据源VO
     * @param request HTTP请求
     * @return 更新后的数据源
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DesignDataSource updateDataSource(DataSourceVO dataSourceVO, HttpServletRequest request) {
        // 检查数据源是否存在
        DesignDataSource dataSource = this.getById(dataSourceVO.getId());
        if (dataSource == null) {
            throw new RuntimeException("数据源不存在");
        }

        // 检查编码是否已被其他数据源使用
        if (!dataSource.getCode().equals(dataSourceVO.getCode())) {
            LambdaQueryWrapper<DesignDataSource> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DesignDataSource::getCode, dataSourceVO.getCode());
            if (this.count(wrapper) > 0) {
                throw new RuntimeException("数据源编码已存在");
            }
        }

        // 设置驱动类
        dataSourceVO.setDbDriver(DbTypeEnum.getDriverClass(dataSourceVO.getDbType()));

        // 测试数据库连接
        if (!testConnection(dataSourceVO)) {
            throw new RuntimeException("数据库连接测试失败");
        }

        BeanUtils.copyProperties(dataSourceVO, dataSource);
        
        // 设置更新信息
        String username = AuthUtil.getUsername();
        dataSource.setUpdateBy(username);
        dataSource.setUpdateTime(LocalDateTime.now());
        
        this.updateById(dataSource);
        return dataSource;
    }

    /**
     * 测试数据库连接
     * @param dataSourceVO 数据源VO
     * @return 是否连接成功
     */
    @Override
    public boolean testConnection(DataSourceVO dataSourceVO) {
        Connection conn = null;
        try {
            // 加载驱动
            Class.forName(dataSourceVO.getDbDriver());
            
            // 获取连接
            conn = DriverManager.getConnection(
                dataSourceVO.getDbUrl(), 
                dataSourceVO.getDbUsername(), 
                dataSourceVO.getDbPassword()
            );
            
            return conn != null;
        } catch (Exception e) {
            log.error("测试数据库连接失败: {}", e.getMessage());
            throw new RuntimeException("测试数据库连接失败: " + e.getMessage());
        } finally {
            if (conn != null) {
                try {
                    conn.close();
                } catch (Exception e) {
                    log.error("关闭数据库连接失败: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 分页查询数据源
     * @param page 分页参数
     * @param code 数据源编码
     * @param name 数据源名称
     * @param dbType 数据库类型
     * @return 分页结果
     */
    @Override
    public PageDTO<DataSourceDTO> pageDataSource(Page<DesignDataSource> page, String code, String name, String dbType) {
        LambdaQueryWrapper<DesignDataSource> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(code != null, DesignDataSource::getCode, code)
               .like(name != null, DesignDataSource::getName, name)
               .eq(dbType != null, DesignDataSource::getDbType, dbType)
               .orderByDesc(DesignDataSource::getCreateTime);
        
        Page<DesignDataSource> resultPage = this.page(page, wrapper);
        
        // 转换为DTO
        List<DataSourceDTO> records = new ArrayList<>();
        for (DesignDataSource dataSource : resultPage.getRecords()) {
            DataSourceDTO dto = new DataSourceDTO();
            BeanUtils.copyProperties(dataSource, dto);
            records.add(dto);
        }
        PageDTO<DataSourceDTO> pageDTO = new PageDTO<>();
        pageDTO.setTotal(page.getTotal());
        pageDTO.setRecords(records);
        return pageDTO;
    }

    /**
     * 根据ID获取数据源
     * @param id 数据源ID
     * @return 数据源DTO
     */
    @Override
    public DataSourceDTO getDataSourceById(String id) {
        DesignDataSource dataSource = this.getById(id);
        if (dataSource == null) {
            return null;
        }
        
        DataSourceDTO dto = new DataSourceDTO();
        BeanUtils.copyProperties(dataSource, dto);
        return dto;
    }

    /**
     * 同步数据库表信息
     * @param dataSourceId 数据源ID
     * @param request HTTP请求
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncDatabaseInfo(String dataSourceId, HttpServletRequest request) {
        // 获取数据源信息
        DesignDataSource dataSource = this.getById(dataSourceId);
        if (dataSource == null) {
            throw new RuntimeException("数据源不存在");
        }

        Connection conn = null;
        try {
            // 加载驱动并获取连接
            Class.forName(dataSource.getDbDriver());
            conn = DriverManager.getConnection(
                dataSource.getDbUrl(),
                dataSource.getDbUsername(),
                dataSource.getDbPassword()
            );

            // 获取数据库元数据
            DatabaseMetaData metaData = conn.getMetaData();
            
            // 删除该数据源之前的表信息
            LambdaQueryWrapper<DesignDbInfo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DesignDbInfo::getDataSourceCode, dataSource.getCode());
            designDbInfoMapper.delete(wrapper);

            // 获取所有表信息
            ResultSet tables = metaData.getTables(dataSource.getDbName(), null, "%", new String[]{"TABLE"});
            String username = AuthUtil.getUsername();
            LocalDateTime now = LocalDateTime.now();

            while (tables.next()) {
                String tableName = tables.getString("TABLE_NAME");
                String tableRemarks = tables.getString("REMARKS");

                // 获取表的所有列信息
                ResultSet columns = metaData.getColumns(dataSource.getDbName(), null, tableName, "%");
                int columnOrder = 1;
                
                while (columns.next()) {
                    DesignDbInfo dbInfo = new DesignDbInfo();
                    dbInfo.setDataSourceCode(dataSource.getCode());
                    dbInfo.setTableName(tableName);
                    dbInfo.setTableCode(tableName.toUpperCase());
                    dbInfo.setTableDescription(tableRemarks);
                    
                    // 设置列信息
                    String columnName = columns.getString("COLUMN_NAME");
                    dbInfo.setColumnName(columnName);
                    dbInfo.setColumnCode(columnName.toUpperCase());
                    dbInfo.setColumnDescription(columns.getString("REMARKS"));
                    dbInfo.setDataType(columns.getString("TYPE_NAME"));
                    dbInfo.setIsNullable(columns.getString("IS_NULLABLE").equals("YES") ? "1" : "0");
                    dbInfo.setColumnOrder(columnOrder++);

                    // 检查是否为主键
                    ResultSet primaryKeys = metaData.getPrimaryKeys(dataSource.getDbName(), null, tableName);
                    while (primaryKeys.next()) {
                        if (columnName.equals(primaryKeys.getString("COLUMN_NAME"))) {
                            dbInfo.setIsPrimary("1");
                            break;
                        }
                    }
                    primaryKeys.close();

                    // 设置创建信息
                    dbInfo.setCreateBy(username);
                    dbInfo.setCreateTime(now);
                    
                    // 保存到数据库
                    designDbInfoMapper.insert(dbInfo);
                }
                columns.close();
            }
            tables.close();

        } catch (Exception e) {
            log.error("同步数据库表信息失败: {}", e.getMessage());
            throw new RuntimeException("同步数据库表信息失败: " + e.getMessage());
        } finally {
            if (conn != null) {
                try {
                    conn.close();
                } catch (SQLException e) {
                    log.error("关闭数据库连接失败: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 获取数据源下所有表信息
     * @param dataSourceCode 数据源编码
     * @return 表信息列表（包含字段信息）
     */
    @Override
    public List<TableInfoDTO> getTablesByDataSourceCode(String dataSourceCode) {
        // 查询该数据源下的所有表信息
        LambdaQueryWrapper<DesignDbInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DesignDbInfo::getDataSourceCode, dataSourceCode)
               .orderByAsc(DesignDbInfo::getTableName)
               .orderByAsc(DesignDbInfo::getColumnOrder);

        List<DesignDbInfo> dbInfoList = designDbInfoMapper.selectList(wrapper);
        
        // 按表名分组
        Map<String, List<DesignDbInfo>> tableMap = dbInfoList.stream()
                .collect(Collectors.groupingBy(DesignDbInfo::getTableName));
        
        // 转换为层级结构
        return tableMap.entrySet().stream()
                .map(entry -> {
                    TableInfoDTO tableInfo = new TableInfoDTO();
                    // 使用第一条记录的表信息
                    DesignDbInfo firstRecord = entry.getValue().get(0);
                    tableInfo.setTableName(firstRecord.getTableName());
                    tableInfo.setTableCode(firstRecord.getTableCode());
                    tableInfo.setTableDescription(firstRecord.getTableDescription());
                    
                    // 转换字段信息
                    List<DbInfoDTO> columns = entry.getValue().stream()
                            .map(dbInfo -> {
                                DbInfoDTO dto = new DbInfoDTO();
                                BeanUtils.copyProperties(dbInfo, dto);
                                return dto;
                            })
                            .collect(Collectors.toList());
                    tableInfo.setColumns(columns);
                    
                    return tableInfo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取所有数据库类型
     * @return 数据库类型列表
     */
    @Override
    public List<DbTypeVO> listDbTypes() {
        return Stream.of(DbTypeEnum.values())
                .map(dbType -> {
                    DbTypeVO vo = new DbTypeVO();
                    vo.setCode(dbType.getCode());
                    vo.setDriverClass(dbType.getDriverClass());
                    vo.setDescription(dbType.getDescription());
                    return vo;
                })
                .collect(Collectors.toList());
    }
} 