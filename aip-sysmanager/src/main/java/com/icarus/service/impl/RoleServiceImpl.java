package com.icarus.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.entity.Role;
import com.icarus.mapper.RoleMapper;
import com.icarus.service.RoleService;
import com.icarus.dto.RoleQueryDTO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 角色服务实现类
 */
@Service
@RequiredArgsConstructor
public class RoleServiceImpl extends ServiceImpl<RoleMapper, Role> implements RoleService {
    private final RoleMapper sysRoleMapper;
    @Value("${sysmanager.default_role_code:register,all_code}")
    private String defaultRoleCode;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createRole(Role role) {
        // 检查角色编码是否已存在
        LambdaQueryWrapper<Role> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Role::getRoleCode, role.getRoleCode());
        if (this.count(wrapper) > 0) {
            throw new RuntimeException("角色编码已存在");
        }
        
        return this.save(role);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRole(Role role) {
        // 检查角色编码是否已被其他角色使用
        LambdaQueryWrapper<Role> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Role::getRoleCode, role.getRoleCode())
              .ne(Role::getId, role.getId());
        if (this.count(wrapper) > 0) {
            throw new RuntimeException("角色编码已被其他角色使用");
        }
        
        return this.updateById(role);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRole(String roleid) {
        Role byId = this.getById(roleid);
        List<String> split = StrUtil.split(defaultRoleCode, ",");
        if (split.contains(byId.getRoleCode())) {
            throw new RuntimeException("该角色无法删除");
        }
        //1.删除角色和用户关系
        sysRoleMapper.deleteRoleUserRelation(roleid);
        //2.删除角色和权限关系
        sysRoleMapper.deleteRolePermissionRelation(roleid);
        //3.删除角色
        this.removeById(roleid);
        return true;
    }

    @Override
    public List<Role> queryRoles(RoleQueryDTO queryDTO) {
        LambdaQueryWrapper<Role> wrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        if (StringUtils.isNotBlank(queryDTO.getRoleName())) {
            wrapper.like(Role::getRoleName, queryDTO.getRoleName());
        }
        if (StringUtils.isNotBlank(queryDTO.getRoleCode())) {
            wrapper.like(Role::getRoleCode, queryDTO.getRoleCode());
        }
        
        // 按创建时间降序排序
        wrapper.orderByDesc(Role::getCreateTime);
        
        return this.list(wrapper);
    }

    @Override
    public Page<Role> listAllSysRole(Page<Role> page, Role role) {
        return page.setRecords(this.getBaseMapper().listAllSysRole(page,role));
    }
} 