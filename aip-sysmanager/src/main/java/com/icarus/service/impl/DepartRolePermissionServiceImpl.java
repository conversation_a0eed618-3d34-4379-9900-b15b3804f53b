package com.icarus.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.entity.DepartRolePermission;
import com.icarus.mapper.DepartRolePermissionMapper;
import com.icarus.service.DepartRolePermissionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

/**
 * 部门角色权限服务实现类
 */
@Slf4j
@Service
public class DepartRolePermissionServiceImpl extends ServiceImpl<DepartRolePermissionMapper, DepartRolePermission> implements DepartRolePermissionService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDepartRolePermissions(String departId, String roleId, List<String> permissionIds, String operateIp) {
        try {
            // 先删除原有的权限关系
            this.deleteByDepartIdAndRoleId(departId, roleId);
            
            // 批量保存新的权限关系
            List<DepartRolePermission> permissions = new ArrayList<>();
            for (String permissionId : permissionIds) {
                DepartRolePermission drp = new DepartRolePermission();
                drp.setDepartId(departId);
                drp.setRoleId(roleId);
                drp.setPermissionId(permissionId);
                drp.setOperateIp(operateIp);
                drp.setOperateDate(LocalDateTime.now());
                permissions.add(drp);
            }
            
            return this.saveBatch(permissions);
        } catch (Exception e) {
            log.error("保存部门角色权限关系失败", e);
            return false;
        }
    }

    @Override
    public List<String> getPermissionIds(String departId, String roleId) {
        return baseMapper.getPermissionIds(departId, roleId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByDepartId(String departId) {
        return baseMapper.deleteByDepartId(departId) >= 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByRoleId(String roleId) {
        return baseMapper.deleteByRoleId(roleId) >= 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByPermissionId(String permissionId) {
        return baseMapper.deleteByPermissionId(permissionId) >= 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByDepartIdAndRoleId(String departId, String roleId) {
        return baseMapper.deleteByDepartIdAndRoleId(departId, roleId) >= 0;
    }
} 