package com.icarus.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.config.MybatisPlusConfig;
import com.icarus.config.TenantContext;
import com.icarus.constant.CommonConstant;
import com.icarus.dto.SysDepartTreeModel;
import com.icarus.entity.Depart;
import com.icarus.entity.DesignSysUser;
import com.icarus.entity.UserDepart;
import com.icarus.mapper.DepartMapper;
import com.icarus.mapper.DesignSysUserMapper;
import com.icarus.mapper.UserDepartMapper;
import com.icarus.service.DepartService;
import io.netty.util.internal.StringUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Consumer;

@Service
@RequiredArgsConstructor
public class DepartServiceImpl extends ServiceImpl<DepartMapper, Depart> implements DepartService {
    private final DesignSysUserMapper sysUserMapper;
    private final UserDepartMapper userDepartMapper;

    @Override
    public List<Depart> getDepartTree() {
        List<Depart> allDeparts = this.list();
        return buildTree(allDeparts);
    }

    @Override
    public List<Depart> getChildren(String parentId) {
        LambdaQueryWrapper<Depart> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Depart::getParentId, parentId);
        wrapper.orderByAsc(Depart::getDepartOrder);
        return this.list(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDepartInfo(Depart depart) {
        if (depart.getId() == null) {
            return false;
        }
        
        // 如果更改了父部门，需要更新机构编码
        Depart oldDepart = this.getById(depart.getId());
        if (oldDepart != null && !oldDepart.getParentId().equals(depart.getParentId())) {
            // 获取新的父部门
            Depart parent = this.getById(depart.getParentId());
            if (parent != null) {
                // 更新机构编码
                depart.setOrgCode(generateNewOrgCode(parent.getOrgCode()));
            }
        }
        
        return this.updateById(depart);
    }

    @Override
    public List<SysDepartTreeModel> queryTreeListByPid(String parentId, String ids, String primaryKey) {
        Consumer<LambdaQueryWrapper<Depart>> square = i -> {
            if (StrUtil.isNotEmpty(ids)) {
                if (CommonConstant.DEPART_KEY_ORG_CODE.equals(primaryKey)) {
                    i.in(Depart::getOrgCode, ids.split(","));
                } else {
                    i.in(Depart::getId, ids.split(","));
                }
            } else {
                if(StrUtil.isEmpty(parentId)){
                    i.and(q->q.isNull(true,Depart::getParentId).or().eq(true,Depart::getParentId,""));
                }else{
                    i.eq(true,Depart::getParentId,parentId);
                }
            }
        };
        LambdaQueryWrapper<Depart> lqw=new LambdaQueryWrapper<>();
        //------------------------------------------------------------------------------------------------
        //是否开启系统管理模块的 SASS 控制
        if(MybatisPlusConfig.OPEN_SYSTEM_TENANT_CONTROL){
            lqw.eq(Depart::getTenantId, Integer.parseInt(TenantContext.getTenant(), 0));
        }
        //------------------------------------------------------------------------------------------------
        lqw.eq(true,Depart::getDelFlag,CommonConstant.DEL_FLAG_0.toString());
        lqw.func(square);
        //update-begin---author:wangshuai ---date:20220527  for：[VUEN-1143]排序不对，vue3和2应该都有问题，应该按照升序排------------
        lqw.orderByAsc(Depart::getDepartOrder);
        //update-end---author:wangshuai ---date:20220527  for：[VUEN-1143]排序不对，vue3和2应该都有问题，应该按照升序排--------------
        List<Depart> list = list(lqw);
        //update-begin---author:wangshuai ---date:20220316  for：[JTC-119]在部门管理菜单下设置部门负责人 创建用户的时候不需要处理
        //设置用户id,让前台显示
        this.setUserIdsByDepList(list);
        //update-end---author:wangshuai ---date:20220316  for：[JTC-119]在部门管理菜单下设置部门负责人 创建用户的时候不需要处理
        List<SysDepartTreeModel> records = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            Depart depart = list.get(i);
            SysDepartTreeModel treeModel = new SysDepartTreeModel(depart);
            String id = treeModel.getId();
            List<SysDepartTreeModel> sysDepartTreeModels = queryTreeListByPid(id, null, null);
            treeModel.setChildren(sysDepartTreeModels);
            records.add(treeModel);
        }
        return records;
    }

    /**
     * saveDepartData 对应 add 保存用户在页面添加的新的部门对象数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDepartData(Depart sysDepart, String username) {
        if (sysDepart != null && username != null) {
            //update-begin---author:wangshuai ---date:20230216  for：[QQYUN-4163]给部门表加个是否有子节点------------
            if (StrUtil.isEmpty(sysDepart.getParentId())) {
                sysDepart.setParentId("");
            }else{
                //将父部门的设成不是叶子结点
                this.getBaseMapper().setMainLeaf(sysDepart.getParentId(),CommonConstant.NOT_LEAF);
            }
            //update-end---author:wangshuai ---date:20230216  for：[QQYUN-4163]给部门表加个是否有子节点------------
            //String s = UUID.randomUUID().toString().replace("-", "");
            sysDepart.setId(IdWorker.getIdStr(sysDepart));
            // 先判断该对象有无父级ID,有则意味着不是最高级,否则意味着是最高级
            // 获取父级ID
            String parentId = sysDepart.getParentId();
            sysDepart.setOrgCode("CODE_" + IdUtil.getSnowflakeNextId());
            String orgType = "1";
            sysDepart.setOrgType(orgType);
            sysDepart.setCreateTime(LocalDateTime.now());
            sysDepart.setDelFlag(CommonConstant.DEL_FLAG_0.toString());
            //新添加的部门是叶子节点
            sysDepart.setIzLeaf(CommonConstant.IS_LEAF);
            if (StrUtil.isEmpty(sysDepart.getOrgCategory())) {
                if (StrUtil.isEmpty(sysDepart.getParentId())) {
                    sysDepart.setOrgCategory("1");
                } else {
                    sysDepart.setOrgCategory("2");
                }
            }
            this.save(sysDepart);
            //update-begin---author:wangshuai ---date:20220307  for：[JTC-119]在部门管理菜单下设置部门负责人 创建用户的时候不需要处理
            //新增部门的时候新增负责部门
            if(StrUtil.isNotEmpty(sysDepart.getDirectorUserIds())){
                this.addDepartByUserIds(sysDepart,sysDepart.getDirectorUserIds());
            }
            //update-end---author:wangshuai ---date:20220307  for：[JTC-119]在部门管理菜单下设置部门负责人 创建用户的时候不需要处理
        }

    }

    @Override
    public List<SysDepartTreeModel> searchByKeyWord(String keyWord, String myDeptSearch, String departIds) {
        LambdaQueryWrapper<Depart> query = new LambdaQueryWrapper<Depart>();
        List<SysDepartTreeModel> newList = new ArrayList<>();
        //myDeptSearch不为空时为我的部门搜索，只搜索所负责部门
        if(!StringUtil.isNullOrEmpty(myDeptSearch)){
            //departIds 为空普通用户或没有管理部门
            if(StringUtil.isNullOrEmpty(departIds)){
                return newList;
            }
            //根据部门id获取所负责部门
            String[] codeArr = this.getMyDeptParentOrgCode(departIds);
            //update-begin-author:taoyan date:20220104 for:/issues/3311 当用户属于两个部门的时候，且这两个部门没有上下级关系，我的部门-部门名称查询条件模糊搜索失效！
            if (codeArr != null && codeArr.length > 0) {
                query.nested(i -> {
                    for (String s : codeArr) {
                        i.or().likeRight(Depart::getOrgCode, s);
                    }
                });
            }
            //update-end-author:taoyan date:20220104 for:/issues/3311 当用户属于两个部门的时候，且这两个部门没有上下级关系，我的部门-部门名称查询条件模糊搜索失效！
            query.eq(Depart::getDelFlag, CommonConstant.DEL_FLAG_0.toString());
        }
        query.like(Depart::getDepartName, keyWord);
        //update-begin--Author:huangzhilin  Date:20140417 for：[bugfree号]组织机构搜索回显优化--------------------
        SysDepartTreeModel model = new SysDepartTreeModel();
        List<Depart> departList = this.list(query);
        if(departList.size() > 0) {
            for(Depart depart : departList) {
                model = new SysDepartTreeModel(depart);
                model.setChildren(null);
                //update-end--Author:huangzhilin  Date:20140417 for：[bugfree号]组织机构搜索功回显优化----------------------
                newList.add(model);
            }
            return newList;
        }
        return newList;
    }

    /**
     * 根据用户所负责部门ids获取父级部门编码
     * @param departIds
     * @return
     */
    private String[] getMyDeptParentOrgCode(String departIds){
        //根据部门id查询所负责部门
        LambdaQueryWrapper<Depart> query = new LambdaQueryWrapper<Depart>();
        query.eq(Depart::getDelFlag, CommonConstant.DEL_FLAG_0.toString());
        if(StrUtil.isNotEmpty(departIds)){
            query.in(Depart::getId, Arrays.asList(departIds.split(",")));
        }

        //------------------------------------------------------------------------------------------------
        //是否开启系统管理模块的多租户数据隔离【SAAS多租户模式】
        if(MybatisPlusConfig.OPEN_SYSTEM_TENANT_CONTROL){
            query.eq(Depart::getTenantId, Integer.parseInt(TenantContext.getTenant(), 0));
        }
        //------------------------------------------------------------------------------------------------
        query.orderByAsc(Depart::getOrgCode);
        List<Depart> list = this.list(query);
        //查找根部门
        if(list == null || list.isEmpty()){
            return null;
        }
        String orgCode = this.getMyDeptParentNode(list);
        String[] codeArr = orgCode.split(",");
        return codeArr;
    }

    /**
     * 获取负责部门父节点
     * @param list
     * @return
     */
    private String getMyDeptParentNode(List<Depart> list){
        Map<String,String> map = new HashMap<>(5);
        //1.先将同一公司归类
        for(Depart dept : list){
            String code = dept.getOrgCode().substring(0,3);
            if(map.containsKey(code)){
                String mapCode = map.get(code)+","+dept.getOrgCode();
                map.put(code,mapCode);
            }else{
                map.put(code,dept.getOrgCode());
            }
        }
        StringBuffer parentOrgCode = new StringBuffer();
        //2.获取同一公司的根节点
        for(String str : map.values()){
            String[] arrStr = str.split(",");
            parentOrgCode.append(",").append(this.getMinLengthNode(arrStr));
        }
        return parentOrgCode.substring(1);
    }

    /**
     * 获取同一公司中部门编码长度最小的部门
     * @param str
     * @return
     */
    private String getMinLengthNode(String[] str){
        int min =str[0].length();
        StringBuilder orgCodeBuilder = new StringBuilder(str[0]);
        for(int i =1;i<str.length;i++){
            if(str[i].length()<=min){
                min = str[i].length();
                orgCodeBuilder.append(StrUtil.COMMA).append(str[i]);
            }
        }
        return orgCodeBuilder.toString();
    }

    /**
     * 通过用户id设置负责部门
     * @param sysDepart SysDepart部门对象
     * @param userIds 多个负责用户id
     */
    public void addDepartByUserIds(Depart sysDepart, String userIds) {
        //获取部门id,保存到用户
        String departId = sysDepart.getId();
        //循环用户id
        String[] userIdArray = userIds.split(",");
        for (String userId:userIdArray) {
            //查询用户表增加负责部门
            DesignSysUser sysUser = sysUserMapper.selectById(userId);
            //如果部门id不为空，那么就需要拼接
            if(StrUtil.isNotEmpty(sysUser.getDepartIds())){
                if(!sysUser.getDepartIds().contains(departId)) {
                    sysUser.setDepartIds(sysUser.getDepartIds() + "," + departId);
                }
            }else{
                sysUser.setDepartIds(departId);
            }
            //设置身份为上级
            sysUser.setUserIdentity(CommonConstant.USER_IDENTITY_2);
            //跟新用户表
            sysUserMapper.updateById(sysUser);
            //判断当前用户是否包含所属部门
            List<UserDepart> userDepartList = userDepartMapper.getUserDepartByUid(userId);
            boolean isExistDepId = userDepartList.stream().anyMatch(item -> departId.equals(item.getDepId()));
            //如果不存在需要设置所属部门
            if(!isExistDepId){
                userDepartMapper.insert(new UserDepart(userId,departId));
            }
        }
    }

    /**
     * 通过部门集合为部门设置用户id，用于前台展示
     * @param departList 部门集合
     */
    private void setUserIdsByDepList(List<Depart> departList) {
        //查询负责部门不为空的情况
        LambdaQueryWrapper<DesignSysUser> query  = new LambdaQueryWrapper<>();
        query.isNotNull(DesignSysUser::getDepartIds);
        List<DesignSysUser> users = sysUserMapper.selectList(query);
        Map<String,Object> map = new HashMap<>(5);
        //先循环一遍找到不同的负责部门id
        for (DesignSysUser user:users) {
            String departIds = user.getDepartIds();
            String[] departIdArray = departIds.split(",");
            for (String departId:departIdArray) {
                //mao中包含部门key，负责用户直接拼接
                if(map.containsKey(departId)){
                    String userIds = map.get(departId) + "," + user.getId();
                    map.put(departId,userIds);
                }else{
                    map.put(departId,user.getId());
                }
            }
        }
        //循环部门集合找到部门id对应的负责用户
        for (Depart sysDepart:departList) {
            if(map.containsKey(sysDepart.getId())){
                sysDepart.setDirectorUserIds(map.get(sysDepart.getId()).toString());
            }
        }
    }

    private List<Depart> buildTree(List<Depart> allDeparts) {
        Map<String, List<Depart>> parentMap = new HashMap<>();
        for (Depart depart : allDeparts) {
            if (depart.getParentId() != null) {
                List<Depart> children = parentMap.computeIfAbsent(
                    depart.getParentId(), 
                    k -> new ArrayList<>()
                );
                children.add(depart);
            }
        }
        
        List<Depart> rootDeparts = new ArrayList<>();
        for (Depart depart : allDeparts) {
            if (depart.getParentId() == null) {
                depart.setChildren(buildChildren(depart.getId(), parentMap));
                rootDeparts.add(depart);
            }
        }
        return rootDeparts;
    }
    
    private List<Depart> buildChildren(String parentId, Map<String, List<Depart>> parentMap) {
        List<Depart> children = parentMap.get(parentId);
        if (children == null) {
            return new ArrayList<>();
        }
        
        for (Depart child : children) {
            child.setChildren(buildChildren(child.getId(), parentMap));
        }
        return children;
    }
    
    private String generateNewOrgCode(String parentOrgCode) {
        // 生成新的机构编码逻辑
        // 这里简单实现，实际可能需要更复杂的逻辑
        LambdaQueryWrapper<Depart> wrapper = new LambdaQueryWrapper<>();
        wrapper.likeRight(Depart::getOrgCode, parentOrgCode);
        wrapper.orderByDesc(Depart::getOrgCode);
        wrapper.last("limit 1");
        
        Depart lastDepart = this.getOne(wrapper);
        if (lastDepart == null) {
            return parentOrgCode + "001";
        }
        
        String lastCode = lastDepart.getOrgCode();
        int lastNum = Integer.parseInt(lastCode.substring(lastCode.length() - 3)) + 1;
        return parentOrgCode + String.format("%03d", lastNum);
    }

    @Override
    public void deleteDepart(String id) {
        //删除部门设置父级的叶子结点
        this.setIzLeaf(id);
        this.delete(id);
        //删除部门用户关系表
        LambdaQueryWrapper<UserDepart> query = new LambdaQueryWrapper<UserDepart>()
                .eq(UserDepart::getDepId, id);
        this.userDepartMapper.delete(query);
    }

    /**
     * 设置父级节点是否存在叶子结点
     * @param id
     */
    private void setIzLeaf(String id) {
        Depart depart = this.getDepartById(id);
        String parentId = depart.getParentId();
        if(StrUtil.isNotEmpty(parentId)){
            Long count = this.count(new QueryWrapper<Depart>().lambda().eq(Depart::getParentId, parentId));
            if(count == 1){
                //若父节点无其他子节点，则该父节点是叶子节点
                this.getBaseMapper().setMainLeaf(parentId, CommonConstant.IS_LEAF);
            }
        }
    }

    @Override
    public Depart getDepartById(String id) {
        return this.getBaseMapper().getDepartById(id);
    }

    /**
     * 根据部门id删除并且删除其可能存在的子级任何部门
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String id) {
        List<String> idList = new ArrayList<>();
        idList.add(id);
        this.checkChildrenExists(id, idList);
        //清空部门树内存
        //FindsDepartsChildrenUtil.clearDepartIdModel();
        boolean ok = this.removeByIds(idList);
        //根据部门id删除用户与部门关系
        userDepartMapper.delete(new LambdaQueryWrapper<UserDepart>().in(UserDepart::getDepId,idList));
        return ok;
    }

    /**
     * delete 方法调用
     * @param id
     * @param idList
     */
    private void checkChildrenExists(String id, List<String> idList) {
        LambdaQueryWrapper<Depart> query = new LambdaQueryWrapper<Depart>();
        query.eq(Depart::getParentId,id);
        List<Depart> departList = this.list(query);
        if(departList != null && !departList.isEmpty()) {
            for(Depart depart : departList) {
                idList.add(depart.getId());
                this.checkChildrenExists(depart.getId(), idList);
            }
        }
    }
} 