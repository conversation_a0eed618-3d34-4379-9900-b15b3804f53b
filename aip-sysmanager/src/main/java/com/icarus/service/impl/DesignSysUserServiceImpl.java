package com.icarus.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.common.redis.RedisUtil;
import com.icarus.common.rest.BaseResponse;
import com.icarus.constant.CommonConstant;
import com.icarus.dto.PageDTO;
import com.icarus.dto.login.LoginDTO;
import com.icarus.dto.user.UserDTO;
import com.icarus.entity.*;
import com.icarus.mapper.DesignSysUserMapper;
import com.icarus.service.DesignSysUserService;
import com.icarus.totp.TotpUtils;
import com.icarus.utils.ConvertUtils;
import com.icarus.utils.PasswordUtil;
import com.icarus.vo.ChangePwdVO;
import com.icarus.vo.RegisterVO;
import com.icarus.vo.UserVO;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName DesignSysUserServiceImpl
 * <AUTHOR>
 * @Date 2025/2/27
 * @Version 1.0
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class DesignSysUserServiceImpl extends ServiceImpl<DesignSysUserMapper, DesignSysUser> implements DesignSysUserService {

    @Value("${sysmanager.init.roleId}")
    private String initRoleId;
    private final RedisUtil redisUtil;
    private final TotpUtils totpUtils;
    private final DesignSysUserRoleServiceImpl designSysUserRoleService;
    private final RoleServiceImpl roleService;
    private final DepartServiceImpl departService;
    private final DesignSysUserDepartServiceImpl designSysUserDepartService;
    @Override
    public DesignSysUser getUserById(String id) {
        DesignSysUser sysUser = this.baseMapper.selectById(id);
        // 2. 批量查询用户角色关系
        List<DesignSysUserRole> userRoles = designSysUserRoleService
                .list(new LambdaQueryWrapper<DesignSysUserRole>()
                        .eq(DesignSysUserRole::getUserId, id));

        List<String> roleIds = userRoles.stream().map(DesignSysUserRole::getRoleId).collect(Collectors.toList());

        if(!CollectionUtils.isEmpty(roleIds)){
            // 3. 批量查询角色信息
            List<Role> roles = roleService
                    .list(new LambdaQueryWrapper<Role>()
                            .in(Role::getId, roleIds));
            sysUser.setRoleNames( String.join(StrUtil.COMMA, roles.stream().map(Role::getRoleName).collect(Collectors.toList())));
            sysUser.setRoleIds( String.join(StrUtil.COMMA, roleIds));
        }

        // 3. 批量查询用户部门关系
        List<DesignSysUserDepart> userDeparts = designSysUserDepartService
                .list(new LambdaQueryWrapper<DesignSysUserDepart>()
                        .eq(DesignSysUserDepart::getUserId, id));

        List<String> departIds = userDeparts.stream().map(DesignSysUserDepart::getDepId).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(departIds)){
            // 3. 批量查询部门信息
            List<Depart> departs = departService
                    .list(new LambdaQueryWrapper<Depart>()
                            .in(Depart::getId, departIds));

            sysUser.setDepartIds(String.join(StrUtil.COMMA, departIds));
            sysUser.setDepartNames(String.join(StrUtil.COMMA, departs.stream().map(Depart::getDepartName).collect(Collectors.toList())));
        }

        return sysUser;
    }

    @Override
    public void register(RegisterVO user, HttpServletRequest request) {
        // 内置一个角色，为所有通过注册接口的用户提供权限的初始化
        if(Objects.isNull(roleService.getById(initRoleId))){
            throw new RuntimeException("数据未初始化，请联系管理员");
        }
        UserVO uservO = new UserVO();
        BeanUtils.copyProperties(user, uservO);
        uservO.setRoleIds(initRoleId);
        uservO.setTotpSecret(user.getSecret());
        saveUser(uservO, request);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveUser(UserVO userVO, HttpServletRequest request) {
        DesignSysUser user = new DesignSysUser();
        BeanUtils.copyProperties(userVO, user);
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());
        String salt = ConvertUtils.randomGen(8);
        user.setSalt(salt);
        String passwordEncode = PasswordUtil.encrypt(user.getPassword(), CommonConstant.ENCRYPT, salt);
        user.setPassword(passwordEncode);
        user.setStatus(1);
        DesignSysUser one = this.getOne(new QueryWrapper<DesignSysUser>().lambda().eq(DesignSysUser::getUsername, user.getUsername()));
        if(!Objects.isNull(one)){
            throw new RuntimeException("用户名已存在");
        }
        Object operator = request.getAttribute(CommonConstant.USER_NAME);
        if(!Objects.isNull(operator)){
            user.setUpdateBy((String)operator);
        }
        // 用户表字段org_code不能在这里设置他的值
        user.setOrgCode(null);
        // 保存用户
        this.save(user);
        // 保存关系表
        saveRelations(user);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editUser(DesignSysUser user, HttpServletRequest request) {
        DesignSysUser sysUser = this.baseMapper.selectById(user.getId());
        if(Objects.isNull(sysUser)){
            throw new RuntimeException("用户不存在");
        }
        DesignSysUser one = getOne(new QueryWrapper<DesignSysUser>().lambda().eq(DesignSysUser::getUsername, user.getUsername()).ne(DesignSysUser::getId, user.getId()));
        if(!Objects.isNull(one)){
            throw new RuntimeException("用户名已存在");
        }
        user.setUpdateTime(LocalDateTime.now());
        user.setUsername(sysUser.getUsername());
        user.setPassword(sysUser.getPassword());
        user.setOrgCode(null);
        Object operator = request.getAttribute(CommonConstant.USER_NAME);
        if(!Objects.isNull(operator)){
            user.setUpdateBy((String)operator);
        }
        // 修改用户基础信息
        this.updateById(user);
        // 移除旧的关系
        designSysUserRoleService.remove(new QueryWrapper<DesignSysUserRole>().lambda().eq(DesignSysUserRole::getUserId, user.getId()));
        designSysUserDepartService.remove(new QueryWrapper<DesignSysUserDepart>().lambda().eq(DesignSysUserDepart::getUserId, user.getId()));
        // 保存关系表
        saveRelations(user);
    }

    private void saveRelations(DesignSysUser user) {
        // 保存角色
        String[] rolesArr = user.getRoleIds().split(StrUtil.COMMA);
        List<DesignSysUserRole> userRoles = new ArrayList<>();
        for (String roleId : rolesArr) {
            // 创建用户角色关系
            DesignSysUserRole userRole = new DesignSysUserRole();
            userRole.setUserId(user.getId());
            userRole.setRoleId(roleId);
            userRoles.add(userRole);
        }
        designSysUserRoleService.saveBatch(userRoles);
        // 保存所属部门
        String[] departArr = user.getDepartIds().split(StrUtil.COMMA);
        List<DesignSysUserDepart> userDeparts = new ArrayList<>();
        for (String departId : departArr) {
            DesignSysUserDepart userDepart = new DesignSysUserDepart();
            userDepart.setUserId(user.getId());
            userDepart.setDepId(departId);
            userDeparts.add(userDepart);
        }
        designSysUserDepartService.saveBatch(userDeparts);
    }


    @Override
    public BaseResponse checkUserIsEffective(DesignSysUser sysUser) {
        //情况1：根据用户信息查询，该用户不存在
        if (sysUser == null) {
            return BaseResponse.badRequest("用户不存在");
        }
        //情况2：根据用户信息查询，该用户已冻结
        if (CommonConstant.USER_FREEZE.equals(sysUser.getStatus())) {
            return BaseResponse.badRequest("用户账号已被禁用");
        }
        return BaseResponse.ok(null);
    }

    @Override
    public BaseResponse setLoginTenant(DesignSysUser sysUser, LoginDTO loginDTO, String username) {

        List<DesignSysUserDepart> userDeparts = designSysUserDepartService.lambdaQuery()
                .eq(DesignSysUserDepart::getUserId, sysUser.getId()).list();
        // 登录会话租户ID，有效性重置
        if (!CollectionUtils.isEmpty(userDeparts)) {
            if (userDeparts.size() == 1) {
                sysUser.setLoginTenantId(userDeparts.get(0).getId());
            } else {
                List<DesignSysUserDepart> listAfterFilter = userDeparts.stream().filter(s -> s.getId().equals(sysUser.getLoginTenantId())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(listAfterFilter)) {
                    //如果上次登录租户ID，在用户拥有的租户集合里面没有了，则随机取用户拥有的第一个租户ID
                    sysUser.setLoginTenantId(listAfterFilter.get(0).getId());
                }
            }
        } else {
            // 无租户的时候，设置为 0
            sysUser.setLoginTenantId("0");
        }
        DesignSysUser update = new DesignSysUser();
        update.setId(sysUser.getId());
        update.setLoginTenantId(sysUser.getLoginTenantId());
        updateById(update);
        log.info(" 登录接口用户的租户ID = {}", sysUser.getLoginTenantId());
//        if(sysUser.getLoginTenantId()!=null){
//            // 登录的时候需要手工设置下会话中的租户ID,不然登录接口无法通过租户隔离查询到数据
//            TenantContext.setTenant(sysUser.getLoginTenantId()+"");
//        }
        return null;
    }

    @Override
    public PageDTO<UserDTO> userPage(Page<DesignSysUser> page, LambdaQueryWrapper<DesignSysUser> wrapper) {
        Page<DesignSysUser> userPage = page(page, wrapper);
        List<UserDTO> userDTOS = userPage.getRecords().stream().map(x -> {
            UserDTO dto = new UserDTO();
            BeanUtils.copyProperties(x, dto);
            return dto;
        }).collect(Collectors.toList());
        // 补充角色和部门信息逻辑
        setUserDTOS(userDTOS);

        Page<UserDTO> resultPage = new Page<>(userPage.getCurrent(), userPage.getSize(), userPage.getTotal());
        resultPage.setRecords(userDTOS);
        return PageDTO.from(resultPage);
    }

    private void setUserDTOS(List<UserDTO> userDTOS) {
        if (!CollectionUtils.isEmpty(userDTOS)) {
            // 1. 提取用户ID集合
            List<String> userIds = userDTOS.stream()
                    .map(UserDTO::getId)
                    .collect(Collectors.toList());

            // 2. 批量查询用户角色关系
            List<DesignSysUserRole> userRoles = designSysUserRoleService
                    .list(new LambdaQueryWrapper<DesignSysUserRole>()
                            .in(DesignSysUserRole::getUserId, userIds));
            Map<String, List<String>> userRoleMap = userRoles
                    .stream()
                    .collect(Collectors.groupingBy(DesignSysUserRole::getUserId,
                            Collectors.mapping(DesignSysUserRole::getRoleId, Collectors.toList())));
            List<Role> roleList = new ArrayList<>();
            if(!CollectionUtils.isEmpty(userRoles)){
                roleList = roleService.list(new LambdaQueryWrapper<Role>().in(Role::getId, userRoles.stream().map(DesignSysUserRole::getRoleId).collect(Collectors.toList())));
            }
            // 3. 批量查询用户部门关系
            List<DesignSysUserDepart> userDeparts = designSysUserDepartService
                    .list(new LambdaQueryWrapper<DesignSysUserDepart>()
                            .in(DesignSysUserDepart::getUserId, userIds));
            Map<String, List<String>> userDepartMap = userDeparts
                    .stream()
                    .collect(Collectors.groupingBy(DesignSysUserDepart::getUserId,
                            Collectors.mapping(DesignSysUserDepart::getDepId, Collectors.toList())));
            List<Depart> departList = new ArrayList<>();
            if(!CollectionUtils.isEmpty(userDeparts)){
                departList = departService.list(new LambdaQueryWrapper<Depart>().in(Depart::getId, userDeparts.stream()
                        .map(DesignSysUserDepart::getDepId).collect(Collectors.toList())));
            }


            // 4. 设置角色和部门信息到DTO
            List<Role> finalRoleList = roleList;
            List<Depart> finalDepartList = departList;
            userDTOS.forEach(dto -> {
                List<String> dtoRoleIds = userRoleMap.getOrDefault(dto.getId(), Collections.emptyList());
                List<String> roleNames = finalRoleList.stream()
                        .filter(role -> dtoRoleIds.contains(role.getId()))
                        .map(Role::getRoleName)
                        .collect(Collectors.toList());
                dto.setRoleNames(String.join(StrUtil.COMMA, roleNames));
                dto.setRoleIds( String.join(StrUtil.COMMA,dtoRoleIds));
                List<String> dtoDepartIds = userDepartMap.getOrDefault(dto.getId(), Collections.emptyList());
                List<String> departNames = finalDepartList.stream()
                        .filter(role -> dtoDepartIds.contains(role.getId()))
                        .map(Depart::getDepartName)
                        .collect(Collectors.toList());
                dto.setDepartIds( String.join(StrUtil.COMMA,
                        userDepartMap.getOrDefault(dto.getId(), Collections.emptyList())));
                dto.setDepartNames(String.join(StrUtil.COMMA, departNames));
            });
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changePwd(ChangePwdVO vo, HttpServletRequest request) {
        DesignSysUser sysUser = this.baseMapper.selectOne(new QueryWrapper<DesignSysUser>().lambda().eq(DesignSysUser::getUsername, vo.getUserName()));
        if(Objects.isNull(sysUser)){
            throw new RuntimeException("用户不存在");
        }
        String salt = sysUser.getSalt();
        // type=1时是忘记密码请求，不校验旧密码，校验验证码，再次校验一次验证码是为了防止请求绕过前端验证
        if(vo.getType() == 1){
            if(totpUtils.getAuthenticationSign() && !totpUtils.verify(sysUser.getTotpSecret(), vo.getTotpCode())){
                throw new RuntimeException("验证码不正确");
            }
        }else{
            String passwordEncode = PasswordUtil.encrypt(vo.getOldPwd(), CommonConstant.ENCRYPT, salt);
            if(!passwordEncode.equals(sysUser.getPassword())){
                throw new RuntimeException("输入的旧密码不正确");
            }
        }
        String password = PasswordUtil.encrypt(vo.getNewPwd(), CommonConstant.ENCRYPT, salt);
        sysUser.setPassword(password);
        Object operator = request.getAttribute(CommonConstant.USER_NAME);
        if(!Objects.isNull(operator)){
            sysUser.setUpdateBy((String)operator);
        }
        sysUser.setUpdateTime(LocalDateTime.now());
        this.baseMapper.updateById(sysUser);
        // 清除token信息
        redisUtil.del(CommonConstant.PREFIX_USER_TOKEN + sysUser.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enable(String userId, Integer status, HttpServletRequest request) {
        DesignSysUser user = this.getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        if (!Arrays.asList(1, 2).contains(status)) {
            throw new RuntimeException("状态值不合法");
        }
        DesignSysUser update = new DesignSysUser();
        update.setId(userId);
        update.setStatus(status);
        update.setUpdateTime(LocalDateTime.now());
        Object username = request.getAttribute(CommonConstant.USER_NAME);
        if (!Objects.isNull(username)) {
            update.setUpdateBy((String) username);
        }
        this.updateById(update);
    }

    @Override
    public void removeById(String id) {
        this.getBaseMapper().deleteById(id);
        designSysUserDepartService.remove(new LambdaQueryWrapper<DesignSysUserDepart>().eq(DesignSysUserDepart::getUserId, id));
        designSysUserRoleService.remove(new LambdaQueryWrapper<DesignSysUserRole>().eq(DesignSysUserRole::getUserId, id));
        // 清除token信息
        redisUtil.del(CommonConstant.PREFIX_USER_TOKEN + id);
    }

    /**
     * 角色、权限发生变化时，需要打标生成标识（不做接口权限时，没有作用）
     */
    @Override
    public void permissionChange(String roleId) {
        // 获取到角色下的所有
        List<String> userNames = new ArrayList<>();
        for (String userName : userNames) {
            redisUtil.set(CommonConstant.PREFIX_USER_PERMISSION_CHANGE + userName, 1);
        }
    }

}
