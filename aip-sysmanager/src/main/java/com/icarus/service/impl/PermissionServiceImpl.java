package com.icarus.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.constant.CommonConstant;
import com.icarus.dto.PermissionResultDTO;
import com.icarus.dto.SysPermissionTree;
import com.icarus.entity.DesignSysUserRole;
import com.icarus.entity.Permission;
import com.icarus.enums.MenuTypeEnum;
import com.icarus.mapper.DepartPermissionMapper;
import com.icarus.mapper.DepartRolePermissionMapper;
import com.icarus.mapper.PermissionMapper;
import com.icarus.mapper.RolePermissionMapper;
import com.icarus.service.DesignSysUserRoleService;
import com.icarus.service.PermissionService;
import com.icarus.service.RolePermissionService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PermissionServiceImpl extends ServiceImpl<PermissionMapper, Permission> implements PermissionService {

    private final RolePermissionMapper sysRolePermissionMapper;
    private final DepartPermissionMapper sysDepartPermissionMapper;
    private final DepartRolePermissionMapper sysDepartRolePermissionMapper;
    private final DesignSysUserRoleService designSysUserRoleService;
    private final RolePermissionService rolePermissionService;


    @Override
    public void addPermission(Permission permission) {
        //----------------------------------------------------------------------
        //判断是否是一级菜单，是的话清空父菜单
        if(CommonConstant.MENU_TYPE_0.equals(permission.getMenuType())) {
            permission.setParentId(null);
        }
        //----------------------------------------------------------------------
        String pid = permission.getParentId();
        if(StrUtil.isNotEmpty(pid)) {
            //设置父节点不为叶子节点
            this.getBaseMapper().setMenuLeaf(pid, 0);
        }
        permission.setCreateTime(LocalDateTime.now());
        permission.setDelFlag(0);
        permission.setIsLeaf(1);
        this.save(permission);
    }

    @Override
    public void editPermission(Permission sysPermission) {
        Permission p = this.getById(sysPermission.getId());
        if(p==null) {
            throw new RuntimeException("未找到菜单信息");
        }else {
            sysPermission.setUpdateTime(LocalDateTime.now());
            //----------------------------------------------------------------------
            //Step1.判断是否是一级菜单，是的话清空父菜单ID
            if(CommonConstant.MENU_TYPE_0.equals(sysPermission.getMenuType())) {
                sysPermission.setParentId("");
            }
            //Step2.判断菜单下级是否有菜单，无则设置为叶子节点
            Long count = this.count(new QueryWrapper<Permission>().lambda().eq(Permission::getParentId, sysPermission.getId()));
            if(count==0) {
                sysPermission.setIsLeaf(1);
            }
            //----------------------------------------------------------------------
            this.updateById(sysPermission);

            //如果当前菜单的父菜单变了，则需要修改新父菜单和老父菜单的，叶子节点状态
            String pid = sysPermission.getParentId();
            boolean flag = (StrUtil.isNotEmpty(pid) && !pid.equals(p.getParentId()))
                    || StrUtil.isEmpty(pid)&&StrUtil.isNotEmpty(p.getParentId());
            if (flag) {
                //a.设置新的父菜单不为叶子节点
                this.getBaseMapper().setMenuLeaf(pid, 0);
                //b.判断老的菜单下是否还有其他子菜单，没有的话则设置为叶子节点
                Long cc = this.count(new QueryWrapper<Permission>().lambda().eq(Permission::getParentId, p.getParentId()));
                if(cc==0) {
                    if(StrUtil.isNotEmpty(p.getParentId())) {
                        this.getBaseMapper().setMenuLeaf(p.getParentId(), 1);
                    }
                }
            }
        }
    }

    @Override
    public void deletePermission(String id) {
        Permission sysPermission = this.getById(id);
        if(sysPermission==null) {
            throw new RuntimeException("未找到菜单信息");
        }
        String pid = sysPermission.getParentId();
        if(StrUtil.isNotEmpty(pid)) {
            Long count = this.count(new QueryWrapper<Permission>().lambda().eq(Permission::getParentId, pid));
            if(count==1) {
                //若父节点无其他子节点，则该父节点是叶子节点
                this.getBaseMapper().setMenuLeaf(pid, 1);
            }
        }
        this.getBaseMapper().deleteById(id);
        // 该节点可能是子节点但也可能是其它节点的父节点,所以需要级联删除
        this.removeChildrenBy(sysPermission.getId());
        //关联删除
        Map<String, Object> map = new HashMap<>(5);
        map.put("permission_id",id);
        //删除角色授权表
        sysRolePermissionMapper.deleteByMap(map);
        //删除部门权限表
        sysDepartPermissionMapper.deleteByMap(map);
        //删除部门角色授权
        sysDepartRolePermissionMapper.deleteByMap(map);
    }

    /**
     * 根据父id删除其关联的子节点数据
     *
     * @return
     */
    public void removeChildrenBy(String parentId) {
        LambdaQueryWrapper<Permission> query = new LambdaQueryWrapper<>();
        // 封装查询条件parentId为主键,
        query.eq(Permission::getParentId, parentId);
        // 查出该主键下的所有子级
        List<Permission> permissionList = this.list(query);
        if (permissionList != null && !permissionList.isEmpty()) {
            // id
            String id = "";
            // 查出的子级数量
            Long num = 0L;
            // 如果查出的集合不为空, 则先删除所有
            this.remove(query);
            // 再遍历刚才查出的集合, 根据每个对象,查找其是否仍有子级
            for (int i = 0, len = permissionList.size(); i < len; i++) {
                id = permissionList.get(i).getId();
                Map<String, Object> map = new HashMap<>(5);
                map.put("permission_id",id);
                //删除角色授权表
                sysRolePermissionMapper.deleteByMap(map);
                //删除部门权限表
                sysDepartPermissionMapper.deleteByMap(map);
                //删除部门角色授权
                sysDepartRolePermissionMapper.deleteByMap(map);
                num = this.count(new LambdaQueryWrapper<Permission>().eq(Permission::getParentId, id));
                // 如果有, 则递归
                if (num > 0) {
                    this.removeChildrenBy(id);
                }
            }
        }
    }

    @Override
    public void buildTreeList(List<SysPermissionTree> treeList,
                              List<Permission> metaList,
                              SysPermissionTree temp) {
        for (Permission permission : metaList) {
            String tempPid = permission.getParentId();
            SysPermissionTree tree = new SysPermissionTree(permission);
            if (temp == null && StrUtil.isEmpty(tempPid)) {
                treeList.add(tree);
                if (tree.getIsLeaf() == 0) {
                    buildTreeList(treeList, metaList, tree);
                }
            } else if (temp != null && tempPid != null && tempPid.equals(temp.getId())) {
                temp.getChildren().add(tree);
                if (tree.getIsLeaf() == 0) {
                    buildTreeList(treeList, metaList, tree);
                }
            }

        }
    }

    /**
     * <p>
     * 获取我的菜单
     * </p>
     *
     * @param userId 用户id
     * @return {@link List }<{@link SysPermissionTree }>
     * <AUTHOR> by 2025-03-01
     */
    @Override
    public List<SysPermissionTree> getMyPermission(String userId) {
        List<SysPermissionTree> treeList = new ArrayList<>();
        if (StrUtil.isEmpty(userId)) {
            return treeList;
        }
        List<DesignSysUserRole> designSysUserRoles = designSysUserRoleService.getRoleByUserId(userId);
        if (CollUtil.isEmpty(designSysUserRoles)) {
            return treeList;
        }
        List<String> roleIds = designSysUserRoles.stream().map(DesignSysUserRole::getRoleId).distinct().collect(Collectors.toList());
        // 如果包含0 则说明拥有全部权限
        boolean contains = roleIds.contains(CommonConstant.IS_ADMIN);
        List<String> permissionIds = rolePermissionService.getPermissionIdsByRoleId(roleIds);
        List<Permission> permissionList;
        if (contains) {
            LambdaQueryWrapper<Permission> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.orderByAsc(Permission::getSortNo);
            permissionList = this.list(lambdaQueryWrapper);
        } else {
            if (CollUtil.isEmpty(permissionIds)) {
                return treeList;
            }
            LambdaQueryWrapper<Permission> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.orderByAsc(Permission::getSortNo);
            lambdaQueryWrapper.in(Permission::getId, permissionIds);
            permissionList = this.list(lambdaQueryWrapper);
        }
        if (CollUtil.isEmpty(permissionList)) {
            return treeList;
        }
        buildTreeList(treeList, permissionList, null);
        return treeList;
    }

    @Override
    public Map<String, List<PermissionResultDTO>> getMyPermissionConfig(String userId) {
        List<SysPermissionTree> treeList = getMyPermission(userId);
        if (CollUtil.isEmpty(treeList)) {
            return Collections.emptyMap();
        }
        Map<String, List<PermissionResultDTO>> resultMap = new HashMap<>();
        for (SysPermissionTree sysPermissionTree : treeList) {
            String region = StrUtil.emptyToDefault(sysPermissionTree.getRegion(), MenuTypeEnum.USER_CONFIG.getCode());
            PermissionResultDTO permissionResultDTO = new PermissionResultDTO();
            permissionResultDTO.setKey(sysPermissionTree.getKey());
            permissionResultDTO.setIcon(sysPermissionTree.getIcon());
            permissionResultDTO.setTitle(sysPermissionTree.getTitle());
            permissionResultDTO.setPath(sysPermissionTree.getUrl());
            List<PermissionResultDTO> permissionResultList = resultMap.get(region);
            if (CollUtil.isEmpty(permissionResultList)) {
                permissionResultList = new ArrayList<>();
                permissionResultList.add(permissionResultDTO);
                resultMap.put(region, permissionResultList);
            } else {
                permissionResultList.add(permissionResultDTO);
            }
            List<SysPermissionTree> children = sysPermissionTree.getChildren();
            setPermissionResult(permissionResultDTO, children);
        }
        return resultMap;
    }


    private void setPermissionResult(PermissionResultDTO permissionResultDTO,
                                     List<SysPermissionTree> children) {
        if (CollUtil.isEmpty(children)) {
            return;
        }
        for (SysPermissionTree child : children) {
            PermissionResultDTO childPermissionResult = new PermissionResultDTO();
            childPermissionResult.setKey(child.getKey());
            childPermissionResult.setIcon(child.getIcon());
            childPermissionResult.setTitle(child.getTitle());
            childPermissionResult.setPath(child.getUrl());
            permissionResultDTO.getChildren().add(childPermissionResult);
            List<SysPermissionTree> children1 = child.getChildren();
            setPermissionResult(permissionResultDTO, children1);
        }
    }


} 