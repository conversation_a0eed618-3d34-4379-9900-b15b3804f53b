package com.icarus.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.entity.DesignSysUserRole;
import com.icarus.mapper.DesignSysUserRoleMapper;
import com.icarus.service.DesignSysUserRoleService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户角色关系表服务实现类
 * @ClassName DesignSysUserRoleService
 * <AUTHOR>
 * @Date 2025/3/01
 * @Version 1.0
 **/
@Service
@RequiredArgsConstructor
public class DesignSysUserRoleServiceImpl extends ServiceImpl<DesignSysUserRoleMapper, DesignSysUserRole> implements DesignSysUserRoleService {

    /**
     * <p>
     * 按用户id获取角色
     * </p>
     *
     * @param userId 用户id
     * @return {@link List }<{@link DesignSysUserRole }>
     * <AUTHOR> by 2025-03-01
     */
    @Override
    public List<DesignSysUserRole> getRoleByUserId(String userId) {
        LambdaQueryWrapper<DesignSysUserRole> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DesignSysUserRole::getUserId, userId);
        return this.list(lambdaQueryWrapper);
    }
}