package com.icarus.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.entity.DepartPermission;
import com.icarus.mapper.DepartPermissionMapper;
import com.icarus.service.DepartPermissionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

/**
 * 部门权限服务实现类
 */
@Slf4j
@Service
public class DepartPermissionServiceImpl extends ServiceImpl<DepartPermissionMapper, DepartPermission> implements DepartPermissionService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDepartPermissions(String departId, List<String> permissionIds) {
        try {
            // 先删除原有的权限关系
            this.deleteByDepartId(departId);
            
            // 批量保存新的权限关系
            List<DepartPermission> departPermissions = new ArrayList<>();
            for (String permissionId : permissionIds) {
                DepartPermission dp = new DepartPermission();
                dp.setDepartId(departId);
                dp.setPermissionId(permissionId);
                departPermissions.add(dp);
            }
            
            return this.saveBatch(departPermissions);
        } catch (Exception e) {
            log.error("保存部门权限关系失败", e);
            return false;
        }
    }

    @Override
    public List<String> getPermissionIdsByDepartId(String departId) {
        return baseMapper.getPermissionIdsByDepartId(departId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByDepartId(String departId) {
        return baseMapper.deleteByDepartId(departId) >= 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByPermissionId(String permissionId) {
        return baseMapper.deleteByPermissionId(permissionId) >= 0;
    }
} 