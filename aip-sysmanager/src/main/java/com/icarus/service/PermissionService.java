package com.icarus.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.icarus.dto.PermissionResultDTO;
import com.icarus.dto.SysPermissionTree;
import com.icarus.entity.Permission;
import java.util.List;
import java.util.Map;

import com.icarus.dto.PermissionQueryDTO;

public interface PermissionService extends IService<Permission> {

    void addPermission(Permission permission);

    void editPermission(Permission permission);

    void deletePermission(String id);

    void buildTreeList(List<SysPermissionTree> treeList,
                       List<Permission> metaList,
                       SysPermissionTree temp);

    List<SysPermissionTree> getMyPermission(String userId);

    Map<String, List<PermissionResultDTO>> getMyPermissionConfig(String userId);
}