package com.icarus.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.icarus.entity.RolePermission;
import java.util.List;

/**
 * 角色权限服务接口
 */
public interface RolePermissionService extends IService<RolePermission> {
    
    /**
     * 保存角色权限关系
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     * @param operateIp 操作IP
     * @return 是否保存成功
     */
    boolean saveRolePermissions(String roleId, List<String> permissionIds, String operateIp);
    
    /**
     * 获取角色的权限ID列表
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    List<String> getPermissionIdsByRoleId(String roleId);

    List<String> getPermissionIdsByRoleId(List<String> roleIds);

    /**
     * 删除角色的所有权限
     * @param roleId 角色ID
     * @return 是否删除成功
     */
    boolean deleteByRoleId(String roleId);
    
    /**
     * 删除某个权限关联的所有角色关系
     * @param permissionId 权限ID
     * @return 是否删除成功
     */
    boolean deleteByPermissionId(String permissionId);

    void saveRolePermission(String roleId, String permissionIds, String lastPermissionIds);
}