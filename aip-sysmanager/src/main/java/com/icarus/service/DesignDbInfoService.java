package com.icarus.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.icarus.dto.DbInfoDTO;
import com.icarus.dto.PageDTO;
import com.icarus.entity.DesignDbInfo;
import jakarta.servlet.http.HttpServletRequest;

import java.util.List;

/**
 * 数据库表信息服务接口
 */
public interface DesignDbInfoService extends IService<DesignDbInfo> {
    /**
     * 同步数据库表信息
     * @param dataSourceCode 数据源编码
     * @param request HTTP请求
     * @return 同步的表信息数量
     */
    int syncDbInfo(String dataSourceCode, HttpServletRequest request);

    /**
     * 分页查询表信息
     * @param page 分页参数
     * @param dataSourceCode 数据源编码
     * @param tableName 表名
     * @param columnName 列名
     * @return 分页结果
     */
    PageDTO<DbInfoDTO> pageDbInfo(Page<DesignDbInfo> page, String dataSourceCode, String tableName, String columnName);

    /**
     * 获取指定数据源的所有表名
     * @param dataSourceCode 数据源编码
     * @return 表名列表
     */
    List<String> getTableNames(String dataSourceCode);

    /**
     * 获取指定表的所有列信息
     * @param dataSourceCode 数据源编码
     * @param tableName 表名
     * @return 列信息列表
     */
    List<DbInfoDTO> getTableColumns(String dataSourceCode, String tableName);
} 