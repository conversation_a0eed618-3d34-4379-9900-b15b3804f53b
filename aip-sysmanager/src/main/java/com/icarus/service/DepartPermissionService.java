package com.icarus.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.icarus.entity.DepartPermission;
import java.util.List;

/**
 * 部门权限服务接口
 */
public interface DepartPermissionService extends IService<DepartPermission> {
    
    /**
     * 保存部门权限关系
     * @param departId 部门ID
     * @param permissionIds 权限ID列表
     * @return 是否保存成功
     */
    boolean saveDepartPermissions(String departId, List<String> permissionIds);
    
    /**
     * 获取部门的权限ID列表
     * @param departId 部门ID
     * @return 权限ID列表
     */
    List<String> getPermissionIdsByDepartId(String departId);
    
    /**
     * 删除部门的所有权限
     * @param departId 部门ID
     * @return 是否删除成功
     */
    boolean deleteByDepartId(String departId);
    
    /**
     * 删除某个权限关联的所有部门关系
     * @param permissionId 权限ID
     * @return 是否删除成功
     */
    boolean deleteByPermissionId(String permissionId);
} 