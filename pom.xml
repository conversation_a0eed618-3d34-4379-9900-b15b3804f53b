<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.metateam.icarus</groupId>
        <artifactId>icarus-parent</artifactId>
        <version>2.0.0-SNAPSHOT</version>
    </parent>
    <groupId>com.metateam.biggroup</groupId>
    <artifactId>aip-biggroup-platform</artifactId>
    <version>2.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <java.version>17</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <pdfbox.version>3.0.1</pdfbox.version>
        <graalvm-js.version>22.3.0</graalvm-js.version>
        <jgit.version>6.7.0.202309050840-r</jgit.version>
        <tika.version>2.9.2</tika.version>
        <poi.version>5.2.3</poi.version>
    </properties>

    <modules>
        <module>aip-common</module>
        <module>aip-flow-plugin</module>
        <module>aip-designcanvas</module>
        <module>aip-testplatform</module>
        <module>aip-runtime</module>
        <module>aip-biggroup-starter</module>
        <module>aip-sysmanager</module>
        <module>aip-monitor</module>
        <module>aip-manager-log</module>
        <module>aip-devops-docker</module>
        <module>aip-gateway</module>
        <module>aip-bi-workbench</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <!-- 内部模块依赖管理 -->
            <dependency>
                <groupId>com.metateam.biggroup</groupId>
                <artifactId>aip-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.metateam.biggroup</groupId>
                <artifactId>aip-flow-plugin</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.metateam.biggroup</groupId>
                <artifactId>aip-designcanvas</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.metateam.biggroup</groupId>
                <artifactId>aip-blueprint</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.metateam.biggroup</groupId>
                <artifactId>aip-testplatform</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.metateam.biggroup</groupId>
                <artifactId>aip-runtime</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.metateam.biggroup</groupId>
                <artifactId>aip-biggroup-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.metateam.biggroup</groupId>
                <artifactId>aip-sysmanager</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.metateam.biggroup</groupId>
                <artifactId>aip-monitor</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.metateam.biggroup</groupId>
                <artifactId>aip-manager-log</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.metateam.biggroup</groupId>
                <artifactId>aip-devops-docker</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.metateam.biggroup</groupId>
                <artifactId>aip-gateway</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.metateam.biggroup</groupId>
                <artifactId>aip-bi-workbench</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 以下为项目中使用但父POM未声明的依赖版本管理 -->
            <!--
                继承自父POM的依赖包括:
                - com.metateam.icarus:icarus-springboot-base
                - com.metateam.icarus:icarus-springboot-web
                - com.metateam.icarus:icarus-llm-prompt
                - com.metateam.icarus:icarus-llm-client
                - com.metateam.icarus:icarus-llm-mcp
                - com.metateam.icarus:icarus-llm-agent
                - com.metateam.icarus:icarus-starter-base
                - com.metateam.icarus:icarus-starter-web
                - com.metateam.icarus:icarus-starter-vlm
                - com.metateam.icarus:icarus-starter-channel-sse
                - com.metateam.icarus:icarus-starter-channel-streamable
                - com.metateam.icarus:icarus-starter-rbac
                - com.metateam.icarus:icarus-starter-whitelist
                - com.metateam.icarus:icarus-starter-es
                - com.metateam.icarus:icarus-starter-kafka
                - com.metateam.icarus:icarus-starter-minio
                - com.metateam.icarus:icarus-common-minio
                - com.metateam.icarus:icarus-starter-redis
                - com.metateam.icarus:icarus-common-redis
                - com.metateam.icarus:icarus-starter-license
                - com.metateam.icarus:icarus-auth-license
                - com.baomidou:mybatis-plus-bom (通过import scope)
                - org.jsoup:jsoup
                - com.fasterxml.jackson.core:jackson-databind
                - com.fasterxml.jackson.module:jackson-module-jsonSchema
                - com.knuddels:jtokkit
                - org.apache.httpcomponents:httpclient
                - io.reactivex.rxjava2:rxjava
                - commons-io:commons-io
                - cn.hutool:hutool-all
                - io.minio:minio
                TODO: - org.elasticsearch.client:elasticsearch-rest-high-level-client
                - org.springframework.kafka:spring-kafka
                - io.swagger:swagger-annotations
            -->

            <!-- 其他外部依赖版本管理 -->
            <!-- 数据库驱动 -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>8.0.28</version>
            </dependency>
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>42.3.8</version>
            </dependency>
            <dependency>
                <groupId>com.oracle.database.jdbc</groupId>
                <artifactId>ojdbc11</artifactId>
                <version>23.3.0.23.09</version>
            </dependency>

            <!-- Office处理 -->
            <dependency>
                <groupId>e-iceblue</groupId>
                <artifactId>spire.office</artifactId>
                <version>10.6.0</version>
            </dependency>
            <dependency>
                <groupId>e-iceblue</groupId>
                <artifactId>spire.pdf</artifactId>
                <version>11.7.0</version>
            </dependency>

            <!-- Apache Tika -->
            <dependency>
                <groupId>org.apache.tika</groupId>
                <artifactId>tika-core</artifactId>
                <version>${tika.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tika</groupId>
                <artifactId>tika-parsers-standard-package</artifactId>
                <version>${tika.version}</version>
            </dependency>

            <!-- Apache POI -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-scratchpad</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- PDF处理 -->
            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>${pdfbox.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox-tools</artifactId>
                <version>${pdfbox.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>fontbox</artifactId>
                <version>${pdfbox.version}</version>
            </dependency>

            <!-- YAML处理 -->
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-yaml</artifactId>
                <version>2.14.2</version>
            </dependency>

            <!-- Lombok -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.26</version>
                <scope>provided</scope>
            </dependency>

            <!-- Commons -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.12.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>2.11.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>1.10.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>1.25.0</version>
            </dependency>

            <!-- 其他工具库 -->
            <dependency>
                <groupId>org.dom4j</groupId>
                <artifactId>dom4j</artifactId>
                <version>2.1.3</version>
            </dependency>
            <dependency>
                <groupId>org.tukaani</groupId>
                <artifactId>xz</artifactId>
                <version>1.9</version>
            </dependency>
            <dependency>
                <groupId>com.googlecode.juniversalchardet</groupId>
                <artifactId>juniversalchardet</artifactId>
                <version>1.0.3</version>
            </dependency>
            <dependency>
                <groupId>com.jcraft</groupId>
                <artifactId>jsch</artifactId>
                <version>0.1.55</version>
            </dependency>
            <dependency>
                <groupId>org.java-websocket</groupId>
                <artifactId>Java-WebSocket</artifactId>
                <version>1.5.3</version>
            </dependency>
            <dependency>
                <groupId>com.sun.mail</groupId>
                <artifactId>javax.mail</artifactId>
                <version>1.6.2</version>
            </dependency>
            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>2.3.31</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jgit</groupId>
                <artifactId>org.eclipse.jgit</artifactId>
                <version>${jgit.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jgit</groupId>
                <artifactId>org.eclipse.jgit.ssh.jsch</artifactId>
                <version>${jgit.version}</version>
            </dependency>
            <dependency>
                <groupId>org.tmatesoft.svnkit</groupId>
                <artifactId>svnkit</artifactId>
                <version>1.10.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.maven.shared</groupId>
                <artifactId>maven-invoker</artifactId>
                <version>3.1.0</version>
            </dependency>
            <dependency>
                <groupId>com.vladsch.flexmark</groupId>
                <artifactId>flexmark-all</artifactId>
                <version>0.64.8</version>
            </dependency>
            <dependency>
                <groupId>net.sourceforge.nekohtml</groupId>
                <artifactId>nekohtml</artifactId>
                <version>1.9.22</version>
            </dependency>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>1.15</version>
            </dependency>

            <!-- 认证和安全 -->
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>4.4.0</version>
            </dependency>
            <dependency>
                <groupId>dev.samstevens.totp</groupId>
                <artifactId>totp</artifactId>
                <version>1.7.1</version>
            </dependency>

            <!-- 脚本引擎 -->
            <dependency>
                <groupId>org.python</groupId>
                <artifactId>jython-standalone</artifactId>
                <version>2.7.2</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-all</artifactId>
                <version>3.0.9</version>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>org.graalvm.js</groupId>
                <artifactId>js</artifactId>
                <version>${graalvm-js.version}</version>
            </dependency>
            <dependency>
                <groupId>org.graalvm.js</groupId>
                <artifactId>js-scriptengine</artifactId>
                <version>${graalvm-js.version}</version>
            </dependency>

            <!-- TODO: Elasticsearch -->
<!--            <dependency>-->
<!--                <groupId>org.elasticsearch</groupId>-->
<!--                <artifactId>elasticsearch</artifactId>-->
<!--                <version>${elasticsearch.version}</version>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>co.elastic.clients</groupId>-->
<!--                <artifactId>elasticsearch-java</artifactId>-->
<!--                <version>${elasticsearch.version}</version>-->
<!--            </dependency>-->

            <!-- 其他服务集成 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>dashscope-sdk-java</artifactId>
                <version>2.18.3</version>
            </dependency>

            <!-- PF4J插件框架 -->
            <dependency>
                <groupId>org.pf4j</groupId>
                <artifactId>pf4j</artifactId>
                <version>3.10.0</version>
            </dependency>
            <dependency>
                <groupId>org.pf4j</groupId>
                <artifactId>pf4j-spring</artifactId>
                <version>0.9.0</version>
            </dependency>

            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-boot-starter</artifactId>
                <version>3.0.0</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!-- jackson -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-jsonSchema</artifactId>
        </dependency>
    </dependencies>

    <repositories>
        <repository>
            <id>yss_releases</id>
            <name>YSS Releases Repository</name>
            <url>http://10.0.76.34:7080/repository/yss_releases/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>yss_snapshots</id>
            <name>YSS Snapshots Repository</name>
            <url>http://10.0.76.34:7080/repository/yss_snapshots/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
        <repository>
            <id>com.e-iceblue</id>
            <name>e-iceblue</name>
            <url>https://repo.e-iceblue.cn/repository/maven-public/</url>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>yss_releases</id>
            <name>YSS Releases Repository</name>
            <url>http://10.0.76.34:7080/repository/yss_releases/</url>
        </repository>

        <snapshotRepository>
            <id>yss_snapshots</id>
            <name>YSS Snapshots Repository</name>
            <url>http://10.0.76.34:7080/repository/yss_snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>