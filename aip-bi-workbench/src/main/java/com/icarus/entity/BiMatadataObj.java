package com.icarus.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 问数-元数据对象表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Getter
@Setter
@TableName("bi_matadata_obj")
@ApiModel(value = "BiMatadataObj对象", description = "问数-元数据对象表")
public class BiMatadataObj implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("元数据CODE")
    private String code;

    @ApiModelProperty("元数据名称")
    private String name;

    @ApiModelProperty("对象类型枚举(表-TABLE, 字段-FIELD, 键-KEY)")
    private String objType;

    @ApiModelProperty("对象自定义属性(json)")
    private String attributes;

    @ApiModelProperty("关联ERD的ID")
    private Integer erdId;

    @ApiModelProperty("对象简单描述")
    private String description;

    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
