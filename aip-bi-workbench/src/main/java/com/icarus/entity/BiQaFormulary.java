package com.icarus.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Getter
@Setter
@TableName("bi_qa_formulary")
@ApiModel(value = "BiQaFormulary对象", description = "")
public class BiQaFormulary implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("原始问题")
    private String question;

    @ApiModelProperty("涉及表格图信息(存前端渲染对象)")
    private String tabsEnvolved;

    @ApiModelProperty("生成的SQL")
    private String sqlGenerated;

    @ApiModelProperty("查询结果")
    private String queryResult;

    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty("所属目录(保留字段)")
    private String directoryId;

    @ApiModelProperty("提问用户(保留字段)")
    private String userId;
}
