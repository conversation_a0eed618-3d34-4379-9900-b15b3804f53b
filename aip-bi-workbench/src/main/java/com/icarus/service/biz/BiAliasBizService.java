package com.icarus.service.biz;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.entity.BiAlias;
import com.icarus.service.IBiAliasService;
import jakarta.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 问数-别名表 业务服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Slf4j
@Service
public class BiAliasBizService {

    @Resource
    private IBiAliasService biAliasService;

    public void saveAlias(BiAlias req) {
        validAliasListJson(req.getAliasListJson());
        LocalDateTime now = LocalDateTime.now();
        req.setAliasListJson(req.getAliasListJson());
        req.setCreateTime(now);
        req.setUpdateTime(now);
        req.setIfDeleted(false);
        biAliasService.save(req);
    }

    public void updateAlias(BiAlias req) {
        BiAlias byId = biAliasService.getById(req.getId());
        if (byId == null) {
            log.error("[别名记录更新]失败, 未找到该别名记录, req:{}", JSONUtil.toJsonStr(req));
            throw new RuntimeException("[别名记录]更新失败, 未找到该别名记录");
        }
        validAliasListJson(req.getAliasListJson());
        LocalDateTime now = LocalDateTime.now();
        req.setAliasListJson(req.getAliasListJson());
        req.setCreateTime(byId.getCreateTime());
        req.setUpdateTime(now);
        req.setIfDeleted(false);
        biAliasService.updateById(req);
    }

    private void validAliasListJson(@NotBlank(message = "[别名列表]不能为空") String aliasListJson) {
        try {
            aliasList2Json(aliasJson2List(aliasListJson));
        } catch (Exception e) {
            log.error("[别名列表JSON]格式有误, aliasListJson:{}", aliasListJson);
            throw new RuntimeException("[别名列表JSON]格式有误", e);
        }
    }

    private String aliasList2Json(List<String> aliasList) {
        try {
            return JSONUtil.toJsonStr(aliasList);
        } catch (Exception e) {
            log.error("[别名列表转JSON]失败, list:{}", aliasList);
            throw new RuntimeException("[别名列表转JSON]失败", e);
        }
    }

    private List<String> aliasJson2List(String aliasList) {
        try {
            return JSONUtil.toList(aliasList, String.class);
        } catch (Exception e) {
            log.error("[别名JSON转列表]失败, JsonStr:{}", aliasList);
            throw new RuntimeException("[别名JSON转列表]失败", e);
        }
    }

    public BiAlias getAliasByFullName(String fullName) {
        BiAlias one = biAliasService.lambdaQuery()
                .eq(BiAlias::getFullName, fullName)
                .eq(BiAlias::getIfDeleted, false)
                .one();
        if (one == null) {
            log.error("[根据全称获取别名配置]失败, 未找到该别名记录, fullName:{}", fullName);
            throw new RuntimeException("[根据全称获取别名配置]失败, 未找到该别名记录");
        }
        return one;
    }

    public Page<BiAlias> pageQueryAlias(int currentPage, int pageSize, String fullName, String aliasNames) {
        return biAliasService.lambdaQuery()
                .eq(BiAlias::getIfDeleted, false)
                .like(StrUtil.isNotBlank(fullName), BiAlias::getFullName, fullName)
                .like(StrUtil.isNotBlank(aliasNames), BiAlias::getAliasListJson, aliasNames)
                .page(new Page<>(currentPage, pageSize));
    }

    public void disableAlias(Integer id) {
        BiAlias byId = biAliasService.getById(id);
        if (byId == null) {
            log.error("[禁用别名]失败, 未找到该别名记录, id:{}", id);
            throw new RuntimeException("[禁用别名]失败, 未找到该别名记录");
        }
        byId.setIfDeleted(true);
        byId.setUpdateTime(LocalDateTime.now());
        biAliasService.updateById(byId);
    }

    public void activeAlias(Integer id) {
        BiAlias byId = biAliasService.getById(id);
        if (byId == null) {
            log.error("[启用别名]失败, 未找到该别名记录, id:{}", id);
            throw new RuntimeException("[启用别名]失败, 未找到该别名记录");
        }
        byId.setIfDeleted(false);
        byId.setUpdateTime(LocalDateTime.now());
        biAliasService.updateById(byId);
    }

    public BiAlias search(@NotBlank(message = "关键字不能为空") String keyword) {
        BiAlias one = biAliasService.lambdaQuery()
                .eq(BiAlias::getFullName, keyword)
                .eq(BiAlias::getIfDeleted, false)
                .one();
        if (one != null){
            return one;
        }

        List<BiAlias> allActive = biAliasService.lambdaQuery()
                .eq(BiAlias::getIfDeleted, false)
                .list();

        if (allActive.isEmpty()){
            return null;
        }

        Map<Integer, BiAlias> idEntityMap = allActive.stream()
                .collect(Collectors.toMap(BiAlias::getId, Function.identity()));

        Map<Integer, Set<String>> idAliasSetMap = allActive.stream()
                .collect(Collectors.toMap(BiAlias::getId, a -> new HashSet<>(aliasJson2List(a.getAliasListJson()))));

        for (Map.Entry<Integer, Set<String>> entry : idAliasSetMap.entrySet()) {
            if (entry.getValue().contains(keyword)) {
                return idEntityMap.get(entry.getKey());
            }
        }
        return null;
    }
}
