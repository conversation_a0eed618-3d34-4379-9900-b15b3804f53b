package com.icarus.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 系统性能指标实体类
 * 用于存储系统各项性能指标的数据
 */
@Data
@TableName("design_system_metrics")
public class SystemMetrics {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * CPU使用率
     * 以百分比表示，范围0-100
     */
    private Double cpuUsage;
    
    /**
     * 已使用内存大小
     * 单位：字节
     */
    private Double memoryUsed;
    
    /**
     * 总内存大小
     * 单位：字节
     */
    private Double memoryTotal;
    
    /**
     * 磁盘读取速率
     * 单位：字节/秒
     */
    private Double diskIoRead;
    
    /**
     * 磁盘写入速率
     * 单位：字节/秒
     */
    private Double diskIoWrite;
    
    /**
     * 网络接收速率
     * 单位：字节/秒
     */
    private Double networkReceived;
    
    /**
     * 网络发送速率
     * 单位：字节/秒
     */
    private Double networkSent;
    
    /**
     * 系统负载
     * 表示系统在过去1分钟内的平均负载
     */
    private Double systemLoad;
    
    /**
     * 当前运行的进程数量
     */
    private Integer processCount;
    
    /**
     * 已使用的磁盘空间
     * 单位：字节
     */
    private Double diskUsed;
    
    /**
     * 总磁盘空间
     * 单位：字节
     */
    private Double diskTotal;
    
    /**
     * 当前数据库活动连接数
     */
    private Double dbConnections;
    
    /**
     * 数据库每秒查询数
     * Queries Per Second
     */
    private Double dbQps;
    
    /**
     * 数据库每秒事务数
     * Transactions Per Second
     */
    private Double dbTps;

    /**
     * 网络延迟
     * 单位：毫秒
     */
    private Double networkLatency;


    /**
     * 数据采集时间
     * 记录该条性能指标的采集时间点
     */
    private Date collectTime;
} 