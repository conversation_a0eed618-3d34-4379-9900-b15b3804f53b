package com.icarus.dto;

import lombok.Data;
import java.util.List;

@Data
public class MetricsDTO {
    private List<CpuMetrics> cpu;
    private List<MemoryMetrics> memory;
    private List<DiskIoMetrics> diskIo;
    private List<NetworkMetrics> network;
    private List<DbMetrics> db;

    @Data
    public static class CpuMetrics {
        private Double cpuUsage;
        private Double systemLoad;
        private Integer processCount;
        private String collectTime;
    }

    @Data
    public static class MemoryMetrics {
        private Double memoryUsed;
        private Double memoryTotal;
        private String collectTime;
    }

    @Data
    public static class DiskIoMetrics {
        private Double diskIoRead;
        private Double diskIoWrite;
        private Double diskUsed;
        private Double diskTotal;
        private String collectTime;
    }

    @Data
    public static class NetworkMetrics {
        private Double networkReceived;
        private Double networkSent;
        private Double networkLatency;
        private String collectTime;
    }

    @Data
    public static class DbMetrics {
        private Double dbConnections;
        private Double dbQps;
        private Double dbTps;
        private String collectTime;
    }
} 