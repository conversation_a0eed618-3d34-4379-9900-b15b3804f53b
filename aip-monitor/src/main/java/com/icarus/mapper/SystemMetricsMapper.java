package com.icarus.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.icarus.entity.SystemMetrics;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Date;

/**
 * 系统性能指标数据访问接口
 */
@Mapper
public interface SystemMetricsMapper extends BaseMapper<SystemMetrics> {

    /**
     * 查询前一小时内每10分钟的性能指标
     * 采集时间显示格式：YYYY-MM-DD HH24:MI:SS
     */
    @Select("SELECT DISTINCT ON (date_trunc('hour', collect_time) + " +
            "INTERVAL '10 min' * (date_part('minute', collect_time)::integer / 10)) " +
            "id, cpu_usage, memory_used, memory_total, disk_io_read, " +
            "disk_io_write, network_received, network_sent, system_load, " +
            "process_count, disk_used, disk_total, db_connections, db_qps, db_tps, " +
            "to_timestamp(to_char(collect_time, 'YYYY-MM-DD HH24:MI:SS'), 'YYYY-MM-DD HH24:MI:SS')::timestamp as collect_time,network_latency " +
            "FROM design_system_metrics " +
            "WHERE collect_time >= #{startTime} " +
            "AND collect_time <= #{endTime} " +
            "AND date_part('minute', collect_time)::integer % 10 = 0 " +
            "ORDER BY date_trunc('hour', collect_time) + " +
            "INTERVAL '10 min' * (date_part('minute', collect_time)::integer / 10), " +
            "collect_time DESC")
    List<SystemMetrics> getLastHourMetrics(Date startTime, Date endTime);

    /**
     * 查询前24小时内每4小时的性能指标（共6条数据）
     * 采集时间显示格式：YYYY-MM-DD HH24:MI:SS
     */
    @Select("SELECT DISTINCT ON (date_trunc('hour', collect_time)) " +
            "id, cpu_usage, memory_used, memory_total, disk_io_read, " +
            "disk_io_write, network_received, network_sent, system_load, " +
            "process_count, disk_used, disk_total, db_connections, db_qps, db_tps, network_latency, " +
            "to_timestamp(to_char(collect_time, 'YYYY-MM-DD HH24:MI:SS'), 'YYYY-MM-DD HH24:MI:SS')::timestamp as collect_time " +
            "FROM design_system_metrics " +
            "WHERE collect_time >= #{startTime} " +
            "AND collect_time <= #{endTime} " +
            "AND date_part('hour', collect_time)::integer % 4 = 0 " +
            "AND date_part('minute', collect_time) = 0 " +
            "ORDER BY date_trunc('hour', collect_time), collect_time DESC " +
            "LIMIT 6")
    List<SystemMetrics> getLast24HoursMetrics(Date startTime, Date endTime);

    /**
     * 查询前七天内每天的性能指标
     * 采集时间显示格式：YYYY-MM-DD
     */
    @Select("SELECT DISTINCT ON (date_trunc('day', collect_time)) " +
            "id, cpu_usage, memory_used, memory_total, disk_io_read, " +
            "disk_io_write, network_received, network_sent, system_load, " +
            "process_count, disk_used, disk_total, db_connections, db_qps, db_tps, network_latency, " +
            "to_timestamp(to_char(collect_time, 'YYYY-MM-DD'), 'YYYY-MM-DD')::timestamp as collect_time " +
            "FROM design_system_metrics " +
            "WHERE collect_time >= #{startTime} " +
            "AND collect_time <= #{endTime} " +
            "AND date_part('hour', collect_time) = 0 " +
            "AND date_part('minute', collect_time) = 0 " +
            "ORDER BY date_trunc('day', collect_time), collect_time DESC")
    List<SystemMetrics> getLastWeekMetrics(Date startTime, Date endTime);

    /**
     * 删除指定日期之前的性能指标数据
     */
    @Delete("DELETE FROM design_system_metrics WHERE collect_time < #{date}")
    int deleteMetricsBeforeDate(@Param("date") Date date);
} 