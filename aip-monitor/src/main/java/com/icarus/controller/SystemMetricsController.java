package com.icarus.controller;

import com.icarus.common.rest.BaseResponse;
import com.icarus.dto.MetricsDTO;
import com.icarus.entity.SystemMetrics;
import com.icarus.service.SystemMetricsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 系统性能指标控制器
 */
@Log4j2
@RestController
@RequestMapping("/aip/metrics")
@RequiredArgsConstructor
public class SystemMetricsController {

    private final SystemMetricsService systemMetricsService;

    @GetMapping("/hour")
    public BaseResponse<MetricsDTO> getLastHourMetrics() {
        try {
            return new BaseResponse<>(200, "查询成功", systemMetricsService.getLastHourMetrics());
        }catch (Exception e) {
            log.error("获取监控指标项失败", e);
            return BaseResponse.serviceError("获取监控指标项失败: " + e.getMessage());
        }
    }

    @GetMapping("/day")
    public BaseResponse<MetricsDTO> getLast24HoursMetrics() {
        try {
            return new BaseResponse<>(200, "查询成功",systemMetricsService.getLast24HoursMetrics());
        }catch (Exception e) {
            log.error("获取监控指标项失败", e);
            return BaseResponse.serviceError("获取监控指标项失败: " + e.getMessage());
        }
    }

    @GetMapping("/week")
    public BaseResponse<MetricsDTO> getLastWeekMetrics() {
        try {
            return new BaseResponse<>(200, "查询成功",systemMetricsService.getLastWeekMetrics());
        }catch (Exception e) {
          log.error("获取监控指标项失败", e);
          return BaseResponse.serviceError("获取监控指标项失败: " + e.getMessage());
        }
    }
} 