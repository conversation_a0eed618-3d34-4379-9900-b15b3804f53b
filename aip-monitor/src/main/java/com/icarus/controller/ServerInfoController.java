package com.icarus.controller;

import com.icarus.common.rest.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.net.InetAddress;
import java.util.HashMap;
import java.util.Map;

/**
 * 服务器信息控制器
 * @ClassName ServerInfoController
 * <AUTHOR>
 * @Date 2025/3/22
 * @Version 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/aip/monitor/server")
public class ServerInfoController {

    @Autowired
    private Environment environment;
    @Value("${ip.address}")
    private  String ipAddress;
    /**
     * 获取服务器基本信息
     * @return 服务器信息
     */
    @GetMapping("/info")
    public BaseResponse<Map<String, String>> getServerInfo() {
        try {
            Map<String, String> serverInfo = new HashMap<>();
            
            // 获取服务器名称
            serverInfo.put("serverName", System.getProperty("user.name"));
            
            // 获取操作系统信息
            serverInfo.put("osName", System.getProperty("os.name"));
            serverInfo.put("osVersion", System.getProperty("os.version"));

            // 解析IP和端口
//          String ip = InetAddress.getLocalHost().getHostAddress();
            //使用配置文件中配置的IP
            String ip = ipAddress;
            serverInfo.put("serverIp", ip);
            String port = environment.getProperty("server.port", "8080");
            serverInfo.put("serverPort", port);

            // 获取系统架构
            serverInfo.put("osArch", System.getProperty("os.arch"));
            
            log.info("获取服务器信息成功: {}", serverInfo);
            return new BaseResponse<>(200, "查询成功", serverInfo);
            
        } catch (Exception e) {
            log.error("获取服务器信息失败", e);
            return BaseResponse.serviceError("获取服务器信息失败: " + e.getMessage());
        }
    }
} 