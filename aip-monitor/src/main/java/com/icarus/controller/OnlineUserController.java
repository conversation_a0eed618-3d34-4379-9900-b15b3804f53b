package com.icarus.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.icarus.common.redis.RedisUtil;
import com.icarus.common.rest.BaseResponse;
import com.icarus.constant.CommonConstant;
import com.icarus.entity.DesignSysUser;
import com.icarus.service.DesignSysUserService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 在线用户监控控制器
 * @ClassName OnlineUserController
 * <AUTHOR>
 * @Date 2025/3/22
 * @Version 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/aip/monitor/online")
@RequiredArgsConstructor
public class OnlineUserController {

    private final RedisUtil redisUtil;
    private final DesignSysUserService designSysUserService;

    @Data
    public static class BatchDeleteRequest {
        private List<String> userIds;
    }

    /**
     * 批量删除用户token缓存
     * @param request 包含用户ID列表的请求对象
     * @return 处理结果
     */
    @DeleteMapping("/batchDeleteCache")
    public BaseResponse<Void> batchDeleteUserCache(@RequestBody BatchDeleteRequest request) {
        try {
            if (request == null || request.getUserIds() == null || request.getUserIds().isEmpty()) {
                return BaseResponse.serviceError("用户ID列表不能为空");
            }

            List<String> userIds = request.getUserIds();
            log.info("开始批量删除用户token缓存: userIds={}", userIds);
            
            // 批量删除token
            for (String userId : userIds) {
                String tokenKey = CommonConstant.PREFIX_USER_TOKEN + userId;
                redisUtil.del(tokenKey);
                log.debug("删除用户token缓存: userId={}", userId);
            }

            log.info("批量删除用户token缓存完成: 处理数量={}", userIds.size());
            return new BaseResponse<>(200, "处理成功");
        } catch (Exception e) {
            log.error("批量删除用户token缓存失败: request={}", request, e);
            return BaseResponse.serviceError("处理失败: " + e.getMessage());
        }
    }


    /**
     * 查询在线用户列表
     * @param username 用户名(可选,支持模糊查询)
     * @param status 用户状态(1-正常,2-冻结)
     * @return 在线用户列表
     */
    @GetMapping("/list")
    public BaseResponse<List<DesignSysUser>> listOnlineUsers(
            @RequestParam(required = false) String username,
            @RequestParam(required = false) Integer status) {
        try {
            // step.1 获取Redis中所有在线用户的token key
            Set<String> keys = redisUtil.keys(CommonConstant.PREFIX_USER_TOKEN + "*");
            if (keys == null || keys.isEmpty()) {
                return new BaseResponse<>(200, "暂无在线用户", Collections.emptyList());
            }

            // step.2 提取用户名主键列表
            List<String> ids = keys.stream()
                    .map(key -> key.replace(CommonConstant.PREFIX_USER_TOKEN, ""))
                    .collect(Collectors.toList());

            // step.3 构建查询条件
            LambdaQueryWrapper<DesignSysUser> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(DesignSysUser::getId, ids);

            // step.4 添加可选的过滤条件
            if (username != null && !username.trim().isEmpty()) {
                queryWrapper.like(DesignSysUser::getUsername, username.trim());
            }
            if (status != null) {
                queryWrapper.eq(DesignSysUser::getStatus, status);
            }

            // step.5 查询用户信息并返回
            List<DesignSysUser> users = designSysUserService.list(queryWrapper);
            return new BaseResponse<>(200, "查询成功", users);

        } catch (Exception e) {
            log.error("查询在线用户失败", e);
            return BaseResponse.serviceError("查询在线用户失败: " + e.getMessage());
        }
    }


    /**
     * 获取当前在线人数
     * @return 在线人数统计
     */
    @GetMapping("/count")
    public BaseResponse<Integer> countOnlineUsers() {
        try {
            Set<String> keys = redisUtil.keys(CommonConstant.PREFIX_USER_TOKEN + "*");
            int count = keys != null ? keys.size() : 0;
            return new BaseResponse<>(200, "查询成功", count);
        } catch (Exception e) {
            log.error("查询在线人数失败", e);
            return BaseResponse.serviceError("查询在线人数失败: " + e.getMessage());
        }
    }

} 