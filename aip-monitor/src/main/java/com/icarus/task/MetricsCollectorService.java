package com.icarus.task;

import com.icarus.config.DatabaseConfig;
import com.icarus.config.SSHConfig;
import com.icarus.entity.SystemMetrics;
import com.icarus.mapper.SystemMetricsMapper;
import com.jcraft.jsch.ChannelExec;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.Calendar;
import java.util.Date;
import java.util.Properties;

/**
 * 系统性能指标采集服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MetricsCollectorService {

    private final SystemMetricsMapper systemMetricsMapper;
    private final SSHConfig sshConfig;  // 使用配置类注入
    private final DatabaseConfig dbConfig;  // 注入数据库配置

    // 用于计算网络速率
    private long lastNetworkReceived = 0;
    private long lastNetworkSent = 0;
    private long lastNetworkTime = System.currentTimeMillis();

    // 用于计算磁盘IO速率
    private long lastDiskRead = 0;
    private long lastDiskWrite = 0;
    private long lastDiskTime = System.currentTimeMillis();

    @Value("${monitor.io.exec}")
    private String collectDiskIoMetricsCommand;

    //    @Scheduled(fixedRateString = "${monitor.scheduled.fixedRate:60000}") // 执行频率/单位毫秒
    public void collectMetrics() {
        try {
            JSch jsch = new JSch();
            Session session = jsch.getSession(
                    sshConfig.getUsername(),
                    sshConfig.getHost(),
                    sshConfig.getPort()
            );
            session.setPassword(sshConfig.getPassword());

            Properties config = new Properties();
            config.put("StrictHostKeyChecking", "no");
            session.setConfig(config);
            session.connect();

            SystemMetrics metrics = new SystemMetrics();

            // CPU使用率
            collectCpuMetrics(session, metrics);

            // 内存使用情况
            collectMemoryMetrics(session, metrics);

            // 磁盘IO情况
            collectDiskIoMetrics(session, metrics);

            // 网络流量
            collectNetworkMetrics(session, metrics);

            // 系统负载
            collectSystemLoadMetrics(session, metrics);

            // 进程数量
            collectProcessMetrics(session, metrics);

            // 磁盘空间使用情况
            collectDiskSpaceMetrics(session, metrics);

            // 数据库性能指标
            collectDatabaseMetrics(metrics);

            // 采集网络延迟
            collectNetworkLatency(session, metrics);

            // 采集时间
            metrics.setCollectTime(new Date());

            // 保存数据
            systemMetricsMapper.insert(metrics);

            session.disconnect();
        } catch (Exception e) {
            log.error("采集系统指标失败", e);
        }
    }

    /**
     * 采集CPU使用率
     */
    private void collectCpuMetrics(Session session, SystemMetrics metrics) throws Exception {
        String command = "top -bn1 | grep 'Cpu(s)' | awk '{print $2}'";
        String result = executeCommand(session, command);
        metrics.setCpuUsage(Double.parseDouble(result));
    }

    /**
     * 采集内存使用情况
     */
    private void collectMemoryMetrics(Session session, SystemMetrics metrics) throws Exception {
        // 获取总内存
        String totalCommand = "free -b | grep 'Mem:' | awk '{print $2}'";
        String totalResult = executeCommand(session, totalCommand);
        metrics.setMemoryTotal(Double.parseDouble(totalResult));

        // 获取已用内存
        String usedCommand = "free -b | grep 'Mem:' | awk '{print $3}'";
        String usedResult = executeCommand(session, usedCommand);
        metrics.setMemoryUsed(Double.parseDouble(usedResult));
    }

    /**
     * 采集磁盘IO情况
     */
    private void collectDiskIoMetrics(Session session, SystemMetrics metrics) throws Exception {
        String command = collectDiskIoMetricsCommand;
        String result = executeCommand(session, command);
        log.info("采集磁盘IO情况：{}", result);
        String[] parts = result.trim().split("\\s+");

        long currentTime = System.currentTimeMillis();
        long totalBytesRead = Long.parseLong(parts[0]);
        long totalBytesWritten = Long.parseLong(parts[1]);

        // 计算速率（字节/秒）
        double timeDiff = (currentTime - lastDiskTime) / 1000.0;
        if (lastDiskTime > 0) {
            metrics.setDiskIoRead((double) (totalBytesRead - lastDiskRead) / timeDiff);
            metrics.setDiskIoWrite((double) (totalBytesWritten - lastDiskWrite) / timeDiff);
        }

        lastDiskRead = totalBytesRead;
        lastDiskWrite = totalBytesWritten;
        lastDiskTime = currentTime;
    }

    /**
     * 采集网络流量
     */
    private void collectNetworkMetrics(Session session, SystemMetrics metrics) throws Exception {
        try {
            // 首先获取默认网络接口名称
            String getInterfaceCommand = "ip route | grep default | awk '{print $5}' | head -n1";
            String networkInterface = executeCommand(session, getInterfaceCommand);

            if (networkInterface == null || networkInterface.trim().isEmpty()) {
                log.warn("未能获取到默认网络接口名称，尝试使用备选命令");
                // 备选方案：获取第一个活动的网络接口
                getInterfaceCommand = "ip link show up | grep -v 'lo:' | head -n1 | cut -d: -f2 | awk '{print $1}'";
                networkInterface = executeCommand(session, getInterfaceCommand);
            }

            if (networkInterface == null || networkInterface.trim().isEmpty()) {
                log.error("无法获取网络接口名称");
                metrics.setNetworkReceived(0.0);
                metrics.setNetworkSent(0.0);
                return;
            }

            networkInterface = networkInterface.trim();
            log.debug("使用网络接口: {}", networkInterface);

            // 使用获取到的网络接口名称查询流量数据
            String command = String.format("cat /proc/net/dev | grep '%s:' | awk '{print $2, $10}'", networkInterface);
            String result = executeCommand(session, command);

            if (result != null && !result.trim().isEmpty()) {
                String[] parts = result.trim().split("\\s+");
                if (parts.length >= 2) {
                    long currentTime = System.currentTimeMillis();
                    long totalBytesReceived = Long.parseLong(parts[0]);
                    long totalBytesSent = Long.parseLong(parts[1]);

                    // 计算速率（字节/秒）
                    double timeDiff = (currentTime - lastNetworkTime) / 1000.0;
                    if (lastNetworkTime > 0 && timeDiff > 0) {
                        double receivedRate = (totalBytesReceived - lastNetworkReceived) / timeDiff;
                        double sentRate = (totalBytesSent - lastNetworkSent) / timeDiff;

                        // 确保速率不为负数
                        metrics.setNetworkReceived(Math.max(0, receivedRate));
                        metrics.setNetworkSent(Math.max(0, sentRate));

                        log.debug("网络接收速率: {} 字节/秒, 发送速率: {} 字节/秒", receivedRate, sentRate);
                    }

                    lastNetworkReceived = totalBytesReceived;
                    lastNetworkSent = totalBytesSent;
                    lastNetworkTime = currentTime;
                } else {
                    log.error("网络流量数据格式错误: {}", result);
                    metrics.setNetworkReceived(0.0);
                    metrics.setNetworkSent(0.0);
                }
            } else {
                log.error("未获取到网络流量数据");
                metrics.setNetworkReceived(0.0);
                metrics.setNetworkSent(0.0);
            }
        } catch (Exception e) {
            log.error("采集网络流量失败", e);
            metrics.setNetworkReceived(0.0);
            metrics.setNetworkSent(0.0);
        }
    }

    /**
     * 采集系统负载
     */
    private void collectSystemLoadMetrics(Session session, SystemMetrics metrics) throws Exception {
        String command = "cat /proc/loadavg | awk '{print $1}'";
        String result = executeCommand(session, command);
        metrics.setSystemLoad(Double.parseDouble(result));
    }

    /**
     * 采集进程数量
     */
    private void collectProcessMetrics(Session session, SystemMetrics metrics) throws Exception {
        String command = "ps aux | wc -l";
        String result = executeCommand(session, command);
        // 减1是因为ps aux的输出包含一行标题
        metrics.setProcessCount(Integer.parseInt(result.trim()) - 1);
    }

    /**
     * 采集磁盘空间使用情况
     */
    private void collectDiskSpaceMetrics(Session session, SystemMetrics metrics) throws Exception {
        // 获取总空间
        String totalCommand = "df -B1 / | tail -1 | awk '{print $2}'";
        String totalResult = executeCommand(session, totalCommand);
        metrics.setDiskTotal(Double.parseDouble(totalResult));

        // 获取已用空间
        String usedCommand = "df -B1 / | tail -1 | awk '{print $3}'";
        String usedResult = executeCommand(session, usedCommand);
        metrics.setDiskUsed(Double.parseDouble(usedResult));
    }

    /**
     * 采集数据库性能指标
     */
    private void collectDatabaseMetrics(SystemMetrics metrics) {
        try {
            try (Connection conn = DriverManager.getConnection(
                    dbConfig.getUrl(),
                    dbConfig.getUsername(),
                    dbConfig.getPassword())) {

                // 获取当前连接数
                try (Statement stmt = conn.createStatement();
                     ResultSet rs = stmt.executeQuery(
                             "SELECT count(*) FROM pg_stat_activity")) {
                    if (rs.next()) {
                        metrics.setDbConnections((double) rs.getLong(1));
                    }
                }

                // 获取QPS（每秒查询数）- 修改后的SQL
                try (Statement stmt = conn.createStatement();
                     ResultSet rs = stmt.executeQuery(
                             "SELECT sum(xact_commit + xact_rollback)::float / " +
                                     "EXTRACT(EPOCH FROM (now() - min(stats_reset))) as qps " +
                                     "FROM pg_stat_database " +
                                     "WHERE datname = current_database()")) {
                    if (rs.next()) {
                        metrics.setDbQps(rs.getDouble(1));
                    }
                }

                // 获取TPS（每秒事务数）- 修改后的SQL
                try (Statement stmt = conn.createStatement();
                     ResultSet rs = stmt.executeQuery(
                             "SELECT sum(xact_commit)::float / " +
                                     "EXTRACT(EPOCH FROM (now() - min(stats_reset))) as tps " +
                                     "FROM pg_stat_database " +
                                     "WHERE datname = current_database()")) {
                    if (rs.next()) {
                        metrics.setDbTps(rs.getDouble(1));
                    }
                }
            }
        } catch (Exception e) {
            log.error("采集数据库指标失败", e);
            metrics.setDbConnections(0.0);
            metrics.setDbQps(0.0);
            metrics.setDbTps(0.0);
        }
    }

    /**
     * 执行SSH命令并返回结果
     */
    private String executeCommand(Session session, String command) throws Exception {
        ChannelExec channel = (ChannelExec) session.openChannel("exec");
        channel.setCommand(command);

        BufferedReader reader = new BufferedReader(new InputStreamReader(channel.getInputStream()));
        channel.connect();

        StringBuilder result = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            result.append(line).append("\n");
        }

        channel.disconnect();
        return result.toString().trim();
    }

    /**
     * 采集网络延迟
     */
    private void collectNetworkLatency(Session session, SystemMetrics metrics) throws Exception {
        try {
            String command = "ping -c 1 " + sshConfig.getHost() + " | grep -oP 'time=\\K[0-9.]+' || echo 0";
            log.debug("执行网络延迟检测命令: {}", command);
            String result = executeCommand(session, command);
            log.debug("网络延迟检测结果: {}", result);

            if (result != null && !result.trim().isEmpty()) {
                try {
                    double latency = Double.parseDouble(result.trim());
                    metrics.setNetworkLatency(latency);
                    log.debug("设置网络延迟值: {}", latency);
                } catch (NumberFormatException e) {
                    log.error("解析网络延迟失败: {}", result, e);
                    metrics.setNetworkLatency(0.0);
                }
            } else {
                log.warn("未获取到网络延迟数据");
                metrics.setNetworkLatency(0.0);
            }
        } catch (Exception e) {
            log.error("采集网络延迟失败", e);
            metrics.setNetworkLatency(0.0);
        }
    }

    /**
     * 每七天执行一次清理任务
     * 清理10天前的性能指标数据
     */
    @Scheduled(cron = "0 0 2 */7 * ?")  // 每7天的凌晨2点执行
    public void cleanupOldMetrics() {
        log.info("开始清理10天前的性能指标数据");
        try {
            // 计算10天前的时间
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.DAY_OF_MONTH, -10);
            Date fifteenDaysAgo = cal.getTime();

            // 执行删除操作
            int deletedCount = systemMetricsMapper.deleteMetricsBeforeDate(fifteenDaysAgo);
            
            log.info("成功清理 {} 条10天前的性能指标数据", deletedCount);
        } catch (Exception e) {
            log.error("清理性能指标数据失败", e);
        }
    }
}