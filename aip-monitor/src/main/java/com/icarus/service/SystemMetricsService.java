package com.icarus.service;

import com.icarus.dto.MetricsDTO;
import com.icarus.entity.SystemMetrics;
import com.icarus.mapper.SystemMetricsMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class SystemMetricsService {

    private final SystemMetricsMapper systemMetricsMapper;
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public MetricsDTO getLastHourMetrics() {
        Calendar cal = Calendar.getInstance();
        Date endTime = cal.getTime();
        cal.add(Calendar.HOUR, -1);
        Date startTime = cal.getTime();
        return convertToMetricsDTO(systemMetricsMapper.getLastHourMetrics(startTime, endTime));
    }

    public MetricsDTO getLast24HoursMetrics() {
        Calendar cal = Calendar.getInstance();
        Date endTime = cal.getTime();
        cal.add(Calendar.DAY_OF_MONTH, -1);
        Date startTime = cal.getTime();
        return convertToMetricsDTO(systemMetricsMapper.getLast24HoursMetrics(startTime, endTime));
    }

    public MetricsDTO getLastWeekMetrics() {
        Calendar cal = Calendar.getInstance();
        Date endTime = cal.getTime();
        cal.add(Calendar.DAY_OF_MONTH, -7);
        Date startTime = cal.getTime();
        return convertToMetricsDTO(systemMetricsMapper.getLastWeekMetrics(startTime, endTime));
    }

    private MetricsDTO convertToMetricsDTO(List<SystemMetrics> metrics) {
        MetricsDTO dto = new MetricsDTO();

        dto.setCpu(metrics.stream().map(m -> {
            MetricsDTO.CpuMetrics cpu = new MetricsDTO.CpuMetrics();
            cpu.setCpuUsage(m.getCpuUsage());
            cpu.setSystemLoad(m.getSystemLoad());
            cpu.setProcessCount(m.getProcessCount());
            cpu.setCollectTime(dateFormat.format(m.getCollectTime()));
            return cpu;
        }).collect(Collectors.toList()));

        dto.setMemory(metrics.stream().map(m -> {
            MetricsDTO.MemoryMetrics memory = new MetricsDTO.MemoryMetrics();
            memory.setMemoryUsed(m.getMemoryUsed());
            memory.setMemoryTotal(m.getMemoryTotal());
            memory.setCollectTime(dateFormat.format(m.getCollectTime()));
            return memory;
        }).collect(Collectors.toList()));

        dto.setDiskIo(metrics.stream().map(m -> {
            MetricsDTO.DiskIoMetrics diskIo = new MetricsDTO.DiskIoMetrics();
            diskIo.setDiskIoRead(m.getDiskIoRead());
            diskIo.setDiskIoWrite(m.getDiskIoWrite());
            diskIo.setDiskUsed(m.getDiskUsed());
            diskIo.setDiskTotal(m.getDiskTotal());
            diskIo.setCollectTime(dateFormat.format(m.getCollectTime()));
            return diskIo;
        }).collect(Collectors.toList()));

        dto.setNetwork(metrics.stream().map(m -> {
            MetricsDTO.NetworkMetrics network = new MetricsDTO.NetworkMetrics();
            network.setNetworkReceived(m.getNetworkReceived());
            network.setNetworkSent(m.getNetworkSent());
            network.setNetworkLatency(m.getNetworkLatency());
            network.setCollectTime(dateFormat.format(m.getCollectTime()));
            return network;
        }).collect(Collectors.toList()));

        dto.setDb(metrics.stream().map(m -> {
            MetricsDTO.DbMetrics db = new MetricsDTO.DbMetrics();
            db.setDbConnections(m.getDbConnections());
            db.setDbQps(m.getDbQps());
            db.setDbTps(m.getDbTps());
            db.setCollectTime(dateFormat.format(m.getCollectTime()));
            return db;
        }).collect(Collectors.toList()));

        return dto;
    }
} 