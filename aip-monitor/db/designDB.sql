-- 创建系统性能指标表
CREATE TABLE design_system_metrics (
  id BIGSERIAL PRIMARY KEY,                           -- 主键ID
  cpu_usage double precision DEFAULT NULL,            -- CPU使用率
  memory_used double precision DEFAULT NULL,          -- 已使用内存
  memory_total double precision DEFAULT NULL,         -- 总内存
  disk_io_read double precision DEFAULT NULL,         -- 磁盘读取速率
  disk_io_write double precision DEFAULT NULL,        -- 磁盘写入速率
  network_received double precision DEFAULT NULL,      -- 网络接收速率
  network_sent double precision DEFAULT NULL,         -- 网络发送速率
  system_load double precision DEFAULT NULL,          -- 系统负载
  process_count integer DEFAULT NULL,                 -- 进程数量
  disk_used double precision DEFAULT NULL,            -- 已使用磁盘空间
  disk_total double precision DEFAULT NULL,           -- 总磁盘空间
  db_connections double precision DEFAULT NULL,       -- 数据库连接数
  db_qps double precision DEFAULT NULL,               -- 数据库每秒查询数
  db_tps double precision DEFAULT NULL,               -- 数据库每秒事务数
  network_latency double precision DEFAULT NULL,      -- 网络延迟(ms)
  collect_time timestamp NOT NULL                     -- 数据采集时间
);

-- 创建采集时间索引
CREATE INDEX idx_collect_time ON design_system_metrics(collect_time);