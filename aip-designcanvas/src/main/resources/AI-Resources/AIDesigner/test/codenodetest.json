{"cells": [{"position": {"x": 270, "y": 180}, "size": {"width": 358, "height": 133}, "shape": "startNode", "data": {"type": "startNode", "inputs": {}, "outputs": {"commons": [], "data": [{"name": "name", "type": "String", "value": "", "description": "", "expanded": false, "children": []}, {"name": "age", "type": "String", "value": "", "description": "", "expanded": false, "children": []}]}, "properties": {}, "ports": [{"id": "1", "group": "right"}], "id": "startNode", "title": "开始节点", "icon": "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/icons/play-circle-fill.svg", "description": "配置请求的基本信息，包括请求头类型、参数等", "components": [{"type": "commonsParams", "field": "outputs"}], "showButton": false}, "id": "2de9fd14-39c7-464b-8266-946c16dee597", "zIndex": 1}, {"position": {"x": 1140, "y": 140}, "size": {"width": 358, "height": 133}, "shape": "endNode", "data": {"type": "endNode", "inputs": {}, "outputs": [{"name": "data", "type": "String", "expanded": false, "children": [], "value": "{modelResult}"}], "properties": {}, "ports": [{"id": "1", "group": "left"}], "id": "endNode", "title": "结束节点", "icon": "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/icons/stop-circle-fill.svg", "description": "结束节点设置出参", "components": [{"type": "reference", "field": "outputs", "label": "输出参数", "props": {"defaultValue": [{"name": "", "value": ""}]}}], "showButton": false}, "id": "9dcef67c-3cf9-44c2-b093-3ce9563f2cbc", "zIndex": 3}, {"position": {"x": 730, "y": 150}, "size": {"width": 358, "height": 211}, "shape": "codeNode", "icon": "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/icons/cpu-fill.svg", "data": {"type": "codeNode", "inputs": [{"name": "name", "type": "String", "value": "{data.name}"}, {"name": "age", "type": "String", "value": "{data.age}"}], "outputs": [{"name": "codeResult", "type": "String", "expanded": false, "children": [], "value": "{current.result}"}], "properties": {"code": "", "language": "python"}, "ports": [{"id": "1", "group": "left"}, {"id": "2", "group": "right"}], "id": "codeNode", "title": "代码节点", "icon": "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/icons/cpu-fill.svg", "description": "务。", "components": [{"type": "reference", "field": "inputs", "label": "输入", "props": {"placeholder": "输入或引用参数值", "defaultValue": []}}, {"type": "paramRef", "field": "code", "label": "代码"}, {"type": "resultParams", "field": "result", "label": "处理结果", "props": {"defaultParams": {"data": [{"name": "result", "type": "String", "description": "生成的内容"}]}}, "noPreview": true}, {"type": "outParams", "field": "outputs", "label": "输出参数"}], "showButton": true}, "id": "302a42e2-ed8a-4512-84ab-8f8d2e1be437", "zIndex": 4}, {"shape": "data-processing-curve", "attrs": {"line": {"targetMarker": {"width": 12, "height": 8}, "strokeDasharray": ""}}, "id": "178f5cf8-95a3-4321-ad57-25cc03bab14b", "zIndex": 100, "data": {"showButton": false}, "source": {"cell": "2de9fd14-39c7-464b-8266-946c16dee597", "port": "1"}, "target": {"cell": "302a42e2-ed8a-4512-84ab-8f8d2e1be437", "port": "1"}}, {"shape": "data-processing-curve", "attrs": {"line": {"targetMarker": {"width": 12, "height": 8}, "strokeDasharray": ""}}, "id": "88097a80-48fa-4fd2-bff2-47ece257fea5", "zIndex": 100, "data": {"showButton": false}, "source": {"cell": "302a42e2-ed8a-4512-84ab-8f8d2e1be437", "port": "2"}, "target": {"cell": "9dcef67c-3cf9-44c2-b093-3ce9563f2cbc", "port": "1"}}], "mode": "all", "id": "17666129-eb02-4d93-b6d3-75040f534fbd"}