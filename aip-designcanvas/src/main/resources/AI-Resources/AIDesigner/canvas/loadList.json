{"cells": [{"position": {"x": 60, "y": 80}, "size": {"width": 780, "height": 200}, "shape": "startNode", "id": "fc2ec7a5-05c1-447b-8e61-603b69046899", "data": {"id": "startNode", "type": "startNode", "title": "开始", "desc": "", "options": {"name": "开始", "width": 780, "height": 200, "desc": "", "inputParams": [], "outputParams": []}, "inputs": {"headers": [], "body": {"type": "application/json", "content": [{"paramsName": "modelName", "variableType": "String", "value": ""}]}, "files": []}, "outputs": [], "only": true, "compType": "startNode", "compId": "startNodeId", "disabled": false, "parentType": "CotNode", "showButton": true, "nextNodeIds": ["112"]}, "zIndex": 1, "ports": {"groups": {"custom": {"position": {"name": "absolute"}, "attrs": {"circle": {"r": 6, "magnet": true, "stroke": "#69b1ff", "fill": "#fff", "strokeWidth": 2}}}, "input-group": {"position": {"name": "absolute"}, "attrs": {"circle": {"r": 6, "magnet": true, "stroke": "#5cdbd3", "fill": "#fff", "strokeWidth": 2}}}, "output-group": {"position": {"name": "absolute"}, "attrs": {"circle": {"r": 6, "magnet": true, "stroke": "#69b1ff", "fill": "#fff", "strokeWidth": 2}}}, "top": {"position": "top", "attrs": {"circle": {"r": 6, "magnet": true, "stroke": "#69b1ff", "fill": "#fff", "strokeWidth": 2, "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 6, "magnet": true, "stroke": "#69b1ff", "fill": "#fff", "strokeWidth": 2, "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "attrs": {"circle": {"r": 6, "magnet": true, "stroke": "#69b1ff", "fill": "#fff", "strokeWidth": 2, "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 6, "magnet": true, "stroke": "#69b1ff", "fill": "#fff", "strokeWidth": 2, "style": {"visibility": "hidden"}}}}}, "items": [{"group": "right", "id": "6edafef9-0b01-43e1-b39c-2bc0b856a4d3"}]}}, {"position": {"x": 150, "y": 120}, "size": {"width": 500, "height": 200}, "view": "vue-shape-view", "shape": "LoadCanvasList", "id": "112", "data": {"id": "LoadCanvasList", "type": "LoadCanvasList", "title": "读取画布列表信息", "desc": "", "options": {"name": "读取画布列表信息", "width": 500, "height": 200, "desc": ""}, "inputs": [{"paramsName": "modelName", "type": "quote", "value": "", "quoteVariable": "modelName", "quoteNodeId": "fc2ec7a5-05c1-447b-8e61-603b69046899"}], "outputs": [{"paramsName": "res", "variableType": "String", "value": ""}], "properties": {"model": "llama3.1-8b", "promptValues": "{question}\n我给你一个名字 你猜是男孩女孩\n\n只返回 男孩或者女孩\n不要返回其它的"}, "compType": "CotLLMNode", "compId": "CotLLMNodeId", "expand": true, "parentType": "CotNode", "prevNodeIds": ["fc2ec7a5-05c1-447b-8e61-603b69046899"], "nextNodeIds": ["113"], "showButton": false}, "zIndex": 3, "ports": {"groups": {"custom": {"position": {"name": "absolute"}, "attrs": {"circle": {"r": 6, "magnet": true, "stroke": "#69b1ff", "fill": "#fff", "strokeWidth": 2}}}, "input-group": {"position": {"name": "absolute"}, "attrs": {"circle": {"r": 6, "magnet": true, "stroke": "#5cdbd3", "fill": "#fff", "strokeWidth": 2}}}, "output-group": {"position": {"name": "absolute"}, "attrs": {"circle": {"r": 6, "magnet": true, "stroke": "#69b1ff", "fill": "#fff", "strokeWidth": 2}}}, "top": {"position": "top", "attrs": {"circle": {"r": 6, "magnet": true, "stroke": "#69b1ff", "fill": "#fff", "strokeWidth": 2, "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 6, "magnet": true, "stroke": "#69b1ff", "fill": "#fff", "strokeWidth": 2, "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "attrs": {"circle": {"r": 6, "magnet": true, "stroke": "#69b1ff", "fill": "#fff", "strokeWidth": 2, "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 6, "magnet": true, "stroke": "#69b1ff", "fill": "#fff", "strokeWidth": 2, "style": {"visibility": "hidden"}}}}}, "items": [{"group": "right", "id": "978251c0-be95-486e-8ede-058421126b9f"}, {"group": "left", "id": "4cc96ea6-307b-4cb5-88ad-657e21e9eb9e"}]}}, {"position": {"x": 1280, "y": 170}, "size": {"width": 500, "height": 200}, "shape": "endNode", "id": "113", "data": {"id": "endNode", "type": "endNode", "title": "结束", "desc": "", "options": {"name": "结束", "width": 500, "height": 200, "desc": ""}, "inputs": [{"paramsName": "res", "type": "quote", "value": "", "quoteVariable": "res", "quoteNodeId": "112"}], "outputs": [{"paramsName": "res", "variableType": "String", "value": ""}], "compType": "endNode", "compId": "endNodeId", "parentType": "CotNode", "showButton": false, "prevNodesIds": ["112"]}, "ports": {"groups": {"custom": {"position": {"name": "absolute"}, "attrs": {"circle": {"r": 6, "magnet": true, "stroke": "#69b1ff", "fill": "#fff", "strokeWidth": 2}}}, "input-group": {"position": {"name": "absolute"}, "attrs": {"circle": {"r": 6, "magnet": true, "stroke": "#5cdbd3", "fill": "#fff", "strokeWidth": 2}}}, "output-group": {"position": {"name": "absolute"}, "attrs": {"circle": {"r": 6, "magnet": true, "stroke": "#69b1ff", "fill": "#fff", "strokeWidth": 2}}}, "top": {"position": "top", "attrs": {"circle": {"r": 6, "magnet": true, "stroke": "#69b1ff", "fill": "#fff", "strokeWidth": 2, "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 6, "magnet": true, "stroke": "#69b1ff", "fill": "#fff", "strokeWidth": 2, "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "attrs": {"circle": {"r": 6, "magnet": true, "stroke": "#69b1ff", "fill": "#fff", "strokeWidth": 2, "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 6, "magnet": true, "stroke": "#69b1ff", "fill": "#fff", "strokeWidth": 2, "style": {"visibility": "hidden"}}}}}, "items": [{"group": "right", "id": "e9b1f37b-c663-43f0-93b6-e7937e9e7c4b"}, {"group": "left", "id": "ae9d2c3c-e91a-4978-bbec-e5a0f854ea6c"}]}, "zIndex": 3}], "mode": "ai", "id": "c457438c-9ca5-4f47-a554-0fb0d43dbd62"}