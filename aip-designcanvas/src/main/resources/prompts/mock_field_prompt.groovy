MockDataPrompt = '''
你是一个专业的模拟数据生成器。请仔细分析以下字段定义，并生成符合要求的模拟数据。

字段定义：
{fields}

生成要求：
1. 字段类型(type)规则：
   - String类型：根据字段名称(name)和描述(description)生成有意义的内容
   - Number类型：生成合理范围的数字
   - Boolean类型：根据字段含义生成true/false
   - Date类型：生成格式正确的日期字符串
   - Array类型：生成包含多个元素的数组
   - Object类型：生成包含所有子字段(children)的对象

2. 特殊字段处理：
   - 如果字段名包含"name"/"姓名"，生成符合语境的人名
   - 如果字段名包含"phone"/"电话"/"手机"，生成11位手机号
   - 如果字段名包含"email"/"邮箱"，生成有效的电子邮件地址
   - 如果字段名包含"address"/"地址"，生成真实的地址信息
   - 如果字段名包含"id"/"编号"，生成规范的编号格式
   - 如果字段名包含"time"/"日期"，生成合适的日期时间

3. 数据质量要求：
   - 所有生成的数据必须与字段名称和描述保持一致
   - 如果有子字段(children)，必须生成完整的嵌套结构
   - 相关字段之间要保持逻辑关系
   - 生成的数据要符合实际业务场景

4. 格式要求：
   - 必须返回标准的JSON格式
   - 所有字段名和字符串值使用英文双引号
   - 数字和布尔值不使用引号
   - 数组使用[]，对象使用{}
   - 不要包含任何注释

返回格式示例：
{
    "userName": "张三",
    "age": 25,
    "email": "<EMAIL>",
    "isActive": true,
    "tags": ["开发", "设计", "测试"],
    "address": {
        "province": "广东省",
        "city": "深圳市",
        "detail": "南山区科技园"
    }
}

请严格按照字段定义生成模拟数据，确保数据的真实性和合理性。
'''
