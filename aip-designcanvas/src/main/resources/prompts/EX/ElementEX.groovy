//首先判断一个文件中，是否包含“多份”单据，如果有“多份”，判断是否“每页”是“一份”
is_multi_doc = '''
{contents}
---
以上是用 json 描述的文件内容，其中 pageRange 是内容在源文件中的页码，请仔细阅读后判断：其中是否包含多份具有相同结构的单据（一份单据可能包含多个表格）。
判断是否多份单据的逻辑如下：
这里的多份单据，仅指结构相同的多份单据。对于结构的判断，需综合考虑表格的数量、表格列名、表头信息、数据格式以及文本内容的布局等方面。若不同 pageRange 对应的内容在这些方面呈现出高度一致性，则判定为多份单据。例如，每份单据都有相同数量的表格，表格列名完全一致，数据都以相同格式呈现，文本说明部分的位置和格式也相似等。
请优先审视不同 pageRange 对应的内容。在判断时，需排除因表格内容行数过多导致的跨页情况。若存在跨页，应通过分析表格的连续性（如是否有连续的行编号、数据是否连贯等）以及文本内容与表格的关联性（如文本是否针对相邻表格进行说明）来确定是否为同一份单据的跨页。若排除跨页因素后，各页内容仍呈现出相似结构，则判定为多份单据。
对于多份单据切分方式的判断逻辑如下：
当不存在多份单据时，则将 split_by 填为 ''。
如果 pageRange 划分的页能够准确识别每页为一个单据，即各页内容在结构和语义上相对独立完整，不存在跨页关联，则将 split_by 填为 'page'。
如果存在多份单据，且不是所有单据都在一页上时，则需寻找一个切分字符串。寻找切分字符串时，应从单据内容中频繁出现且具有明显分隔作用的短字符串入手，例如一些固定的分隔符号组合（如 “---”“###” 等），或者单据中特定的标志性词汇（如 “下一份单据开始” 等）。切分字符串应满足在整个 content 内容中能够准确且唯一地将其切分成每个完整的单据，即按照该字符串切分后，每份切分后的内容都符合单据结构的定义。
请按照以下格式回复：
{
"is_multi_doc": "true/false", // 是否多份单据
"split_by": "page / 用来切分的字符串 / 空字符串（当不需要切分时）" // 多份单据时的切分方式
}
注意：
确保返回是有效的 json，不要附加任何其他内容。
'''

//通过scope分组后，用extraction_config信息来定位要的表格
Ex_locate_table = '''
【表格的描述信息】：
{extraction_configs}

【有结构化标识的数据表格定义】：
1.表格应该为结构化的二维表，用标签区分行和字段的，如果纯文本用空格、"|"、制表符等来表示的不算。
2.使用<table>标签来定义整个表格,表格中的每行数据都在单独的<tr>标签内，每个单元格都在各自的<td>标签内。
---
分析下文，找到同时符合【表格的描述信息】和【有结构化标识的数据表格定义】的结构化数据表格，从中分析出指定字段所在列的序列，并用以下格式返回：
{
    "pos_tab_idx" : "不可为空，表格在整个文本中的序号，序号从1开始，如果没找到符合条件的表格返回0"，
    "rows_used_by_table_head" : "不可为空，表头（表格中要抽取的数据之前的行都算表头）包含了几行，只可以返回1，2，3，如果表头太复杂无法准确描述就填0"，
    "cols_seq_in_pos_tab" : "不可为空，（{exHeadsNames}）这几个字段的所在列位置，第一列值为1，第二列值为2，找不到的填0，用','作为分隔符"
}

请确保：
1.返回内容为有效的json格式，不要附加额外的信息。

以下为待分析的内容：
---
{contents}
'''

//在定位表格失败或抽取发生异常时，通过LLM重建表格来尝试补强
Ex_rebuild_table_by_llm = '''
【表格的描述信息】：
{extraction_configs}
---
分析下文，依次完成以下任务：
1.找符合【表格的描述信息】的表格（注意有些表格是使用 HTML <table> 标签来表示的，有些表格则是使用纯文字表示的）
2.如果找到，则使用 HTML 的 <table>、<tr>、<td> 标签来重建这个表格（不要用<thead><th><tbody>，表头用普遍的<tr><td>表示），仅保留（{exHeadsNames}）这些字段。
3.如果没找到，返回空的<table></table>。

以下为待分析的内容：
---
{contents}
'''

Ex_scene_by_llm = '''
{contents}
---
【要抽取的要素信息】：
{ElementConfigs}
---
【可能的近义词有】：
{SynonymConfigs}
---
分析上文，从中提取【要抽取的要素信息】中列出的要素，并按以下格式返回结果，有找不到的就留空：
{
"要素名称1":"提取到的要素名称1对应的值",
"要素名称2":"提取到的要素名称2对应的值",
"要素名称3":"提取到的要素名称3对应的值",
...
}
注意事项：
1.返回结果必须是一个有效的json，不要附加任何符号。
'''

//这是兜底逻辑，也是最不靠谱的实现了，与普遍ocr+nlp差不多的效果，要尽量避免走进这个分支
Ex_scene_table_by_llm = '''
【表格的描述信息】：
{extraction_configs}

【要抽取的要素信息】：
{ElementConfigs}
---
分析下文，找到同时符合【表格的描述信息】的表格，从中提取【要抽取的要素信息】中列出的要素，如果有找不到的信息就留空：
[
{
"要素名称1":"提取到的要素名称1对应的值",
"要素名称2":"提取到的要素名称2对应的值",
"要素名称3":"提取到的要素名称3对应的值",
...
},
...
]

注意事项：
1.确保返回内容为有效的json格式，不要附加额外的信息。

以下为待分析的内容：
---
{contents}

'''
