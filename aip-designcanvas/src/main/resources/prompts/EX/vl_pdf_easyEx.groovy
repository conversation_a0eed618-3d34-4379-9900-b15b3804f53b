pdf_easyEx = '''

Your task is to perform recognition and output based on the input image in accordance with the specified requirements.
The image to be processed is as follows:
Preprocessing requirements: First, remove headers, footers, and watermark interference items from the image.
Content extraction requirements:
Maintain the top-to-bottom reading order, strictly follow the original layout of the document, and completely extract all content from the document, including titles, body text, appendices, annotations, table contents, and text in the layer below the seal.
Output all textual content in plain text format without adding any additional formatting or explanations.
Table output format requirements:
If there are table contents in the image, output each table directly in HTML format. Use <table> for tables, <tr> for table rows, <th> for header cells, and <td> for regular cells. Do not add any attributes (such as class, style, border, etc.). If the table structure is ambiguous, infer and generate standard HTML tables based on the context.
Other notes:
Ensure that no content contains the | symbol used to represent table structures.
Do not omit any content, and do not output any comments or explanations, only the extracted content.
'''