<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.icarus.mapper.CrawlerFileMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.icarus.entity.CrawlerFile">
        <id column="id" property="id" />
        <result column="task_id" property="taskId" />
        <result column="file_name" property="fileName" />
        <result column="file_type" property="fileType" />
        <result column="file_size" property="fileSize" />
        <result column="minio_url" property="minioUrl" />
        <result column="original_url" property="originalUrl" />
        <result column="title" property="title" />
        <result column="publish_time" property="publishTime" />
        <result column="source_url" property="sourceUrl" />
        <result column="extra_info" property="extraInfo" />
        <result column="content_hash" property="contentHash" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_id, file_name, file_type, file_size, minio_url, original_url, title, publish_time, 
        source_url, extra_info, content_hash, status, create_time, update_time
    </sql>

    <!-- 分页查询爬虫文件 -->
    <select id="selectPageWithParams" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM crawler_files
        <where>
            <if test="keyword != null and keyword != ''">
                AND (file_name LIKE CONCAT('%', #{keyword}, '%') OR original_url LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="taskId != null">
                AND task_id = #{taskId}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 根据任务ID查询文件列表 -->
    <select id="selectByTaskId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM crawler_files
        WHERE task_id = #{taskId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据ID列表查询文件列表 -->
    <select id="selectByIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM crawler_files
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!-- 根据标题、发布时间和配置ID查询文件列表，用于内容去重 -->
    <select id="findByTitleAndPublishTimeAndSettingsId" resultMap="BaseResultMap">
        SELECT f.*
        FROM crawler_files f
        JOIN crawler_tasks t ON f.task_id = t.id
        WHERE f.title = #{title}
        <if test="publishTime != null and publishTime != ''">
            AND DATE_FORMAT(f.publish_time, '%Y-%m-%d %H:%i:%s') = #{publishTime}
        </if>
        AND t.settings_id = #{settingsId}
    </select>
</mapper>