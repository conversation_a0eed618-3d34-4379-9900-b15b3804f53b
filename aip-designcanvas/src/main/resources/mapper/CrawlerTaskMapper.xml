<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.icarus.mapper.CrawlerTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.icarus.entity.CrawlerTask">
        <id column="id" property="id" />
        <result column="settings_id" property="settingsId" />
        <result column="batch_id" property="batchId" />
        <result column="status" property="status" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="page_count" property="pageCount" />
        <result column="file_count" property="fileCount" />
        <result column="failed_count" property="failedCount" />
        <result column="error_message" property="errorMessage" />
        <result column="text_content" property="textContent" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="last_processed_url" property="lastProcessedUrl" />
        <result column="current_page" property="currentPage" />
        <result column="processed_urls" property="processedUrls" />
        <result column="failed_files" property="failedFiles" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, settings_id, batch_id, status, start_time, end_time, page_count, file_count,
        failed_count, error_message,text_content, create_time, update_time, last_processed_url,
        current_page, processed_urls, failed_files
    </sql>

    <!-- 分页查询爬虫任务 -->
    <select id="selectPageWithParams" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM crawler_tasks
        <where>
            <if test="keyword != null and keyword != ''">
                AND (settings_id LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="settingsId != null and settingsId != '' ">
                AND settings_id = #{settingsId}
            </if>
            <if test="batchId != null and batchId != ''">
                AND batch_id = #{batchId}
            </if>
            <if test="startTime != null and startTime != ''">
                AND create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND create_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>