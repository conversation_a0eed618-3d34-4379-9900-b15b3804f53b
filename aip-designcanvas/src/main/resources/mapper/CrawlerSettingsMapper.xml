<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.icarus.mapper.CrawlerSettingsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.icarus.entity.CrawlerSettings">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="crawl_url" property="crawlUrl" />
        <result column="click_list_path" property="clickListPath" />
        <result column="next_page_path" property="nextPagePath" />
        <result column="get_title" property="getTitle" />
        <result column="get_pub_time" property="getPubTime" />
        <result column="get_content" property="getContent" />
        <result column="get_file" property="getFile" />
        <result column="iframe" property="iframe" />
        <result column="headers" property="headers" />
        <result column="proxy" property="proxy" />
        <result column="timeout" property="timeout" />
        <result column="retry_count" property="retryCount" />
        <result column="max_pages" property="maxPages" />
        <result column="main_extra_info" property="mainExtraInfo" />
        <result column="status" property="status" />
        <result column="description" property="description" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, crawl_url, click_list_path, next_page_path, get_title, get_pub_time, get_content, get_file, 
        iframe, headers, proxy, timeout, retry_count, max_pages, main_extra_info, status, description, 
        create_time, update_time
    </sql>

    <!-- 分页查询爬虫配置 -->
    <select id="selectPageWithParams" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM crawler_settings
        <where>
            <if test="keyword != null and keyword != ''">
                AND (name LIKE CONCAT('%', #{keyword}, '%') OR crawl_url LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="startTime != null and startTime != ''">
                AND create_time &gt;= CAST(#{startTime} AS timestamp)
            </if>
            <if test="endTime != null and endTime != ''">
                AND create_time &lt;= CAST(#{endTime} AS timestamp)
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>