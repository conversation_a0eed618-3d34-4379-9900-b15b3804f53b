<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.icarus.common.cotnode.ab.cmtt.vectorNode.mapper.AivecChunkMapper">

    <!-- 基本向量相似度搜索 -->
    <select id="searchSimilarVectors" resultType="com.icarus.common.cotnode.ab.cmtt.vectorNode.entity.AivecChunk">
        <![CDATA[
        SELECT *, (1-(embedding_vector <=> #{queryVector}::vector)) AS similarity,
                ROW_NUMBER() OVER (ORDER BY (1 - (embedding_vector <=> #{queryVector}::vector)) DESC) AS number
        FROM aivec_chunk
        WHERE 1=1
        ]]>
        <if test="threshold != null">
            <![CDATA[
            AND (1-(embedding_vector <=> #{queryVector}::vector)) > #{threshold}
            ]]>
        </if>
        <if test="baseId != null">
            AND knowledge_base_ids::jsonb @> jsonb_build_array(#{baseId}::text)
        </if>
        <if test="taskId != null">
            AND task_id = #{taskId}
        </if>
        <![CDATA[
        ORDER BY 1-(embedding_vector <=> #{queryVector}::vector) DESC
        LIMIT #{limit}
        ]]>
    </select>

    <!-- HNSW索引向量相似度搜索 -->
    <select id="searchSimilarVectorsWithHNSW" resultType="com.icarus.common.cotnode.ab.cmtt.vectorNode.entity.AivecChunk">
        <![CDATA[
        SELECT *
        FROM aivec_chunk
        WHERE 1=1
        ]]>
        <if test="threshold != null">
            <![CDATA[
            AND (embedding_vector <=> #{queryVector}::vector) > #{threshold}
            ]]>
        </if>
        <![CDATA[
        ORDER BY embedding_vector <=> #{queryVector}::vector DESC
        LIMIT #{limit}
        ]]>
    </select>

    <!-- IVFFlat索引向量相似度搜索 -->
    <select id="searchSimilarVectorsWithIVFFlat" resultType="com.icarus.common.cotnode.ab.cmtt.vectorNode.entity.AivecChunk">
        <![CDATA[
        SELECT *
        FROM aivec_chunk
        WHERE 1=1
        ]]>
        <if test="threshold != null">
            <![CDATA[
            AND (embedding_vector <=> #{queryVector}::vector) > #{threshold}
            ]]>
        </if>
        <![CDATA[
        ORDER BY embedding_vector <=> #{queryVector}::vector DESC
        LIMIT #{limit}
        ]]>
    </select>
    <select id="getChunksBySystemId"
            resultType="com.icarus.common.cotnode.ab.cmtt.vectorNode.entity.AivecChunk">
        <![CDATA[
        SELECT *
        FROM aivec_chunk
        WHERE knowledge_base_ids::jsonb @>  to_jsonb(#{systemId}::text)
        ]]>
    </select>

</mapper>