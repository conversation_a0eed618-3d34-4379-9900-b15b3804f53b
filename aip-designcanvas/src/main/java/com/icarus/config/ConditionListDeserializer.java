package com.icarus.config;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icarus.common.cotnode.midware.ElasticsearchNode;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * Condition列表的自定义反序列化器
 */
@Slf4j
public class ConditionListDeserializer extends JsonDeserializer<List<ElasticsearchNode.Condition>> {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public List<ElasticsearchNode.Condition> deserialize(JsonParser p, DeserializationContext ctxt)
            throws IOException {
        try {
            String value = p.getValueAsString();
            if (value == null || value.trim().isEmpty()) {
                return new ArrayList<>();
            }

            // 如果是字符串，尝试解析为JSON数组
            if (value.startsWith("[")) {
                return objectMapper.readValue(
                        value,
                        new TypeReference<List<ElasticsearchNode.Condition>>() {}
                );
            } else {
                log.warn("Conditions value is not a valid JSON array: {}", value);
                return new ArrayList<>();
            }
        } catch (Exception e) {
            log.error("Failed to deserialize conditions: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
}