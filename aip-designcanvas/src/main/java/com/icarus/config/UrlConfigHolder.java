package com.icarus.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class UrlConfigHolder {
    private static String myServerUrl;

    @Value("${ip.myServerUrl}")
    public void setMyConfigValue(String value) {
        UrlConfigHolder.myServerUrl = value;
    }

    public static String getMyServerUrl() {
        return myServerUrl;
    }
}