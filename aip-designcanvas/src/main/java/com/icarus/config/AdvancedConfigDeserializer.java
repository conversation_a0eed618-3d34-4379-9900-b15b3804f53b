package com.icarus.config;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icarus.common.cotnode.midware.ElasticsearchNode;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

/**
 * AdvancedConfig的自定义反序列化器
 */
@Slf4j
public class AdvancedConfigDeserializer extends JsonDeserializer<ElasticsearchNode.AdvancedConfig> {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public ElasticsearchNode.AdvancedConfig deserialize(JsonParser p, DeserializationContext ctxt) 
            throws IOException {
        try {
            String value = p.getValueAsString();
            if (value == null || value.trim().isEmpty()) {
                return new ElasticsearchNode.AdvancedConfig();
            }

            // 如果是字符串，尝试解析为JSON对象
            return objectMapper.readValue(value, ElasticsearchNode.AdvancedConfig.class);
        } catch (Exception e) {
            log.error("Failed to deserialize advancedConfig: {}", e.getMessage(), e);
            return new ElasticsearchNode.AdvancedConfig();
        }
    }
} 