package com.icarus.config;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Map类型的自定义反序列化器
 */
@Slf4j
public class MapDeserializer extends JsonDeserializer<Map<String, Object>> {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public Map<String, Object> deserialize(JsonParser p, DeserializationContext ctxt) 
            throws IOException {
        try {
            String value = p.getValueAsString();
            if (value == null || value.trim().isEmpty()) {
                return new HashMap<>();
            }

            // 如果是字符串，尝试解析为JSON对象
            if (value.startsWith("{")) {
                return objectMapper.readValue(
                    value,
                    new TypeReference<Map<String, Object>>() {}
                );
            } else {
                log.warn("Document value is not a valid JSON object: {}", value);
                return new HashMap<>();
            }
        } catch (Exception e) {
            log.error("Failed to deserialize document: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }
} 