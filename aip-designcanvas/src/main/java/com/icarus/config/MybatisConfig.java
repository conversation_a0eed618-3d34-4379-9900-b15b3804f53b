package com.icarus.config;

import com.icarus.common.cotnode.ab.cmtt.vectorNode.typehandler.VectorTypeHandler;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * MyBatis配置类
 * <p>
 * 用于配置MyBatis的全局设置，如注册自定义TypeHandler等
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Configuration
public class MybatisConfig implements InitializingBean {

    private final List<SqlSessionFactory> sqlSessionFactoryList;

    public MybatisConfig(List<SqlSessionFactory> sqlSessionFactoryList) {
        this.sqlSessionFactoryList = sqlSessionFactoryList;
    }

    @Override
    public void afterPropertiesSet() {
        // 注册自定义TypeHandler
        for (SqlSessionFactory sqlSessionFactory : sqlSessionFactoryList) {
            TypeHandlerRegistry registry = sqlSessionFactory.getConfiguration().getTypeHandlerRegistry();
            // 注册VectorTypeHandler
            registry.register(VectorTypeHandler.class);
        }
    }
} 