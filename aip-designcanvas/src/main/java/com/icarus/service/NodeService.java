package com.icarus.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.icarus.entity.Node;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface NodeService extends IService<Node> {
    /**
     * 导出节点信息到Excel
     * 
     * @param ids      节点ID列表
     * @param response HTTP响应对象，用于返回Excel文件
     */
    void exportToExcel(List<String> ids, HttpServletResponse response);

    /**
     * 从Excel导入节点信息
     * 
     * @param file Excel文件
     * @return 成功导入的节点数量
     */
    int importFromExcel(MultipartFile file);
}