package com.icarus.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.icarus.entity.SceneElement;
import java.util.List;

/**
 * 场景要素服务
 * <AUTHOR>
 * @date 2025-03-24
 */
public interface SceneElementService extends IService<SceneElement> {

    /**
     * 根据场景ID获取要素
     * @param sceneId 场景ID
     * @return
     */
    List<SceneElement> findScentElementBySceneId(String sceneId);

} 