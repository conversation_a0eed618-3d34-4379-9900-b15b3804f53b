package com.icarus.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.icarus.dto.CrawlerSettingsDTO;
import com.icarus.entity.CrawlerSettings;
import com.icarus.vo.CrawlerPageRequest;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 爬虫配置服务接口
 */
public interface CrawlerSettingsService extends IService<CrawlerSettings> {
    
    /**
     * 分页查询爬虫配置
     *
     * @param request 查询请求
     * @return 分页结果
     */
    Page<CrawlerSettings> page(CrawlerPageRequest request);
    
    /**
     * 获取爬虫配置详情
     *
     * @param id 配置ID
     * @return 配置详情
     */
    CrawlerSettingsDTO getDetail(Long id);
    
    /**
     * 创建爬虫配置
     *
     * @param dto 配置信息
     * @return 配置ID
     */
    Long create(CrawlerSettingsDTO dto);
    
    /**
     * 更新爬虫配置
     *
     * @param dto 配置信息
     * @return 是否成功
     */
    boolean update(CrawlerSettingsDTO dto);
    
    /**
     * 删除爬虫配置
     *
     * @param id 配置ID
     * @return 是否成功
     */
    boolean delete(Long id);
    
    /**
     * 更新爬虫配置状态
     *
     * @param id     配置ID
     * @param status 状态：inactive-禁用，active-启用
     * @return 是否成功
     */
    boolean updateStatus(Long id, String status);
    
    /**
     * 根据状态获取爬虫配置列表
     *
     * @param status 状态：inactive-禁用，active-启用
     * @return 配置列表
     */
    List<CrawlerSettingsDTO> listByStatus(String status);
    
    /**
     * 批量获取爬虫配置
     *
     * @param ids 配置ID列表
     * @return 配置列表
     */
    List<CrawlerSettingsDTO> batchGetByIds(List<Long> ids);
}