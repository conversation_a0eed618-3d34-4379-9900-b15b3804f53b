package com.icarus.service;


import java.util.Map;

/**
 * 定时调度服务接口
 */
public interface TimerSchedulerService {
    /**
     * 添加基于Cron表达式的定时任务
     * 
     * @param triggerId      触发器ID
     * @param cronExpression Cron表达式
     * @param workflowId     要执行的工作流ID
     * @param params         执行工作流时的参数
     */
    void addCronTask(String triggerId, String cronExpression, String workflowId, Map<String, Object> params);


    /**
     * 添加基于预设模式的定时任务
     * 
     * @param triggerId   触发器ID
     * @param triggerTime 触发时间（格式: HH:mm:ss）
     * @param frequency   触发频率（daily-每日, weekly-每周, monthly-每月, interval-间隔）
     * @param day         天数/日期（对于每周是星期几[1-7]，每月是日期[1-31]，间隔是间隔天数）
     * @param workflowId  要执行的工作流ID
     * @param params      执行工作流时的参数
     */
    void addPresetTask(String triggerId, String triggerTime, String frequency, int day,
            String workflowId, Map<String, Object> params);

    /**
     * 移除定时任务
     *
     * @param triggerId 触发器ID
     */
    void removeTask(String triggerId);

    /**
     * 检查定时任务是否存在
     *
     * @param triggerId 触发器ID
     * @return 是否存在
     */
    boolean exists(String triggerId);
}