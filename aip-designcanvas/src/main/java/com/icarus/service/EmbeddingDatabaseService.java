package com.icarus.service;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.icarus.entity.DesignEmbeddingDetail;
import com.icarus.entity.DesignEmbeddingFile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.sql.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

@Slf4j
@Service
public class EmbeddingDatabaseService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 插入文件信息到yml配置的数据库
     */
    public String insertFileInfo(DesignEmbeddingFile file) {
        String id = UUID.randomUUID().toString();
        String sql = "INSERT INTO design_embedding_file (id, fileName, fileUrl) VALUES (?, ?, ?)";
        
        jdbcTemplate.update(sql, 
            id,
            file.getFileName(),
            file.getFileUrl()
        );
        
        return id;
    }

    /**
     * 插入向量详情
     */
    public void insertEmbeddingDetails(Connection connection, List<DesignEmbeddingDetail> details) throws SQLException {
        String sql = "INSERT INTO design_embedding_detail (id, fileId, splitTitle, \"index\", context, embedding, model) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?)";

        final int batchSize = 100;
        int count = 0;

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            for (DesignEmbeddingDetail detail : details) {
                String id = UUID.randomUUID().toString();
                stmt.setString(1, id);
                stmt.setString(2, detail.getFileId());
                stmt.setString(3, detail.getSplitTitle());
                stmt.setInt(4, detail.getIndex() == null ? 1 : detail.getIndex());
                stmt.setString(5, detail.getContext());
                
                // 处理向量数据
                org.postgresql.util.PGobject pgObject = new org.postgresql.util.PGobject();
                pgObject.setType("vector");
                pgObject.setValue(detail.getEmbedding());
                stmt.setObject(6, pgObject);
                
                stmt.setString(7, detail.getModel());
                stmt.addBatch();
                count++;

                if (count % batchSize == 0) {
                    log.info("执行批量插入，当前处理第 {} 条记录", count);
                    stmt.executeBatch();
                    stmt.clearBatch();
                }
            }

            if (count % batchSize != 0) {
                log.info("执行最后一批插入，总共处理 {} 条记录", count);
                stmt.executeBatch();
            }
        }
        log.info("向量详情批量插入完成，共处理 {} 条记录", count);
    }


    /**
     * 根据fileId删除数据
     */
    public void deleteByFileId(Connection connection, String fileId) throws SQLException {
        // 删除detail表数据
        String detailSql = "DELETE FROM design_embedding_detail WHERE fileId = ?";
        try (PreparedStatement stmt = connection.prepareStatement(detailSql)) {
            stmt.setString(1, fileId);
            stmt.executeUpdate();
        }

        // 删除file表数据（使用yml配置的数据库）
        String fileSql = "DELETE FROM design_embedding_file WHERE id = ?";
        jdbcTemplate.update(fileSql, fileId);
    }

    /**
     * 查询向量数据
     */
    public DesignEmbeddingFile queryEmbeddingData(Connection connection, String fileId) throws SQLException {
        // 从yml配置的数据库查询文件信息
        String fileSql = "SELECT id, fileName, fileUrl FROM design_embedding_file WHERE id = ?";
        DesignEmbeddingFile file = jdbcTemplate.queryForObject(fileSql, 
            (rs, rowNum) -> {
                DesignEmbeddingFile result = new DesignEmbeddingFile();
                result.setId(rs.getString("id"));
                result.setFileName(rs.getString("fileName"));
                result.setFileUrl(rs.getString("fileUrl"));
                return result;
            }, 
            fileId
        );

        if (file != null) {
            // 从传入的数据库连接查询detail信息
            String detailSql = "SELECT id, fileId, splitTitle, index, context, embedding, model " +
                             "FROM design_embedding_detail WHERE fileId = ?";
            List<DesignEmbeddingDetail> details = new ArrayList<>();
            
            try (PreparedStatement stmt = connection.prepareStatement(detailSql)) {
                stmt.setString(1, fileId);
                try (ResultSet rs = stmt.executeQuery()) {
                    while (rs.next()) {
                        DesignEmbeddingDetail detail = new DesignEmbeddingDetail();
                        detail.setId(rs.getString("id"));
                        detail.setFileId(rs.getString("fileId"));
                        detail.setSplitTitle(rs.getString("splitTitle"));
                        detail.setIndex(rs.getInt("index"));
                        detail.setContext(rs.getString("context"));
                        detail.setEmbedding(rs.getString("embedding"));
                        detail.setModel(rs.getString("model"));
                        details.add(detail);
                    }
                }
            }
            file.setEmbeddingDetails(details);
        }

        return file;
    }

    /**
     * 查询向量数据
     * @param connection
     * @param embedding 向量数据（关键字转换成一个数组，调之前转成[1,2,3,4....]的字符串）
     */
    public List<DesignEmbeddingFile> embeddingData(Connection connection, String embedding) throws SQLException {
        // 使用子查询先获取最相近的10个文件的详细信息
        String sql = "WITH RankedDetails AS (\n" +
                "            SELECT \n" +
                "                d.id,\n" +
                "                d.fileid,\n" +
                "                d.splitTitle,\n" +
                "                d.context,\n" +
                "                d.model,\n" +
                "                d.index,\n" +
                "                d.embedding,\n" +
                "                f.fileName,\n" +
                "                f.fileUrl,\n" +
                "                ROW_NUMBER() OVER (PARTITION BY d.fileid ORDER BY d.embedding <-> ?) as rn\n" +
                "            FROM design_embedding_detail d\n" +
                "            INNER JOIN design_embedding_file f ON d.fileid = f.id\n" +
                "            ORDER BY d.embedding <-> ? \n" +
                "        )\n" +
                "        SELECT *\n" +
                "        FROM RankedDetails\n" +
                "        WHERE rn = 1  -- 每个文件只取最相似的一条记录\n" +
                "        ORDER BY embedding <-> ?\n" +
                "        LIMIT 10";

        List<DesignEmbeddingFile> result = new ArrayList<>();
        Map<String, DesignEmbeddingFile> fileMap = new HashMap<>();

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            // 设置向量参数（需要设置三次，因为SQL中有三处使用）
            org.postgresql.util.PGobject pgObject = new org.postgresql.util.PGobject();
            pgObject.setType("vector");
            pgObject.setValue(embedding);

            stmt.setObject(1, pgObject);
            stmt.setObject(2, pgObject);
            stmt.setObject(3, pgObject);

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    String fileId = rs.getString("fileid");

                    // 获取或创建文件对象
                    DesignEmbeddingFile file = fileMap.computeIfAbsent(fileId, k -> {
                        DesignEmbeddingFile newFile = new DesignEmbeddingFile();
                        newFile.setId(fileId);
                        try {
                            newFile.setFileName(rs.getString("fileName"));
                        } catch (SQLException e) {
                            throw new RuntimeException(e);
                        }
                        try {
                            newFile.setFileUrl(rs.getString("fileUrl"));
                        } catch (SQLException e) {
                            throw new RuntimeException(e);
                        }
                        newFile.setEmbeddingDetails(new ArrayList<>());
                        return newFile;
                    });

                    // 创建并添加详情对象
                    DesignEmbeddingDetail detail = new DesignEmbeddingDetail();
                    detail.setId(rs.getString("id"));
                    detail.setFileId(fileId);
                    detail.setSplitTitle(rs.getString("splitTitle"));
                    detail.setContext(rs.getString("context"));
                    detail.setModel(rs.getString("model"));
                    detail.setIndex(rs.getInt("index"));

                    file.getEmbeddingDetails().add(detail);
                }
            }
        } catch (SQLException e) {
            log.error("执行向量查询时发生错误: {}", e.getMessage(), e);
            throw e;
        }

        return new ArrayList<>(fileMap.values());
    }


    /**
     * 查询向量数据并组装多对一关系（示例）
     * @param connection
     * @param embedding 向量数据（关键字转换成一个数组，调之前转成[1,2,3,4....]的字符串）
     */
    public List<DesignEmbeddingFile> embeddingDataBak(Connection connection, String embedding) throws SQLException {
        // 查询相近的的数据
        String embeddingSql = "SELECT id, fileid, splitTitle, context, model, index FROM design_embedding_detail ORDER BY embedding <-> ? LIMIT 10";
        List<DesignEmbeddingDetail> details = new ArrayList<>();
        Set<String> fileIds = new HashSet<>();
        
        try (PreparedStatement stmt = connection.prepareStatement(embeddingSql)) {
            // 处理向量参数
            org.postgresql.util.PGobject pgObject = new org.postgresql.util.PGobject();
            pgObject.setType("vector");
            pgObject.setValue(embedding);
            stmt.setObject(1, pgObject);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    DesignEmbeddingDetail detail = new DesignEmbeddingDetail();
                    detail.setId(rs.getString("id"));
                    detail.setFileId(rs.getString("fileid"));
                    detail.setSplitTitle(rs.getString("splitTitle"));
                    detail.setContext(rs.getString("context"));
                    detail.setModel(rs.getString("model"));
                    detail.setIndex(rs.getInt("index"));
                    details.add(detail);
                    fileIds.add(detail.getFileId());
                }
            }
        } catch (SQLException e) {
            log.error("执行向量查询时发生错误: {}", e.getMessage(), e);
            throw e;
        }
        
        // 如果没有找到相关的detail数据，直接返回空列表
        if (fileIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 2. 查询相关的file数据
        // 构建IN查询的参数
        String placeholders = String.join(",", Collections.nCopies(fileIds.size(), "?"));
        String fileSql = "SELECT id, fileName, fileUrl FROM design_embedding_file WHERE id IN (" + placeholders + ")";
        
        // 使用Map存储file数据，方便后续组装
        Map<String, DesignEmbeddingFile> fileMap = new HashMap<>();
        
        try (PreparedStatement stmt = connection.prepareStatement(fileSql)) {
            // 设置IN查询的参数
            int paramIndex = 1;
            for (String fileId : fileIds) {
                stmt.setString(paramIndex++, fileId);
            }
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    DesignEmbeddingFile file = new DesignEmbeddingFile();
                    file.setId(rs.getString("id"));
                    file.setFileName(rs.getString("fileName"));
                    file.setFileUrl(rs.getString("fileUrl"));
                    // 初始化details列表
                    file.setEmbeddingDetails(new ArrayList<>());
                    fileMap.put(file.getId(), file);
                }
            }
        }

        // 3. 组装数据
        for (DesignEmbeddingDetail detail : details) {
            DesignEmbeddingFile file = fileMap.get(detail.getFileId());
            if (file != null) {
                file.getEmbeddingDetails().add(detail);
            }
        }

        // 4. 返回组装后的结果
        return new ArrayList<>(fileMap.values());
    }


} 