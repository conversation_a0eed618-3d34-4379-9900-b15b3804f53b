package com.icarus.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.icarus.entity.Model;
import com.icarus.dto.ModelRequest;
import com.icarus.dto.ModelResponse;

/**
 * 模型服务
 * 处理与大模型交互的核心逻辑
 */
public interface ModelService extends IService<Model> {
    
    /**
     * 调用大模型
     * @param request 模型请求参数
     * @return 模型响应结果
     */
    ModelResponse callModel(ModelRequest request);
} 