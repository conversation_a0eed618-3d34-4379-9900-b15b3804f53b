package com.icarus.service;

import com.icarus.entity.CrawlerFile;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 爬虫文件服务接口
 */
public interface CrawlerFileService extends IService<CrawlerFile> {

    /**
     * 根据任务ID分页查询文件列表
     *
     * @param taskId 任务ID
     * @param page 页码
     * @param size 每页大小
     * @param fileType 文件类型
     * @return 分页文件列表
     */
    Page<CrawlerFile> findByTaskId(Long taskId, int page, int size, String fileType);

    /**
     * 根据标题和发布时间和配置ID查询文件，用于去重判断
     *
     * @param title 标题
     * @param publishTime 发布时间
     * @param settingsId 配置ID
     * @return 文件列表
     */
    List<CrawlerFile> findByTitleAndPublishTimeAndSettingsId(String title, String publishTime, Long settingsId);
}