package com.icarus.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.dto.CrawlerSettingsDTO;
import com.icarus.entity.CrawlerSettings;
import com.icarus.mapper.CrawlerSettingsMapper;
import com.icarus.service.CrawlerSettingsService;
import com.icarus.vo.CrawlerPageRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 爬虫配置服务实现类
 */
@Slf4j
@Service
public class CrawlerSettingsServiceImpl extends ServiceImpl<CrawlerSettingsMapper, CrawlerSettings> implements CrawlerSettingsService {

    @Override
    public org.springframework.data.domain.Page<CrawlerSettings> page(CrawlerPageRequest request) {
        log.info("分页查询爬虫配置，参数：{}", request);
        
        // 使用MyBatis-Plus自带的分页功能
        Page<CrawlerSettings> page = new Page<>(request.getCurrent(), request.getPageSize());
        
        // 构建查询条件
        LambdaQueryWrapper<CrawlerSettings> queryWrapper = new LambdaQueryWrapper<>();
        
        // 关键字查询 - 匹配名称或URL
        if (request.getKeyword() != null && !request.getKeyword().isEmpty()) {
            queryWrapper.like(CrawlerSettings::getName, request.getKeyword())
                    .or()
                    .like(CrawlerSettings::getCrawlUrl, request.getKeyword());
        }
        
        // 状态过滤
        if (request.getStatus() != null && !request.getStatus().isEmpty()) {
            queryWrapper.eq(CrawlerSettings::getStatus, request.getStatus());
        }
        
        // 时间范围过滤
        if (request.getStartTime() != null && !request.getStartTime().isEmpty()) {
            queryWrapper.ge(CrawlerSettings::getCreateTime, request.getStartTime());
        }
        
        if (request.getEndTime() != null && !request.getEndTime().isEmpty()) {
            queryWrapper.le(CrawlerSettings::getCreateTime, request.getEndTime());
        }
        
        // 按id升序序排序
        queryWrapper.orderByAsc(CrawlerSettings::getId);
        
        // 执行分页查询 - 调用父类的page方法
        Page<CrawlerSettings> pageResult = super.page(page, queryWrapper);
        
        // 转换为Spring Data Page
        // 注意：MyBatis-Plus的页码从1开始，Spring Data的页码从0开始，需要转换
        org.springframework.data.domain.Pageable pageable =
            org.springframework.data.domain.PageRequest.of((int)(request.getCurrent() - 1), (int)request.getPageSize());
        return new PageImpl<>(pageResult.getRecords(), pageable, pageResult.getTotal());
    }

    @Override
    public CrawlerSettingsDTO getDetail(Long id) {
        log.info("获取爬虫配置详情，ID：{}", id);
        CrawlerSettings settings = getById(id);
        if (settings == null) {
            return null;
        }

        CrawlerSettingsDTO dto = new CrawlerSettingsDTO();
        BeanUtils.copyProperties(settings, dto);
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(CrawlerSettingsDTO dto) {
        log.info("创建爬虫配置，参数：{}", dto);
        CrawlerSettings settings = new CrawlerSettings();
        BeanUtils.copyProperties(dto, settings);

        // 设置默认值
        settings.setCreateTime(LocalDateTime.now());
        settings.setUpdateTime(LocalDateTime.now());
        settings.setStatus("active"); // 默认启用

        save(settings);
        return settings.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(CrawlerSettingsDTO dto) {
        log.info("更新爬虫配置，参数：{}", dto);
        CrawlerSettings settings = getById(dto.getId());
        if (settings == null) {
            return false;
        }

        BeanUtils.copyProperties(dto, settings);
        settings.setUpdateTime(LocalDateTime.now());

        return updateById(settings);
    }

    @Override
    public boolean delete(Long id) {
        return removeById(id);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(Long id, String status) {
        log.info("更新爬虫配置状态，ID：{}，状态：{}", id, status);
        CrawlerSettings settings = getById(id);
        if (settings == null) {
            return false;
        }

        settings.setStatus(status);
        settings.setUpdateTime(LocalDateTime.now());

        return updateById(settings);
    }
    
    @Override
    public List<CrawlerSettingsDTO> listByStatus(String status) {
        log.info("获取状态为 {} 的爬虫配置列表", status);
        
        // 构建查询条件
        LambdaQueryWrapper<CrawlerSettings> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CrawlerSettings::getStatus, status);
        queryWrapper.orderByDesc(CrawlerSettings::getUpdateTime);
        
        // 执行查询
        List<CrawlerSettings> settingsList = list(queryWrapper);
        
        // 转换为DTO
        return convertToDTO(settingsList);
    }
    
    @Override
    public List<CrawlerSettingsDTO> batchGetByIds(List<Long> ids) {
        log.info("批量获取爬虫配置，ID列表：{}", ids);
        
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        
        // 批量查询
        List<CrawlerSettings> settingsList = listByIds(ids);
        
        // 转换为DTO
        return convertToDTO(settingsList);
    }
    
    /**
     * 将实体列表转换为DTO列表
     *
     * @param settingsList 爬虫配置实体列表
     * @return DTO列表
     */
    private List<CrawlerSettingsDTO> convertToDTO(List<CrawlerSettings> settingsList) {
        if (CollectionUtils.isEmpty(settingsList)) {
            return Collections.emptyList();
        }
        
        return settingsList.stream().map(settings -> {
            CrawlerSettingsDTO dto = new CrawlerSettingsDTO();
            BeanUtils.copyProperties(settings, dto);
            return dto;
        }).collect(Collectors.toList());
    }
}