package com.icarus.service.impl;

import com.icarus.common.cotnode.BaseNode;
import com.icarus.common.cotnode.GitOperateNode;
import com.icarus.common.pojo.AIPFlowDefinition;
import com.icarus.common.runtime.NodeBuilder;
import com.icarus.config.GitConfig;
import com.icarus.dto.LLMRequest;
import com.icarus.dto.ModelResponse;
import com.icarus.sdk.client.utils.LLMClientUtil;
import com.icarus.service.NodeAIGenService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.maven.shared.invoker.*;
import java.io.File;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;

@Service
@Slf4j
public class NodeAIGenServiceImpl implements NodeAIGenService {


    @Autowired
    private GitConfig gitConfig;


    @Override
    public ModelResponse getNodeConfig(LLMRequest  request) {
        log.info("requestId:{},Starting to call model: {}", request.getRequestId(), request.getModelName());
        ModelResponse response = new ModelResponse();
        response.setRequestId(request.getRequestId());
        response.setModelName(request.getModelName());
        try{
            String s = LLMClientUtil.builder()
                    .modelName(request.getModelName())
                    .systemPrompt(request.getSystemPrompt())
                    .chat(request.getChat())
                    .temperature(request.getTemperature())
                    .maxTokens(request.getMaxTokens())
                    .removeThinkTAG()
                    .runChatCompletion();

            // 设置响应结果
            response.setResult(s);
            response.setStatus("success");

            log.info("Model call successful, requestId: {}", request.getRequestId());
        }catch (Exception e){
            log.error("request:{}, Model call failed: {}",request.getRequestId(), e.getMessage(), e);
            response.setError(e.getMessage());
            response.setStatus("failed");
        }
        return response;
    }

    @Override
    public void gitOperation(String operation, String commitMessage) {
        AIPFlowDefinition.Cell cell = new AIPFlowDefinition.Cell();
        cell.setShape("gitOperateNode");
        BaseNode node = NodeBuilder.build(cell);
        GitOperateNode.Properties properties = new GitOperateNode.Properties();
        BeanUtils.copyProperties(gitConfig, properties);
        properties.setOperation(operation);
        if(StringUtils.isNotBlank(commitMessage)){
            properties.setCommitMessage(commitMessage);
        }
        // 向下转型为子类
        if (node instanceof GitOperateNode) {
            GitOperateNode operateNode = (GitOperateNode) node;
            operateNode.executeGitOperation(properties);  // 成功调用子类方法
            return;
        }
        log.error(String.format("大模型生成节点[%s]代码git代码失败",operation));
    }

    /**
     * 要求环境安装maven
     * @return
     */
    @Override
    public Boolean runMaven(String pomPath) {
        InvocationRequest request = new DefaultInvocationRequest();
        request.setPomFile(new File(pomPath));
        request.setGoals(Arrays.asList("clean", "install"));

        Invoker invoker = new DefaultInvoker();

        try {
            InvocationResult result = invoker.execute(request);

            if (result.getExitCode() != 0) {
                if (result.getExecutionException() != null) {
                    throw new RuntimeException("Maven 构建失败",
                            result.getExecutionException());
                } else {
                    throw new RuntimeException("Maven 构建失败，退出码: " +
                            result.getExitCode());
                }
            }
            log.info("Maven 构建成功");

        } catch (MavenInvocationException e) {
            throw new RuntimeException("执行 Maven 命令失败", e);
        }
        return true;
    }


    public void codeAutoGenerateByLLM(){
        //1.根据配置路径拉取git代码
        String localPath = gitConfig.getLocalPath();
        if (isPathExists(localPath)){
            gitOperation("pull", StringUtils.EMPTY);
        }else {
            gitOperation("clone", StringUtils.EMPTY);
        }
        //2.生成代码
        //3.maven编译
        Boolean compileStatus = runMaven(localPath + "/pom.xml");
        //4.单元测试
        if (compileStatus){

        }
        //5.生成文档
        //git 提交
        gitOperation("add", StringUtils.EMPTY);
        gitOperation("commit", "commitMessage");
        gitOperation("push", StringUtils.EMPTY);

    }

    /**
     * 判断路径是否存在
     * @param localPath
     * @return
     */
    public boolean isPathExists(String localPath) {
        if (localPath == null || localPath.isEmpty()) {
            return false;
        }
        Path path = Paths.get(localPath);
        return Files.exists(path);
    }
}
