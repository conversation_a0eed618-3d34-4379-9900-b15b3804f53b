package com.icarus.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.icarus.entity.TimerTrigger;
import com.icarus.service.TimerSchedulerService;
import com.icarus.service.TimerTriggerDataService;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;

/**
 * 定时调度服务实现类
 */
@Service
@Slf4j
public class TimerSchedulerServiceImpl implements TimerSchedulerService {

    @Autowired
    private TimerTriggerDataService timerTriggerDataService;

    private TaskScheduler taskScheduler;
    private final Map<String, ScheduledFuture<?>> scheduledTasks = new ConcurrentHashMap<>();
    private final Map<String, TaskInfo> taskInfoMap = new ConcurrentHashMap<>();

    /**
     * 初始化调度器
     */
    @PostConstruct
    public void init() {
        log.info("初始化定时调度器");
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(10);
        scheduler.setThreadNamePrefix("workflow-timer-");
        scheduler.setWaitForTasksToCompleteOnShutdown(true);
        scheduler.setAwaitTerminationSeconds(60);
        scheduler.initialize();
        this.taskScheduler = scheduler;

        // 系统启动时加载所有启用的定时触发器
        initTimerTriggers();
    }

    /**
     * 系统启动时初始化所有启用状态的定时触发器
     */
    private void initTimerTriggers() {
        try {
            log.info("开始加载数据库中的定时触发器...");

            // 查询所有启用状态的触发器
            List<TimerTrigger> triggers = timerTriggerDataService.listEnabledTriggers();
            if (triggers == null || triggers.isEmpty()) {
                log.info("没有找到启用状态的定时触发器");
                return;
            }

            log.info("找到 {} 个启用状态的定时触发器", triggers.size());

            // 逐个初始化调度
            for (TimerTrigger trigger : triggers) {
                try {
                    // 准备参数
                    Map<String, Object> params = new HashMap<>();
                    params.put("triggerId", trigger.getId());
                    params.put("triggerName", trigger.getTriggerName());
                    params.put("userId", trigger.getUserId());

                    if ("cron".equals(trigger.getTriggerTimeType())) {
                        addCronTask(trigger.getId(), trigger.getCronExpression(), trigger.getWorkflowId(), params);
                        log.info("已调度Cron定时触发器: {}, 表达式: {}", trigger.getId(), trigger.getCronExpression());
                    } else if ("preset".equals(trigger.getTriggerTimeType())) {
                        addPresetTask(
                                trigger.getId(),
                                trigger.getTriggerTime(),
                                trigger.getFrequencyType(),
                                trigger.getFrequencyDay(),
                                trigger.getWorkflowId(),
                                params);
                        log.info("已调度预设定时触发器: {}, 时间: {}, 频率: {}",
                                trigger.getId(), trigger.getTriggerTime(), trigger.getFrequencyType());
                    }
                } catch (Exception e) {
                    log.error("初始化定时触发器失败: {}", trigger.getId(), e);
                }
            }

            log.info("定时触发器加载完成");
        } catch (Exception e) {
            log.error("初始化定时触发器时出错", e);
        }
    }

    @Override
    public void addCronTask(String triggerId, String cronExpression, String workflowId, Map<String, Object> params) {
        log.info("添加Cron定时任务，triggerId: {}, cron: {}, workflowId: {}", triggerId, cronExpression, workflowId);

        // 先移除已存在的任务
        removeTask(triggerId);

        // 创建任务信息
        TaskInfo taskInfo = new TaskInfo(workflowId, params);
        taskInfo.setCronExpression(cronExpression);

        // 创建Runnable任务
        Runnable task = createTaskRunnable(triggerId, taskInfo);

        // 调度任务
        ScheduledFuture<?> future = taskScheduler.schedule(task, new CronTrigger(cronExpression));

        // 保存任务引用
        scheduledTasks.put(triggerId, future);
        taskInfoMap.put(triggerId, taskInfo);

        // 更新数据库中的任务状态
        updateTriggerStatus(triggerId, true);
    }

    @Override
    public void addPresetTask(String triggerId, String triggerTime, String frequency, int day,
            String workflowId, Map<String, Object> params) {
        log.info("添加预设定时任务，triggerId: {}, time: {}, frequency: {}, day: {}, workflowId: {}",
                triggerId, triggerTime, frequency, day, workflowId);

        // 先移除已存在的任务
        removeTask(triggerId);

        // 将预设模式转换为Cron表达式
        String cronExpression = convertPresetToCron(triggerTime, frequency, day);
        log.info("预设时间转换为Cron表达式: {}", cronExpression);

        // 使用Cron表达式创建任务
        addCronTask(triggerId, cronExpression, workflowId, params);
    }

    @Override
    public void removeTask(String triggerId) {
        log.info("尝试移除定时任务，triggerId: {}", triggerId);

        // 取消并移除任务
        ScheduledFuture<?> future = scheduledTasks.remove(triggerId);
        if (future != null) {
            future.cancel(false);
            taskInfoMap.remove(triggerId);
            log.info("成功移除定时任务，triggerId: {}", triggerId);

            // 更新数据库中的任务状态为禁用
            updateTriggerStatus(triggerId, false);
        }
    }

    /**
     * 更新触发器状态
     * 
     * @param triggerId 触发器ID
     * @param enabled   是否启用
     */
    private void updateTriggerStatus(String triggerId, boolean enabled) {
        try {
            TimerTrigger trigger = timerTriggerDataService.getById(triggerId);
            if (trigger != null) {
                trigger.setStatus(enabled ? 1 : 0);
                trigger.setUpdateTime(LocalDateTime.now());
                timerTriggerDataService.updateById(trigger);
            }
        } catch (Exception e) {
            log.error("更新触发器状态失败: {}", triggerId, e);
        }
    }

    @Override
    public boolean exists(String triggerId) {
        return scheduledTasks.containsKey(triggerId);
    }

    /**
     * 将预设时间模式转换为Cron表达式
     * 
     * @param triggerTime 触发时间 (HH:mm:ss)
     * @param frequency   频率类型
     * @param day         天数或星期几
     * @return Cron表达式
     */
    private String convertPresetToCron(String triggerTime, String frequency, int day) {
        // 解析时间
        String[] timeParts = triggerTime.split(":");
        String hour = timeParts.length > 0 ? timeParts[0] : "0";
        String minute = timeParts.length > 1 ? timeParts[1] : "0";
        String second = timeParts.length > 2 ? timeParts[2] : "0";

        // 根据不同频率生成不同的Cron表达式
        // Cron表达式格式：秒 分 时 日 月 星期 [年]
        switch (frequency) {
            case "daily": // 每日触发
                return String.format("%s %s %s * * ?", second, minute, hour);

            case "weekly": // 每周触发，day表示星期几(1-7)
                return String.format("%s %s %s ? * %d", second, minute, hour, day);

            case "monthly": // 每月触发，day表示日期(1-31)
                return String.format("%s %s %s %d * ?", second, minute, hour, day);

            case "interval": // 间隔天数触发，day表示间隔天数
                if (day <= 0) {
                    day = 1; // 至少1天
                }
                // 这里简化处理，实际可能需要更复杂的逻辑
                return String.format("%s %s %s */%d * ?", second, minute, hour, day);

            default:
                log.warn("未知的频率类型: {}, 默认使用每日触发", frequency);
                return String.format("%s %s %s * * ?", second, minute, hour);
        }
    }

    /**
     * 创建任务运行方法
     */
    private Runnable createTaskRunnable(String triggerId, TaskInfo taskInfo) {
        return () -> {
            try {
                log.info("执行定时任务，triggerId: {}, workflowId: {}", triggerId, taskInfo.getWorkflowId());

                // 确认触发器仍处于启用状态
                TimerTrigger trigger = timerTriggerDataService.getById(triggerId);
                if (trigger == null || trigger.getStatus() == null || trigger.getStatus() != 1) {
                    log.warn("触发器已被禁用或删除，取消执行: {}", triggerId);
                    return;
                }

                // 通过HTTP POST请求调用工作流执行服务
                if (trigger.getPath() != null && !trigger.getPath().isEmpty()) {
                    try {
                        log.info("调用工作流接口: {}", trigger.getPath());

                        // 使用Hutool的HttpRequest发送POST请求
                        HttpResponse response = HttpRequest.post(trigger.getPath())
                                .contentType("application/json") // 设置Content-Type
                                .body("{}") // 空JSON请求体
                                .timeout(10000) // 设置超时时间（毫秒）
                                .execute();

                        log.info("工作流执行结果: {}, 状态码: {}",
                                response.body(), response.getStatus());
                    } catch (Exception e) {
                        log.error("调用工作流接口失败: {}", e.getMessage(), e);
                    }
                } else {
                    log.warn("触发器未配置执行路径: {}", triggerId);
                }

                // 更新最后执行时间
                trigger.setUpdateTime(LocalDateTime.now());
                timerTriggerDataService.updateById(trigger);

                log.info("定时任务执行完成，triggerId: {}", triggerId);
            } catch (Exception e) {
                log.error("定时任务执行失败，triggerId: {}", triggerId, e);
            }
        };
    }

    /**
     * 任务信息类
     */
    private static class TaskInfo {
        private final String workflowId;
        private final Map<String, Object> params;
        private String cronExpression;
        private String frequency;
        private int day;

        public TaskInfo(String workflowId, Map<String, Object> params) {
            this.workflowId = workflowId;
            this.params = params != null ? new HashMap<>(params) : new HashMap<>();
        }

        public String getWorkflowId() {
            return workflowId;
        }

        public Map<String, Object> getParams() {
            return params;
        }

        public String getCronExpression() {
            return cronExpression;
        }

        public void setCronExpression(String cronExpression) {
            this.cronExpression = cronExpression;
        }

        public String getFrequency() {
            return frequency;
        }

        public void setFrequency(String frequency) {
            this.frequency = frequency;
        }

        public int getDay() {
            return day;
        }

        public void setDay(int day) {
            this.day = day;
        }
    }
}