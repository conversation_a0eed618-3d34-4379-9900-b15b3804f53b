package com.icarus.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.entity.TimerTrigger;
import com.icarus.mapper.TimerTriggerMapper;
import com.icarus.service.TimerTriggerDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 定时触发器数据服务实现类
 * 专门处理TimerTrigger的数据库操作，避免循环依赖
 */
@Service
@Slf4j
public class TimerTriggerDataServiceImpl extends ServiceImpl<TimerTriggerMapper, TimerTrigger>
        implements TimerTriggerDataService {

    @Override
    public TimerTrigger getById(String id) {
        log.info("获取定时触发器，ID: {}", id);
        return super.getById(id);
    }

    @Override
    public boolean updateById(TimerTrigger trigger) {
        log.info("更新定时触发器，ID: {}", trigger.getId());
        return super.updateById(trigger);
    }

    @Override
    public boolean save(TimerTrigger trigger) {
        log.info("保存定时触发器");
        return super.save(trigger);
    }

    @Override
    public boolean removeById(String id) {
        log.info("删除定时触发器，ID: {}", id);
        return super.removeById(id);
    }

    @Override
    public List<TimerTrigger> listEnabledTriggers() {
        log.info("查询所有启用状态的定时触发器");
        LambdaQueryWrapper<TimerTrigger> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TimerTrigger::getStatus, 1);
        return list(wrapper);
    }

    @Override
    public List<TimerTrigger> getByWorkflowId(String workflowId) {
        log.info("根据工作流ID查询定时触发器: {}", workflowId);
        LambdaQueryWrapper<TimerTrigger> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TimerTrigger::getWorkflowId, workflowId);
        return list(wrapper);
    }

    @Override
    public Page<TimerTrigger> page(Page<TimerTrigger> page, LambdaQueryWrapper<TimerTrigger> wrapper) {
        log.info("分页查询定时触发器");
        return super.page(page, wrapper);
    }

    @Override
    public List<TimerTrigger> list(LambdaQueryWrapper<TimerTrigger> wrapper) {
        log.info("条件查询定时触发器列表");
        return super.list(wrapper);
    }
}