package com.icarus.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.dto.ModelRequest;
import com.icarus.dto.ModelResponse;
import com.icarus.entity.Model;
import com.icarus.mapper.ModelMapper;
import com.icarus.sdk.client.utils.LLMClientUtil;
import com.icarus.service.ModelService;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

@Log4j2
@Service
public class ModelServiceImpl extends ServiceImpl<ModelMapper, Model> implements ModelService {

    /**
     * 调用大模型
     * @param request 模型请求参数
     * @return 模型响应结果
     */
    @Override
    public ModelResponse callModel(ModelRequest request) {
        log.info("Starting to call model: {}", request.getModelName());
        ModelResponse response = new ModelResponse();

        try {
//            //调大模型
            String result = LLMClientUtil.builder()
                    .modelName(request.getModelName())
                    .chat(request.getPrompt())
                    .removeThinkTAG()
                    .runChatCompletion();

            // 预处理结果，确保是有效的JSON
            result = preprocessJsonResult(result);

            // 设置响应结果
            response.setResult(result);
            response.setRequestId(request.getRequestId());
            response.setModelName(request.getModelName());
            response.setStatus("success");

            log.info("Model call successful, requestId: {}", request.getRequestId());

        } catch (Exception e) {
            log.error("Model call failed: {}", e.getMessage(), e);
            response.setError(e.getMessage());
            response.setStatus("failed");
        }

        return response;
    }

    /**
     * 预处理JSON结果
     */
    private String preprocessJsonResult(String result) {
        try {
            // 1. 查找第一个 { 和最后一个 } 的位置
            int startIndex = result.indexOf("{");
            int endIndex = result.lastIndexOf("}");

            if (startIndex == -1 || endIndex == -1 || startIndex > endIndex) {
                throw new RuntimeException("Invalid JSON format in model response");
            }

            // 2. 提取JSON部分
            String jsonPart = result.substring(startIndex, endIndex + 1);

            // 3. 清理可能的非法字符
            jsonPart = jsonPart.replaceAll("[\\x00-\\x1F\\x7F]", ""); // 移除控制字符

            // 4. 处理中文引号（使用Unicode编码）
            jsonPart = jsonPart.replaceAll("[\u201C\u201D\u2018\u2019]", "\"");

            // 5. 确保属性名都是双引号
            jsonPart = jsonPart.replaceAll("([{,])\\s*([a-zA-Z_][a-zA-Z0-9_]*)\\s*:", "$1\"$2\":");

            // 6. 处理可能的转义字符
            jsonPart = jsonPart.replace("\\'", "'")
                             .replace("\\\"", "\"")
                             .replace("\\n", "\n")
                             .replace("\\r", "\r")
                             .replace("\\t", "\t");

            return jsonPart;
        } catch (Exception e) {
            log.error("Failed to preprocess JSON result: {}", e.getMessage());
            throw new RuntimeException("Failed to process model response", e);
        }
    }
}