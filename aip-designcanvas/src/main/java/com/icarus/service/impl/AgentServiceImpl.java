package com.icarus.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.controller.AgentController;
import com.icarus.entity.Agent;
import com.icarus.entity.Canvas;
import com.icarus.mapper.AgentMapper;
import com.icarus.sdk.prompt.PromptTemplate;
import com.icarus.service.AgentService;
import com.icarus.service.CanvasService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class AgentServiceImpl extends ServiceImpl<AgentMapper, Agent> implements AgentService {
    @Autowired
    private CanvasService canvasService;

    private String model = "deepseek-32B";

    @Override
    public void completions(AgentController.DebuggerContext debuggerContext) {

        Agent agent = getById(debuggerContext.getAgentId());
        String agentPrompt = agent.getPrompt();// agent的提示词

        JSONArray arr = JSONUtil.parseArray(debuggerContext.getWorkFlowIds());
        for (Object obj : arr) {
            String id = (String) obj;
            Canvas c = canvasService.getById(id);
            // 获取它的开始节点的参数，让ai生成代码进行调用

        }
        String prompt = getPrompt("", "");// 提示词模版

        // String str = LLMProviderEnum.fromModelName(model).process(model, prompt);

    }

    public String getPrompt(String... kvs) {
        PromptTemplate promptTemplate = PromptTemplate.builder("prompts/agentDebug.groovy", "prompt");

        Map<String, String> ptMap = new HashMap<>();

        for (int i = 0; i < kvs.length; i += 2) {
            ptMap.put(kvs[i], kvs[i + 1]);
        }
        return promptTemplate.format(ptMap).build();
    }
}