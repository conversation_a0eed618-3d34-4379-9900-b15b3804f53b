package com.icarus.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.entity.TimerTrigger;
import com.icarus.mapper.TimerTriggerMapper;
import com.icarus.service.TimerSchedulerService;
import com.icarus.service.TimerTriggerDataService;
import com.icarus.service.TimerTriggerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 定时触发器服务实现类
 */
@Service
@Slf4j
public class TimerTriggerServiceImpl implements TimerTriggerService {

    @Autowired
    private TimerTriggerDataService timerTriggerDataService;

    @Autowired
    private TimerSchedulerService timerSchedulerService;

    @Override
    @Transactional
    public boolean enableTrigger(String id) {
        log.info("启用定时触发器，ID: {}", id);

        // 获取触发器
        TimerTrigger trigger = timerTriggerDataService.getById(id);
        if (trigger == null) {
            log.error("定时触发器不存在，ID: {}", id);
            return false;
        }

        // 更新状态
        trigger.setStatus(1);
        trigger.setUpdateTime(LocalDateTime.now());
        boolean updated = timerTriggerDataService.updateById(trigger);

        if (updated) {
            // 重新调度
            rescheduleTrigger(id);
        }

        return updated;
    }

    @Override
    @Transactional
    public boolean disableTrigger(String id) {
        log.info("禁用定时触发器，ID: {}", id);

        // 获取触发器
        TimerTrigger trigger = timerTriggerDataService.getById(id);
        if (trigger == null) {
            log.error("定时触发器不存在，ID: {}", id);
            return false;
        }

        // 更新状态
        trigger.setStatus(0);
        trigger.setUpdateTime(LocalDateTime.now());
        boolean updated = timerTriggerDataService.updateById(trigger);

        if (updated) {
            // 移除调度
            timerSchedulerService.removeTask(id);
        }

        return updated;
    }

    @Override
    public boolean rescheduleTrigger(String id) {
        log.info("重新调度定时触发器，ID: {}", id);

        // 获取触发器
        TimerTrigger trigger = timerTriggerDataService.getById(id);
        if (trigger == null) {
            log.error("定时触发器不存在，ID: {}", id);
            return false;
        }

        // 如果状态为禁用，移除调度
        if (trigger.getStatus() != null && trigger.getStatus() == 0) {
            timerSchedulerService.removeTask(trigger.getWorkflowId());
            timerSchedulerService.removeTask(trigger.getId());
            log.info("触发器已禁用，已移除调度，ID: {}", id);
            return true;
        }

        // 准备参数
        Map<String, Object> params = new HashMap<>();
        params.put("triggerId", id);
        params.put("triggerName", trigger.getTriggerName());
        params.put("userId", trigger.getUserId());

        try {
            // 根据触发时间类型进行不同处理
            if ("cron".equals(trigger.getTriggerTimeType())) {
                // Cron表达式调度
                timerSchedulerService.addCronTask(
                        id,
                        trigger.getCronExpression(),
                        trigger.getWorkflowId(),
                        params);
            } else if ("preset".equals(trigger.getTriggerTimeType())) {
                // 预设时间调度
                timerSchedulerService.addPresetTask(
                        id,
                        trigger.getTriggerTime(),
                        trigger.getFrequencyType(),
                        trigger.getFrequencyDay(),
                        trigger.getWorkflowId(),
                        params);
            } else {
                log.error("不支持的触发时间类型: {}", trigger.getTriggerTimeType());
                return false;
            }

            log.info("定时触发器调度成功，ID: {}", id);
            return true;
        } catch (Exception e) {
            log.error("定时触发器调度失败，ID: {}", id, e);
            return false;
        }
    }

    @Override
    public List<TimerTrigger> listEnabledTriggers() {
        return timerTriggerDataService.listEnabledTriggers();
    }

    @Override
    public List<TimerTrigger> getByWorkflowId(String workflowId) {
        return timerTriggerDataService.getByWorkflowId(workflowId);
    }

    @Override
    public TimerTrigger getById(String id) {
        return timerTriggerDataService.getById(id);
    }

    @Override
    public boolean updateById(TimerTrigger entity) {
        return timerTriggerDataService.updateById(entity);
    }

    @Override
    public boolean save(TimerTrigger entity) {
        return timerTriggerDataService.save(entity);
    }

    /**
     * 删除触发器
     *
     * @param id 触发器ID
     * @return 是否成功
     */
    public boolean removeById(String id) {
        // 确保先禁用触发器的调度
        disableTrigger(id);
        return timerTriggerDataService.removeById(id);
    }
}