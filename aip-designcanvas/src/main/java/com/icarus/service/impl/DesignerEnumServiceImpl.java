package com.icarus.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.entity.DesignerEnum;
import com.icarus.mapper.DesignerEnumMapper;
import com.icarus.service.DesignerEnumService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DesignerEnumServiceImpl extends ServiceImpl<DesignerEnumMapper, DesignerEnum> implements DesignerEnumService {
    
    @Override
    public List<DesignerEnum> listByType(String type) {
        LambdaQueryWrapper<DesignerEnum> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DesignerEnum::getType, type);
        return list(wrapper);
    }
} 