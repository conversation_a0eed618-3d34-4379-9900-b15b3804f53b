package com.icarus.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.entity.CrawlerFile;
import com.icarus.mapper.CrawlerFileMapper;
import com.icarus.service.CrawlerFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 爬虫文件服务实现类
 */
@Slf4j
@Service
public class CrawlerFileServiceImpl extends ServiceImpl<CrawlerFileMapper, CrawlerFile> implements CrawlerFileService {

    /**
     * 根据任务ID分页查询文件列表
     *
     * @param taskId 任务ID
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param fileType 文件类型
     * @return 分页文件列表
     */
    @Override
    public org.springframework.data.domain.Page<CrawlerFile> findByTaskId(Long taskId, int pageNum, int pageSize, String fileType) {
        if (taskId == null) {
            return org.springframework.data.domain.Page.empty();
        }
        
        // 构建查询条件
        LambdaQueryWrapper<CrawlerFile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CrawlerFile::getTaskId, taskId);
        
        // 添加文件类型过滤
        if (StringUtils.hasText(fileType)) {
            queryWrapper.eq(CrawlerFile::getFileType, fileType);
        }
        
        // 按创建时间降序排序
        queryWrapper.orderByDesc(CrawlerFile::getCreateTime);
        
        // 执行分页查询
        Page<CrawlerFile> page = new Page<>(pageNum + 1, pageSize);  // MyBatis-Plus页码从1开始
        Page<CrawlerFile> result = this.page(page, queryWrapper);
        
        // 转换为Spring Data Page
        return new PageImpl<>(result.getRecords(), 
                             org.springframework.data.domain.PageRequest.of(pageNum, pageSize), 
                             result.getTotal());
    }

    /**
     * 根据标题和发布时间和配置ID查询文件，用于去重判断
     *
     * @param title 标题
     * @param publishTime 发布时间
     * @param settingsId 配置ID
     * @return 文件列表
     */
    @Override
    public List<CrawlerFile> findByTitleAndPublishTimeAndSettingsId(String title, String publishTime, Long settingsId) {
        if (!StringUtils.hasText(title) || !StringUtils.hasText(publishTime) || settingsId == null) {
            return List.of();
        }
        
        // 将发布时间字符串转换为LocalDateTime
        LocalDateTime pubTime = null;
        try {
            pubTime = LocalDateTime.parse(publishTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        } catch (Exception e) {
            log.warn("解析发布时间失败: {}", publishTime);
            return List.of();
        }
        
        // 构建查询条件
        LambdaQueryWrapper<CrawlerFile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CrawlerFile::getTitle, title);
        queryWrapper.eq(CrawlerFile::getPublishTime, pubTime);
        
        // 执行查询
        // 注意：由于CrawlerFile中没有settingsId字段，需要通过关联CrawlerTask表来查询
        // 这里简化处理，实际应用中可能需要自定义SQL或者使用其他方式实现
        return this.list(queryWrapper);
    }
}