package com.icarus.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.entity.SceneElement;
import com.icarus.mapper.SceneElementMapper;
import com.icarus.service.SceneElementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 场景要素服务
 * <AUTHOR>
 * @date 2025-03-24
 */
@Service
@Slf4j
public class SceneElementServiceImpl extends ServiceImpl<SceneElementMapper, SceneElement> implements SceneElementService {


    @Override
    public List<SceneElement> findScentElementBySceneId(String sceneId) {
        Assert.notBlank(sceneId);
        LambdaQueryWrapper<SceneElement> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SceneElement::getSceneId, sceneId);
        queryWrapper.orderByAsc(SceneElement::getSequence);
        return getBaseMapper().selectList(queryWrapper);
    }

}