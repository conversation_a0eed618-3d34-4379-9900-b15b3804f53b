package com.icarus.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icarus.constant.MessageStatus;
import com.icarus.dto.ChatContextDTO;
import com.icarus.sdk.client.cfg.LLMClientConfig;
import com.icarus.utils.SSEUtils;
import com.icarus.vo.ErrorCode;
import com.icarus.vo.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
public class ModelDebugService {

    private final ObjectMapper objectMapper = new ObjectMapper();

    public void debugModelMessage(ChatContextDTO chatContext, DataCallback callback) {
//todo:先关闭流式处理
        
//        LLMClientConfig modelConfig = LlmClientConfig.getModelMap().get(chatContext.getModel().getCode());
//
//        try {
//            //streamChat(chatContext.getMessages(), modelConfig.getBaseUrl(), modelConfig.getModel(), modelConfig.getApiKey(), callback, chatContext);
//            streamChat(chatContext, modelConfig, callback);
//            callback.complete(); // 完成发送
//        } catch (Exception e) {
//            callback.error(e); // 发生错误时调用错误回调
//        }
    }


    public  void streamChat(ChatContextDTO chatContext,
                            LLMClientConfig modelConfig,
                            DataCallback callback) {
//todo:先关闭流式处理

//        List<ChatMessage> messageList = chatContext.getMessages();
//        String model = modelConfig.getModel();
//        String apiKey = modelConfig.getApiKey();
//
//        if (messageList == null || messageList.isEmpty()) {
//            log.error("问题内容不能为空");
//            return;
//        }
//
//        String url = modelConfig.getBaseUrl();
//        if (StringUtils.isEmpty(url)) {
//            log.error("API URL未配置");
//            return;
//        }
//
//        if (url.endsWith("/"))
//            url = url + "v1/chat/completions";
//        else
//            url = url + "/v1/chat/completions";
//
//        HttpURLConnection connection = null;
//        BufferedReader reader = null;
//
//        try {
//            boolean streamOnOff = true;
//            // 构建请求体
//            Map<String, Object> requestBody = new HashMap<>();
//            requestBody.put("model", model);
//            requestBody.put("stream", streamOnOff);
//
//            requestBody.put("messages", messageList);
//
//            Float temperature = chatContext.getTemperature();
//            if (temperature != null) requestBody.put("temperature", temperature);
//
//            Float topP = chatContext.getTopP();
//            if (topP!= null) requestBody.put("top_p", topP);
//
//            Integer maxTokens = chatContext.getMaxTokens();
//            if (maxTokens!= null) requestBody.put("max_tokens", maxTokens);
//
//            Float frequencyPenalty = chatContext.getFrequencyPenalty();
//            if (frequencyPenalty!= null) requestBody.put("frequency_penalty", frequencyPenalty);
//
//            Float presencePenalty = chatContext.getPresencePenalty();
//            if (presencePenalty!= null) requestBody.put("presence_penalty", presencePenalty);
//
//
//            String jsonBody = objectMapper.writeValueAsString(requestBody);
//
//            log.info(jsonBody);
//
//            // 创建连接
//            URL apiUrl = new URL(url);
//            connection = (HttpURLConnection) apiUrl.openConnection();
//            connection.setRequestMethod("POST");
//            if (model.startsWith("llama3")) {
//                connection.setRequestProperty("Content-Type", "text/plain");// llama 模型调用要设置成 text/plain
//            } else {
//                connection.setRequestProperty("Content-Type", "application/json");
//            }
//            //
//            connection.setRequestProperty("Accept", "text/event-stream");
//            connection.setRequestProperty("Authorization", "Bearer " + apiKey);
//            connection.setDoOutput(true);
//            connection.setDoInput(true);
//            connection.setConnectTimeout(10000);
//            connection.setReadTimeout(0);
//
//            // 发送请求
//            try (OutputStream os = connection.getOutputStream()) {
//                byte[] input = jsonBody.getBytes(StandardCharsets.UTF_8);
//                os.write(input, 0, input.length);
//            }
//
//            // 读取响应
//            if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
//                reader = new BufferedReader(
//                        new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));
//                String line;
//                while ((line = reader.readLine()) != null) {
//                    //log.info(line);
//                    if (line.startsWith("data: ")) {
//                       String data = line.substring(6).trim();
//
//                        if ("[DONE]".equals(data)) {
//                            break;
//                        } else {
//                            callback.send(data);
//                        }
//                    }
//                }
//            } else {
//                // 处理错误响应
//                reader = new BufferedReader(
//                        new InputStreamReader(connection.getErrorStream(), StandardCharsets.UTF_8));
//                StringBuilder errorResponse = new StringBuilder();
//                String line;
//                while ((line = reader.readLine()) != null) {
//                    errorResponse.append(line);
//                }
//                String errorMessage = "HTTP error code: " + connection.getResponseCode()
//                        + ", error message: " + errorResponse.toString();
//                log.error(errorMessage);
//                callback.error(new RuntimeException(errorResponse.toString()));
////                SSEUtils.sendTextMessage(questionId,messageId, sessionId, "answer",
////                        errorMessage, MessageStatus.ERROR.getStatus());
//            }
//
//        } catch (Exception e) {
//            log.error("请求失败", e);
//            try {
////                SSEUtils.sendTextMessage(questionId,messageId, sessionId, "answer",
////                        "请求失败: " + e.getMessage(), MessageStatus.ERROR.getStatus());
//            } catch (Exception ex) {
//                log.error("发送错误消息失败", ex);
//            }
//        } finally {
//            // 清理资源
//            if (reader != null) {
//                try {
//                    reader.close();
//                } catch (IOException e) {
//                    log.error("关闭reader失败", e);
//                }
//            }
//            if (connection != null) {
//                connection.disconnect();
//            }
//        }
    }

    /**
     * 模拟生成大模型返回的分块数据
     *
     * @param state 当前状态
     * @param params 自定义参数
     * @return 模拟的响应数据
     */
    private String generateResponseChunk(int state, Map<String, String> params) {
        StringBuilder response = new StringBuilder();
        response.append("Chunk ").append(state).append(": ");

        if (params.containsKey("prompt")) {
            response.append("Prompt: ").append(params.get("prompt"));
        } else {
            response.append("Default response for chunk ").append(state);
        }

        return response.toString();
    }

    public interface DataCallback {
        void send(String data); // 发送数据
        void complete(); // 完成发送
        void error(Exception e); // 错误处理
    }

    public static class SSEDataCallback implements DataCallback {
        protected String sessionId;
        public SSEDataCallback(String sessionId) {
            this.sessionId = sessionId;
        }

        @Override
        public void send(String data) {


        }

        @Override
        public void complete() {
            //SseEmitterManager.get(sessionId).complete();
        }

        @Override
        public void error(Exception e) {
            ProcessResult pr = ProcessResult.error(ErrorCode.SYSTEM_ERROR, e.getMessage());
            SSEUtils.sendTextMessage(pr.getSessionId(), pr.getRequestId(), pr.getTabId(), "result", pr, MessageStatus.ERROR.getStatus());

        }
    }

    public static class QwenSSEDataCallback extends SSEDataCallback {

        public QwenSSEDataCallback(String sessionId) {
            super(sessionId);
        }

        StringBuilder fullContent = new StringBuilder();

        @Override
        public void send(String data) {
            //{"id":"","object":"","created":0,"model":"","choices":[{"delta":{"role":"assistant","content":"你好"},"index":0,"finish_reason":null}]}
            try {
                JSONObject obj = JSONUtil.parseObj(data);
                JSONArray choicesNode = (JSONArray)obj.get("choices");
                JSONObject firstObj = (JSONObject)choicesNode.get(0);
                JSONObject delta = (JSONObject)firstObj.get("delta");

                String reason = firstObj.get("finish_reason").toString();
                if (!"stop".equals(reason)) {
                    fullContent.append(delta.getStr("content"));
                }

            } catch (Exception e) {
                log.error("send error", e);
            }
        }

        public void complete() {
            log.info("ai: {}", fullContent);
            ProcessResult pr = ProcessResult.success("answer", fullContent.toString());
            pr.setSessionId(sessionId);
            SSEUtils.sendTextMessage(pr.getSessionId(), pr.getRequestId(), pr.getTabId(), "result", pr, MessageStatus.SUCCESS.getStatus());
            //SseEmitterManager.get(sessionId).complete();
        }
    }

    public static class DeekSeepSSEDataCallback extends QwenSSEDataCallback {

        public DeekSeepSSEDataCallback(String sessionId) {
            super(sessionId);
        }

        @Override
        public void send(String data) {
            super.send(data);
        }
    }

    public static class MetaSSEDataCallback extends QwenSSEDataCallback {

        public MetaSSEDataCallback(String sessionId) {
            super(sessionId);
        }


    }


}