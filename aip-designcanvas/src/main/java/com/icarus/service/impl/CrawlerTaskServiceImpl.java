package com.icarus.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.entity.CrawlerTask;
import com.icarus.mapper.CrawlerFileMapper;
import com.icarus.mapper.CrawlerTaskMapper;
import com.icarus.service.CrawlerTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 爬虫任务服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CrawlerTaskServiceImpl extends ServiceImpl<CrawlerTaskMapper, CrawlerTask> implements CrawlerTaskService {

    private final CrawlerFileMapper crawlerFileMapper;

    /**
     * 分页查询爬虫任务列表
     *
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param status 任务状态
     * @return 分页任务列表
     */
    @Override
    public org.springframework.data.domain.Page<CrawlerTask> findTasks(int pageNum, int pageSize, LocalDateTime startTime, LocalDateTime endTime, String status) {
        // 构建查询条件
        LambdaQueryWrapper<CrawlerTask> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加时间范围条件
        if (startTime != null) {
            queryWrapper.ge(CrawlerTask::getCreateTime, startTime);
        }
        if (endTime != null) {
            queryWrapper.le(CrawlerTask::getCreateTime, endTime);
        }
        
        // 添加状态条件
        if (StringUtils.hasText(status)) {
            queryWrapper.eq(CrawlerTask::getStatus, status);
        }
        
        // 按创建时间降序排序
        queryWrapper.orderByDesc(CrawlerTask::getCreateTime);
        
        // 执行分页查询
        Page<CrawlerTask> page = new Page<>(pageNum + 1, pageSize);  // MyBatis-Plus页码从1开始
        Page<CrawlerTask> result = this.page(page, queryWrapper);
        
        // 转换为Spring Data Page
        return new PageImpl<>(result.getRecords(), 
                             org.springframework.data.domain.PageRequest.of(pageNum, pageSize), 
                             result.getTotal());
    }

    /**
     * 根据批次ID查询爬虫任务
     *
     * @param batchId 批次ID
     * @return 爬虫任务列表
     */
    @Override
    public List<CrawlerTask> findByBatchId(String batchId) {
        if (!StringUtils.hasText(batchId)) {
            return List.of();
        }
        
        // 构建查询条件
        LambdaQueryWrapper<CrawlerTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CrawlerTask::getBatchId, batchId);
        queryWrapper.orderByDesc(CrawlerTask::getCreateTime);
        
        // 执行查询
        return this.list(queryWrapper);
    }

    /**
     * 全文搜索爬虫内容
     *
     * @param keywords 关键字
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 匹配的任务列表
     */
    @Override
    public org.springframework.data.domain.Page<CrawlerTask> searchContent(String keywords, int pageNum, int pageSize) {
        if (!StringUtils.hasText(keywords)) {
            return new PageImpl<>(List.of(), org.springframework.data.domain.PageRequest.of(pageNum, pageSize), 0);
        }
        
        // 构建查询条件 - 在文本内容中搜索关键字
        LambdaQueryWrapper<CrawlerTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(CrawlerTask::getTextContent, keywords);
        queryWrapper.orderByDesc(CrawlerTask::getCreateTime);
        
        // 执行分页查询
        Page<CrawlerTask> page = new Page<>(pageNum + 1, pageSize);  // MyBatis-Plus页码从1开始
        Page<CrawlerTask> result = this.page(page, queryWrapper);
        
        // 转换为Spring Data Page
        return new PageImpl<>(result.getRecords(), 
                             org.springframework.data.domain.PageRequest.of(pageNum, pageSize), 
                             result.getTotal());
    }

    /**
     * 获取爬虫统计信息
     *
     * @return 统计信息
     */
    @Override
    public Map<String, Object> getStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // 统计任务总数
        long taskCount = this.count();
        stats.put("taskCount", taskCount);
        
        // 统计不同状态的任务数量
        Map<String, Long> statusCounts = new HashMap<>();
        statusCounts.put("running", this.count(new LambdaQueryWrapper<CrawlerTask>().eq(CrawlerTask::getStatus, "running")));
        statusCounts.put("completed", this.count(new LambdaQueryWrapper<CrawlerTask>().eq(CrawlerTask::getStatus, "completed")));
        statusCounts.put("failed", this.count(new LambdaQueryWrapper<CrawlerTask>().eq(CrawlerTask::getStatus, "failed")));
        stats.put("statusCounts", statusCounts);
        
        // 统计文件总数
        long fileCount = crawlerFileMapper.selectCount(null);
        stats.put("fileCount", fileCount);
        
        // 统计不同类型的文件数量
        Map<String, Object> fileCounts = new HashMap<>();
        List<Map<String, Object>> fileTypes = crawlerFileMapper.selectFileCounts();
        if (fileTypes != null) {
            fileTypes.forEach(map -> fileCounts.put(String.valueOf(map.get("fileType")), map.get("count")));
        }
        stats.put("fileTypeCounts", fileCounts);
        
        // 统计最近7天的任务数量趋势
        List<Map<String, Object>> dailyTasks = baseMapper.selectDailyTaskCounts(7);
        stats.put("dailyTasks", dailyTasks);
        
        return stats;
    }
    
    @Override
    public CrawlerTask findUnfinishedTask(Long settingsId, String batchId) {
        log.info("查找未完成的爬虫任务：settingsId={}, batchId={}", settingsId, batchId);
        
        // 构建查询条件
        LambdaQueryWrapper<CrawlerTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CrawlerTask::getSettingsId, settingsId);
        queryWrapper.eq(CrawlerTask::getBatchId, batchId);
        queryWrapper.in(CrawlerTask::getStatus, Arrays.asList("running", "failed"));
        queryWrapper.orderByDesc(CrawlerTask::getUpdateTime);
        queryWrapper.last("LIMIT 1"); // 只取最新的一条
        
        // 执行查询
        List<CrawlerTask> tasks = list(queryWrapper);
        
        if (tasks != null && !tasks.isEmpty()) {
            CrawlerTask task = tasks.get(0);
            log.info("找到未完成的爬虫任务：id={}, status={}", task.getId(), task.getStatus());
            return task;
        }
        
        log.info("未找到未完成的爬虫任务");
        return null;
    }
}