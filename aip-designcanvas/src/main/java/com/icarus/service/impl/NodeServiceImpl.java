package com.icarus.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.entity.Directory;
import com.icarus.entity.Node;
import com.icarus.mapper.NodeMapper;
import com.icarus.service.DirectoryService;
import com.icarus.service.NodeService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.List;

@Service
@Slf4j
public class NodeServiceImpl extends ServiceImpl<NodeMapper, Node> implements NodeService {
    @Resource
    DirectoryService directoryService;

    @Override
    public void exportToExcel(List<String> ids, HttpServletResponse response) {
        log.info("开始导出节点到Excel，节点ID: {}", ids);
        try {
            // 获取节点数据
            List<Node> nodeList = this.listByIds(ids);

            // 创建工作簿和工作表
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("节点信息");

            // 创建表头
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("名称");
            headerRow.createCell(1).setCellValue("类型");
            headerRow.createCell(2).setCellValue("内容");

            // 填充数据
            for (int i = 0; i < nodeList.size(); i++) {
                Node node = nodeList.get(i);
                Row dataRow = sheet.createRow(i + 1);
                dataRow.createCell(0).setCellValue(node.getName());
                dataRow.createCell(1).setCellValue(node.getType());
                dataRow.createCell(2).setCellValue(node.getContent());
            }

            // 自动调整列宽
            for (int i = 0; i < 3; i++) {
                sheet.autoSizeColumn(i);
            }

            // 设置响应头
            String fileName = URLEncoder.encode("节点信息导出.xlsx", "UTF-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

            // 写入响应流
            workbook.write(response.getOutputStream());
            workbook.close();
            log.info("节点导出Excel成功，共导出{}条记录", nodeList.size());
        } catch (Exception e) {
            log.error("导出节点Excel失败", e);
            throw new RuntimeException("导出节点Excel失败: " + e.getMessage());
        }
    }

    @Override
    public int importFromExcel(MultipartFile file) {
        log.info("开始从Excel导入节点，文件名: {}", file.getOriginalFilename());
        int successCount = 0;

        try (InputStream is = file.getInputStream();
                Workbook workbook = WorkbookFactory.create(is)) {

            Sheet sheet = workbook.getSheetAt(0);
            int rowCount = sheet.getPhysicalNumberOfRows();

            // 从第二行开始读取数据（跳过表头）
            for (int i = 1; i < rowCount; i++) {
                Row row = sheet.getRow(i);
                if (row == null)
                    continue;

                // 读取单元格数据
                String name = getCellValueAsString(row.getCell(0));
                String type = getCellValueAsString(row.getCell(1));
                String content = getCellValueAsString(row.getCell(2));

                // 验证必填字段
                if (name == null || name.isEmpty() || type == null || type.isEmpty()) {
                    log.warn("第{}行数据不完整，跳过导入", i + 1);
                    continue;
                }

                // 查找对应的目录
                LambdaQueryWrapper<Directory> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(Directory::getName, type).eq(Directory::getType, "node");
                Directory directory = directoryService.getOne(wrapper, false);

                if (directory == null) {
                    log.warn("未找到匹配的目录，跳过导入: name={}, type={}", name, type);
                    continue;
                }

                // 创建并保存节点
                Node node = new Node();
                node.setName(name);
                node.setType(type);
                node.setContent(content);
                node.setDirectoryId(directory.getId());

                this.save(node);
                successCount++;
                log.info("成功导入节点: {}", name);
            }

            log.info("节点导入完成，成功导入{}条记录", successCount);
            return successCount;
        } catch (IOException e) {
            log.error("导入节点Excel失败", e);
            throw new RuntimeException("导入节点Excel失败: " + e.getMessage());
        }
    }

    /**
     * 安全获取单元格的字符串值
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                }
                return String.valueOf((long) cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    return String.valueOf(cell.getNumericCellValue());
                } catch (Exception e) {
                    return cell.getStringCellValue();
                }
            default:
                return "";
        }
    }
}