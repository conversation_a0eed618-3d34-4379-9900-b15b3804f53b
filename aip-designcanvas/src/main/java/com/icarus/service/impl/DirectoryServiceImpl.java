package com.icarus.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.entity.Directory;
import com.icarus.mapper.DirectoryMapper;
import com.icarus.service.DirectoryService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class DirectoryServiceImpl extends ServiceImpl<DirectoryMapper, Directory> implements DirectoryService {

    @Override
    public List<Directory> buildTree(List<Directory> all) {
        // 构建父子关系映射
        Map<String, List<Directory>> parentMap = new HashMap<>();
        for (Directory dir : all) {
            if (dir.getParentId() != null) {
                List<Directory> children = parentMap.computeIfAbsent(dir.getParentId(), k -> new ArrayList<>());
                children.add(dir);
            }
        }

        // 构建树结构
        List<Directory> result = new ArrayList<>();
        for (Directory dir : all) {
            if (dir.getParentId() == null) {
                dir.setChildren(buildChildren(dir.getId(), parentMap));
                result.add(dir);
            }
        }
        return result;
    }

    @Override
    public List<Directory> buildChildren(String parentId, Map<String, List<Directory>> parentMap) {
        List<Directory> children = parentMap.get(parentId);
        if (children == null) {
            return new ArrayList<>();
        }

        for (Directory child : children) {
            child.setChildren(buildChildren(child.getId(), parentMap));
        }
        return children;
    }

} 