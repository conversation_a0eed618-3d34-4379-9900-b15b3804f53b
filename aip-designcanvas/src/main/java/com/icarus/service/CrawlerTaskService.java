package com.icarus.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.icarus.entity.CrawlerTask;
import org.springframework.data.domain.Page;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 爬虫任务服务接口
 */
public interface CrawlerTaskService extends IService<CrawlerTask> {

    /**
     * 分页查询爬虫任务列表
     *
     * @param page 页码
     * @param size 每页大小
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param status 任务状态
     * @return 分页任务列表
     */
    Page<CrawlerTask> findTasks(int page, int size, LocalDateTime startTime, LocalDateTime endTime, String status);

    /**
     * 根据批次ID查询爬虫任务
     *
     * @param batchId 批次ID
     * @return 爬虫任务列表
     */
    List<CrawlerTask> findByBatchId(String batchId);

    /**
     * 全文搜索爬虫内容
     *
     * @param keywords 关键字
     * @param page 页码
     * @param size 每页大小
     * @return 匹配的任务列表
     */
    Page<CrawlerTask> searchContent(String keywords, int page, int size);

    /**
     * 获取爬虫统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getStats();

    /**
     * 查找未完成的爬虫任务
     * 
     * @param settingsId 配置ID
     * @param batchId 批次ID
     * @return 未完成的任务，如果不存在则返回null
     */
    CrawlerTask findUnfinishedTask(Long settingsId, String batchId);
}