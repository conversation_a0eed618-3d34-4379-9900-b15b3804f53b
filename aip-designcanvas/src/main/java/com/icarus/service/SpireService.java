package com.icarus.service;


import com.icarus.common.cotnode.ab.ex.ExFormatNode;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface SpireService {

    /**
     * 将PDF文件转换为EasyEX格式
     * @param filePath 文件路径
     * @param startPage 开始页码
     * @param endPage 结束页码
     * @return 转换后的EasyEX格式数据
     */
    List<Map<String, String>> convert2EasyEX(String filePath, int startPage, int endPage, ExFormatNode.Properties properties) throws IOException;

    /**
     * 获取页数
     * @param filePath 文件路径
     * @return 文件的总页数
     * @throws IOException 如果文件读取失败
     */
    int getPageCount(String filePath) throws IOException;
}
