package com.icarus.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.entity.TimerTrigger;
import java.util.List;

/**
 * 定时触发器数据服务接口
 * 处理TimerTrigger的基本数据访问，用于解决循环依赖问题
 */
public interface TimerTriggerDataService {

    /**
     * 根据ID获取触发器
     *
     * @param id 触发器ID
     * @return 触发器实体
     */
    TimerTrigger getById(String id);

    /**
     * 更新触发器
     *
     * @param trigger 触发器实体
     * @return 是否成功
     */
    boolean updateById(TimerTrigger trigger);

    /**
     * 保存触发器
     *
     * @param trigger 触发器实体
     * @return 是否成功
     */
    boolean save(TimerTrigger trigger);

    /**
     * 删除触发器
     *
     * @param id 触发器ID
     * @return 是否成功
     */
    boolean removeById(String id);

    /**
     * 查询所有启用状态的触发器
     *
     * @return 启用状态的触发器列表
     */
    List<TimerTrigger> listEnabledTriggers();

    /**
     * 根据工作流ID查询触发器
     *
     * @param workflowId 工作流ID
     * @return 触发器列表
     */
    List<TimerTrigger> getByWorkflowId(String workflowId);

    /**
     * 分页查询
     *
     * @param page    分页参数
     * @param wrapper 查询条件
     * @return 分页结果
     */
    Page<TimerTrigger> page(Page<TimerTrigger> page, LambdaQueryWrapper<TimerTrigger> wrapper);

    /**
     * 根据条件查询列表
     *
     * @param wrapper 查询条件
     * @return 触发器列表
     */
    List<TimerTrigger> list(LambdaQueryWrapper<TimerTrigger> wrapper);
}