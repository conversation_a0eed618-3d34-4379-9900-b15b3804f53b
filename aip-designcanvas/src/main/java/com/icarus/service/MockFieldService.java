package com.icarus.service;

import com.icarus.dto.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icarus.sdk.prompt.PromptTemplate;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import java.util.Map;
import java.util.UUID;
import java.util.HashMap;
import java.util.List;
import java.lang.StringBuilder;
import java.util.ArrayList;


/**
 * Mock字段服务
 * 处理字段模拟数据生成的核心逻辑
 * dym
 */
@Log4j2
@Service
public class MockFieldService {
    
    private static final String MODEL_NAME = "deepseek-32B";
    private static final String PROMPT_TEMPLATE = "prompts/mock_field_prompt.groovy";
    
    private final ModelService modelService;
    private final ObjectMapper objectMapper;
    
    public MockFieldService(ModelService modelService, 
                          ObjectMapper objectMapper) {
        this.modelService = modelService;
        this.objectMapper = objectMapper;
    }
    
    /**
     * 生成模拟数据
     */
    public MockFieldResponse generateMockData(MockFieldRequest request) {
        MockFieldResponse response = new MockFieldResponse();
        
        try {
            if (request == null || request.getFields() == null) {
                throw new IllegalArgumentException("Request and fields cannot be null");
            }
            
            // 获取字段定义，支持多种格式
            List<Map<String, Object>> fieldsList = parseFields(request.getFields());
            
            if (fieldsList == null || fieldsList.isEmpty()) {
                throw new IllegalArgumentException("Fields list cannot be null or empty");
            }
            
            // 构建字段定义字符串
            StringBuilder fieldsStr = new StringBuilder();
            
            for (Map<String, Object> field : fieldsList) {
                fieldsStr.append("字段名称：").append(field.get("name")).append("\n");
                fieldsStr.append("字段类型：").append(field.get("type")).append("\n");
                fieldsStr.append("描述：").append(field.get("description") != null ? field.get("description") : "无").append("\n");
                
                // 处理子字段
                List<Map<String, Object>> children = (List<Map<String, Object>>) field.get("children");
                if (children != null && !children.isEmpty()) {
                    fieldsStr.append("子字段：\n");
                    for (Map<String, Object> child : children) {
                        fieldsStr.append("  - 名称：").append(child.get("name")).append("\n");
                        fieldsStr.append("    类型：").append(child.get("type")).append("\n");
                        fieldsStr.append("    描述：").append(child.get("description") != null ? child.get("description") : "无").append("\n");
                    }
                }
                fieldsStr.append("---\n");
            }
            
            // 使用 PromptTemplate 处理模板
            Map<String, String> ptMap = new HashMap<>();
            ptMap.put("fields", fieldsStr.toString());
            
            String prompt = PromptTemplate.builder(PROMPT_TEMPLATE, "MockDataPrompt")
                                        .format(ptMap)
                                        .build();
            
            Map<String, Object> mockData = callDeepSeekAndParseMockData(prompt);
            
            response.setMockData(mockData);
            response.setCode(200);
            response.setMessage("Success");
            
        } catch (IllegalArgumentException e) {
            log.warn("Invalid request: {}", e.getMessage());
            response.setCode(400);
            response.setMessage(e.getMessage());
        } catch (Exception e) {
            log.error("Failed to generate mock data", e);
            response.setCode(500);
            response.setMessage("Error generating mock data: " + e.getMessage());
        }
        
        return response;
    }
    
    /**
     * 解析字段定义，支持多种格式
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> parseFields(Object fields) {
        try {
            if (fields instanceof List) {
                // 格式1: 直接是字段列表
                return (List<Map<String, Object>>) fields;
            } else if (fields instanceof Map) {
                Map<String, Object> fieldsMap = (Map<String, Object>) fields;
                
                if (fieldsMap.containsKey("data")) {
                    // 格式2: {"data":[...]}
                    return (List<Map<String, Object>>) fieldsMap.get("data");
                } else if (fieldsMap.containsKey("fields")) {
                    // 格式3: {"fields":[...]} 或 {"fields":{"data":[...]}}
                    Object fieldsObj = fieldsMap.get("fields");
                    if (fieldsObj instanceof List) {
                        return (List<Map<String, Object>>) fieldsObj;
                    } else if (fieldsObj instanceof Map) {
                        Map<String, Object> nestedMap = (Map<String, Object>) fieldsObj;
                        if (nestedMap.containsKey("data")) {
                            return (List<Map<String, Object>>) nestedMap.get("data");
                        }
                    }
                } else {
                    // 格式4: 单个字段对象
                    List<Map<String, Object>> list = new ArrayList<>();
                    list.add((Map<String, Object>) fields);
                    return list;
                }
            }
            
            throw new IllegalArgumentException("Unsupported fields format");
        } catch (Exception e) {
            log.error("Failed to parse fields: {}", fields, e);
            throw new IllegalArgumentException("Invalid fields format: " + e.getMessage());
        }
    }
    
    /**
     * 调用 DeepSeek 模型并解析返回数据
     */
    private Map<String, Object> callDeepSeekAndParseMockData(String prompt) {
        try {
            ModelRequest modelRequest = new ModelRequest();
            modelRequest.setRequestId(UUID.randomUUID().toString());
            modelRequest.setModelName(MODEL_NAME);
            modelRequest.setPrompt(prompt);
            
            // 设置模型参数
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("temperature", 0.7);
            parameters.put("max_tokens", 2000);
            modelRequest.setParameters(parameters);
            
            ModelResponse modelResponse = modelService.callModel(modelRequest);
            
            if (modelResponse.getError() != null) {
                throw new RuntimeException("Model error: " + modelResponse.getError());
            }
            
            String jsonResult = modelResponse.getResult();
            log.debug("Model raw response: {}", jsonResult);
            
            // 只提取 mockData 部分
            return extractMockData(jsonResult);
        } catch (Exception e) {
            log.error("Failed to call DeepSeek model", e);
            throw new RuntimeException("Failed to generate mock data: " + e.getMessage(), e);
        }
    }
    
    /**
     * 从模型返回结果中提取 mockData
     */
    private Map<String, Object> extractMockData(String result) {
        try {
            log.debug("Raw model response: {}", result);
            
            // 1. 清理响应文本，只保留JSON部分
            result = result.trim();
            
            // 2. 如果有多余的反引号(```)，去掉它们
            if (result.startsWith("```json")) {
                result = result.substring(7);
            }
            if (result.startsWith("```")) {
                result = result.substring(3);
            }
            if (result.endsWith("```")) {
                result = result.substring(0, result.length() - 3);
            }
            
            result = result.trim();
            
            // 3. 确保是以 { 开始，以 } 结束
            if (!result.startsWith("{") || !result.endsWith("}")) {
                throw new RuntimeException("Invalid JSON format: response must start with { and end with }");
            }
            
            // 4. 预处理 JSON 字符串
            String processedJson = result
                .replaceAll("[\u201C\u201D\u2018\u2019]", "\"") // 处理中文引号
                .replaceAll("([{,])\\s*(\\w+)\\s*:", "$1\"$2\":") // 给字段名加双引号
                .replaceAll(":\\s*'([^']*)'", ": \"$1\"") // 将单引号值改为双引号
                .replaceAll(":\\s*\"([^\"]*)'", ": \"$1\"") // 处理混合引号
                .replaceAll("'([^']*)\",", "\"$1\",") // 处理混合引号
                .replaceAll("[\\x00-\\x1F\\x7F]", ""); // 移除控制字符
                
            log.debug("Processed JSON: {}", processedJson);
            
            // 5. 解析JSON
            try {
                return objectMapper.readValue(processedJson, Map.class);
            } catch (Exception e) {
                log.error("Failed to parse JSON: {}", processedJson);
                throw e;
            }
        } catch (Exception e) {
            log.error("Failed to extract mock data. Raw response: {}", result, e);
            throw new RuntimeException("Failed to parse mock data: " + e.getMessage());
        }
    }
} 