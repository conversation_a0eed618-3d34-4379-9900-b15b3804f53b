package com.icarus.service;

import com.icarus.entity.TimerTrigger;

import java.util.List;

/**
 * 定时触发器服务接口
 */
public interface TimerTriggerService {

    /**
     * 根据ID获取触发器
     *
     * @param id 触发器ID
     * @return 触发器实体
     */
    TimerTrigger getById(String id);

    /**
     * 更新触发器
     *
     * @param trigger 触发器实体
     * @return 是否成功
     */
    boolean updateById(TimerTrigger trigger);

    /**
     * 保存触发器
     *
     * @param trigger 触发器实体
     * @return 是否成功
     */
    boolean save(TimerTrigger trigger);

    /**
     * 删除触发器
     *
     * @param id 触发器ID
     * @return 是否成功
     */
    boolean removeById(String id);

    /**
     * 启用定时触发器
     *
     * @param id 触发器ID
     * @return 是否成功
     */
    boolean enableTrigger(String id);

    /**
     * 禁用定时触发器
     *
     * @param id 触发器ID
     * @return 是否成功
     */
    boolean disableTrigger(String id);

    /**
     * 根据ID重新调度触发器
     *
     * @param id 触发器ID
     * @return 是否成功
     */
    boolean rescheduleTrigger(String id);

    /**
     * 获取所有启用状态的触发器
     * 
     * @return 启用状态的触发器列表
     */
    List<TimerTrigger> listEnabledTriggers();

    /**
     * 根据工作流ID获取关联的触发器
     * 
     * @param workflowId 工作流ID
     * @return 触发器列表
     */
    List<TimerTrigger> getByWorkflowId(String workflowId);
}