package com.icarus.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.icarus.dto.RuntimeRequest;
import com.icarus.service.FileStorageService;
import com.icarus.service.model.FileInfo;
import com.icarus.utils.SpringContextHolder;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.support.StandardMultipartHttpServletRequest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Runtime控制器接口
 * <AUTHOR>
 * @date 2025-04-15
 */
public interface BaseRuntime {

    /**
     * 文件参数解析处理
     * @param request
     * @param runtimeRequest
     */
 default void pretreatmentFile(HttpServletRequest request, RuntimeRequest runtimeRequest) {
    if (!(request instanceof StandardMultipartHttpServletRequest)) return;

    StandardMultipartHttpServletRequest multipartRequest = (StandardMultipartHttpServletRequest) request;
    Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
    if (CollUtil.isEmpty(fileMap)) return;

    // 解析 data 参数
    Object data_ = runtimeRequest.getData();
    JSONObject data = ObjUtil.isNotNull(data_) && StrUtil.isNotEmpty(data_.toString())
            ? JSONUtil.parseObj(data_)
            : new JSONObject();

    FileStorageService fileStorageService = SpringContextHolder.getBean(FileStorageService.class);

    // 使用 map 来临时保存字段 -> 文件列表映射
    Map<String, List<FileInfo>> fileGroupMap = new HashMap<>();

    for (Map.Entry<String, MultipartFile> entry : fileMap.entrySet()) {
        String fieldName = entry.getKey();
        MultipartFile file = entry.getValue();

        // 上传文件
        FileInfo info = fileStorageService.uploadFile(file, "temp");

        // 按字段分组存储文件信息
        fileGroupMap.computeIfAbsent(fieldName, k -> new ArrayList<>()).add(info);
    }

    // 遍历每个字段，构建对应的 url 列表和文件名列表
    for (Map.Entry<String, List<FileInfo>> entry : fileGroupMap.entrySet()) {
        String fieldName = entry.getKey();
        List<FileInfo> fileInfoList = entry.getValue();

        // 单个文件直接取第一个值，避免 list 影响兼容性
        if (fileInfoList.size() == 1) {
            data.put(fieldName, fileInfoList.get(0).getUrl());
            data.put(fieldName + "Name", fileInfoList.get(0).getName());
        } else {
            // 多个文件转为 url 列表和 name 列表
            List<String> urls = fileInfoList.stream().map(FileInfo::getUrl).toList();
            List<String> names = fileInfoList.stream().map(FileInfo::getName).toList();

            data.put(fieldName, urls);
            data.put(fieldName + "Name", names);
        }
    }

    // 设置回 runtimeRequest
    runtimeRequest.setData(JSONUtil.toJsonStr(data));
}


}
