package com.icarus.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.common.rest.BaseResponse;
import com.icarus.dto.PageDTO;
import com.icarus.entity.Endpoint;
import com.icarus.entity.TimerTrigger;
import com.icarus.service.CanvasService;
import com.icarus.service.EndpointService;
import com.icarus.service.TimerTriggerDataService;
import com.icarus.service.TimerTriggerService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.*;

import java.security.SecureRandom;
import java.time.LocalDateTime;

/**
 * 端点管理控制器
 * 提供端点的增删改查功能
 */
@Slf4j
@RestController
@RequestMapping("/aip/endpoint")
@RequiredArgsConstructor
public class EndpointController {
    @Autowired
    private Environment environment;

    @Resource
    private final EndpointService endpointService;

    @Resource
    private final CanvasService canvasService;

    @Resource
    private final TimerTriggerService timerTriggerService;

    @Resource
    private final TimerTriggerDataService timerTriggerDataService;

    @Value("${ip.address}")
    private  String ipAddress;

    private static final SecureRandom secureRandom = new SecureRandom();

    /**
     * 生成分布式唯一ID
     * 格式：时间戳(13位) + 机器标识(4位) + 随机数(6位)
     * 
     * @return 唯一ID字符串
     */
    private String generateUniqueId() {
        // 获取当前时间戳（毫秒）
        long timestamp = System.currentTimeMillis();

        // 生成6位随机数
        int randomNum = secureRandom.nextInt(1000000);

        // 组合ID：时间戳 + 随机数
        return String.format("%d%06d", timestamp, randomNum);
    }

    /**
     * 创建新端点
     *
     * @param endpoint 端点信息，必须包含project和templateId
     * @return 创建后的端点信息，包含自动生成的ID
     */
    @PostMapping("/create")
    public BaseResponse<Endpoint> create(@RequestBody Endpoint endpoint) {
        try {
            if (endpoint.getTemplateId() == null) {
                return BaseResponse.badRequest("模板ID不能为空");
            }

            // 获取画布内容
            String canvasJson = canvasService.getById(endpoint.getTemplateId()).getContent();
            if (canvasJson == null) {
                return BaseResponse.badRequest("画布不存在");
            }

            // 解析画布内容，获取outputs信息
            String outputs = null;
            try {
                JSONObject canvasJsonObj = JSONUtil.parseObj(canvasJson);
                JSONArray cells = canvasJsonObj.getJSONArray("cells");
                if (cells != null) {
                    for (int i = 0; i < cells.size(); i++) {
                        JSONObject cell = cells.getJSONObject(i);
                        JSONObject data = cell.getJSONObject("data");
                        if (data != null && "startNode".equals(data.getStr("type"))) {
                            JSONObject outputsObj = data.getJSONObject("outputs");
                            if (outputsObj != null) {
                                // 创建新的输出对象
                                JSONObject newOutputs = new JSONObject();

                                // 处理data数组
                                JSONArray dataArray = outputsObj.getJSONArray("data");
                                if (dataArray != null && !dataArray.isEmpty()) {
                                    JSONObject dataItem = new JSONObject();

                                    for (int j = 0; j < dataArray.size(); j++) {
                                        JSONObject item = dataArray.getJSONObject(j);
                                        String name = item.getStr("name");
                                        String type = item.getStr("type");

                                        if (type != null && type.startsWith("Array<")) {
                                            // 处理数组类型
                                            JSONArray children = item.getJSONArray("children");
                                            if (children != null && !children.isEmpty()) {
                                                JSONArray arrayValues = new JSONArray();
                                                JSONObject childrenObj = new JSONObject();
                                                for (int k = 0; k < children.size(); k++) {
                                                    JSONObject child = children.getJSONObject(k);
                                                    String childName = child.getStr("name");
                                                    childrenObj.set(childName, "");
                                                }
                                                arrayValues.add(childrenObj);
                                                dataItem.set(name, arrayValues);
                                            }
                                        } else {
                                            // 处理普通类型
                                            dataItem.set(name, "");
                                        }
                                    }
                                    newOutputs.set("data", dataItem);
                                }

                                // 处理headers数组
                                JSONArray headers = outputsObj.getJSONArray("headers");
                                if (headers != null && !headers.isEmpty()) {
                                    JSONObject headerItem = new JSONObject();
                                    for (int j = 0; j < headers.size(); j++) {
                                        JSONObject item = headers.getJSONObject(j);
                                        String name = item.getStr("name");
                                        headerItem.set(name, "");
                                    }
                                    newOutputs.set("headers", headerItem);
                                }

                                // 处理commons数组
                                JSONArray commons = outputsObj.getJSONArray("commons");
                                if (commons != null && !commons.isEmpty()) {
                                    JSONObject commonItem = new JSONObject();
                                    for (int j = 0; j < commons.size(); j++) {
                                        JSONObject item = commons.getJSONObject(j);
                                        String name = item.getStr("name");
                                        commonItem.set(name, "");
                                    }
                                    newOutputs.set("commons", commonItem);
                                }

                                outputs = newOutputs.toString();
                            }
                            break;
                        }
                    }
                }
            } catch (Exception e) {
                log.error("解析画布outputs信息失败", e);
            }

            // 设置req_param和path字段
            endpoint.setReqParam(outputs);
            endpoint.setAlias(generateUniqueId());
            endpointService.save(endpoint);
            //修改访问的ip地址防止接口调用不到
//            String ip = InetAddress.getLocalHost().getHostAddress();
            String ip = ipAddress;
            String port = environment.getProperty("server.port", "8080");
            String httpPrefix = "http://" + ip + ":" + port + "/aip/api";
            endpoint.setPath(httpPrefix + "/" + endpoint.getProject() + "/" + endpoint.getSubsystem() + "/"
                    + endpoint.getModule() + "/" + endpoint.getPath());

            // 检测并处理画布中的定时器节点
            processTimerNodes(canvasJson, endpoint);

            return BaseResponse.ok(endpoint);
        } catch (Exception e) {
            log.error("创建端点失败", e);
            return BaseResponse.serviceError("创建端点失败: " + e.getMessage());
        }
    }

    /**
     * 处理画布中的定时器节点
     * 
     * @param canvasJson 画布JSON
     * @param endpoint   端点信息
     */
    private void processTimerNodes(String canvasJson, Endpoint endpoint) {
        try {
            JSONObject canvasJsonObj = JSONUtil.parseObj(canvasJson);
            JSONArray cells = canvasJsonObj.getJSONArray("cells");

            if (cells == null || cells.isEmpty()) {
                return;
            }

            log.info("开始扫描画布中的定时器节点, 端点ID: {}", endpoint.getId());

            // 遍历所有节点，找出定时器节点
            for (int i = 0; i < cells.size(); i++) {
                JSONObject cell = cells.getJSONObject(i);
                JSONObject data = cell.getJSONObject("data");

                if (data != null && "timerNode".equals(data.getStr("type"))) {
                    log.info("发现定时器节点: {}", cell.getStr("id"));

                    // 获取定时器节点属性
                    JSONObject properties = data.getJSONObject("properties");
                    if (properties == null) {
                        continue;
                    }

                    // 创建TimerTrigger记录
                    TimerTrigger timerTrigger = new TimerTrigger();
                    timerTrigger.setTriggerName(endpoint.getDescription() + " 的定时任务");
                    timerTrigger.setWorkflowId(properties.getStr("workflowId"));
                    Endpoint workflowById = endpointService.getById(properties.getStr("workflowId"));
                    String ip = ipAddress;
                    String port = environment.getProperty("server.port", "8080");
                    String httpPrefix = "http://" + ip + ":" + port + "/aip/api";
                    //设置定时调用的路径为定时节点的工作流的路径
                    timerTrigger.setPath(httpPrefix + "/" + workflowById.getProject() + "/" + workflowById.getSubsystem() + "/"
                            + workflowById.getModule() + "/" + workflowById.getPath());
                    // 设置时间类型和表达式
                    String triggerTimeType = properties.getStr("triggerTimeType");
                    timerTrigger.setTriggerTimeType(triggerTimeType);

                    if ("cron".equals(triggerTimeType)) {
                        timerTrigger.setCronExpression(properties.getStr("cronExpression"));
                    } else if ("preset".equals(triggerTimeType)) {
                        timerTrigger.setTriggerTime(properties.getStr("triggerTime"));
                        timerTrigger.setFrequencyType(properties.getStr("frequencyType"));
                        timerTrigger.setFrequencyDay(properties.getInt("frequencyDay", 1));
                    }

                    // 设置时区
                    timerTrigger.setTimezone(properties.getStr("timezone", "Asia/Shanghai"));

                    // 设置其他信息
                    timerTrigger.setDescription("由端点 " + endpoint.getPath() + " 自动创建的定时任务");
                    timerTrigger.setStatus(1); // 默认启用
                    timerTrigger.setCreateTime(LocalDateTime.now());
                    timerTrigger.setUpdateTime(LocalDateTime.now());

                    // 保存触发器
                    boolean saved = timerTriggerService.save(timerTrigger);
                    if (saved) {
                        log.info("已创建定时触发器记录，ID: {}", timerTrigger.getId());
                        // 调度任务
                        timerTriggerService.rescheduleTrigger(timerTrigger.getId());
                    } else {
                        log.error("创建定时触发器记录失败");
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理定时器节点时出错", e);
        }
    }

    /**
     * 更新端点信息
     *
     * @param endpoint 端点信息，必须包含id
     * @return 更新后的端点信息
     */
    @PostMapping("/update")
    public BaseResponse<Endpoint> update(@RequestBody Endpoint endpoint) {
        try {
            Endpoint oldEndpoint = endpointService.getById(endpoint.getId());
            if (oldEndpoint == null) {
                return BaseResponse.badRequest("端点不存在");
            }

            // 获取画布内容
            String canvasJson = null;
            if (endpoint.getTemplateId() != null) {
                canvasJson = canvasService.getById(endpoint.getTemplateId()).getContent();
                if (canvasJson == null) {
                    return BaseResponse.badRequest("画布不存在");
                }
            }

            String ip = ipAddress;
            String port = environment.getProperty("server.port", "8080");
            String httpPrefix = "http://" + ip + ":" + port + "/aip/api";

           //path不变兼容
            if (StrUtil.equals(endpoint.getPath(), httpPrefix + "/" + oldEndpoint.getProject() + "/" + oldEndpoint.getSubsystem() + "/"
                    + oldEndpoint.getModule() + "/" + oldEndpoint.getPath())){
                endpoint.setPath(oldEndpoint.getPath());
            }

            endpointService.updateById(endpoint);

            // 如果模板ID发生变化，重新处理定时器节点
            if (endpoint.getTemplateId() != null && !endpoint.getTemplateId().equals(oldEndpoint.getTemplateId())) {
                // 先禁用与旧模板关联的定时触发器
                disableOldTimerTriggers(oldEndpoint.getTemplateId());

                // 检测并处理新画布中的定时器节点
                processTimerNodes(canvasJson, endpoint);
            }

            return BaseResponse.ok(endpoint);
        } catch (Exception e) {
            log.error("更新端点失败", e);
            return BaseResponse.serviceError("更新端点失败: " + e.getMessage());
        }
    }

    /**
     * 禁用与指定模板关联的定时触发器
     * 
     * @param templateId 模板ID
     */
    private void disableOldTimerTriggers(String templateId) {
        try {
            // 查询与此模板关联的所有触发器
            LambdaQueryWrapper<TimerTrigger> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TimerTrigger::getWorkflowId, templateId);

            // 使用数据服务查询，然后使用业务服务禁用
            timerTriggerDataService.list(wrapper).forEach(trigger -> {
                timerTriggerService.disableTrigger(trigger.getId());
                log.info("已禁用关联的定时触发器，ID: {}", trigger.getId());
            });
        } catch (Exception e) {
            log.error("禁用旧定时触发器失败", e);
        }
    }

    /**
     * 删除端点
     *
     * @param id 端点ID
     */
    @PostMapping("/delete/{id}")
    public BaseResponse<Void> delete(@PathVariable String id) {
        try {
            // 获取端点信息
            Endpoint endpoint = endpointService.getById(id);
            if (endpoint != null && endpoint.getTemplateId() != null) {
                // 禁用与此端点关联的定时触发器
                disableOldTimerTriggers(endpoint.getTemplateId());
            }

            endpointService.removeById(id);
            return BaseResponse.ok(null);
        } catch (Exception e) {
            log.error("删除端点失败", e);
            return BaseResponse.serviceError("删除端点失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取端点信息
     *
     * @param id 端点ID
     * @return 端点信息
     */
    @GetMapping("/get/{id}")
    public BaseResponse<Endpoint> getById(@PathVariable String id) {
        try {
            Endpoint endpoint = endpointService.getById(id);
            if (endpoint == null) {
                return BaseResponse.badRequest("端点不存在");
            }

            // String ip = InetAddress.getLocalHost().getHostAddress();
            // String port = environment.getProperty("server.port", "8080");
            // String httpPrefix = "http://" + ip + ":" + port + "/aip/api";
            // endpoint.setPath(httpPrefix + "/" + endpoint.getProject() + "/" +
            // endpoint.getSubsystem() + "/" + endpoint.getModule() + "/" +
            // endpoint.getPath());

            return BaseResponse.ok(endpoint);
        } catch (Exception e) {
            log.error("获取端点失败", e);
            return BaseResponse.serviceError("获取端点失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询端点列表
     *
     * @param current   当前页码，默认1
     * @param pageSize  每页大小，默认10
     * @param project   项目名称，可选，精确匹配
     * @param subsystem 子系统名称，可选，精确匹配
     * @param module    模块名称，可选，精确匹配
     * @param reqParam  请求参数，可选，模糊匹配
     * @param path      请求路径，可选，模糊匹配
     * @param description      描述，可选，模糊匹配
     * @return 分页结果
     */
    @GetMapping("/page")
    public BaseResponse<PageDTO<Endpoint>> page(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String project,
            @RequestParam(required = false) String subsystem,
            @RequestParam(required = false) String module,
            @RequestParam(required = false) String reqParam,
            @RequestParam(required = false) String path,
            @RequestParam(required = false) String description) {
        try {
            Page<Endpoint> page = new Page<>(current, pageSize);
            LambdaQueryWrapper<Endpoint> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(project != null, Endpoint::getProject, project)
                    .eq(subsystem != null, Endpoint::getSubsystem, subsystem)
                    .eq(module != null, Endpoint::getModule, module)
                    .like(reqParam != null, Endpoint::getReqParam, reqParam)
                    .like(path != null, Endpoint::getPath, path)
                    .like(description != null, Endpoint::getDescription, description)
                    .orderByDesc(Endpoint::getCreateTime);

            Page<Endpoint> resultPage = endpointService.page(page, wrapper);

            //修改访问的ip地址防止接口调用不到
//            String ip = InetAddress.getLocalHost().getHostAddress();
            String ip = ipAddress;
            String port = environment.getProperty("server.port", "8080");
            String httpPrefix = "http://" + ip + ":" + port + "/aip/api";

            // 给每个endpoint的path拼接aipPath
            resultPage.getRecords().forEach(endpoint -> {
                endpoint.setPath(httpPrefix + "/" + endpoint.getProject() + "/" + endpoint.getSubsystem() + "/"
                        + endpoint.getModule() + "/" + endpoint.getPath());
            });

            return BaseResponse.ok(PageDTO.from(resultPage));
        } catch (Exception e) {
            log.error("分页查询端点失败", e);
            return BaseResponse.serviceError("分页查询端点失败: " + e.getMessage());
        }
    }
}