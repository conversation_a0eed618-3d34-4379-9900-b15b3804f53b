package com.icarus.controller;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.common.rest.BaseResponse;
import com.icarus.dto.PageDTO;
import com.icarus.entity.TimerTrigger;
import com.icarus.service.TimerTriggerDataService;
import com.icarus.service.TimerTriggerService;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;

/**
 * 定时触发器控制器
 */
@Slf4j
@RestController
@RequestMapping("/aip/timerTrigger")
@RequiredArgsConstructor
public class TimerTriggerController {
    @Resource
    private TimerTriggerService timerTriggerService;

    @Resource
    private TimerTriggerDataService timerTriggerDataService;


    /**
     * 更新定时触发器
     */
    @PostMapping("/update")
    public BaseResponse<TimerTrigger> update(@RequestBody TimerTrigger timerTrigger) {
        try {
            // 参数验证
            if (StringUtils.isBlank(timerTrigger.getId())) {
                return BaseResponse.badRequest("触发器ID不能为空");
            }

            // 检查是否存在
            TimerTrigger oldTrigger = timerTriggerService.getById(timerTrigger.getId());
            if (oldTrigger == null) {
                return BaseResponse.badRequest("触发器不存在");
            }

            // 设置更新时间
            timerTrigger.setUpdateTime(LocalDateTime.now());

            // 更新触发器
            boolean success = timerTriggerService.updateById(timerTrigger);
            if (success) {
                // 根据状态重新调度或取消调度
                if (timerTrigger.getStatus() != null && timerTrigger.getStatus() == 0) {
                    timerTriggerService.disableTrigger(timerTrigger.getId());
                } else {
                    timerTriggerService.rescheduleTrigger(timerTrigger.getId());
                }
                return BaseResponse.ok(timerTrigger);
            } else {
                return BaseResponse.serviceError("更新触发器失败");
            }
        } catch (Exception e) {
            log.error("更新触发器失败", e);
            return BaseResponse.serviceError("更新触发器失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询定时触发器
     */
    @PostMapping("/page")
    public BaseResponse<PageDTO<TimerTrigger>> page(@RequestBody PageQueryParams params) {
        try {
            Page<TimerTrigger> page = new Page<>(params.getCurrent(), params.getPageSize());
            LambdaQueryWrapper<TimerTrigger> wrapper = new LambdaQueryWrapper<>();

            // 添加查询条件
            if (StringUtils.isNotBlank(params.getName())) {
                wrapper.like(TimerTrigger::getTriggerName, params.getName());
            }
            if (StringUtils.isNotBlank(params.getUserId())) {
                wrapper.eq(TimerTrigger::getUserId, params.getUserId());
            }
            if (params.getStatus() != null) {
                wrapper.eq(TimerTrigger::getStatus, params.getStatus());
            }

            // 添加排序
            wrapper.orderByDesc(TimerTrigger::getCreateTime);

            // 执行分页查询 - 使用数据服务而不是业务服务
            Page<TimerTrigger> resultPage = timerTriggerDataService.page(page, wrapper);
            return BaseResponse.ok(PageDTO.from(resultPage));
        } catch (Exception e) {
            log.error("分页查询触发器失败", e);
            return BaseResponse.serviceError("分页查询触发器失败: " + e.getMessage());
        }
    }


    /**
     * 删除定时触发器
     */
    @PostMapping("/delete/{id}")
    public BaseResponse<Void> delete(@PathVariable String id) {
        try {
            // 先禁用触发器
            timerTriggerService.disableTrigger(id);

            // 删除触发器 - 使用数据服务执行删除操作
            boolean success = timerTriggerDataService.removeById(id);
            if (success) {
                return BaseResponse.ok(null);
            } else {
                return BaseResponse.serviceError("删除触发器失败");
            }
        } catch (Exception e) {
            log.error("删除触发器失败", e);
            return BaseResponse.serviceError("删除触发器失败: " + e.getMessage());
        }
    }


    /**
     * 分页查询参数
     */
    @Data
    public static class PageQueryParams {
        private Integer current = 1;
        private Integer pageSize = 10;
        private String name;
        private String userId;
        private Integer status;
    }
}