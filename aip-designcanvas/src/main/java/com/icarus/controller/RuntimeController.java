package com.icarus.controller;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.icarus.common.minio.MinioUtil;
import com.icarus.common.pojo.AIPFlowDefinition;
import com.icarus.common.runtime.AIPFlowRuntime;
import com.icarus.common.runtime.NodeContext;
import com.icarus.dto.RuntimeRequest;
import com.icarus.dto.RuntimeResponse;
import com.icarus.entity.Canvas;
import com.icarus.entity.Endpoint;
import com.icarus.sdk.client.api.completion.chat.ChatMessage;
import com.icarus.sdk.client.utils.LLMClientUtil;
import com.icarus.service.CanvasService;
import com.icarus.service.EndpointService;
import com.icarus.utils.TxtUtil_SpireTxt;
import io.minio.StatObjectResponse;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

@RestController
@Slf4j
@RequestMapping("/aip/api")
@CrossOrigin(origins = "*", allowedHeaders = "*")
public class RuntimeController implements BaseSse, BaseRuntime{

	@Resource
	private CanvasService canvasService;

	@Resource
	private EndpointService endpointService;
	
	// 注入配置属性
    @Value("${runtime.use-stream-mode:false}")
    private boolean useStreamMode;



	@Value("${minio.bucket-name}")
	private String bucketName;

	@Resource
	private MinioUtil minioUtil;


	/**
	 * 通用接入接口，可以接受get/post/文件/sse-eventsource
	 *
	 * @param request
	 * @param response
	 * @param headers
	 * @return
	 */
	@RequestMapping("/**") // 使用 ** 来匹配多层路径
	@Transactional(readOnly = true)  // 添加事务注解
	public Object handleRequest(
			@RequestHeader(required = false) Map<String, String> headers,
			@ModelAttribute RuntimeRequest runtimeRequest,
			@RequestParam(required = false) Boolean stream,
			HttpServletRequest request,
			HttpServletResponse response) {

        // 根据配置和请求参数决定使用哪种模式处理请求
        boolean useStream = stream != null ? stream : useStreamMode;
        
        if (useStream) {
            log.info("使用Stream模式处理请求");
            return handleStreamRequest(headers, runtimeRequest, request, response);
        } else {
            log.info("使用普通模式处理请求");
            return handleNormalRequest(runtimeRequest, request);
        }
	}
    
    /**
     * 使用Stream模式处理请求
     */
    private Object handleStreamRequest(
            Map<String, String> headers,
            RuntimeRequest runtimeRequest,
            HttpServletRequest request,
            HttpServletResponse response) {
        // 获取会话ID，优先从请求头获取，其次从请求参数获取，最后生成新的
        String sessionId = headers.getOrDefault("aip-session", 
            request.getParameter("aip-session")) != null ?
            headers.getOrDefault("aip-session", request.getParameter("aip-session")) :
            UUID.randomUUID().toString();
        
        // 获取SSE发射器
        SseEmitter emitter = getOrInitSseEmitter(sessionId, response);
        
        // 设置响应头以防止缓冲
        response.setHeader("X-Accel-Buffering", "no");
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Connection", "keep-alive");
        
        // 确保RuntimeRequest的headers中包含sessionId
        if (runtimeRequest.getHeaders() == null) {
            runtimeRequest.setHeaders(new HashMap<>());
        }
        runtimeRequest.getHeaders().put("aip-session", sessionId);
        
        // 从请求中提取动态结构的数据（包含文件处理）
        Map<String, Object> dynamicData = extractDynamicRequestData(request, runtimeRequest);
        if (!dynamicData.isEmpty() && runtimeRequest.getData() == null) {
            runtimeRequest.setData(JSONUtil.toJsonStr(dynamicData));
        }

        // 异步处理请求
        CompletableFuture.runAsync(() -> {
            try {
                // 执行API请求处理
                RuntimeResponse<Object> result = procAIPRequest(request, runtimeRequest);
                
                // 发送完成事件
                emitter.send(SseEmitter.event().name("complete").data(result));
                
                // 关闭连接
                emitter.complete();
                
            } catch (Exception e) {
                log.error("处理SSE请求异常", e);
                try {
                    emitter.send(SseEmitter.event().name(sessionId).data(e.getMessage()));
                    emitter.complete();
                } catch (IOException ex) {
                    log.error("发送错误消息异常", ex);
                    emitter.completeWithError(ex);
                }
            }
        });
        
        // 设置回调
        emitter.onTimeout(() -> log.info("连接超时, sessionId={}", sessionId));
        emitter.onCompletion(() -> log.info("连接已关闭, sessionId={}", sessionId));
        emitter.onError((ex) -> log.info("连接发生错误, sessionId={}", sessionId, ex));
        
        return emitter;
    }
    
    /**
     * 使用普通模式处理请求
     */
    private Object handleNormalRequest(RuntimeRequest runtimeRequest, HttpServletRequest request) {
        // 处理文件
        pretreatmentFile(request, runtimeRequest);
        
        // 从请求中提取动态结构的数据
        Map<String, Object> dynamicData = extractDynamicRequestData(request, runtimeRequest);
        if (!dynamicData.isEmpty() && runtimeRequest.getData() == null) {
            runtimeRequest.setData(JSONUtil.toJsonStr(dynamicData));
        }
        
        // 同步执行流程并返回结果
        return procAIPRequest(request, runtimeRequest);
    }

	/**
	 * 获取会话标识
	 * @param request
	 * @return
	 */
	public String getSessionId(HttpServletRequest request){
		return request.getParameter(SESSION_ID);
	}

	/**
	 * 从请求中提取动态结构的数据，根据 Content-Type 进行不同处理
	 * 只负责提取原始数据，不进行额外加工
	 * 同时调用pretreatmentFile处理文件上传
	 */
	private Map<String, Object> extractDynamicRequestData(HttpServletRequest request, RuntimeRequest runtimeRequest) {
		try {
			// 先调用pretreatmentFile处理文件上传
			pretreatmentFile(request, runtimeRequest);
			
			String contentType = request.getContentType();
			log.info("请求 Content-Type: {}", contentType);

			Map<String, Object> requestData = new HashMap<>();

			if (contentType == null) {
				// 无 Content-Type 直接返回空Map
				return requestData;
			}

			// 处理JSON请求
			if (contentType.contains(MediaType.APPLICATION_JSON_VALUE)) {
				try (InputStream inputStream = request.getInputStream()) {
					String jsonContent = StreamUtils.copyToString(inputStream, StandardCharsets.UTF_8);
					if (StrUtil.isNotBlank(jsonContent)) {
						// 直接返回解析后的JSON数据，不做额外处理
						return JSONUtil.toBean(jsonContent, Map.class);
					}
				}
				return requestData;
			}
			
			// 处理表单数据请求（文件已由pretreatmentFile处理）
			else if (contentType.contains(MediaType.MULTIPART_FORM_DATA_VALUE)) {
				try {
					MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
					
					// 提取普通表单参数（文件已由pretreatmentFile处理）
					multipartRequest.getParameterMap().forEach((key, values) -> {
						if (values != null && values.length > 0) {
							requestData.put(key, values.length == 1 ? values[0] : values);
						}
					});
				} catch (Exception e) {
					log.info("处理 multipart 请求失败", e);
				}
				
				return requestData;
			}
			
			// 处理二进制流请求
			else if (contentType.contains(MediaType.APPLICATION_OCTET_STREAM_VALUE)) {
				try (InputStream inputStream = request.getInputStream()) {
					byte[] data = StreamUtils.copyToByteArray(inputStream);
					requestData.put("rawData", data);
					
					// 提取文件名（如果有）
					String contentDisposition = request.getHeader("Content-Disposition");
					if (contentDisposition != null && contentDisposition.contains("filename=")) {
						String fileName = contentDisposition
								.substring(contentDisposition.indexOf("filename=") + 9)
								.replaceAll(";?\\s*filename[*]?=.*", "")
								.replaceAll("\"", "")
								.trim();
						requestData.put("fileName", fileName);
					}
				}
				return requestData;
			}
			
			return requestData;
		} catch (Exception e) {
			log.error("提取请求数据异常", e);
			return new HashMap<>();
		}
	}

	/**
	 * 执行api请求
	 */
	private RuntimeResponse<Object> procAIPRequest(HttpServletRequest request, RuntimeRequest runtimeRequest) {
		// 获取完整的请求路径,反查画布配置
		String endpointURI = request.getRequestURI();
		endpointURI = endpointURI.substring(endpointURI.indexOf("/aip/api") + "/aip/api".length());
		String[] subs = endpointURI.split("/");
		String project = subs[1];
		String subsystem = subs[2];
		String module = subs[3];
		String path = subs[4];

		Endpoint endpoint = endpointService.getOne(new QueryWrapper<Endpoint>()
				.eq("project", project)
				.eq("subsystem", subsystem)
				.eq("module", module)
				.eq("path", path)
		);
		if (endpoint == null) {
			return RuntimeResponse.builder()
					.code(404)
					.statusText(endpointURI + " not found")
					.msg("未找到对应的端点:" + endpointURI)
					.build();
		}
		Canvas canvas = canvasService.getOne(new QueryWrapper<Canvas>()
				.eq("id", endpoint.getTemplateId())
		);
		if (canvas == null) {
			return RuntimeResponse.builder()
					.code(404)
					.statusText(endpointURI + " not found")
					.msg("未找到对应的Canvas:" + endpointURI)
					.build();
		}
		String canvasJson = canvas.getContent();

		// 转换为流程定义
		AIPFlowDefinition cotMap = JSONUtil.toBean(canvasJson, AIPFlowDefinition.class);
		// 构建运行器
		AIPFlowRuntime AIPFlowRuntime = new AIPFlowRuntime(cotMap);
		// 执行流程
		Map<String, Object> context = AIPFlowRuntime.executeFlow(runtimeRequest);
		// 获取最后节点
		AIPFlowDefinition.Cell endNode = AIPFlowRuntime.getEndNode();
		// 获取最后节点上下文
		NodeContext nodeContext = AIPFlowRuntime.getNodeContext(context, endNode.getId());
		RuntimeResponse<Object> result = RuntimeResponse.builder()
				.code(200)
				.statusText("success")
				.msg("执行成功")
				.build();
		if(ObjUtil.isNotNull(nodeContext)) result.setData(nodeContext.getOutputs());
		return result;
	}


	@PostMapping("/loadFile")
	public void loadFile(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
		String key = jsonObject.getStr("key");
		try (InputStream inputStream = minioUtil.downloadFile(bucketName, key)) {
			if (inputStream == null) {
				throw new RuntimeException("文件不存在");
			}

			// 获取文件信息以获取内容类型和文件名
			StatObjectResponse fileInfo = minioUtil.getFileInfo(bucketName, key);
			String contentType = fileInfo != null && fileInfo.contentType() != null
								? fileInfo.contentType()
								: "application/octet-stream";
			String objectName = key.contains("/") ? key.substring(key.lastIndexOf("/")+1) : key;
			String encodedObjectName = java.net.URLEncoder.encode(objectName, java.nio.charset.StandardCharsets.UTF_8);
			// 设置响应内容类型和文件名
			response.setHeader("Access-Control-Expose-Headers", "Content-Type, Content-Disposition");
			response.setContentType(contentType);
			response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedObjectName + "\"");

			byte[] buffer = new byte[4096];
			int bytesRead;
			while ((bytesRead = inputStream.read(buffer)) != -1) {
				response.getOutputStream().write(buffer, 0, bytesRead);
			}
		} catch (Exception e) {
			log.error("下载文件失败", e);
		}
	}
}
