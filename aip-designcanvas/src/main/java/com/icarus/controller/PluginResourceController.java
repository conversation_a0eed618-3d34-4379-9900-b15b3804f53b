package com.icarus.controller;

import com.icarus.common.cotnode.BaseNode;
import com.icarus.deploy.PluginResourceService;
import com.icarus.plugin.api.NodeProvider;
import com.icarus.plugin.container.PluginManagerContainer;
import com.icarus.plugin.info.PluginInfo;
import com.icarus.plugin.spring.PluginSpringContextHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 插件功能接口
 * <AUTHOR>
 */
@RestController
@RequestMapping("/plugin")
@RequiredArgsConstructor
@Slf4j
public class PluginResourceController {


    private final PluginResourceService pluginResourceService;

    /**
     * 获取插件信息
     * @return 返回插件信息
     */
    @GetMapping
    public List<PluginInfo> getPluginInfo(){
        return pluginResourceService.getPlugins();
    }


    /**
     * 安装和启动插件
     * @param file 插件文件
     * @return 返回操作结果
     */
    @PostMapping("/installAndStart")
    public String installAndStart(@RequestParam ("jarFile") MultipartFile file) throws Exception{
       return pluginResourceService.installAndStart(file);
    }


    /**
     * 根据插件id卸载插件
     * @param id 插件id
     * @return 返回操作结果
     */
    @PostMapping("/uninstall/{id}")
    public boolean uninstall(@PathVariable("id") String id){
        return pluginResourceService.uninstall(id);
    }


    /**
     * 根据插件路径安装插件。该插件jar必须在服务器上存在。注意: 该操作只适用于生产环境
     * @param path 插件路径名称
     * @return 操作结果
     */
    @PostMapping("/installByPath")
    public String install(@RequestParam("path") String path){
        return pluginResourceService.loadPlugin(path);
    }

    /**
     * <AUTHOR>
     * @description 获取插件扩展信息
     **/
    @GetMapping("/getPluginExtInfoByPluginId")
    public String getPluginExtInfoByPluginId(@RequestParam String pluginId, String shape) {
        // 获取插件管理容器类
        PluginManagerContainer pluginManagerContainer = PluginSpringContextHolder.getBean(PluginManagerContainer.class);
        // 从插件管理容器中获取插件的ApplicationContext
        ApplicationContext pluginApplicationContext = pluginManagerContainer.getPluginApplicationContext(pluginId);
        if(pluginApplicationContext == null){
            log.info("当前插件已停止或未安装:{}",pluginId);
            return "当前插件已停止或未安装";
        }
        // 从插件ApplicationContext中获取插件中定义的bean
        NodeProvider provider = pluginApplicationContext.getBean(NodeProvider.class);
        BaseNode node = provider.build(shape);
        log.info("node:{}",node);
        return node.toString();
    }
}
