package com.icarus.controller;

import com.icarus.common.rest.BaseResponse;
import com.icarus.dto.LLMRequest;
import com.icarus.dto.ModelResponse;
import com.icarus.service.NodeAIGenService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@Slf4j
@RequestMapping("/aip/nodemake")
public class NodeAIGenController {

    @Resource
    private NodeAIGenService nodeAIGenService;
    @PostMapping("/getconfig")
    public BaseResponse<ModelResponse> getConfig(@RequestBody LLMRequest  request){
        //调用大模型生成节点配置
        ModelResponse result = nodeAIGenService.getNodeConfig(request);
        if (StringUtils.equals(result.getStatus(), "success")){
            return BaseResponse.ok(result);
        }
        String error = String.format("requestId:%s,调用模型失败", result.getRequestId());
        return BaseResponse.serviceError(error,result);
    }

}
