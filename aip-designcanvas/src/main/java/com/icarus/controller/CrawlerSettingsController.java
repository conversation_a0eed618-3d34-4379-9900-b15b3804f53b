package com.icarus.controller;

import com.icarus.common.rest.BaseResponse;
import com.icarus.dto.PageDTO;
import com.icarus.dto.CrawlerSettingsDTO;
import com.icarus.entity.CrawlerSettings;
import com.icarus.service.CrawlerSettingsService;
import com.icarus.vo.CrawlerPageRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.BeanUtils;

/**
 * 爬虫配置管理接口
 * 提供爬虫配置的增删改查及状态管理等功能
 */
@Slf4j
@RestController
@RequestMapping("/aip/crawler/settings")
@RequiredArgsConstructor
@Api(tags = "爬虫配置管理")
public class CrawlerSettingsController {

    private final CrawlerSettingsService crawlerSettingsService;

    /**
     * 分页查询爬虫配置
     *
     * @param dto 包含查询参数的配置对象
     * @return 分页结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询爬虫配置", notes = "支持关键词搜索、状态过滤等")
    public BaseResponse<PageDTO<CrawlerSettingsDTO>> page(@RequestBody CrawlerSettingsDTO dto) {
        try {
            Integer current = dto.getCurrent() != null ? dto.getCurrent() : 1;
            Integer pageSize = dto.getPageSize() != null ? dto.getPageSize() : 10;
            
            log.info("分页查询爬虫配置: current={}, pageSize={}, keyword={}, status={}", 
                     current, pageSize, dto.getKeyword(), dto.getStatus());
            
            // 构建请求参数
            CrawlerPageRequest request = new CrawlerPageRequest();
            request.setCurrent(current);
            request.setPageSize(pageSize);
            request.setKeyword(dto.getKeyword());
            request.setStatus(dto.getStatus());
            request.setStartTime(dto.getStartTime());
            request.setEndTime(dto.getEndTime());
            
            // 调用服务
            Page<CrawlerSettings> result = crawlerSettingsService.page(request);
            
            // 转换为统一的PageDTO格式
            PageDTO<CrawlerSettingsDTO> pageDTO = new PageDTO<>();
            List<CrawlerSettingsDTO> dtoList = result.getContent().stream()
                .map(entity -> {
                    CrawlerSettingsDTO settingsDTO = new CrawlerSettingsDTO();
                    BeanUtils.copyProperties(entity, settingsDTO);
                    return settingsDTO;
                })
                .collect(Collectors.toList());
            
            pageDTO.setRecords(dtoList);
            pageDTO.setCurrent(current);
            pageDTO.setPageSize(pageSize);
            pageDTO.setTotal(result.getTotalElements());
            
            return BaseResponse.ok(pageDTO);
        } catch (Exception e) {
            log.error("分页查询爬虫配置失败", e);
            return BaseResponse.serviceError("分页查询爬虫配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有可用的爬虫配置
     *
     * @return 配置列表
     */
    @PostMapping("/list")
    @ApiOperation(value = "获取所有可用的爬虫配置", notes = "返回所有状态为active的爬虫配置")
    public BaseResponse<List<CrawlerSettingsDTO>> listActive() {
        try {
            log.info("获取所有可用的爬虫配置");
            List<CrawlerSettingsDTO> list = crawlerSettingsService.listByStatus("active");
            return BaseResponse.ok(list);
        } catch (Exception e) {
            log.error("获取可用爬虫配置失败", e);
            return BaseResponse.serviceError("获取可用爬虫配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取爬虫配置详情
     *
     * @param dto 包含ID的爬虫配置
     * @return 配置详情
     */
    @PostMapping("/get")
    @ApiOperation(value = "获取爬虫配置详情", notes = "根据ID获取爬虫配置的详细信息")
    public BaseResponse<CrawlerSettingsDTO> getDetail(@RequestBody CrawlerSettingsDTO dto) {
        try {
            log.info("获取爬虫配置详情: id={}", dto.getId());
            if (dto.getId() == null) {
                return BaseResponse.badRequest("配置ID不能为空");
            }
            
            CrawlerSettingsDTO result = crawlerSettingsService.getDetail(dto.getId());
            if (result == null) {
                return BaseResponse.badRequest("配置不存在");
            }
            return BaseResponse.ok(result);
        } catch (Exception e) {
            log.error("获取爬虫配置详情失败", e);
            return BaseResponse.serviceError("获取爬虫配置详情失败: " + e.getMessage());
        }
    }

    /**
     * 批量获取爬虫配置
     *
     * @param ids 配置ID列表
     * @return 配置列表
     */
    @PostMapping("/batch")
    @ApiOperation(value = "批量获取爬虫配置", notes = "根据ID列表批量获取爬虫配置")
    public BaseResponse<List<CrawlerSettingsDTO>> batchGet(@RequestBody List<Long> ids) {
        try {
            log.info("批量获取爬虫配置: ids={}", ids);
            if (ids == null || ids.isEmpty()) {
                return BaseResponse.badRequest("配置ID列表不能为空");
            }
            
            List<CrawlerSettingsDTO> list = crawlerSettingsService.batchGetByIds(ids);
            return BaseResponse.ok(list);
        } catch (Exception e) {
            log.error("批量获取爬虫配置失败", e);
            return BaseResponse.serviceError("批量获取爬虫配置失败: " + e.getMessage());
        }
    }

    /**
     * 创建爬虫配置
     *
     * @param dto 配置信息
     * @return 创建结果
     */
    @PostMapping("/create")
    @ApiOperation(value = "创建爬虫配置", notes = "创建新的爬虫配置")
    public BaseResponse<CrawlerSettingsDTO> create(@RequestBody @Valid CrawlerSettingsDTO dto) {
        try {
            log.info("创建爬虫配置: {}", dto);
            if (dto.getName() == null) {
                return BaseResponse.badRequest("配置名称不能为空");
            }
            
            Long id = crawlerSettingsService.create(dto);
            dto.setId(id);
            return BaseResponse.ok(dto);
        } catch (Exception e) {
            log.error("创建爬虫配置失败", e);
            return BaseResponse.serviceError("创建爬虫配置失败: " + e.getMessage());
        }
    }

    /**
     * 更新爬虫配置
     *
     * @param dto 配置信息
     * @return 更新结果
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新爬虫配置", notes = "更新现有的爬虫配置")
    public BaseResponse<CrawlerSettingsDTO> update(@RequestBody @Valid CrawlerSettingsDTO dto) {
        try {
            log.info("更新爬虫配置: dto={}", dto);
            if (dto.getId() == null) {
                return BaseResponse.badRequest("配置ID不能为空");
            }
            
            CrawlerSettingsDTO oldConfig = crawlerSettingsService.getDetail(dto.getId());
            if (oldConfig == null) {
                return BaseResponse.badRequest("配置不存在");
            }
            
            boolean success = crawlerSettingsService.update(dto);
            if (!success) {
                return BaseResponse.badRequest("更新失败");
            }
            
            return BaseResponse.ok(dto);
        } catch (Exception e) {
            log.error("更新爬虫配置失败", e);
            return BaseResponse.serviceError("更新爬虫配置失败: " + e.getMessage());
        }
    }

    /**
     * 删除爬虫配置
     *
     * @param dto 包含ID的爬虫配置
     * @return 删除结果
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除爬虫配置", notes = "删除指定的爬虫配置")
    public BaseResponse<Void> delete(@RequestBody CrawlerSettingsDTO dto) {
        try {
            log.info("删除爬虫配置: id={}", dto.getId());
            if (dto.getId() == null) {
                return BaseResponse.badRequest("配置ID不能为空");
            }
            
            CrawlerSettings existConfig = crawlerSettingsService.getById(dto.getId());
            if (existConfig == null) {
                return BaseResponse.badRequest("配置不存在");
            }
            
            boolean success = crawlerSettingsService.delete(dto.getId());
            if (!success) {
                return BaseResponse.badRequest("删除失败");
            }
            
            return BaseResponse.ok(null);
        } catch (Exception e) {
            log.error("删除爬虫配置失败", e);
            return BaseResponse.serviceError("删除爬虫配置失败: " + e.getMessage());
        }
    }

    /**
     * 更新爬虫配置状态
     *
     * @param dto 包含ID和status的爬虫配置
     * @return 更新结果
     */
    @PostMapping("/update-status")
    @ApiOperation(value = "更新爬虫配置状态", notes = "启用或禁用爬虫配置")
    public BaseResponse<Void> updateStatus(@RequestBody CrawlerSettingsDTO dto) {
        try {
            log.info("更新爬虫配置状态: id={}, status={}", dto.getId(), dto.getStatus());
            if (dto.getId() == null) {
                return BaseResponse.badRequest("配置ID不能为空");
            }
            
            if (dto.getStatus() == null) {
                return BaseResponse.badRequest("状态不能为空");
            }
            
            // 验证状态值
            String status = dto.getStatus();
            if (!status.equals("active") && !status.equals("inactive")) {
                return BaseResponse.badRequest("无效的状态值，只能是active或inactive");
            }
            
            CrawlerSettings existConfig = crawlerSettingsService.getById(dto.getId());
            if (existConfig == null) {
                return BaseResponse.badRequest("配置不存在");
            }
            
            boolean success = crawlerSettingsService.updateStatus(dto.getId(), status);
            if (!success) {
                return BaseResponse.badRequest("状态更新失败");
            }
            
            return BaseResponse.ok(null);
        } catch (Exception e) {
            log.error("更新爬虫配置状态失败", e);
            return BaseResponse.serviceError("更新爬虫配置状态失败: " + e.getMessage());
        }
    }

    /**
     * 复制爬虫配置
     *
     * @param dto 包含ID的爬虫配置
     * @return 新配置信息
     */
    @PostMapping("/duplicate")
    @ApiOperation(value = "复制爬虫配置", notes = "复制现有的爬虫配置为新配置")
    public BaseResponse<CrawlerSettingsDTO> duplicate(@RequestBody CrawlerSettingsDTO dto) {
        try {
            log.info("复制爬虫配置: id={}", dto.getId());
            if (dto.getId() == null) {
                return BaseResponse.badRequest("配置ID不能为空");
            }
            
            // 获取原配置
            CrawlerSettingsDTO source = crawlerSettingsService.getDetail(dto.getId());
            if (source == null) {
                return BaseResponse.badRequest("原配置不存在");
            }
            
            // 创建副本
            source.setId(null); // 清除ID以创建新记录
            source.setName(source.getName() + " (复制)");
            source.setStatus("inactive"); // 默认设置为禁用状态
            
            // 保存新配置
            Long newId = crawlerSettingsService.create(source);
            source.setId(newId);
            
            return BaseResponse.ok(source);
        } catch (Exception e) {
            log.error("复制爬虫配置失败", e);
            return BaseResponse.serviceError("复制爬虫配置失败: " + e.getMessage());
        }
    }

    /**
     * 测试爬虫配置
     *
     * @param dto 包含ID的爬虫配置
     * @return 测试结果
     */
    @PostMapping("/test")
    @ApiOperation(value = "测试爬虫配置", notes = "测试爬虫配置是否有效")
    public BaseResponse<Object> test(@RequestBody CrawlerSettingsDTO dto) {
        try {
            log.info("测试爬虫配置: id={}", dto.getId());
            if (dto.getId() == null) {
                return BaseResponse.badRequest("配置ID不能为空");
            }
            
            // 获取配置
            CrawlerSettingsDTO config = crawlerSettingsService.getDetail(dto.getId());
            if (config == null) {
                return BaseResponse.badRequest("配置不存在");
            }
            
            // TODO: 实现配置测试逻辑
            // 这里可以实现简单的网页访问测试，验证选择器是否能匹配到元素等
            // 具体实现可以根据实际需求扩展
            
            return BaseResponse.ok("配置有效，测试通过");
        } catch (Exception e) {
            log.error("测试爬虫配置失败", e);
            return BaseResponse.serviceError("测试爬虫配置失败: " + e.getMessage());
        }
    }
} 