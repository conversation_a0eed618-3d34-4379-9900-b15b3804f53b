package com.icarus.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.icarus.entity.Scene;
import com.icarus.service.SceneService;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 场景控制器
 * <AUTHOR>
 * @date 2025-03-25
 */
@RestController
@RequestMapping("/aip/scene")
public class SceneController {

    @Autowired
    private SceneService sceneService;

    /**
     * 查询所有场景项
     */
    @GetMapping("/item")
    public List<SceneItem> item(){
        LambdaQueryWrapper<Scene> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Scene::getId, Scene::getSceneName);
        List<Scene> list = sceneService.list(queryWrapper);
        return list.stream()
                .map(scene -> new SceneItem(scene.getId(), scene.getSceneName()))
                .collect(Collectors.toList());
    }

    /**
     * 场景字典项
     */
    @Data
    @AllArgsConstructor
    public static class SceneItem {
        private String id; // id
        private String name; // name
    }

}
