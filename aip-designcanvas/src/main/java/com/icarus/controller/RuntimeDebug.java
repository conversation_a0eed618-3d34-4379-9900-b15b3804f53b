package com.icarus.controller;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.icarus.common.pojo.AIPFlowDefinition;
import com.icarus.common.runtime.AIPFlowRuntime;
import com.icarus.common.runtime.NodeContext;
import com.icarus.dto.RuntimeRequest;
import com.icarus.dto.RuntimeResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@RestController
@Slf4j
@RequestMapping("/aip/debug/canvas")
public class RuntimeDebug implements BaseRuntime, BaseSse {

    // 注入配置属性
    @Value("${runtime.use-stream-mode:false}")
    private boolean useStreamMode;

    /**
     * 如果前端是试运行（debug）则整个json都要运行，这时候传进来当前节点是startnode
     * 如果前端是debug某个节点，这时当前传进来的currentnode当前节点应该是某个节点，只执行一步
     * commons必须传入两个数据：1. canvas，2. 当前节点
     * @return
     */
    @RequestMapping(value = "/debug")
    public Object debug(
            @RequestHeader(required = false) Map<String, String> headers,
            @ModelAttribute RuntimeRequest runtimeRequest,
            @RequestParam(required = false) Boolean stream,
            HttpServletRequest request,
            HttpServletResponse response
    ) {
        // 根据配置和请求参数决定使用哪种模式处理请求
        boolean useStream = stream != null ? stream : useStreamMode;
        
        if (useStream) {
            log.info("使用Stream模式处理请求");
            return handleStreamRequest(headers, runtimeRequest, request, response);
        } else {
            log.info("使用普通模式处理请求");
            return handleNormalRequest(runtimeRequest, request);
        }
    }
    
    /**
     * 使用Stream模式处理请求
     */
    private Object handleStreamRequest(
            Map<String, String> headers,
            RuntimeRequest runtimeRequest,
            HttpServletRequest request,
            HttpServletResponse response) {
        // 获取会话ID，优先从请求头获取，其次从请求参数获取，最后生成新的
        String sessionId = headers.getOrDefault("aip-session", 
            request.getParameter("aip-session")) != null ?
            headers.getOrDefault("aip-session", request.getParameter("aip-session")) :
            UUID.randomUUID().toString();
        
        // 获取SSE发射器
        SseEmitter emitter = getOrInitSseEmitter(sessionId, response);
        
        // 设置响应头以防止缓冲
        response.setHeader("X-Accel-Buffering", "no");
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Connection", "keep-alive");
        
        // 确保RuntimeRequest的headers中包含sessionId
        if (runtimeRequest.getHeaders() == null) {
            runtimeRequest.setHeaders(new HashMap<>());
        }
        runtimeRequest.getHeaders().put("aip-session", sessionId);
        
        // 从请求中提取动态结构的数据（包含文件处理）
        Map<String, Object> dynamicData = extractDynamicRequestData(request, runtimeRequest);
        if (!dynamicData.isEmpty() && runtimeRequest.getData() == null) {
            runtimeRequest.setData(JSONUtil.toJsonStr(dynamicData));
        }

        // 异步处理请求
        CompletableFuture.runAsync(() -> {
            try {
                // 执行流程
                RuntimeResponse<Object> result = getObjectRuntimeResponse(runtimeRequest);
                
                // 发送处理结果
                emitter.send(SseEmitter.event().name("complete").data(result));
                
                // 关闭连接
                emitter.complete();

            } catch (Exception e) {
                log.error("处理SSE请求异常", e);
                try {
                    emitter.send(SseEmitter.event().name(sessionId).data(e.getMessage()));
                    emitter.complete();
                } catch (IOException ex) {
                    log.error("发送错误消息异常", ex);
                    emitter.completeWithError(ex);
                }
            }
        });
        
        // 设置回调
        emitter.onTimeout(() -> log.info("连接超时, sessionId={}", sessionId));
        emitter.onCompletion(() -> log.info("连接已关闭, sessionId={}", sessionId));
        emitter.onError((ex) -> log.info("连接发生错误, sessionId={}", sessionId, ex));
        
        return emitter;
    }
    
    /**
     * 使用普通模式处理请求
     */
    private Object handleNormalRequest(RuntimeRequest runtimeRequest, HttpServletRequest request) {
        // 处理文件
        pretreatmentFile(request, runtimeRequest);
        
        // 从请求中提取动态结构的数据
        Map<String, Object> dynamicData = extractDynamicRequestData(request, runtimeRequest);
        if (!dynamicData.isEmpty() && runtimeRequest.getData() == null) {
            runtimeRequest.setData(JSONUtil.toJsonStr(dynamicData));
        }
        
        // 同步执行流程并返回结果
        return getObjectRuntimeResponse(runtimeRequest);
    }

    /**
     * 从请求中提取动态结构的数据，根据 Content-Type 进行不同处理
     * 同时调用pretreatmentFile处理文件上传
     */
    private Map<String, Object> extractDynamicRequestData(HttpServletRequest request, RuntimeRequest runtimeRequest) {
        try {
            // 先调用pretreatmentFile处理文件上传
            pretreatmentFile(request, runtimeRequest);
            
            String contentType = request.getContentType();
            log.info("请求 Content-Type: {}", contentType);

            Map<String, Object> requestData = new HashMap<>();

            if (contentType == null) {
                // 无 Content-Type 直接返回空Map
                return requestData;
            }

            // 处理JSON请求
            if (contentType.contains(MediaType.APPLICATION_JSON_VALUE)) {
                try (InputStream inputStream = request.getInputStream()) {
                    String jsonContent = StreamUtils.copyToString(inputStream, StandardCharsets.UTF_8);
                    if (StrUtil.isNotBlank(jsonContent)) {
                        // 直接返回解析后的JSON数据，不做额外处理
                        return JSONUtil.toBean(jsonContent, Map.class);
                    }
                }
                return requestData;
            }
            
            // 处理表单数据请求（文件已由pretreatmentFile处理）
            else if (contentType.contains(MediaType.MULTIPART_FORM_DATA_VALUE)) {
                try {
                    MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
                    
                    // 提取普通表单参数（文件已由pretreatmentFile处理）
                    multipartRequest.getParameterMap().forEach((key, values) -> {
                        if (values != null && values.length > 0) {
                            requestData.put(key, values.length == 1 ? values[0] : values);
                        }
                    });
                } catch (Exception e) {
                    log.info("处理 multipart 请求失败", e);
                }
                
                return requestData;
            }
            
            // 处理二进制流请求
            else if (contentType.contains(MediaType.APPLICATION_OCTET_STREAM_VALUE)) {
                try (InputStream inputStream = request.getInputStream()) {
                    byte[] data = StreamUtils.copyToByteArray(inputStream);
                    requestData.put("rawData", data);
                    
                    // 提取文件名（如果有）
                    String contentDisposition = request.getHeader("Content-Disposition");
                    if (contentDisposition != null && contentDisposition.contains("filename=")) {
                        String fileName = contentDisposition
                                .substring(contentDisposition.indexOf("filename=") + 9)
                                .replaceAll(";?\\s*filename[*]?=.*", "")
                                .replaceAll("\"", "")
                                .trim();
                        requestData.put("fileName", fileName);
                    }
                }
                return requestData;
            }
            
            return requestData;
        } catch (Exception e) {
            log.error("提取请求数据异常", e);
            return new HashMap<>();
        }
    }

    private RuntimeResponse<Object> getObjectRuntimeResponse(RuntimeRequest request) {
        // 获取 commons 中的字段
        Map<String, Object> commons = JSONUtil.toBean((String) request.getCommons(), Map.class);
        Object canvas = commons.get("canvas");
        // 将 canvas 转换为 JSON 字符串
        String canvasJson = JSONUtil.parse(canvas).toString();
        AIPFlowDefinition cotMap = JSONUtil.toBean(canvasJson, AIPFlowDefinition.class);
        // 清理 request 中的 commons.canvas 数据
        commons.remove("canvas");  // 移除 canvas 数据
        request.setCommons(JSONUtil.toJsonStr(commons));
        AIPFlowRuntime AIPFlowRuntime = new AIPFlowRuntime(cotMap);
        Object currentNode = commons.get("currentNode");
        if(ObjUtil.isNotNull(currentNode)) {
            // 执行节点
            Map<String, Object> context = AIPFlowRuntime.executeNode(currentNode.toString(), request);
            return getResponseData(context);
        }else{
            // 执行流程
            Map<String, Object> context = AIPFlowRuntime.executeFlow(request);
            return getResponseData(context);
        }
    }

    private static RuntimeResponse<Object> getResponseData(Map<String, Object> context) {
        // 构建返回结果
        Map<String, Object> responseData = new HashMap<>();
        Map<String, Object> flowContext = new HashMap<>();
        List<NodeContext> nodes = new ArrayList<>();
        
        // 遍历 context，提取节点数据和流程数据
        for (Map.Entry<String, Object> entry : context.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            if (value instanceof NodeContext) {
                // 节点数据，添加到 nodes 列表
                nodes.add((NodeContext) value);
            } else {
                // 非节点数据，添加到流程上下文
                if (!"request".equals(key)) {  // 排除原始 request
                    flowContext.put(key, value);
                }
            }
        }
        
        // 构建最终返回结构
        responseData.put("flow", flowContext);  // 流程相关数据
        responseData.put("nodes", nodes);       // 节点数据列表
        
        return RuntimeResponse.builder()
                .code(200)
                .statusText("success")
                .msg("执行成功")
                .data(responseData)
                .build();
    }
}
