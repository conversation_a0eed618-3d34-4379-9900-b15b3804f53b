package com.icarus.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.common.rest.BaseResponse;
import com.icarus.dto.PageDTO;
import com.icarus.entity.Canvas;
import com.icarus.entity.Endpoint;
import com.icarus.service.CanvasService;
import com.icarus.service.EndpointService;
import com.icarus.service.FileStorageService;
import com.icarus.service.model.FileInfo;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 画布管理控制器
 * 提供画布的增删改查功能
 */
@Slf4j
@RestController
@RequestMapping("/aip/canvas")
@RequiredArgsConstructor
public class CanvasController {
    @Resource
    private final CanvasService canvasService;
    @Autowired(required = false)
    private FileStorageService fileStorageService;
    @Resource private final EndpointService endpointService;
    /**
     * 创建新画布
     * @param canvas 画布信息，必须包含name和directoryId
     * @return 创建后的画布信息，包含自动生成的ID
     */
    @PostMapping("/create")
    public BaseResponse<Canvas> create(@RequestBody Canvas canvas) {
        try {
            if (canvas.getDirectoryId() == null) {
                return BaseResponse.badRequest("目录ID不能为空");
            }
            //权限放开所有人可以查看画布
            canvas.setUserId("system");
            canvasService.save(canvas);
            return BaseResponse.ok(canvas);
        } catch (Exception e) {
            log.error("创建画布失败", e);
            return BaseResponse.serviceError("创建画布失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新画布信息
     * @param canvas 画布信息，必须包含id
     * @return 更新后的画布信息
     */
    @PostMapping("/update")
    public BaseResponse<Canvas> update(@RequestBody Canvas canvas) {
        try {
            Canvas oldCanvas = canvasService.getById(canvas.getId());
            if (oldCanvas == null) {
                return BaseResponse.badRequest("画布不存在");
            }
            //权限放开所有人后续可以去掉
            canvas.setUserId("system");
            canvasService.updateById(canvas);
            return BaseResponse.ok(canvas);
        } catch (Exception e) {
            log.error("更新画布失败", e);
            return BaseResponse.serviceError("更新画布失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除画布
     * 如果画布被端点引用，需要先删除相关端点
     * @param canvas 对象
     */
    @PostMapping("/delete")
    public BaseResponse<Void> delete(@RequestBody Canvas canvas) {
        try {
            // TODO: 检查画布是否被端点引用
            String id = canvas.getId();
            Canvas queryResult = canvasService.getById(id);
            canvas.setUserId("system");
            if (queryResult == null) {
                return BaseResponse.badRequest("画布不存在");
            }
            if (!StringUtils.equals(canvas.getUserId(),queryResult.getUserId())){
                return BaseResponse.badRequest("无权限删除该画布");
            }
            //删除画布前先校验有没有接口发布
            Endpoint one = endpointService.getOne(new LambdaQueryWrapper<Endpoint>().eq(Endpoint::getTemplateId, id).last("limit 1"));
            if(one!= null){
                return BaseResponse.badRequest("画布已经发布为接口，确认接口不使用，删除相关接口后再删除画布");
            }
            canvasService.removeById(id);
            return BaseResponse.ok(null);
        } catch (Exception e) {
            log.error("删除画布失败", e);
            return BaseResponse.serviceError("删除画布失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据ID获取画布信息
     * @param id 画布ID
     * @return 画布信息
     */
    @GetMapping("/get/{id}")
    public BaseResponse<Canvas> getById(@PathVariable String id) {
        try {
            Canvas canvas = canvasService.getById(id);
            if (canvas == null) {
                return BaseResponse.badRequest("画布不存在");
            }
            return BaseResponse.ok(canvas);
        } catch (Exception e) {
            log.error("获取画布失败", e);
            return BaseResponse.serviceError("获取画布失败: " + e.getMessage());
        }
    }
    
    /**
     * 分页查询画布列表
     * @param current 当前页码，默认1
     * @param pageSize 每页大小，默认10
     * @param name 画布名称，可选，支持模糊查询
     * @return 分页结果
     */
    @GetMapping("/page")
    public BaseResponse<PageDTO<Canvas>> page(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String name) {
        try {
            Page<Canvas> page = new Page<>(current, pageSize);
            LambdaQueryWrapper<Canvas> wrapper = new LambdaQueryWrapper<>();
            wrapper.like(name != null, Canvas::getName, name);
            return BaseResponse.ok(PageDTO.from(canvasService.page(page, wrapper)));
        } catch (Exception e) {
            log.error("分页查询画布失败", e);
            return BaseResponse.serviceError("分页查询画布失败: " + e.getMessage());
        }
    }

    /**
     * 上传文件
     * @param file
     */
    @PostMapping("/upload-file")
    public BaseResponse uploadFile(@RequestPart(value = "file") MultipartFile file){
        FileInfo fileInfo = fileStorageService.uploadFile(file,"upload");
        return BaseResponse.ok(fileInfo);
    }
} 