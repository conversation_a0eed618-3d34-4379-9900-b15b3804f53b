package com.icarus.controller;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.common.rest.BaseResponse;
import com.icarus.dto.PageDTO;
import com.icarus.entity.DesignerEnum;
import com.icarus.service.DesignerEnumService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 设计器枚举管理控制器
 * 提供枚举的增删改查功能
 */
@Slf4j
@RestController
@RequestMapping("/aip/designer-enum")
@RequiredArgsConstructor
public class DesignerEnumController {
    
    private final DesignerEnumService designerEnumService;
    
    /**
     * 创建枚举
     * @param designerEnum 枚举信息，必须包含type和content
     * @return 创建后的枚举信息
     */
    @PostMapping("/create")
    public BaseResponse<DesignerEnum> create(@RequestBody DesignerEnum designerEnum) {
        try {
            // 1. 基础验证
            if (designerEnum.getType() == null || designerEnum.getContent() == null) {
                return BaseResponse.badRequest("类型和内容不能为空");
            }
            
            // 2. 检查type是否重复
            LambdaQueryWrapper<DesignerEnum> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DesignerEnum::getType, designerEnum.getType());
            if (designerEnumService.count(wrapper) > 0) {
                return BaseResponse.badRequest("枚举类型已存在");
            }
            
            designerEnumService.save(designerEnum);
            return BaseResponse.ok(designerEnum);
        } catch (Exception e) {
            log.error("创建枚举失败", e);
            return BaseResponse.serviceError("创建枚举失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新枚举
     * @param designerEnum 枚举信息，包含id或type
     * @return 更新后的枚举信息
     */
    @PostMapping("/update")
    public BaseResponse<DesignerEnum> update(@RequestBody DesignerEnum designerEnum) {
        try {
            if (designerEnum.getType() == null) {
                return BaseResponse.badRequest("枚举类型不能为空");
            }
            
            DesignerEnum existingEnum = null;
            
            // 1. 如果提供了id，优先通过id查找
            if (designerEnum.getId() != null) {
                existingEnum = designerEnumService.getById(designerEnum.getId());
                if (existingEnum == null) {
                    return BaseResponse.badRequest("指定ID的枚举不存在");
                }
                // 直接更新
                designerEnumService.updateById(designerEnum);
            } else {
                // 2. 如果没有提供id，通过type查找
                LambdaQueryWrapper<DesignerEnum> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(DesignerEnum::getType, designerEnum.getType());
                existingEnum = designerEnumService.getOne(queryWrapper);
                
                if (existingEnum == null) {
                    // 如果记录不存在，则创建新记录
                    designerEnumService.save(designerEnum);
                } else {
                    // 如果记录存在，则更新现有记录
                    designerEnum.setId(existingEnum.getId());
                    designerEnumService.updateById(designerEnum);
                }
            }
            
            return BaseResponse.ok(designerEnum);
        } catch (Exception e) {
            log.error("更新枚举失败", e);
            return BaseResponse.serviceError("更新枚举失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除枚举
     * @param id 枚举ID
     */
    @PostMapping("/delete/{id}")
    public BaseResponse<Void> delete(@PathVariable String id) {
        try {
            designerEnumService.removeById(id);
            return BaseResponse.ok(null);
        } catch (Exception e) {
            log.error("删除枚举失败", e);
            return BaseResponse.serviceError("删除枚举失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据ID获取枚举
     * @param id 枚举ID
     * @return 枚举信息
     */
    @GetMapping("/get/{id}")
    public BaseResponse<DesignerEnum> getById(@PathVariable String id) {
        try {
            DesignerEnum designerEnum = designerEnumService.getById(id);
            if (designerEnum == null) {
                return BaseResponse.badRequest("枚举不存在");
            }
            return BaseResponse.ok(designerEnum);
        } catch (Exception e) {
            log.error("获取枚举失败", e);
            return BaseResponse.serviceError("获取枚举失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据类型获取枚举列表
     * @param type 枚举类型
     * @return 枚举列表
     */
    @GetMapping("/list/{type}")
    public BaseResponse<List<Map<String, Object>>> listByType(@PathVariable String type) {
        try {
            List<Map<String, Object>> result = designerEnumService.listByType(type)
                .stream()
                .map(item -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", item.getId());
                    map.put("type", item.getType());
                    // 解析 content 字符串为 List
                    map.put("content", JSONUtil.parseArray(item.getContent()));
                    map.put("createTime", item.getCreateTime());
                    map.put("updateTime", item.getUpdateTime());
                    return map;
                })
                .collect(Collectors.toList());
            
            return BaseResponse.ok(result);
        } catch (Exception e) {
            log.error("获取枚举列表失败", e);
            return BaseResponse.serviceError("获取枚举列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 分页查询枚举列表
     * @param current 当前页码，默认1
     * @param pageSize 每页大小，默认10
     * @param type 枚举类型，可选，精确匹配
     * @return 枚举列表
     */
    @GetMapping("/page")
    public BaseResponse<PageDTO<DesignerEnum>> page(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String type) {
        try {
            Page<DesignerEnum> page = new Page<>(current, pageSize);
            LambdaQueryWrapper<DesignerEnum> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(type != null, DesignerEnum::getType, type);
            designerEnumService.page(page, wrapper);
            return BaseResponse.ok(PageDTO.from(designerEnumService.page(page, wrapper)));
        } catch (Exception e) {
            log.error("分页查询枚举失败", e);
            return BaseResponse.serviceError("分页查询枚举失败: " + e.getMessage());
        }
    }

    /**
     * 查询所有枚举列表
     * @return 枚举列表
     */
    @GetMapping("/list")
    public BaseResponse<List<Map<String, Object>>> list() {
        try {
            List<Map<String, Object>> list = designerEnumService.list()
                    .stream()
                    .map(item -> {
                        Map<String, Object> map = new HashMap<>();
                        map.put("id", item.getId());
                        map.put("type", item.getType());
                        map.put("content", JSONUtil.parseArray(item.getContent()));
                        map.put("createTime", item.getCreateTime());
                        map.put("updateTime", item.getUpdateTime());
                        return map;
                    })
                    .collect(Collectors.toList());
            return BaseResponse.ok(list);
        } catch (Exception e) {
            log.error("查询枚举失败", e);
            return BaseResponse.serviceError("查询枚举失败: " + e.getMessage());
        }
    }
} 