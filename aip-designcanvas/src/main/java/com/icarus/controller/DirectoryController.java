package com.icarus.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.icarus.common.rest.BaseResponse;
import com.icarus.config.AppConfigs;
import com.icarus.dto.DirectoryContentDTO;
import com.icarus.entity.Canvas;
import com.icarus.entity.Directory;
import com.icarus.entity.Node;
import com.icarus.service.CanvasService;
import com.icarus.service.DirectoryService;
import com.icarus.service.NodeService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 目录管理控制器
 * 提供目录的增删改查和树形结构获取功能
 */
@Slf4j
@RestController
@RequestMapping("/aip/directory")
@RequiredArgsConstructor
public class DirectoryController {

    @Resource
    private final DirectoryService directoryService;
    @Resource
    private final CanvasService canvasService;
    @Resource
    private final NodeService nodeService;
    
    /**
     * 创建目录
     * 如果指定了parentId，会自动计算level和path
     * @param directory 目录信息，必须包含name，可选parentId
     * @return 创建后的目录信息，包含自动生成的ID
     */
    @PostMapping("/create")
    public BaseResponse<Directory> create(@RequestBody Directory directory) {
        try {
            // 设置层级和路径
            if (directory.getParentId() != null) {
                Directory parent = directoryService.getById(directory.getParentId());
                if (parent == null) {
                    return BaseResponse.badRequest("父目录不存在");
                }
                directory.setLevel(parent.getLevel() + 1);
                directory.setPath(parent.getPath() + "/" + directory.getName());
            } else {
                directory.setLevel(1);
                directory.setPath("/" + directory.getName());
            }
            //权限放开所有人可以创建目录
            directory.setUserId("system");
            directoryService.save(directory);
            return BaseResponse.ok(directory);
        } catch (Exception e) {
            log.error("创建目录失败", e);
            return BaseResponse.serviceError("创建目录失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新目录信息
     * 如果修改了name，会自动更新path
     * @param directory 目录信息，必须包含id
     * @return 更新后的目录信息
     */
    @PostMapping("/update")
    public BaseResponse<Directory> update(@RequestBody Directory directory) {
        try {
            Directory oldDir = directoryService.getById(directory.getId());
            if (oldDir == null) {
                return BaseResponse.badRequest("目录不存在");
            }

            // 检查用户权限
            String userId = directory.getUserId();
            //权限放开后续可以去掉
            userId="system";
            if (!oldDir.getUserId().equals(userId)) {
                return BaseResponse.badRequest("无权限更新该目录");
            }
            directory.setUserId(userId);
            if (!oldDir.getName().equals(directory.getName())) {
                String newPath = oldDir.getPath().substring(0, 
                    oldDir.getPath().lastIndexOf("/")) + "/" + directory.getName();
                directory.setPath(newPath);
            }
            UpdateWrapper<Directory> wrapper = new UpdateWrapper<>();
            wrapper.eq("user_id", userId);
            wrapper.eq("id", directory.getId());
            directoryService.update(directory,wrapper);
            return BaseResponse.ok(directory);
        } catch (Exception e) {
            log.error("更新目录失败", e);
            return BaseResponse.serviceError("更新目录失败: " + e.getMessage());
        }
    }

    /**
     * 删除目录
     * 如果目录下存在子目录或画布，将无法删除
     *
     * @param directory
     * @throws RuntimeException 当目录下存在子目录或画布时
     */
    @PostMapping("/delete")
    public BaseResponse<Void> delete(@RequestBody Directory directory) {
        try {
            // 获取目录信息
            String id = directory.getId();
            Directory queryResult = directoryService.getById(id);
            if (queryResult == null) {
                return BaseResponse.badRequest("目录不存在");
            }
            
            // 检查用户权限
            String userId = directory.getUserId();
            //权限放开后续可以去掉
            userId="system";
            if (!queryResult.getUserId().equals(userId)) {
                return BaseResponse.badRequest("无权限删除该目录");
            }

            // 检查是否有子目录
            LambdaQueryWrapper<Directory> dirWrapper = new LambdaQueryWrapper<>();
            dirWrapper.eq(Directory::getParentId, id);
            if (directoryService.count(dirWrapper) > 0) {
                return BaseResponse.badRequest("目录下存在子目录，无法删除");
            }
            
            // 检查是否有画布
            LambdaQueryWrapper<Canvas> canvasWrapper = new LambdaQueryWrapper<>();
            canvasWrapper.eq(Canvas::getDirectoryId, id);
            if (canvasService.count(canvasWrapper) > 0) {
                return BaseResponse.badRequest("目录下存在你或者其他用户的画布，无法删除");
            }
            
            directoryService.removeById(id);
            return BaseResponse.ok(null);
        } catch (Exception e) {
            log.error("删除目录失败", e);
            return BaseResponse.serviceError("删除目录失败: " + e.getMessage());
        }
    }

    /**
     * 根据用户ID获取目录信息-CMTT语料切片用
     */
    @GetMapping("/full-tree/cmtt")
    public BaseResponse<List<Directory>> getDirectoryForCmtt() {
        try {
            String cmttCanvasDirRootId = AppConfigs.defaultSettings().getCmttCanvasDirRootId();
            if (StrUtil.isBlank(cmttCanvasDirRootId)){
                return BaseResponse.serviceError("获取CMTT目录树失败: 默认根节点未配置！");
            }
            // 获取所有目录
            LambdaQueryWrapper<Directory> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Directory::getType, "canvas");
            List<Directory> allDirectories = directoryService.list(wrapper);

            //校验获取根目录有数据
            boolean existing = allDirectories.stream().anyMatch(directory -> cmttCanvasDirRootId.equals(directory.getParentId()));
            if (!existing){
                return BaseResponse.serviceError("获取CMTT目录树失败: 默认根节点配置无数据！");
            }

            // 获取所有画布，排除content字段
            LambdaQueryWrapper<Canvas> canvasWrapper = new LambdaQueryWrapper<>();
            //子流程文件夹下画布补充content字段。用于添加子流程节点
            // 获取名为"子流程（禁止删除）"的目录ID
            List<String> subprocessDirIds = allDirectories.stream()
                    .filter(dir -> "子流程(禁止删除)".equals(dir.getName()))
                    .map(Directory::getId)
                    .collect(Collectors.toList());

            List<Canvas> allCanvases;
            if (!subprocessDirIds.isEmpty()) {
                // 分别查询"子流程（禁止删除）"目录下的画布和其他目录下的画布
                LambdaQueryWrapper<Canvas> normalWrapper = new LambdaQueryWrapper<>();
                normalWrapper.notIn(!subprocessDirIds.isEmpty(), Canvas::getDirectoryId, subprocessDirIds);
                normalWrapper.select(Canvas::getId, Canvas::getName, Canvas::getDirectoryId,
                        Canvas::getUserId, Canvas::getCreateTime, Canvas::getUpdateTime,
                        Canvas::getDescription);

                LambdaQueryWrapper<Canvas> specialWrapper = new LambdaQueryWrapper<>();
                specialWrapper.in(Canvas::getDirectoryId, subprocessDirIds);

                // 合并两种查询结果
                List<Canvas> normalCanvases = canvasService.list(normalWrapper);
                List<Canvas> specialCanvases = canvasService.list(specialWrapper);

                allCanvases = new ArrayList<>(normalCanvases);
                allCanvases.addAll(specialCanvases);
            } else {
                // 只选择必要的字段，排除content字段
                canvasWrapper.select(Canvas::getId, Canvas::getName, Canvas::getDirectoryId,
                        Canvas::getUserId, Canvas::getCreateTime, Canvas::getUpdateTime,
                        Canvas::getDescription);
                allCanvases = canvasService.list(canvasWrapper);
            }

            // 构建画布的目录映射
            buildContentTree(allDirectories, allCanvases, Canvas::getDirectoryId);

            return BaseResponse.ok(directoryService.buildChildren(cmttCanvasDirRootId, allDirectories.stream().filter(d -> Objects.nonNull(d.getParentId())).collect(Collectors.groupingBy(Directory::getParentId))));
        } catch (Exception e) {
            log.error("获取CMTT目录树失败", e);
            return BaseResponse.serviceError("获取CMTT目录树失败: " + e.getMessage());
        }
    }

    /**
     * 根据用户ID获取目录信息
     *
     * @param userId 用户id
     * @param type 类型：canvas-画布树，node-节点树
     * @return 目录信息
     */
    @GetMapping("/full-tree/v2")
    public BaseResponse<List<Directory>> getDirectoryV2(
            @RequestParam String userId,
            @RequestParam(defaultValue = "canvas") String type) {
        try {
            List<String> ids = Arrays.asList("system", userId);
            // 获取所有目录
            LambdaQueryWrapper<Directory> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(Directory::getUserId, ids);
            wrapper.eq(Directory::getType, type);
            List<Directory> allDirectories = directoryService.list(wrapper);

            // 根据类型获取对应的内容，但不查询content字段
            if ("canvas".equals(type)) {
                // 获取所有画布，排除content字段
                LambdaQueryWrapper<Canvas> canvasWrapper = new LambdaQueryWrapper<>();
                canvasWrapper.in(Canvas::getUserId, ids);
                //子流程文件夹下画布补充content字段。用于添加子流程节点
                // 获取名为"子流程（禁止删除）"的目录ID
                List<String> subprocessDirIds = allDirectories.stream()
                        .filter(dir -> "子流程(禁止删除)".equals(dir.getName()))
                        .map(Directory::getId)
                        .collect(Collectors.toList());

                List<Canvas> allCanvases;
                if (!subprocessDirIds.isEmpty()) {
                    // 分别查询"子流程（禁止删除）"目录下的画布和其他目录下的画布
                    LambdaQueryWrapper<Canvas> normalWrapper = new LambdaQueryWrapper<>();
                    normalWrapper.in(Canvas::getUserId, ids);
                    normalWrapper.notIn(!subprocessDirIds.isEmpty(), Canvas::getDirectoryId, subprocessDirIds);
                    normalWrapper.select(Canvas::getId, Canvas::getName, Canvas::getDirectoryId,
                            Canvas::getUserId, Canvas::getCreateTime, Canvas::getUpdateTime,
                            Canvas::getDescription);

                    LambdaQueryWrapper<Canvas> specialWrapper = new LambdaQueryWrapper<>();
                    specialWrapper.in(Canvas::getUserId, ids);
                    specialWrapper.in(Canvas::getDirectoryId, subprocessDirIds);

                    // 合并两种查询结果
                    List<Canvas> normalCanvases = canvasService.list(normalWrapper);
                    List<Canvas> specialCanvases = canvasService.list(specialWrapper);

                    allCanvases = new ArrayList<>(normalCanvases);
                    allCanvases.addAll(specialCanvases);
                } else {
                    // 只选择必要的字段，排除content字段
                    canvasWrapper.select(Canvas::getId, Canvas::getName, Canvas::getDirectoryId,
                            Canvas::getUserId, Canvas::getCreateTime, Canvas::getUpdateTime,
                            Canvas::getDescription);
                    allCanvases = canvasService.list(canvasWrapper);
                }

                // 构建画布的目录映射
                buildContentTree(allDirectories, allCanvases, Canvas::getDirectoryId);
            } else if ("node".equals(type)) {
                // 获取所有节点
                LambdaQueryWrapper<Node> nodeWrapper = new LambdaQueryWrapper<>();
                List<Node> allNodes = nodeService.list(nodeWrapper);
                // 构建节点的目录映射
                buildContentTree(allDirectories, allNodes, Node::getDirectoryId);
            } else {
                return BaseResponse.badRequest("不支持的类型：" + type);
            }

            return BaseResponse.ok(directoryService.buildTree(allDirectories));
        } catch (Exception e) {
            log.error("获取完整目录树失败", e);
            return BaseResponse.serviceError("获取完整目录树失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取指定目录的直接子目录
     * @param id 父目录ID
     * @return 子目录列表
     */
    @GetMapping("/children/{id}")
    public BaseResponse<List<Directory>> getChildren(@PathVariable String id) {
        try {
            LambdaQueryWrapper<Directory> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Directory::getParentId, id);
            return BaseResponse.ok(directoryService.list(wrapper));
        } catch (Exception e) {
            log.error("获取子目录失败", e);
            return BaseResponse.serviceError("获取子目录失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取目录信息
     * @param id 目录ID
     * @return 目录信息
     */
    @GetMapping("/get/{id}")
    public BaseResponse<Directory> getById(@PathVariable String id) {
        try {
            Directory directory = directoryService.getById(id);
            if (directory == null) {
                return BaseResponse.badRequest("目录不存在");
            }
            return BaseResponse.ok(directory);
        } catch (Exception e) {
            log.error("获取目录失败", e);
            return BaseResponse.serviceError("获取目录失败: " + e.getMessage());
        }
    }


    /**
     * 获取完整的目录树结构
     * @return 从根目录开始的完整目录树
     */
    @GetMapping("/tree")
    public BaseResponse<List<Directory>> getTree() {
        try {
            List<Directory> all = directoryService.list();
            return BaseResponse.ok(directoryService.buildTree(all));
        } catch (Exception e) {
            log.error("获取目录树失败", e);
            return BaseResponse.serviceError("获取目录树失败: " + e.getMessage());
        }
    }
    

    /**
     * 获取指定目录下的所有内容（包括子目录和画布）
     * @param id 目录ID，如果为null则获取根目录内容
     * @return 目录内容，包括子目录和画布
     */
    @GetMapping("/content")
    public BaseResponse<DirectoryContentDTO> getDirectoryContent(
            @RequestParam(required = false) String id) {
        try {
            DirectoryContentDTO content = new DirectoryContentDTO();
            
            // 获取当前目录信息
            if (id != null) {
                Directory currentDir = directoryService.getById(id);
                if (currentDir == null) {
                    return BaseResponse.badRequest("目录不存在");
                }
                content.setCurrentDirectory(currentDir);
            }
            
            // 查询子目录
            LambdaQueryWrapper<Directory> dirWrapper = new LambdaQueryWrapper<>();
            dirWrapper.eq(Directory::getParentId, id);  // id为null时查询根目录
            content.setSubDirectories(directoryService.list(dirWrapper));
            
            // 查询当前目录下的画布
            LambdaQueryWrapper<Canvas> canvasWrapper = new LambdaQueryWrapper<>();
            canvasWrapper.eq(Canvas::getDirectoryId, id);  // id为null时查询根目录的画布
            content.setCanvases(canvasService.list(canvasWrapper));
            
            return BaseResponse.ok(content);
        } catch (Exception e) {
            log.error("获取目录内容失败", e);
            return BaseResponse.serviceError("获取目录内容失败: " + e.getMessage());
        }
    }

    /**
     * 获取完整的目录树结构（包含画布信息）
     * @return 从根目录开始的完整目录树
     */
    @GetMapping("/full-tree")
    public BaseResponse<List<Directory>> getFullTree() {
        try {
            // 获取所有目录
            List<Directory> allDirectories = directoryService.list();
            // 获取所有画布
            List<Canvas> allCanvases = canvasService.list();
            buildContentTree(allDirectories, allCanvases, Canvas::getDirectoryId);

            return BaseResponse.ok(directoryService.buildTree(allDirectories));
        } catch (Exception e) {
            log.error("获取完整目录树失败", e);
            return BaseResponse.serviceError("获取完整目录树失败: " + e.getMessage());
        }
    }



    /**
     * 构建内容树
     * @param directories 目录列表
     * @param contents 内容列表
     * @param directoryIdGetter 获取目录ID的函数
     * @param <T> 内容类型
     */
    private <T> void buildContentTree(List<Directory> directories, List<T> contents, 
            Function<T, String> directoryIdGetter) {
        // 构建内容的目录映射
        Map<String, List<T>> directoryContentMap = contents.stream()
                .filter(content -> StringUtils.isNotBlank(directoryIdGetter.apply(content)))
                .collect(Collectors.groupingBy(directoryIdGetter));

        // 为每个目录设置其包含的内容
        for (Directory dir : directories) {
            dir.setContents(directoryContentMap.getOrDefault(dir.getId(), new ArrayList<>()));
        }
    }
} 