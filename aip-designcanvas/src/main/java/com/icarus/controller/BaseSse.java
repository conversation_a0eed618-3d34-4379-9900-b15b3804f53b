package com.icarus.controller;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.icarus.common.rest.ProcessResult;
import com.icarus.constant.MessageStatus;
import com.icarus.constant.MessageType;
import com.icarus.starter.channel.queue.sse.SseEmitterManager;
import com.icarus.utils.SSEUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * sse控制器接口，提供sse基础能力
 * <AUTHOR>
 * @date 2025-03-22
 */
public interface BaseSse {

    Logger logger = LoggerFactory.getLogger(BaseSse.class);

    /**
     * 会话标识key
     */
    String SESSION_ID = "aip-session";

    /**
     * 构建sse连接
     * @param request
     * @param response
     * @return SseEmitter
     */
    default SseEmitter getOrInitSseEmitter(String sessionId, HttpServletResponse response) {
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("X-Accel-Buffering", "no");
//        String sessionId = getSessionId(request);
        try {
            Assert.notEmpty(sessionId);
            //aip-session是维持sse的依据
            SseEmitter sseEmitter = SseEmitterManager.get(sessionId);
            if(ObjUtil.isNull(sseEmitter))
                sseEmitter = SseEmitterManager.init(sessionId);
            return sseEmitter;
        } catch (Exception e) {
            logger.error("Error in SSE connection: ", e);
            if (StrUtil.isNotEmpty(sessionId))
                SseEmitterManager.del(sessionId);
            throw new RuntimeException("Failed to establish SSE connection", e);
        }
    }

    /**
     * 获取会话标识
     * @param request
     * @return
     */
    default String getSessionId(HttpServletRequest request){
        return request.getHeader(SESSION_ID);
    }

    /**
     * 消息发送
     * @param session 会话标识
     * @param msg 消息
     */
    default void send(String session, String msg){
        synchronized (session.intern()){ // 防止多线程并发
            // 消息预处理
            msg = pretreatment(msg);
            // 构建result
            ProcessResult result = buildProcessResult(session, msg);
            try{
                // 发送消息
                SSEUtils.sendTextMessage(null, session, "answer", result, MessageStatus.SUCCESS.getStatus());
            }catch (Exception e){
                // 出现异常后关闭连接
                SseEmitterManager.del(session);
                throw new RuntimeException("消息发送失败.", e);
            }
        }
    }

    /**
     * 构建result
     * @param session
     * @param msg
     * @return
     */
    default ProcessResult buildProcessResult(String session, String msg){
        // 创建成功响应
        ProcessResult result = ProcessResult.success("对话成功", msg);
        // 设置基础信息
        result.setSessionId(session);
        result.setMessageId("msg_" + System.currentTimeMillis());
        // 设置组件信息
        ProcessResult.Component component = result.getComponent();
        component.setType(MessageType.MESSAGE_1001.getCode());  // 使用文本类型组件
        return  result;
    }

    /**
     * 消息预处理
     * @param msg
     * @return
     */
    default String pretreatment(String msg) {
        // 先去除think标签及其内容
        msg = msg.replaceAll("[\\s\\S]*?</think>", "");
        // 去除多余的空行和首尾空白
        msg = msg.trim();
        // 如果需要，可以去除末尾的分号
        // response = response.replaceAll(";$", "");
        msg = msg.replaceAll("```json", "").replaceAll("```", "");
        return msg;
    }

}
