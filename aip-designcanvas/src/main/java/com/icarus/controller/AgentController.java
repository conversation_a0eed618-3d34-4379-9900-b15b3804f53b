package com.icarus.controller;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.common.rest.BaseResponse;
import com.icarus.dto.PageDTO;
import com.icarus.entity.*;
import com.icarus.service.*;
import com.icarus.service.AgentService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/aip/agent")
@RequiredArgsConstructor
public class AgentController {
    
    private final AgentService agentService;

    private final AgentWorkFlowService agentWorkFlowService;

    private final DesignerEnumService designerEnum;

    @Resource
    private final CanvasService canvasService;

    @Resource
    private final EndpointService endpointService;

    @PostMapping("/create")
    public BaseResponse<Agent> create(@RequestBody Agent agent) {
        try {
            if (agent.getName() == null ) {
                return BaseResponse.badRequest("名称和类型不能为空");
            }
            agentService.save(agent);
            return BaseResponse.ok(agent);
        } catch (Exception e) {
            log.error("创建失败", e);
            return BaseResponse.serviceError("创建失败: " + e.getMessage());
        }
    }


    @PostMapping("/update")
    public BaseResponse<Agent> update(@RequestBody Agent agent) {
        try {
            Agent old = agentService.getById(agent.getId());
            if (old == null) {
                return BaseResponse.badRequest("节点不存在");
            }
            agentService.updateById(agent);
            return BaseResponse.ok(agent);
        } catch (Exception e) {
            log.error("更新节点失败", e);
            return BaseResponse.serviceError("更新节点失败: " + e.getMessage());
        }
    }

    @GetMapping("/navigation")
    public BaseResponse<List<Object>> navigation() {
        try {
            List<Object> types = new ArrayList<>();

            {
                JSONObject type1 = new JSONObject();
                type1.set("name", "助手型");

                JSONObject q = new JSONObject();
                type1.set("param", q);
                q.set("agentType", "助手型");
                types.add(type1);
            }

            {
                JSONObject type1 = new JSONObject();
                type1.set("name", "任务型");

                JSONObject q = new JSONObject();
                type1.set("param", q);
                q.set("agentType", "任务型");
                types.add(type1);
            }

            {
                JSONObject type1 = new JSONObject();
                type1.set("name", "分析型");

                JSONObject q = new JSONObject();
                type1.set("param", q);
                q.set("agentType", "分析型");
                types.add(type1);
            }
            {
                JSONObject type1 = new JSONObject();
                type1.set("name", "创意型");

                JSONObject q = new JSONObject();
                type1.set("param", q);
                q.set("agentType", "创意型");
                types.add(type1);
            }
            {
                JSONObject type1 = new JSONObject();
                type1.set("name", "研究型");

                JSONObject q = new JSONObject();
                type1.set("param", q);
                q.set("agentType", "研究型");
                types.add(type1);
            }


            return BaseResponse.ok(types);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return BaseResponse.serviceError(e.getMessage() + e.getMessage());
        }
    }

    @RequestMapping("/getPrompt")
    public BaseResponse<String> getPrompt(@RequestBody(required = true) JSONObject params) {
        String id = params.getStr("id");
        // 执行查询
        Agent agent = agentService.getById(id);

        // 是否存在
        if (agent == null) {
            return BaseResponse.serviceError("对象不存在");
        }

        return BaseResponse.ok(agent.getPrompt());
    }

    @RequestMapping("/getWorkFlows")
    public BaseResponse<PageDTO<Canvas>> getWorkFlows(
            @RequestBody(required = true) JSONObject params
            ) {

        try {
            Integer current = params.getInt("current");
            Integer pageSize = params.getInt("pageSize");
            String agentId = params.getStr("agentId");
            LambdaQueryWrapper<AgentWorkFlow> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(AgentWorkFlow::getAgentId, agentId);
            List<AgentWorkFlow> awfs = agentWorkFlowService.list(wrapper1);

            Page<Canvas> page = new Page<>(current, pageSize);
            LambdaQueryWrapper<Canvas> wrapper = new LambdaQueryWrapper<>();

            List<String> ids = new ArrayList<>();
            for (AgentWorkFlow awf : awfs) {
                ids.add(awf.getWorkFlowId());
            }
            if (!ids.isEmpty())
                wrapper.in(Canvas::getId, ids);

            return BaseResponse.ok(PageDTO.from(canvasService.page(page, wrapper)));
        } catch (Exception e) {
            log.error("分页查询画布失败", e);
            return BaseResponse.serviceError("分页查询画布失败: " + e.getMessage());
        }
    }

    @RequestMapping("/savePrompt")
    public BaseResponse<Void> savePrompt(@RequestBody(required = true) JSONObject params) {
        try {
            String prompt = params.getStr("prompt");
            String id = params.getStr("id");
            // 参数校验
            if (prompt == null || prompt.trim().isEmpty()) {
                return BaseResponse.serviceError("名称不能为空");
            }
            if (id == null || id.trim().isEmpty()) {
                return BaseResponse.serviceError("ID不能为空");
            }

            // 执行查询
            Agent agent = agentService.getById(id);

            // 是否存在
            if (agent == null) {
                return BaseResponse.serviceError("对象不存在");
            }

            agent.setPrompt(prompt);

            agentService.saveOrUpdate(agent);

            return BaseResponse.ok(null);
        } catch (Exception e) {
            log.error("修改AGENT失败", e);
            return BaseResponse.serviceError("修改AGENT失败: " + e.getMessage());
        }
    }

    @RequestMapping("/saveWorkFlowIds")
    public BaseResponse<Void> saveWorkFlowIds(@RequestBody(required = true) JSONObject params) {
        try {
            String workFlowIds = params.getStr("workFlowIds");
            String id = params.getStr("id");
            // 参数校验
            if (workFlowIds == null || workFlowIds.trim().isEmpty()) {
                return BaseResponse.serviceError("workFlowIds不能为空");
            }
            if (id == null || id.trim().isEmpty()) {
                return BaseResponse.serviceError("ID不能为空");
            }

            JSONArray arr = JSONUtil.parseArray(workFlowIds);

            LambdaQueryWrapper<AgentWorkFlow> wrapper = new LambdaQueryWrapper<>();
            // 根据id查询所有的
            wrapper.eq(AgentWorkFlow::getAgentId, id);

            agentWorkFlowService.remove(wrapper);

            for (Object o : arr) {
                AgentWorkFlow agentWorkFlow = new AgentWorkFlow();
                agentWorkFlow.setAgentId(id);
                agentWorkFlow.setWorkFlowId(o.toString());
                agentWorkFlowService.save(agentWorkFlow);
            }

            return BaseResponse.ok(null);
        } catch (Exception e) {
            log.error("修改AGENTWorkFlow失败", e);
            return BaseResponse.serviceError("AGENTWorkFlow: " + e.getMessage());
        }
    }

    @GetMapping("/delete")
    public BaseResponse<Void> delete( @RequestParam(required = true) String id) {
        try {
            agentService.removeById(id);
            return BaseResponse.ok(null);
        } catch (Exception e) {
            log.error("删除失败", e);
            return BaseResponse.serviceError("删除失败: " + e.getMessage());
        }
    }

    @RequestMapping("/completions")
    public BaseResponse<Void> completions(@RequestBody(required = false) DebuggerContext debuggerContext,
                            HttpServletRequest httpServletRequest,
                            HttpServletResponse response)  {

        // 处理常规请求（request 不为 null）
        String sessionId = debuggerContext.getSessionId();
        String tabId = debuggerContext.getTabId();
        // 验证必要参数
        if (StringUtils.isBlank(sessionId)) {
            log.info("请求中未包含sessionId");
            return BaseResponse.serviceError("请求中未包含sessionId");
        }
        if (StringUtils.isBlank(tabId)) {
            log.info("请求中未包含tabId, sessionId=" + sessionId);
            return BaseResponse.serviceError("请求中未包含tabId, sessionId=" + sessionId);
        }

        if (StringUtils.isBlank(debuggerContext.getAgentId())) {
            log.info("请求中未包含agentId, sessionId=" + sessionId);
            return BaseResponse.serviceError("请求中未包含agentId, sessionId=" + sessionId);
        }

        agentService.completions(debuggerContext);

        //ProcessResult pr = ProcessResult.success("answer", "你好");
       // pr.setSessionId(sessionId);
       // SSEUtils.sendTextMessage(pr.getSessionId(), pr.getRequestId(), pr.getTabId(), "result", pr, MessageStatus.SUCCESS.getStatus());


        return BaseResponse.ok(null);
    }

    /**
     * 分页查询Agent端点列表
     * @return 分页结果
     */
    @PostMapping("/page")
    public BaseResponse<PageDTO<Endpoint>> page(@RequestBody PageQueryParams params) {
        try {
            Page<Endpoint> page = new Page<>(params.getCurrent(), params.getPageSize());
            LambdaQueryWrapper<Endpoint> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Endpoint::getType, "agent")
                    .like(null != params.getName(), Endpoint::getProject, params.getName())
                    .orderByDesc(Endpoint::getCreateTime);
            return BaseResponse.ok(PageDTO.from(endpointService.page(page, wrapper)));
        } catch (Exception e) {
            log.error("分页查询端点失败", e);
            return BaseResponse.serviceError("分页查询端点失败: " + e.getMessage());
        }
    }

    @Data // 自动生成 getter、setter、toString 等方法
    public static class PageQueryParams {

        private Integer current = 1; // 当前页码
        private Integer pageSize = 1000; // 每页大小
        private String name; // 名称/编码
        private String agentType; // 类型

    }

    @Data // 自动生成 getter、setter、toString 等方法
    public static class DebuggerContext {

        private String tabId;        // 标签页ID
        private String sessionId;    // 会话ID
        private Long timestamp;      // 请求时间戳
        private String requestId;    // 请求唯一标识

        private String agentId;
        private List<String> workFlowIds;
    }

} 