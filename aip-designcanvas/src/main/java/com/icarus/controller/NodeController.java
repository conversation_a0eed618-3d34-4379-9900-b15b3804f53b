package com.icarus.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.common.pojo.AIPFlowDefinition;
import com.icarus.common.rest.BaseResponse;
import com.icarus.deploy.PluginResourceService;
import com.icarus.entity.Node;
import com.icarus.service.NodeService;
import com.icarus.dto.PageDTO;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 节点管理控制器
 * 提供节点的增删改查功能
 */
@Slf4j
@RestController
@RequestMapping("/aip/node")
@RequiredArgsConstructor
public class NodeController {

    private final NodeService nodeService;
    private final PluginResourceService pluginResourceService;

    /**
     * 创建节点
     * 
     * @param node 节点信息，必须包含name和type
     * @return 创建后的节点信息
     */
    @PostMapping("/create")
    public BaseResponse<Node> create(@RequestBody Node node) {
        try {
            if (node.getName() == null || node.getType() == null) {
                return BaseResponse.badRequest("名称和类型不能为空");
            }
            nodeService.save(node);
            return BaseResponse.ok(node);
        } catch (Exception e) {
            log.error("创建节点失败", e);
            return BaseResponse.serviceError("创建节点失败: " + e.getMessage());
        }
    }

    /**
     * 导入扩展节点
     * 
     * @param name    名称
     * @param type    类型
     * @param content 内容
     * @param file    热部署的jar包
     * @return 导入后的节点信息
     */
    @PostMapping("/import")
    public BaseResponse<Node> imports(
            @RequestParam("name") String name,
            @RequestParam("type") String type,
            @RequestParam("content") String content,
            @RequestParam MultipartFile file) {
        try {
            // 热部署节点
            String plugin = pluginResourceService.installAndStart(file);
            if (StrUtil.isEmpty(plugin)) {
                return BaseResponse.badRequest("导入节点失败!");
            }
            // 保存节点信息
            Node node = new Node();
            node.setName(name); // 名称
            node.setType(type); // 类型
            node.setExtension(1); // 扩展节点标识
            AIPFlowDefinition.CellData cellData = JSONUtil.toBean(content, AIPFlowDefinition.CellData.class);
            cellData.setProvider(plugin); // 设置节点提供者:插件ID
            node.setContent(JSONUtil.toJsonStr(cellData)); // 设置节点内容
            nodeService.save(node);
            return BaseResponse.ok(node);
        } catch (Exception e) {
            log.error("导入节点失败", e);
            return BaseResponse.serviceError("导入节点失败: " + e.getMessage());
        }
    }

    /**
     * 更新节点
     * 
     * @param node 节点信息，必须包含id
     * @return 更新后的节点信息
     */
    @PostMapping("/update")
    public BaseResponse<Node> update(@RequestBody Node node) {
        try {
            Node old = nodeService.getById(node.getId());
            if (old == null) {
                return BaseResponse.badRequest("节点不存在");
            }
            nodeService.updateById(node);
            return BaseResponse.ok(node);
        } catch (Exception e) {
            log.error("更新节点失败", e);
            return BaseResponse.serviceError("更新节点失败: " + e.getMessage());
        }
    }

    /**
     * 删除节点
     * 
     * @param id 节点ID
     */
    @PostMapping("/delete/{id}")
    public BaseResponse<Void> delete(@PathVariable String id) {
        try {
            nodeService.removeById(id);
            return BaseResponse.ok(null);
        } catch (Exception e) {
            log.error("删除节点失败", e);
            return BaseResponse.serviceError("删除节点失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取节点
     * 
     * @param id 节点ID
     * @return 节点信息
     */
    @GetMapping("/get/{id}")
    public BaseResponse<Node> getById(@PathVariable String id) {
        try {
            Node node = nodeService.getById(id);
            if (node == null) {
                return BaseResponse.badRequest("节点不存在");
            }
            return BaseResponse.ok(node);
        } catch (Exception e) {
            log.error("获取节点失败", e);
            return BaseResponse.serviceError("获取节点失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询节点列表
     * 
     * @param current  当前页码，默认1
     * @param pageSize 每页大小，默认10
     * @param name     节点名称，可选，支持模糊查询
     * @param type     节点类型，可选，精确匹配
     * @return 分页结果
     */
    @GetMapping("/page")
    public BaseResponse<PageDTO<Node>> page(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String type) {
        try {
            Page<Node> page = new Page<>(current, pageSize);
            LambdaQueryWrapper<Node> wrapper = new LambdaQueryWrapper<>();
            wrapper.like(name != null, Node::getName, name)
                    .eq(type != null, Node::getType, type);
            return BaseResponse.ok(PageDTO.from(nodeService.page(page, wrapper)));
        } catch (Exception e) {
            log.error("分页查询节点失败", e);
            return BaseResponse.serviceError("分页查询节点失败: " + e.getMessage());
        }
    }

    /**
     * 导出选中节点信息到Excel
     * 
     * @param ids      节点id集合
     * @param response 响应流
     */
    @PostMapping("/exportExcel")
    public void exportNodesToExcel(@RequestBody List<String> ids, HttpServletResponse response) {
        log.info("导出节点到Excel，节点id集合: {}", ids);
        nodeService.exportToExcel(ids, response);
    }

    /**
     * 导入节点信息Excel
     * 
     * @param file Excel文件
     * @return 导入结果
     */
    @PostMapping("/importExcel")
    public BaseResponse<String> importNodesFromExcel(@RequestParam("file") MultipartFile file) {
        log.info("导入节点Excel: {}", file.getOriginalFilename());
        try {
            int successCount = nodeService.importFromExcel(file);
            return BaseResponse.ok("成功导入" + successCount + "条节点");
        } catch (Exception e) {
            log.error("节点导入Excel失败", e);
            return BaseResponse.serviceError("节点导入Excel失败: " + e.getMessage());
        }
    }

}