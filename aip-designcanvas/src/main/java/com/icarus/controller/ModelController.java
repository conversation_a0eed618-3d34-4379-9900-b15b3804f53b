package com.icarus.controller;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.common.rest.BaseResponse;
import com.icarus.dto.ChatContextDTO;
import com.icarus.dto.ModelProviderDTO;
import com.icarus.dto.PageDTO;
import com.icarus.entity.DesignerEnum;
import com.icarus.entity.Model;
import com.icarus.sdk.springboot.base.utils.SpringContextUtils;
import com.icarus.service.DesignerEnumService;
import com.icarus.service.ModelService;
import com.icarus.service.impl.ModelDebugService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;

@Slf4j
@RestController
@RequestMapping("/aip/model")
@RequiredArgsConstructor
public class ModelController {

    private final ModelService modelService;

    private final DesignerEnumService designerEnum;
    private final ModelDebugService debugService;

    //private Map<String, ChatContextDTO> sessionIdToContext = new ConcurrentHashMap<>();

    @PostMapping("/create")
    public BaseResponse<Model> create(@RequestBody Model model) {
        try {
            if (model.getName() == null ) {
                return BaseResponse.badRequest("名称和类型不能为空");
            }
            modelService.save(model);
            return BaseResponse.ok(model);
        } catch (Exception e) {
            log.error("创建失败", e);
            return BaseResponse.serviceError("创建失败: " + e.getMessage());
        }
    }


    @PostMapping("/update")
    public BaseResponse<Model> update(@RequestBody Model model) {
        try {
            Model old = modelService.getById(model.getId());
            if (old == null) {
                return BaseResponse.badRequest("节点不存在");
            }
            modelService.updateById(model);
            return BaseResponse.ok(model);
        } catch (Exception e) {
            log.error("更新节点失败", e);
            return BaseResponse.serviceError("更新节点失败: " + e.getMessage());
        }
    }

    @GetMapping("/navigation")
    public BaseResponse<List<Object>> navigation() {
        try {
            List<Object> types = new ArrayList<>();

            {
                JSONObject type1 = new JSONObject();
                type1.set("value", "usageType");
                type1.set("name", "类型");

                type1.set("details", modelTypeList());
                types.add(type1);
            }

            {
                JSONObject type1 = new JSONObject();
                type1.set("value", "groupOrProvider");
                type1.set("name", "系列/厂商");

                type1.set("details", modelGroupOrProviderList());
                types.add(type1);
            }

            {
                JSONObject type1 = new JSONObject();
                type1.set("value", "contentLength");
                type1.set("name", "上下文");

                type1.set("details", contentLengthList());
                types.add(type1);
            }

            {
                JSONObject type1 = new JSONObject();
                type1.set("value", "size");
                type1.set("name", "规格");

                type1.set("details", sizeList());
                types.add(type1);
            }


            return BaseResponse.ok(types);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return BaseResponse.serviceError(e.getMessage() + e.getMessage());
        }
    }

    private List<JSONObject> modelGroupOrProviderList() {
        List<JSONObject> details = new ArrayList<>();
        {
            JSONObject d = new JSONObject();
            details.add(d);
            d.set("name", "DeepSeek");
            JSONObject q = new JSONObject();
            d.set("param", q);

            q.set("group", "");
            q.set("provider", "DeepSeek");
        }
        {
            JSONObject d = new JSONObject();
            details.add(d);
            d.set("name", "Qwen2.5");
            JSONObject q = new JSONObject();
            d.set("param", q);

            q.set("group", "Qwen2.5");
            q.set("provider", "Qwen");
        }

        {
            JSONObject d = new JSONObject();
            details.add(d);
            d.set("name", "QVQ");
            JSONObject q = new JSONObject();
            d.set("param", q);

            q.set("group", "QVQ");
            q.set("provider", "Qwen");
        }
        {
            JSONObject d = new JSONObject();
            details.add(d);
            d.set("name", "QwQ");
            JSONObject q = new JSONObject();
            d.set("param", q);

            q.set("group", "QwQ");
            q.set("provider", "Qwen");
        }
        {
            JSONObject d = new JSONObject();
            details.add(d);
            d.set("name", "Llama3");
            JSONObject q = new JSONObject();
            d.set("param", q);

            q.set("group", "Llama3");
            q.set("provider", "Meta");
        }
        return details;
    }

    private List<JSONObject> contentLengthList() {
        List<JSONObject> details = new ArrayList<>();
        {
            JSONObject d = new JSONObject();
            details.add(d);
            d.set("name", "≥ 8K");

            JSONObject q = new JSONObject();
            d.set("param", q);

            q.set("min", "8");
            q.set("minOp", ">=");
        }
        {
            JSONObject d = new JSONObject();
            details.add(d);
            d.set("name", "≥ 16K");
            JSONObject q = new JSONObject();
            d.set("param", q);

            q.set("min", "16");
            q.set("minOp", ">=");
        }
        {
            JSONObject d = new JSONObject();
            details.add(d);
            d.set("name", "≥ 32K");
            JSONObject q = new JSONObject();
            d.set("param", q);

            q.set("min", "32");
            q.set("minOp", ">=");
        }
        {
            JSONObject d = new JSONObject();
            details.add(d);
            d.set("name", "≥ 128K");
            JSONObject q = new JSONObject();
            d.set("param", q);

            q.set("min", "128");
            q.set("minOp", ">=");
        }
        return details;
    }

    private List<JSONObject> sizeList() {
        List<JSONObject> details = new ArrayList<>();
        {
            JSONObject d = new JSONObject();
            details.add(d);
            d.set("name", "MoE");
            JSONObject q = new JSONObject();
            d.set("param", q);

            q.set("architecture", "MoE");
        }
        {
            JSONObject d = new JSONObject();
            details.add(d);
            d.set("name", "10B以下");

            JSONObject q = new JSONObject();
            d.set("param", q);

            q.set("max", "10");
            q.set("maxOp", "<=");

        }
        {
            JSONObject d = new JSONObject();
            details.add(d);
            d.set("name", "10~50B");

            JSONObject q = new JSONObject();
            d.set("param", q);

            q.set("max", "50");
            q.set("maxOp", "<=");
            q.set("min", "10");
            q.set("minOp", ">");
        }

        {
            JSONObject d = new JSONObject();
            details.add(d);
            d.set("name", "50~100B");

            JSONObject q = new JSONObject();
            d.set("param", q);

            q.set("max", "100");
            q.set("maxOp", "<=");
            q.set("min", "50");
            q.set("minOp", ">");
        }

        {
            JSONObject d = new JSONObject();
            details.add(d);
            d.set("name", "100B以上");

            JSONObject q = new JSONObject();
            d.set("param", q);

            q.set("min", "100");
            q.set("minOp", ">");
        }
        return details;
    }


    private List<JSONObject> modelTypeList() {
        List<JSONObject> details = new ArrayList<>();
        {
            JSONObject d = new JSONObject();
            details.add(d);
            d.set("name", "对话");

            JSONObject q = new JSONObject();
            d.set("param", q);
            q.set("value", "chat");
        }
        {
            JSONObject d = new JSONObject();
            details.add(d);
            d.set("name", "生图");

            JSONObject q = new JSONObject();
            d.set("param", q);
            q.set("value", "createImg");
        }
        {
            JSONObject d = new JSONObject();
            details.add(d);
            d.set("name", "视频");

            JSONObject q = new JSONObject();
            d.set("param", q);
            q.set("value", "video");
        }
        {
            JSONObject d = new JSONObject();
            details.add(d);
            d.set("name", "语音");

            JSONObject q = new JSONObject();
            d.set("param", q);
            q.set("value", "voice");
        }
        {
            JSONObject d = new JSONObject();
            details.add(d);
            d.set("name", "嵌入");

            JSONObject q = new JSONObject();
            d.set("param", q);
            q.set("value", "embed");
        }
        {
            JSONObject d = new JSONObject();
            details.add(d);
            d.set("name", "重排序");

            JSONObject q = new JSONObject();
            d.set("param", q);
            q.set("value", "reSort");
        }
        return details;
    }

    @GetMapping("/checkName")
    public BaseResponse<Void> checkName(@RequestParam(required = true) String name) {
        try {
            // 参数校验
            if (name == null || name.trim().isEmpty()) {
                return BaseResponse.badRequest("名称不能为空");
            }

            // 构造查询条件
            LambdaQueryWrapper<Model> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Model::getName, name); // 查询 name 是否已存在

            // 执行查询
            List<Model> models = modelService.list(wrapper);

            // 判断名称是否已存在
            if (!models.isEmpty()) {
                return BaseResponse.badRequest("名称已经存在");
            }

            // 如果名称不存在，则返回成功
            return BaseResponse.ok(null);
        } catch (Exception e) {
            log.error("检查名称失败", e);
            return BaseResponse.serviceError("检查名称失败: " + e.getMessage());
        }
    }


    @GetMapping("/listProvider")
    public BaseResponse<List<ModelProviderDTO>> listProvider() {
        try {
            List<ModelProviderDTO> providerList = new ArrayList<>();

            List<DesignerEnum> list = designerEnum.listByType("modelProvider");
            if (list.isEmpty())
                return BaseResponse.ok(providerList);

            DesignerEnum de = list.get(0);
            JSONArray jArr = JSONUtil.parseArray(de.getContent());
            for (Object e : jArr) {
                providerList.add(new ModelProviderDTO(e.toString(), e.toString()));
            }

            return BaseResponse.ok(providerList);
        } catch (Exception e) {
            log.error("删除节点失败", e);
            return BaseResponse.serviceError("删除节点失败: " + e.getMessage());
        }
    }

    @GetMapping("/delete")
    public BaseResponse<Void> delete( @RequestParam(required = true) String id) {
        try {
            modelService.removeById(id);
            return BaseResponse.ok(null);
        } catch (Exception e) {
            log.error("删除失败", e);
            return BaseResponse.serviceError("删除失败: " + e.getMessage());
        }
    }



    @PostMapping("/page")
    public BaseResponse<PageDTO<Model>> page(@RequestBody PageQueryParams params, HttpServletRequest request) {

        try {

            // 初始化分页对象
            Page<Model> page = new Page<>(params.getCurrent(), params.getPageSize());

            // 构建查询条件
            LambdaQueryWrapper<Model> wrapper = new LambdaQueryWrapper<>();

            // 动态判断 Name 是 Code 或 Name，并同时进行模糊查询
            if (StringUtils.isNotBlank(params.getName())) {
                wrapper.and(queryWrapper -> queryWrapper
                        .like(Model::getName, params.getName()) // 对 name 字段模糊查询
                        .or() // 使用 or 连接条件
                        .like(Model::getCode, params.getName())); // 对 code 字段模糊查询
            }

            // 使用类型查询
            if (params.getUsageType() != null && StringUtils.isNotBlank(params.getUsageType().getValue())) {
                wrapper.eq(Model::getUsageType, params.getUsageType().getValue());
            }

            // 组或提供者查询
            if (params.getGroupOrProvider() != null) {
                if (StringUtils.isNotBlank(params.getGroupOrProvider().getGroup())) {
                    wrapper.eq(Model::getGroupType, params.getGroupOrProvider().getGroup());
                }
                if (StringUtils.isNotBlank(params.getGroupOrProvider().getProvider())) {
                    wrapper.eq(Model::getProvider, params.getGroupOrProvider().getProvider());
                }
            }

            // 内容长度范围查询
            if (params.getContentLength() != null) {
                if (params.getContentLength().getMin() != null && StringUtils.isNotBlank(params.getContentLength().getMinOp())) {
                    switch (params.getContentLength().getMinOp()) {
                        case ">":
                            wrapper.gt(Model::getContentLength, params.getContentLength().getMin());
                            break;
                        case "<":
                            wrapper.lt(Model::getContentLength, params.getContentLength().getMin());
                            break;
                        case "=":
                            wrapper.eq(Model::getContentLength, params.getContentLength().getMin());
                            break;
                        case ">=":
                            wrapper.ge(Model::getContentLength, params.getContentLength().getMin());
                            break;
                        case "<=":
                            wrapper.le(Model::getContentLength, params.getContentLength().getMin());
                            break;
                        default:
                            log.warn("Unsupported minOp for contentLength: {}", params.getContentLength().getMinOp());
                    }
                }
                if (params.getContentLength().getMax() != null && StringUtils.isNotBlank(params.getContentLength().getMaxOp())) {
                    switch (params.getContentLength().getMaxOp()) {
                        case ">":
                            wrapper.gt(Model::getContentLength, params.getContentLength().getMax());
                            break;
                        case "<":
                            wrapper.lt(Model::getContentLength, params.getContentLength().getMax());
                            break;
                        case "=":
                            wrapper.eq(Model::getContentLength, params.getContentLength().getMax());
                            break;
                        case ">=":
                            wrapper.ge(Model::getContentLength, params.getContentLength().getMax());
                            break;
                        case "<=":
                            wrapper.le(Model::getContentLength, params.getContentLength().getMax());
                            break;
                        default:
                            log.warn("Unsupported maxOp for contentLength: {}", params.getContentLength().getMaxOp());
                    }
                }
            }

            // 大小范围查询
            if (params.getSize() != null) {
                if (params.getSize().getMin() != null && StringUtils.isNotBlank(params.getSize().getMinOp())) {
                    switch (params.getSize().getMinOp()) {
                        case ">":
                            wrapper.gt(Model::getSize, params.getSize().getMin());
                            break;
                        case "<":
                            wrapper.lt(Model::getSize, params.getSize().getMin());
                            break;
                        case "=":
                            wrapper.eq(Model::getSize, params.getSize().getMin());
                            break;
                        case ">=":
                            wrapper.ge(Model::getSize, params.getSize().getMin());
                            break;
                        case "<=":
                            wrapper.le(Model::getSize, params.getSize().getMin());
                            break;
                        default:
                            log.warn("Unsupported minOp for size: {}", params.getSize().getMinOp());
                    }
                }
                if (params.getSize().getMax() != null && StringUtils.isNotBlank(params.getSize().getMaxOp())) {
                    switch (params.getSize().getMaxOp()) {
                        case ">":
                            wrapper.gt(Model::getSize, params.getSize().getMax());
                            break;
                        case "<":
                            wrapper.lt(Model::getSize, params.getSize().getMax());
                            break;
                        case "=":
                            wrapper.eq(Model::getSize, params.getSize().getMax());
                            break;
                        case ">=":
                            wrapper.ge(Model::getSize, params.getSize().getMax());
                            break;
                        case "<=":
                            wrapper.le(Model::getSize, params.getSize().getMax());
                            break;
                        default:
                            log.warn("Unsupported maxOp for size: {}", params.getSize().getMaxOp());
                    }
                }
                if (params.getSize().getArchitecture() != null) {
                    wrapper.eq(Model::getArchitectureType, params.getSize().getArchitecture());
                }
            }

            if (params.getArchitectureType() != null) {
                wrapper.eq(Model::getArchitectureType, params.getArchitectureType());
            }
            // 执行分页查询
            Page<Model> resultPage = modelService.page(page, wrapper);


            // Convert to DTO and return
            return BaseResponse.ok(PageDTO.from(resultPage));
        } catch (Exception e) {
            log.error("分页查询失败", e);
            return BaseResponse.serviceError("分页查询失败: " + e.getMessage());
        }
    }
    /*
    @PostMapping(value = "/completions")
    public BaseResponse<String> completions(@RequestBody ChatContextDTO chatContext) {
        // log.info(chatContext.getModel());
        String sid = UUID.randomUUID().toString();
        sessionIdToContext.put(sid, chatContext);
        return BaseResponse.ok(sid);
    }*/

    /*@RequestMapping(value = "/completions/stream")
    public SseEmitter completionsStream(@RequestHeader(required = false) Map<String, String> headers,
                                        @RequestBody(required = false) ChatContextDTO chatContext,
                                        HttpServletRequest httpServletRequest,
                                        HttpServletResponse response,
                                        String sessionId)  throws IOException {

        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("X-Accel-Buffering", "no");

        SseEmitter sseEmitter = null;

        if (chatContext == null) {
            throw new IllegalArgumentException("chat params is null");
            chatContext = new ChatContextDTO();
            Model model = new Model();
            model.setCode("Qwen-14B");
            model.setProvider("qwen");
            chatContext.setModel(model);
            List<ChatMessage> messages = new ArrayList<>();
            messages.add(new ChatMessage("user", "你好"));
            chatContext.setMessages(messages);
        }

        if (sessionId == null) {
            sessionId = UUID.randomUUID().toString();
            sseEmitter = SseEmitterManager.init(sessionId);
            //return sseEmitter;
        } else {
            sseEmitter = SseEmitterManager.get(sessionId);
            if (sseEmitter == null) {
                sseEmitter = SseEmitterManager.init(sessionId);
                //return sseEmitter;
            }
        }

        ModelDebugService.DataCallback callback = switchSSEDataCallback(chatContext.getModel().getProvider(), sessionId);

        log.info(chatContext.getModel().getCode());

        ChatContextDTO finalChatContext = chatContext;

        String finalSessionId = sessionId;
        getThreadPoolExecutor().execute(() -> {
            debugService.debugModelMessage(finalChatContext, callback);
        });

        return sseEmitter;
    }*/

    @RequestMapping("/completions")
    public void completions(@RequestBody(required = false) ChatContextDTO chatContext,
                               HttpServletRequest httpServletRequest,
                               HttpServletResponse response)  {

        // 处理常规请求（request 不为 null）
        String sessionId = chatContext.getSessionId();
        String tabId = chatContext.getTabId();
        // 验证必要参数
        if (StringUtils.isBlank(sessionId)) {
            log.info("请求中未包含sessionId");
            throw new RuntimeException("请求中未包含sessionId");
        }
        if (StringUtils.isBlank(tabId)) {
            log.info("请求中未包含tabId, sessionId=" + sessionId);
            return;
        }

        ModelDebugService.DataCallback callback = switchSSEDataCallback(chatContext.getModel().getProvider(), sessionId);

        log.info(chatContext.getModel().getCode());

        debugService.debugModelMessage(chatContext, callback);

    }


    private ThreadPoolExecutor getThreadPoolExecutor() {
        return SpringContextUtils.getBean("flowExecutorPool", ThreadPoolExecutor.class);
    }

    private ModelDebugService.SSEDataCallback switchSSEDataCallback(String provider, String sessionId) {
        ModelDebugService.SSEDataCallback callback = null;
        if ("Qwen".equalsIgnoreCase(provider)) {
            callback = new ModelDebugService.QwenSSEDataCallback(sessionId);
        } else if ("DeepSeek".equalsIgnoreCase(provider)) {
            callback = new ModelDebugService.DeekSeepSSEDataCallback(sessionId);
        } else if ("Meta".equalsIgnoreCase(provider)) {
            callback = new ModelDebugService.MetaSSEDataCallback(sessionId);
        }
        return callback;
    }

    @Data // 自动生成 getter、setter、toString 等方法
    public static class PageQueryParams {

        private Integer current = 1; // 当前页码
        private Integer pageSize = 1000; // 每页大小
        private String name; // 模型名称/编码

        private UsageType usageType; // 使用类型
        private GroupOrProvider groupOrProvider; // 组或提供者
        private RangeCondition<Integer> contentLength; // 内容长度
        private Size size; // 大小

        private String architectureType;
    }

    @Data
    public static class UsageType {
        private String value;
    }

    @Data
    public static class GroupOrProvider {
        private String group;
        private String provider;
    }


    // 定义通用的范围查询条件类
    @Data
    public static class RangeCondition<T extends Number> {
        private T min;
        private String minOp;
        private T max;
        private String maxOp;
    }
    @Data
    public static class Size extends RangeCondition<Number>  {
        private String architecture;

    }

}