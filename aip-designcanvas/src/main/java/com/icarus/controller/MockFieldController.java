package com.icarus.controller;

import com.icarus.dto.MockFieldRequest;
import com.icarus.dto.MockFieldResponse;
import com.icarus.service.MockFieldService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * Mock字段控制器
 * 提供生成模拟数据的API接口
 * dym
 */
@RestController
@RequestMapping("/aip/mock-fields")
@CrossOrigin
public class MockFieldController {
    
    private final MockFieldService mockFieldService;
    
    public MockFieldController(MockFieldService mockFieldService) {
        this.mockFieldService = mockFieldService;
    }
    
    /**
     * 生成模拟数据
     *
     * @param request 包含字段规则和mock配置的请求
     * @return 生成的模拟数据
     */
    @PostMapping("/generate")
    public MockFieldResponse generateMockData(@Validated @RequestBody MockFieldRequest request) {
        return mockFieldService.generateMockData(request);
    }
} 