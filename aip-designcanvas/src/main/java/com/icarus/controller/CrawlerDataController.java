package com.icarus.controller;

import com.icarus.common.rest.BaseResponse;
import com.icarus.dto.PageDTO;
import com.icarus.dto.CrawlerFileDTO;
import com.icarus.dto.CrawlerTaskDTO;
import com.icarus.entity.CrawlerFile;
import com.icarus.entity.CrawlerTask;
import com.icarus.service.CrawlerFileService;
import com.icarus.service.CrawlerTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 爬虫数据API接口
 * 提供爬虫收集数据的查询接口，供其他系统调用
 */
@Slf4j
@RestController
@RequestMapping("/aip/crawler/data")
@RequiredArgsConstructor
@Api(tags = "爬虫数据API")
public class CrawlerDataController {

    private final CrawlerTaskService crawlerTaskService;
    private final CrawlerFileService crawlerFileService;

    /**
     * 分页查询爬虫任务
     *
     * @param dto 包含查询参数的任务对象
     * @return 分页结果
     */
    @PostMapping("/tasks/page")
    @ApiOperation(value = "分页查询爬虫任务", notes = "支持分页、时间范围和状态过滤")
    public BaseResponse<PageDTO<CrawlerTask>> pageTask(@RequestBody CrawlerTaskDTO dto) {
        try {
            Integer current = dto.getCurrent() != null ? dto.getCurrent() : 1;
            Integer pageSize = dto.getPageSize() != null ? dto.getPageSize() : 10;
            
            log.info("分页查询爬虫任务: current={}, pageSize={}, status={}", 
                     current, pageSize, dto.getStatus());
            
            // 调用Service层方法查询任务列表
            Page<CrawlerTask> tasks = crawlerTaskService.findTasks(
                current - 1, 
                pageSize, 
                dto.getStartTime(), 
                dto.getEndTime(), 
                dto.getStatus()
            );
            
            // 转换为统一的PageDTO格式
            PageDTO<CrawlerTask> pageDTO = new PageDTO<>();
            pageDTO.setRecords(tasks.getContent());
            pageDTO.setCurrent(current);
            pageDTO.setPageSize(pageSize);
            pageDTO.setTotal(tasks.getTotalElements());
            
            return BaseResponse.ok(pageDTO);
        } catch (Exception e) {
            log.error("分页查询爬虫任务失败", e);
            return BaseResponse.serviceError("分页查询爬虫任务失败: " + e.getMessage());
        }
    }

    /**
     * 根据批次ID获取爬虫任务
     *
     * @param dto 包含批次ID的任务对象
     * @return 爬虫任务列表
     */
    @PostMapping("/tasks/batch")
    @ApiOperation(value = "根据批次ID获取爬虫任务", notes = "返回指定批次的所有爬虫任务")
    public BaseResponse<List<CrawlerTask>> getTasksByBatchId(@RequestBody CrawlerTaskDTO dto) {
        try {
            String batchId = dto.getBatchId();
            log.info("根据批次ID查询爬虫任务: batchId={}", batchId);
            
            if (batchId == null) {
                return BaseResponse.badRequest("批次ID不能为空");
            }
            
            // 调用Service层方法查询任务
            List<CrawlerTask> tasks = crawlerTaskService.findByBatchId(batchId);
            return BaseResponse.ok(tasks);
        } catch (Exception e) {
            log.error("根据批次ID查询爬虫任务失败", e);
            return BaseResponse.serviceError("根据批次ID查询爬虫任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取爬虫任务详情，包含文本内容
     *
     * @param dto 包含ID的任务对象
     * @return 爬虫任务详情
     */
    @PostMapping("/task/detail")
    @ApiOperation(value = "获取爬虫任务详情", notes = "返回任务详情，包括网页正文内容")
    public BaseResponse<CrawlerTask> getTaskDetail(@RequestBody CrawlerTaskDTO dto) {
        try {
            Long taskId = dto.getId();
            log.info("查询爬虫任务详情: taskId={}", taskId);
            
            if (taskId == null) {
                return BaseResponse.badRequest("任务ID不能为空");
            }
            
            // 调用Service层方法查询任务详情
            CrawlerTask result = crawlerTaskService.getById(taskId);
            if (result == null) {
                return BaseResponse.badRequest("任务不存在");
            }
            return BaseResponse.ok(result);
        } catch (Exception e) {
            log.error("查询爬虫任务详情失败", e);
            return BaseResponse.serviceError("查询爬虫任务详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务的文本内容
     *
     * @param dto 包含ID的任务对象
     * @return 文本内容
     */
    @PostMapping("/task/content")
    @ApiOperation(value = "获取任务的文本内容", notes = "单独返回任务爬取的网页正文内容")
    public BaseResponse<String> getTaskContent(@RequestBody CrawlerTaskDTO dto) {
        try {
            Long taskId = dto.getId();
            log.info("查询爬虫任务文本内容: taskId={}", taskId);
            
            if (taskId == null) {
                return BaseResponse.badRequest("任务ID不能为空");
            }
            
            // 调用Service层方法查询任务
            CrawlerTask result = crawlerTaskService.getById(taskId);
            if (result == null) {
                return BaseResponse.badRequest("任务不存在");
            }
            return BaseResponse.ok(result.getTextContent());
        } catch (Exception e) {
            log.error("查询爬虫任务文本内容失败", e);
            return BaseResponse.serviceError("查询爬虫任务文本内容失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务爬取的文件列表
     *
     * @param dto 包含任务ID和分页参数的文件对象
     * @return 文件列表
     */
    @PostMapping("/task/files")
    @ApiOperation(value = "获取任务爬取的文件列表", notes = "返回任务爬取的所有文件，支持分页和文件类型过滤")
    public BaseResponse<PageDTO<CrawlerFile>> getTaskFiles(@RequestBody CrawlerFileDTO dto) {
        try {
            Long taskId = dto.getTaskId();
            Integer current = dto.getCurrent() != null ? dto.getCurrent() : 1;
            Integer pageSize = dto.getPageSize() != null ? dto.getPageSize() : 10;
            String fileType = dto.getFileType();
            
            log.info("查询爬虫任务文件列表: taskId={}, current={}, pageSize={}, fileType={}", 
                    taskId, current, pageSize, fileType);
            
            if (taskId == null) {
                return BaseResponse.badRequest("任务ID不能为空");
            }
            
            // 调用Service层方法查询文件列表
            Page<CrawlerFile> files = crawlerFileService.findByTaskId(taskId, current - 1, pageSize, fileType);
            
            // 转换为统一的PageDTO格式
            PageDTO<CrawlerFile> pageDTO = new PageDTO<>();
            pageDTO.setRecords(files.getContent());
            pageDTO.setCurrent(current);
            pageDTO.setPageSize(pageSize);
            pageDTO.setTotal(files.getTotalElements());
            
            return BaseResponse.ok(pageDTO);
        } catch (Exception e) {
            log.error("查询爬虫任务文件列表失败", e);
            return BaseResponse.serviceError("查询爬虫任务文件列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件详情
     *
     * @param dto 包含ID的文件对象
     * @return 文件详情
     */
    @PostMapping("/file/detail")
    @ApiOperation(value = "获取文件详情", notes = "返回指定文件的详细信息")
    public BaseResponse<CrawlerFile> getFileDetail(@RequestBody CrawlerFileDTO dto) {
        try {
            Long fileId = dto.getId();
            log.info("查询文件详情: fileId={}", fileId);
            
            if (fileId == null) {
                return BaseResponse.badRequest("文件ID不能为空");
            }
            
            // 调用Service层方法查询文件详情
            CrawlerFile result = crawlerFileService.getById(fileId);
            if (result == null) {
                return BaseResponse.badRequest("文件不存在");
            }
            return BaseResponse.ok(result);
        } catch (Exception e) {
            log.error("查询文件详情失败", e);
            return BaseResponse.serviceError("查询文件详情失败: " + e.getMessage());
        }
    }

    /**
     * 搜索爬虫内容
     *
     * @param dto 包含关键字和分页参数的任务对象
     * @return 匹配的任务列表
     */
    @PostMapping("/search")
    @ApiOperation(value = "搜索爬虫内容", notes = "全文搜索爬虫收集的内容，返回匹配的任务")
    public BaseResponse<PageDTO<CrawlerTask>> searchContent(@RequestBody CrawlerTaskDTO dto) {
        try {
            String keywords = dto.getKeywords();
            Integer current = dto.getCurrent() != null ? dto.getCurrent() : 1;
            Integer pageSize = dto.getPageSize() != null ? dto.getPageSize() : 10;
            
            log.info("搜索爬虫内容: keywords={}, current={}, pageSize={}", keywords, current, pageSize);
            
            if (keywords == null || keywords.trim().isEmpty()) {
                return BaseResponse.badRequest("搜索关键字不能为空");
            }
            
            // 调用Service层方法搜索内容
            Page<CrawlerTask> tasks = crawlerTaskService.searchContent(keywords, current - 1, pageSize);
            
            // 转换为统一的PageDTO格式
            PageDTO<CrawlerTask> pageDTO = new PageDTO<>();
            pageDTO.setRecords(tasks.getContent());
            pageDTO.setCurrent(current);
            pageDTO.setPageSize(pageSize);
            pageDTO.setTotal(tasks.getTotalElements());
            
            return BaseResponse.ok(pageDTO);
        } catch (Exception e) {
            log.error("搜索爬虫内容失败", e);
            return BaseResponse.serviceError("搜索爬虫内容失败: " + e.getMessage());
        }
    }

    /**
     * 获取爬虫统计信息
     *
     * @return 统计信息
     */
    @PostMapping("/stats")
    @ApiOperation(value = "获取爬虫统计信息", notes = "返回爬虫任务和文件的统计信息")
    public BaseResponse<Map<String, Object>> getStats() {
        try {
            log.info("获取爬虫统计信息");
            
            // 调用Service层方法获取统计信息
            Map<String, Object> stats = crawlerTaskService.getStats();
            return BaseResponse.ok(stats);
        } catch (Exception e) {
            log.error("获取爬虫统计信息失败", e);
            return BaseResponse.serviceError("获取爬虫统计信息失败: " + e.getMessage());
        }
    }
} 