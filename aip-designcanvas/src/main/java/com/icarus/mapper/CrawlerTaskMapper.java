package com.icarus.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.entity.CrawlerTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 爬虫任务Mapper接口
 */
@Mapper
public interface CrawlerTaskMapper extends BaseMapper<CrawlerTask> {
    
    /**
     * 分页查询爬虫任务
     *
     * @param page     分页参数
     * @param keyword  关键词
     * @param status   状态
     * @param settingsId 配置ID
     * @param batchId  批次ID
     * @return 分页结果
     */
    IPage<CrawlerTask> selectPageWithParams(Page<CrawlerTask> page,
                                           @Param("keyword") String keyword,
                                           @Param("status") String status,
                                           @Param("settingsId") Long settingsId,
                                           @Param("batchId") String batchId,
                                           @Param("startTime") String startTime,
                                           @Param("endTime") String endTime);

    /**
     * 查询最近N天的任务数量
     *
     * @param days 天数
     * @return 每日任务数量
     */
    @Select("SELECT TO_CHAR(create_time, 'YYYY-MM-DD') as day, COUNT(*) as count " +
            "FROM crawler_tasks " +
            "WHERE create_time >= CURRENT_DATE - #{days} * INTERVAL '1 day' " +
            "GROUP BY TO_CHAR(create_time, 'YYYY-MM-DD') " +
            "ORDER BY day")
    List<Map<String, Object>> selectDailyTaskCounts(@Param("days") int days);
}