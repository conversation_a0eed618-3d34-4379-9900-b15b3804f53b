package com.icarus.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.entity.CrawlerFile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 爬虫文件Mapper接口
 */
@Mapper
public interface CrawlerFileMapper extends BaseMapper<CrawlerFile> {
    
    /**
     * 分页查询爬虫文件
     *
     * @param page 分页参数
     * @param params 查询参数
     * @return 分页结果
     */
    Page<CrawlerFile> selectPageWithParams(Page<CrawlerFile> page, @Param("params") Map<String, Object> params);
    
    /**
     * 根据任务ID查询文件列表
     *
     * @param taskId 任务ID
     * @return 文件列表
     */
    List<CrawlerFile> selectByTaskId(@Param("taskId") Long taskId);
    
    /**
     * 根据ID列表查询文件列表
     *
     * @param ids ID列表
     * @return 文件列表
     */
    List<CrawlerFile> selectByIds(@Param("ids") List<Long> ids);
    
    /**
     * 根据标题、发布时间和配置ID查询文件列表，用于内容去重
     *
     * @param title 标题
     * @param publishTime 发布时间
     * @param settingsId 配置ID
     * @return 文件列表
     */
    List<CrawlerFile> findByTitleAndPublishTimeAndSettingsId(
        @Param("title") String title, 
        @Param("publishTime") String publishTime, 
        @Param("settingsId") Long settingsId);

    /**
     * 统计各类型文件数量
     *
     * @return 文件类型统计
     */
    @Select("SELECT file_type as fileType, COUNT(*) as count FROM crawler_files GROUP BY file_type")
    List<Map<String, Object>> selectFileCounts();
}