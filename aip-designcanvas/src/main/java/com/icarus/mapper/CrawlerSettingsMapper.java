package com.icarus.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.entity.CrawlerSettings;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 爬虫配置Mapper接口
 */
@Mapper
public interface CrawlerSettingsMapper extends BaseMapper<CrawlerSettings> {
    
    /**
     * 分页查询爬虫配置
     *
     * @param page    分页参数
     * @param keyword 关键词
     * @param status  状态
     * @return 分页结果
     */
    IPage<CrawlerSettings> selectPageWithParams(Page<CrawlerSettings> page,
                                               @Param("keyword") String keyword,
                                               @Param("status") String status,
                                               @Param("startTime") String startTime,
                                               @Param("endTime") String endTime);
}