package com.icarus.common.enums;

import lombok.Getter;
import org.elasticsearch.search.sort.SortOrder;

/**
 * Elasticsearch排序方式枚举
 * 用于指定查询结果的排序方式
 */
@Getter
public enum ElasticsearchSortOrderEnum {
    
    ASC("asc", "升序", "从小到大排序", SortOrder.ASC),
    DESC("desc", "降序", "从大到小排序", SortOrder.DESC);

    private final String code;        // 排序方式代码
    private final String name;        // 排序方式名称
    private final String desc;        // 排序方式描述
    private final SortOrder sortOrder;// ES原生排序枚举

    ElasticsearchSortOrderEnum(String code, String name, String desc, SortOrder sortOrder) {
        this.code = code;
        this.name = name;
        this.desc = desc;
        this.sortOrder = sortOrder;
    }

    /**
     * 根据代码获取枚举值
     * @param code 排序方式代码
     * @return 对应的枚举值，如果未找到则默认返回升序
     */
    public static ElasticsearchSortOrderEnum getByCode(String code) {
        if (code == null) {
            return ASC;
        }
        
        for (ElasticsearchSortOrderEnum order : values()) {
            if (order.getCode().equalsIgnoreCase(code)) {
                return order;
            }
        }
        return ASC; // 默认返回升序
    }

    /**
     * 获取ES原生的排序枚举
     * @return ES的SortOrder枚举值
     */
    public SortOrder getSortOrder() {
        return this.sortOrder;
    }

    /**
     * 判断是否为升序
     * @return 是否为升序
     */
    public boolean isAsc() {
        return this == ASC;
    }

    /**
     * 判断是否为降序
     * @return 是否为降序
     */
    public boolean isDesc() {
        return this == DESC;
    }
} 