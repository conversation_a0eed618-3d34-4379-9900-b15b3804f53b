package com.icarus.common.enums;

import lombok.Getter;

/**
 * Elasticsearch逻辑关系枚举
 */
@Getter
public enum ElasticsearchLogicEnum {
    
    AND("AND", "与", "必须同时满足所有条件"),
    OR("OR", "或", "满足任一条件即可");

    private final String code;
    private final String name;
    private final String desc;

    ElasticsearchLogicEnum(String code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public static ElasticsearchLogicEnum getByCode(String code) {
        for (ElasticsearchLogicEnum logic : values()) {
            if (logic.getCode().equalsIgnoreCase(code)) {
                return logic;
            }
        }
        return AND; // 默认返回AND逻辑
    }
} 