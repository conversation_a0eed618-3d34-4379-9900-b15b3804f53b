package com.icarus.common.enums;

import lombok.Getter;

/**
 * Elasticsearch操作类型枚举
 */
@Getter
public enum ElasticsearchOperationEnum {
    
    SEARCH("search", "查询操作"),
    INSERT("insert", "插入操作"),
    UPDATE("update", "更新操作"),
    DELETE("delete", "删除操作"),
    BULK("bulk", "批量操作");

    private final String code;
    private final String desc;

    ElasticsearchOperationEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ElasticsearchOperationEnum getByCode(String code) {
        for (ElasticsearchOperationEnum operation : values()) {
            if (operation.getCode().equals(code)) {
                return operation;
            }
        }
        throw new IllegalArgumentException("不支持的操作类型: " + code);
    }
} 