package com.icarus.common.enums;

import lombok.Getter;

/**
 * Elasticsearch查询操作符枚举
 */
@Getter
public enum ElasticsearchOperatorEnum {
    
    MATCH("match", "全文匹配", "会对查询词进行分词，如'北京大学'会被分为'北京'和'大学'"),
    TERM("term", "精确匹配", "不进行分词处理，完全匹配"),
    RANGE("range", "范围查询", "支持大于等于(gte)和小于等于(lte)"),
    WILDCARD("wildcard", "通配符查询", "支持*和?通配符"),
    PREFIX("prefix", "前缀查询", "匹配字段前缀"),
    EXISTS("exists", "存在性查询", "检查字段是否存在");

    private final String code;
    private final String name;
    private final String desc;

    ElasticsearchOperatorEnum(String code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public static ElasticsearchOperatorEnum getByCode(String code) {
        for (ElasticsearchOperatorEnum operator : values()) {
            if (operator.getCode().equals(code)) {
                return operator;
            }
        }
        throw new IllegalArgumentException("不支持的操作符: " + code);
    }
} 