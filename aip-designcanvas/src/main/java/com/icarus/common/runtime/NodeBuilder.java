package com.icarus.common.runtime;


import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.icarus.common.cotnode.BaseNode;
import com.icarus.common.cotnode.Node;
import com.icarus.common.pojo.AIPFlowDefinition;
import com.icarus.plugin.api.NodeProvider;
import com.icarus.plugin.container.PluginManagerContainer;
import com.icarus.plugin.spring.PluginSpringContextHolder;
import com.icarus.util.SpiUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;

/**
 * 节点构建器
 * @autor ksw
 * @date 2025-03-11
 */
public class NodeBuilder {

    private static final Logger logger = LoggerFactory.getLogger(NodeBuilder.class);

    /**
     * 根据形状构建Node
     * @param cell
     * @return
     */
    public static BaseNode build(AIPFlowDefinition.Cell cell){

        /**
         * 这里根据节点定义中的信息获取节点信息
         * 1. 判断节点提供者[内置节点/扩展节点)];data.getProvider(),内置节点为空,外置节点为热部署插件ID(插件唯一标识)
         * 2. 根据形状获取节点(节点唯一标识)
         */
        AIPFlowDefinition.CellData data = cell.getData();
        if(StrUtil.isEmpty(data.getProvider())){
            // 内置节点处理
            Class<? extends BaseNode> nodeClass = getNodeClass(cell.getShape());
            try{
                // 内置节点通过反射获取节点实例
                return nodeClass.newInstance();
            }catch (Exception e){
                throw new RuntimeException(e);
            }
        }else{
            /**
             * 外置节点处理(获取热部署插件中的NodeProvider来构建Node)
             */
            // 获取插件管理容器
            PluginManagerContainer pluginManagerContainer = PluginSpringContextHolder.getBean(PluginManagerContainer.class);
            // 从插件管理容器中获取插件的Spring上下文
            ApplicationContext pluginApplicationContext = pluginManagerContainer.getPluginApplicationContext(data.getProvider());
            if(ObjUtil.isNull(pluginApplicationContext)){
                logger.error("当前插件已停止或未安装:{}", data.getProvider());
                return null;
            }
            // 从插件Spring上下文中获取插件中定义的bean
            NodeProvider nodeProvider = pluginApplicationContext.getBean(NodeProvider.class);
            BaseNode node = nodeProvider.build(cell.getShape());
            if(ObjUtil.isNull(node)) logger.error("插件[{}]未提供扩展节点:[{}]", data.getProvider(), cell.getShape());
            return node;
        }

    }

    /**
     * 获取节点class
     * @param type
     * @return
     */
    public static Class<? extends BaseNode> getNodeClass(String type) {
        Node node = SpiUtil.getExtensionLoader(Node.class).getExtension(type);
        return (Class<? extends BaseNode>) node.getClass();
    }

}
