package com.icarus.common.runtime;

import com.icarus.service.SpireService;
import com.icarus.utils.*;

import java.util.HashMap;
import java.util.Map;

/**
 * @description: 文件转化器工厂
 */
public class FileConverterFactory {
    private static final Map<String, SpireService> CONVERTER_MAP = new HashMap<>();

    static {
        CONVERTER_MAP.put("pdf", new PdfUtil_SpirePDF());
        CONVERTER_MAP.put("docx", new DocxUtil_SpireDocx());
        CONVERTER_MAP.put("doc", new DocxUtil_SpireDocx());
        CONVERTER_MAP.put("txt", new TxtUtil_SpireTxt());
        CONVERTER_MAP.put("xlsx", new XlsxUtil_SpireXlsx());
        CONVERTER_MAP.put("xls", new XlsxUtil_SpireXlsx());
        CONVERTER_MAP.put("jpg", new PicUtil_SpirePic());
        CONVERTER_MAP.put("jpeg", new PicUtil_SpirePic());
        CONVERTER_MAP.put("bmp", new PicUtil_SpirePic());
        CONVERTER_MAP.put("png", new PicUtil_SpirePic());
        CONVERTER_MAP.put("tif", new PicUtil_SpirePic());
        CONVERTER_MAP.put("tiff", new PicUtil_SpirePic());





    }
    public static SpireService getConverter(String fileExtension) {
        SpireService converter = CONVERTER_MAP.get(fileExtension.toLowerCase());
        if (converter == null) {
            throw new IllegalArgumentException("不支持的文件类型: " + fileExtension);
        }
        return converter;
    }
}