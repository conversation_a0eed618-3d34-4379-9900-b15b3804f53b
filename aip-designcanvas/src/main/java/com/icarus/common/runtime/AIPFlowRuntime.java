package com.icarus.common.runtime;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.icarus.common.cotnode.BaseNode;
import com.icarus.common.cotnode.flow.EndNode;
import com.icarus.common.cotnode.flow.IFELSENode;
import com.icarus.common.cotnode.flow.StartNode;
import com.icarus.common.pojo.AIPFlowDefinition;
import com.icarus.dto.RuntimeRequest;
import com.icarus.sdk.springboot.base.utils.SpringContextUtils;
import com.icarus.utils.SSEUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;


/**
 * 画布信息与节点信息处理的类
 */
@Data
public class AIPFlowRuntime {
    private Map<String, AIPFlowDefinition.Cell> nodeMap; // 存储所有节点
    private Map<String, List<AIPFlowDefinition.Cell>> connectionMap; // 存储节点间的连接关系
    private AIPFlowDefinition.Cell currentNode; // 当前节点
    private AIPFlowDefinition.Cell startNode; // 开始节点
    private Map<String, Object> executionContext; // 添加一个成员变量保存执行后的上下文
    private AIPFlowDefinition.Cell realEndNode; // 添加一个成员变量保存真正的结束节点
    private static final Logger log = LoggerFactory.getLogger(AIPFlowRuntime.class);

    public AIPFlowRuntime(AIPFlowDefinition AIPFlowDefinition) {
        this.nodeMap = new HashMap<>();
        this.connectionMap = new HashMap<>();

        // 将 cells 转换为 map 结构，方便后续处理
        Map<String, AIPFlowDefinition.Cell> cotMap = new HashMap<>();
        for (AIPFlowDefinition.Cell cell : AIPFlowDefinition.getCells()) {
            cotMap.put(cell.getId(), cell);
        }

        initializeRuntime(cotMap);
    }

    /**
     * 初始化运行时环境
     */
    private void initializeRuntime(Map<String, AIPFlowDefinition.Cell> cotMap) {
        // 第一次遍历：存储所有业务节点
        for (AIPFlowDefinition.Cell cell : cotMap.values()) {
            if (!cell.getShape().equals("data-processing-curve")) {
                // 初始化前后节点列表
                cell.setNextNodeIds(new ArrayList<>());
                cell.setPrevNodeIds(new ArrayList<>());
                
                // 存储业务节点
                nodeMap.put(cell.getId(), cell);

                // 识别开始节点或设置第一个非连接线节点为当前节点
                if (cell.getShape().equals("startNode")) {
                    this.startNode = cell;
                    this.currentNode = cell;  // 初始化当前节点为开始节点
                } else if (this.currentNode == null) {
                    // 如果没有startNode，将第一个非连接线节点设为当前节点
                    this.currentNode = cell;
                }
            }
        }

        // 第二次遍历：处理连接关系
        for (AIPFlowDefinition.Cell cell : cotMap.values()) {
            if (cell.getShape().equals("data-processing-curve")) {
                // 处理连接线
                String sourceId = cell.getSource().getCell();
                String targetId = cell.getTarget().getCell();

                // 获取源节点和目标节点
                AIPFlowDefinition.Cell sourceNode = nodeMap.get(sourceId);
                AIPFlowDefinition.Cell targetNode = nodeMap.get(targetId);

                if (sourceNode != null && targetNode != null) {
                    // 建立节点连接关系
                    connectionMap.computeIfAbsent(sourceId, k -> new ArrayList<>())
                            .add(targetNode);
                    
                    // 设置节点的前后关系
                    sourceNode.getNextNodeIds().add(targetId);
                    targetNode.getPrevNodeIds().add(sourceId);
                } else {
                    log.error("Invalid connection: source={}, target={}", sourceId, targetId);
                }
            }
        }

        // 验证连接关系
        log.info("节点连接关系初始化完成：");
        nodeMap.values().forEach(node -> {
            log.info("Node: {}, Previous nodes: {}, Next nodes: {}", 
                    node.getId(), 
                    String.join(", ", node.getPrevNodeIds()), 
                    String.join(", ", node.getNextNodeIds()));
        });
    }

    /**
     * 获取当前节点
     */
    public AIPFlowDefinition.Cell getCurrNode() {
        return currentNode;
    }

    /**
     * 获取开始节点
     */
    public AIPFlowDefinition.Cell getStratNode() {
        return startNode;
    }

    /**
     * 获取最后的节点(最后节点定义:真正的执行完成的EndNode节点)修复
     * debug可以执行但是发布后输出结果为空问题）
     */
    public AIPFlowDefinition.Cell getEndNode() {
        // 如果有已执行完成的真正结束节点，则返回
        if (realEndNode != null) {
            return realEndNode;
        }

        // 如果执行上下文不为空，则查找节点类型为EndNode且状态为completed的节点
        if (executionContext != null) {
            for (Map.Entry<String, Object> entry : executionContext.entrySet()) {
                if (entry.getValue() instanceof NodeContext) {
                    NodeContext nodeContext = (NodeContext) entry.getValue();
                    if ("EndNode".equals(nodeContext.getNodeType()) && "completed".equals(nodeContext.getStatus())) {
                        AIPFlowDefinition.Cell endNodeCell = nodeMap.get(nodeContext.getNodeId());
                        if (endNodeCell != null) {
                            // 保存找到的真正结束节点，以便下次直接返回
                            realEndNode = endNodeCell;
                            return endNodeCell;
                        }
                    }
                }
            }
        }

        // 如果当前节点是EndNode类型，则返回当前节点
        if (currentNode != null && "endNode".equals(currentNode.getShape())) {
            return currentNode;
        }

        // 最后才返回默认的最后节点（不存在后置节点的节点）
        return nodeMap.values().stream().filter(cell -> {
            return CollUtil.isEmpty(cell.getNextNodeIds()) ? true : false;
        }).findFirst().orElse(null);
    }

    /**
     * 判断当前节点是否是结束节点
     * @param node 要判断的节点
     * @return 是否是结束节点
     */
    public Boolean isEndNode(AIPFlowDefinition.Cell node) {
        return node != null && "endNode".equals(node.getShape());
    }

    /**
     * 创建节点执行器,入参名称与配置文件路径映射
     * @param cell 节点定义
     * @return 节点执行器实例
     */
    private BaseNode createNodeExecutor(AIPFlowDefinition.Cell cell) {
        try {
            // 构建Node
            BaseNode node = NodeBuilder.build(cell);
            // 设置基本属性
            node.setId(cell.getId());
            node.setNextNodeIds(cell.getNextNodeIds());
            node.setPrevNodeIds(cell.getPrevNodeIds());
            
            if (cell.getData() != null) {
                // 转换输入参数
                if (cell.getData().getInputs() != null) {
                    List<BaseNode.Parameter> inputParams = new ArrayList<>();
                    Object inputs = cell.getData().getInputs();
                    if (inputs instanceof List) {
                        for (Object input : (List<?>) inputs) {
                            BaseNode.Parameter param = convertToParameter(input);
                            if (param != null) {
                                inputParams.add(param);
                            }
                        }
                    }
                    node.setInputs(inputParams);
                }

                // 转换输出参数
                if (cell.getData().getOutputs() != null) {
                    List<BaseNode.Parameter> outputParams = new ArrayList<>();
                    Object outputs = cell.getData().getOutputs();
                    if (outputs instanceof List) {
                        for (Object output : (List<?>) outputs) {
                            BaseNode.Parameter param = convertToParameter(output);
                            if (param != null) {
                                outputParams.add(param);
                            }
                        }
                    }
                    node.setOutputs(outputParams);
                }
                // 处理properties
                if (cell.getData().getProperties() != null) {
                    node.setProperties(cell.getData().getProperties());
                }
            }
            
            return node;
        } catch (Exception e) {
            log.error("创建节点执行器失败：{}", cell.getShape(), e);
            throw new RuntimeException("创建节点执行器失败", e);
        }
    }

    /**
     * 将输入输出对象转换为Parameter对象
     */
    private BaseNode.Parameter convertToParameter(Object obj) {
        if (obj == null) return null;
        
        try {
            JSONObject jsonObj = JSONUtil.parseObj(obj);
            BaseNode.Parameter param = new BaseNode.Parameter();
            
            // 设置基本属性
            param.setName(jsonObj.getStr("name"));
            param.setValue(jsonObj.get("value"));
            param.setType(jsonObj.getStr("type", "string")); // 默认类型为string

            // 处理子参数
            if (jsonObj.containsKey("children")) {
                List<Object> children = jsonObj.getJSONArray("children");
                if (children != null) {
                    for (Object child : children) {
                        // 添加循环检测
                        if (isCircularReference(child, new ArrayList<>(Arrays.asList(obj)))) {
                            log.error("检测到循环引用，跳过子参数处理");
                            continue;
                        }
                        
                        BaseNode.Parameter childParam = convertToParameter(child);
                        if (childParam != null) {
                            param.getChildren().add(childParam);
                        }
                    }
                }
            }

            // 根据类型转换值
            if (param.getType() != null && param.getValue() != null) {
                switch (param.getType().toLowerCase()) {
                    case "string":
                        param.setValue(param.getValue().toString());
                        break;
                    case "integer":
                        try {
                            param.setValue(Integer.parseInt(param.getValue().toString()));
                        } catch (NumberFormatException e) {
                            log.warn("Failed to parse integer value: {}", param.getValue());
                        }
                        break;
                    case "boolean":
                        param.setValue(Boolean.parseBoolean(param.getValue().toString()));
                        break;
                    case "number":
                        try {
                            param.setValue(Double.parseDouble(param.getValue().toString()));
                        } catch (NumberFormatException e) {
                            log.warn("Failed to parse number value: {}", param.getValue());
                        }
                        break;
                    case "object":
                        if (!(param.getValue() instanceof JSONObject)) {
                            param.setValue(JSONUtil.parseObj(param.getValue()));
                        }
                        break;
                    case "array":
                        if (!(param.getValue() instanceof List)) {
                            param.setValue(JSONUtil.parseArray(param.getValue()));
                        }
                        break;
                }
            }

            return param;
        } catch (Exception e) {
            log.error("参数转换失败：{}", obj, e);
            return null;
        }
    }

    /**
     * 获取节点的所有后续节点
     */
    public List<AIPFlowDefinition.Cell> getNextNodes(String nodeId) {
        return connectionMap.getOrDefault(nodeId, new ArrayList<>());
    }

    /**
     * 获取当前节点的下一个节点
     * @return 下一个节点，如果没有下一个节点则返回null
     */
    public AIPFlowDefinition.Cell getNextNode() {
        if (currentNode == null) {
            throw new RuntimeException("Current node is null");
        }

        // 获取当前节点的所有后续节点
        List<AIPFlowDefinition.Cell> nextNodes = connectionMap.get(currentNode.getId());
        if (nextNodes == null || nextNodes.isEmpty()) {
            return null;  // 没有后续节点
        }

        // 返回第一个后续节点
        return nextNodes.get(0);
    }

    /**
     * 通过节点ID获取节点
     * @param nodeId 节点ID
     * @return 对应的节点，如果不存在返回null
     */
    public AIPFlowDefinition.Cell getNodeById(String nodeId) {
        if (nodeId == null || nodeId.isEmpty()) {
            log.warn("节点ID为空");
            return null;
        }
        
        AIPFlowDefinition.Cell node = nodeMap.get(nodeId);
        if (node == null) {
            log.warn("未找到ID为 {} 的节点", nodeId);
        }
        
        return node;
    }

    /**
     * 执行指定节点并返回结果
     * @param id 要执行的节点ID
     * @param request 请求参数
     * @return 上下文
     */
    public Map<String, Object> executeNode(String id, RuntimeRequest request) {
        AIPFlowDefinition.Cell currNode = nodeMap.get(id);
        if (currNode == null) {
            log.error("未找到节点[{}]", id);
            return CollUtil.empty(Map.class);
        }
        // 创建上下文
        Map<String, Object> context = new HashMap<>();
        // 设置流程数据
        BaseNode.setFlowData(context, request.getHeaders(), request.getCommons(), request.getData());

        // 获取会话ID用于发送SSE事件
        String sessionId = null;
        if (request.getHeaders() != null && request.getHeaders().containsKey("aip-session")) {
            sessionId = request.getHeaders().get("aip-session");
            context.put("aip-session", sessionId);
        }

        try {
            // 创建节点执行器
            BaseNode executor = createNodeExecutor(currNode);
            if (executor != null) {
                // 执行节点
                context = executor.execute(context);

                // 节点执行完成后，发送SSE事件
                sendNodeExecutionEvent(sessionId, getNodeContext(context, id));
            } else {
                log.warn("未能创建节点执行器，节点类型: {}", currNode.getShape());
                // 创建错误状态的节点上下文
                NodeContext nodeContext = new NodeContext();
                nodeContext.setNodeId(currNode.getId());
                nodeContext.setNodeType(currNode.getShape());
                nodeContext.setStartTime(System.currentTimeMillis());
                nodeContext.setEndTime(System.currentTimeMillis());
                nodeContext.setStatus("failed");
                nodeContext.setErrorMessage("未能创建节点执行器");
                context.put(currNode.getId(), nodeContext);

                // 发送错误状态的SSE事件
                sendNodeExecutionEvent(sessionId, nodeContext);
            }
        } catch (Exception e) {
            log.error("节点执行失败，ID: {}", currNode.getId(), e);
            // 更新节点上下文的错误状态
            NodeContext nodeContext = new NodeContext();
            nodeContext.setNodeId(currNode.getId());
            nodeContext.setNodeType(currNode.getShape());
            nodeContext.setStartTime(System.currentTimeMillis());
            nodeContext.setEndTime(System.currentTimeMillis());
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
            context.put(currNode.getId(), nodeContext);

            // 发送错误状态的SSE事件
            sendNodeExecutionEvent(sessionId, nodeContext);

            throw new RuntimeException("Node execution failed", e);
        }
        return context;
    }

    public AIPFlowDefinition.Cell getExitNode(AIPFlowDefinition.Cell node, Map<String, Object> context) {
        if (node == null) {
            return null;
        }

        List<AIPFlowDefinition.Cell> nextNodes = connectionMap.get(node.getId());
        if (nextNodes == null || nextNodes.isEmpty()) {
            return null;
        }
        // 如果只有一个输出节点，直接返回
        if (nextNodes.size() == 1) {
            return nextNodes.get(0);
        }
        // 首先尝试从节点上下文中获取下一个节点信息
        String nodeId = node.getId();
        NodeContext nodeContext = getNodeContext(context, nodeId);
        if (nodeContext != null && nodeContext.getMetadata() != null) {
            String nextNodeId = (String) nodeContext.getMetadata().get("nextNode");
            if (StringUtils.isNotBlank(nextNodeId)) {
                // 优先使用节点ID匹配
                for (AIPFlowDefinition.Cell nextNode : nextNodes) {
                    if (nextNode.getId().equals(nextNodeId)) {
                        return nextNode;
                    }
                }
            }
        }
        // 默认返回第一个输出节点
        return nextNodes.get(0);
    }

    /**
     * 执行流程
     * 
     * @param request 运行时请求参数
     */
    public Map<String, Object> executeFlow(RuntimeRequest request) {
        // 初始化流程上下文(运行时参数设置)
        Map<String, Object> context = new HashMap<String, Object>() {
            {
                put("request", request);
            }
        };
        Map<String, Object> result = new FlowRunner(context).execute();

        // 通过执行后的context查找真正的EndNode节点
        for (Map.Entry<String, Object> entry : result.entrySet()) {
            if (entry.getValue() instanceof NodeContext) {
                NodeContext nodeContext = (NodeContext) entry.getValue();
                if ("EndNode".equals(nodeContext.getNodeType()) && "completed".equals(nodeContext.getStatus())) {
                    AIPFlowDefinition.Cell endNodeCell = nodeMap.get(nodeContext.getNodeId());
                    if (endNodeCell != null) {
                        // 更新currentNode为真正的结束节点
                        this.currentNode = endNodeCell;
                        this.realEndNode = endNodeCell;
                        break;
                    }
                }
            }
        }

        this.executionContext = result;
        return result;
    }

    /**
     * 执行流程
     * @param context 上下文
     */
    public void executeFlow(Map<String, Object> context) {
        new FlowRunner(context).execute();
    }

    /**
     * 获取节点上下文
     * @param context 执行上下文
     * @param nodeId 节点ID
     * @return 节点上下文对象
     */
    public NodeContext getNodeContext(Map<String, Object> context, String nodeId) {
        Object nodeContextObj = context.get(nodeId);
        if (nodeContextObj instanceof NodeContext) {
            return (NodeContext) nodeContextObj;
        }
        return null;
    }

    /**
     * 获取flowExecutorPool
     * @return
     */
    private ThreadPoolExecutor getThreadPoolExecutor() {
        return SpringContextUtils.getBean("flowExecutorPool", ThreadPoolExecutor.class);
    }

    /**
     * 流程执行器
     * <AUTHOR>
     * @date 2025-03-05
     */
    public class FlowRunner {
        private final Logger logger = LoggerFactory.getLogger(FlowRunner.class);
        // 节点执行器
        private NodeRunner nodeRunner;
        // 上下文
        public volatile Map<String, Object> context;
        // 设置线程通知状态[true:已通知/false:未通知]
        public boolean notified = false;

        public FlowRunner(Map<String, Object> context) {
            this.context = context;
        }

        public Map<String, Object> execute(){
            // 构建节点执行器
            nodeRunner = new NodeRunner(currentNode.getId(), this);
            // 开始执行
            getThreadPoolExecutor().execute(nodeRunner);
            // 等待执行完毕
            wait(nodeRunner);
            return context;
        }

        /**
         * 线程等待
         */
        private void wait(NodeRunner nodeRunner){
            synchronized (nodeRunner){
                if(!notified){
                    try {
                        if(logger.isDebugEnabled())
                            logger.debug("当前线程将等待流程[{}]执行完毕.", this.hashCode());
                        nodeRunner.wait(); // 当前线程等待
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        }

        /**
         * 通知唤醒等待的线程
         */
        private void notifyAll_(){
            synchronized (nodeRunner){
                if(logger.isDebugEnabled())
                    logger.debug("流程[{}]执行完毕,等待的线程将被唤醒.", this.hashCode());
                notified = true; // 设置已通知状态
                nodeRunner.notifyAll(); // 通知唤醒等待的线程
            }
        }
    }

    /**
     * Node节点执行器(所有流程必须遵守仅有一个输入、输出节点)
     * 1. 执行当前节点
     * 2. 唤醒下个节点（唤醒条件:下个节点中前置节点列表中的所有节点执行完毕）
     * <AUTHOR>
     * @date 2025-03-05
     */
    @AllArgsConstructor
    public class NodeRunner implements Runnable{
        private final Logger logger = LoggerFactory.getLogger(NodeRunner.class);
        private String id; // 节点ID
        private FlowRunner flowRunner; // 流程执行器

        @Override
        public void run() {
            try{
                // 获取指定节点的流程定义
                AIPFlowDefinition.Cell cell = nodeMap.get(id);
                // 开始节点处理: 判断是否开始节点记录流程执行状态与开始时间(约定一个流程只有一个开始节点)
                if(StartNode.code.equals(cell.getShape())){
                    flowRunner.context.put("flowStartTime", System.currentTimeMillis()); // 流程开始执行时间
                    flowRunner.context.put("flowStatus", "running"); // 流程状态
                }
                // 执行当前节点
                createNodeExecutor(cell).execute(flowRunner.context);

                // 节点执行完成后，发送SSE事件
                String sessionId = getSessionIdFromContext(flowRunner.context);
                NodeContext nodeContext = getNodeContext(flowRunner.context, id);
                sendNodeExecutionEvent(sessionId, nodeContext);

                // 获取后置节点列表
                List<String> nextNodeIds = getNextNodeIds(cell);
                for(String node : nextNodeIds){
                    AIPFlowDefinition.Cell cell_ = nodeMap.get(node); // 获取节点流程定义
                    wakeNode(cell_); // 唤醒节点
                }
                // 结束节点处理: 判断是否结束节点并记录结束时间和执行时长(约定一个流程只有一个结束节点)
                if(EndNode.code.equals(cell.getShape())){
                    long endTime = System.currentTimeMillis();
                    long duration = endTime - (Long) flowRunner.context.get("flowStartTime");
                    flowRunner.context.put("flowEndTime", endTime);
                    flowRunner.context.put("flowDuration", duration);
                }
                // 后置处理: 节点没有后置节点时被认为流程执行完毕(例如:循环体中没有开始、结束节点)
                if(CollUtil.isEmpty(nextNodeIds) && isCompleted()){
                    // 通知等待的线程继续执行
                    flowRunner.notifyAll_();
                }
            }catch (Exception e){
                log.error("节点执行失败", e);

                // 发送执行失败的SSE事件
                try {
                    NodeContext nodeContext = new NodeContext();
                    nodeContext.setNodeId(id);
                    nodeContext.setNodeType(nodeMap.get(id).getShape());
                    nodeContext.setStartTime(System.currentTimeMillis());
                    nodeContext.setEndTime(System.currentTimeMillis());
                    nodeContext.setStatus("failed");
                    nodeContext.setErrorMessage(e.getMessage());
                    flowRunner.context.put(id, nodeContext);

                    String sessionId = getSessionIdFromContext(flowRunner.context);
                    sendNodeExecutionEvent(sessionId, nodeContext);
                } catch (Exception ex) {
                    logger.error("发送节点执行失败事件出错", ex);
                }

                // 通知等待的线程继续执行
                flowRunner.notifyAll_();
            }
        }

        /**
         * 获取后置节点列表
         * @param cell 当前节点
         * @return
         */
        private List<String> getNextNodeIds(AIPFlowDefinition.Cell cell) {
            if(IFELSENode.code.equals(cell.getShape())){ // 选择器节点处理
                // 获取当前节点(选择器节点)上下文
                NodeContext nodeContext = getNodeContext(flowRunner.context, cell.getId());
                // 获取元数据
                Map<String, Object> metadata = nodeContext.getMetadata();
                if(CollUtil.isNotEmpty(metadata)){
                    // 获取下个节点ID
                    String nextNode = (String)metadata.get("nextNode");
                    if(StrUtil.isNotEmpty(nextNode)) return Arrays.asList(nextNode);
                }
                throw new RuntimeException(String.format("流程执行失败: [%s-%s]未输出下个节点.",
                        IFELSENode.code, cell.getId()));
            }else{
                // 其它节点获取下个节点列表
                return cell.getNextNodeIds();
            }
        }

        /**
         * 唤醒节点
         * @param cell
         * @return
         */
        private void wakeNode(AIPFlowDefinition.Cell cell){
            // 执行
            getThreadPoolExecutor().execute(new NodeRunner(cell.getId(), flowRunner));
        }

        /**
         * 判断流程是否完成
         * @return
         */
        private boolean isCompleted(){
            for(String node : nodeMap.keySet()){
                Object obj = flowRunner.context.get(node);
                if(ObjUtil.isNotNull(obj)){
                    NodeContext context = (NodeContext)obj;
                    if("running".equals(context.getStatus())){
                        return false;
                    }
                }
            }
            return true;
        }

    }

    /**
     * 检测循环引用
     * @param obj 要检查的对象
     * @param path 当前对象路径
     * @return 是否存在循环引用
     */
    private boolean isCircularReference(Object obj, List<Object> path) {
        if (obj == null) return false;
        
        // 如果当前对象已经在路径中，说明存在循环引用
        if (path.contains(obj)) {
            return true;
        }
        
        // 如果是JSONObject，检查其所有值
        if (obj instanceof JSONObject) {
            JSONObject jsonObj = (JSONObject) obj;
            for (Object value : jsonObj.values()) {
                path.add(obj);
                if (isCircularReference(value, path)) {
                    return true;
                }
                path.remove(path.size() - 1);
            }
        }
        
        // 如果是数组，检查每个元素
        if (obj instanceof List) {
            List<?> list = (List<?>) obj;
            for (Object item : list) {
                path.add(obj);
                if (isCircularReference(item, path)) {
                    return true;
                }
                path.remove(path.size() - 1);
            }
        }
        
        return false;
    }

    /**
     * 从上下文中获取会话ID
     * @param context 执行上下文
     * @return 会话ID，如果不存在则返回null
     */
    private String getSessionIdFromContext(Map<String, Object> context) {
        // 直接从上下文中获取
        if (context.containsKey("aip-session")) {
            return context.get("aip-session").toString();
        }

        // 从请求对象中获取headers
        Object request = context.get("request");
        if (request instanceof RuntimeRequest) {
            Map<String, String> headers = ((RuntimeRequest) request).getHeaders();
            if (headers != null && headers.containsKey("aip-session")) {
                return headers.get("aip-session");
            }
        }

        return null;
    }

    /**
     * 发送节点执行事件
     * @param sessionId 会话ID
     * @param nodeContext 节点上下文
     */
    private void sendNodeExecutionEvent(String sessionId, NodeContext nodeContext) {
        if (sessionId == null || nodeContext == null) {
            return;
        }

        try {
            SSEUtils.sendTextMessage(
                    null,
                    sessionId,
                    "node_execution",
                    nodeContext,
                    "success"
            );
        } catch (Exception e) {
            log.error("sessionId:{},nodeContext:{},发送节点执行结果失败: {}", sessionId, StrUtil.toString(nodeContext), e.getMessage());
        }
    }
}