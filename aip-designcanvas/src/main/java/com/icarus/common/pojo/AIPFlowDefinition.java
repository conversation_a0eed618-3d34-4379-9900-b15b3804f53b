package com.icarus.common.pojo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

@Data
public class AIPFlowDefinition {
    private List<Cell> cells;
    private String mode;
    private String id;


    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Cell {
        private Position position;
        private String shape;
        private CellData data;
        private Ports ports;
        private String id;
        private Integer zIndex;
        private Source source;
        private Target target;
        private Attrs attrs;
        protected List<String> nextNodeIds;
        protected List<String> prevNodeIds;
    }

    @Data
    public static class Position {
        private Integer x;
        private Integer y;
    }

    @Data
    public static class CellData {
        private String type;
        // add by ksw 20250318 节点提供者[内置节点此值为空/扩展节点为热部署插件ID(plugin-id)]
        private String provider;
        private Object inputs;
        private Object outputs;
        private Object properties;
        private PortsConfig ports;
        private String id;
        private String title;
        private String icon;
        private String description;
        private List<Component> components;
        private Boolean showButton;
    }

    @Data
    public static class InputOutput {
        private String type;
        private List<Header> headers;
        private List<Object> file;
        private List<BodyItem> body;
    }

    @Data
    public static class Header {
        private String name;
        private String type;
        private Boolean expanded;
        private List<Object> children;
    }

    @Data
    public static class BodyItem {
        private String name;
        private String type;
        private Boolean expanded;
        private List<ChildItem> children;
    }

    @Data
    public static class ChildItem {
        private String name;
        private String type;
        private Boolean expanded;
        private List<Object> children;
    }

/*    @Data
    public static class Properties {
        private String model;
        private String systemPrompt;
    }*/

    @Data
    public static class PortsConfig {
        private Boolean left;
        private Boolean right;
        private Boolean top;
        private Boolean bottom;
    }

    @Data
    public static class Component {
        private String type;
        private String field;
        private String label;
        private ComponentProps props;
        private Object showObj;
    }

    @Data
    public static class ComponentProps {
        private List<Option> options;
        private Object defaultValue;
        private String placeholder;
        private String accept;
        private Integer rows;
    }

    @Data
    public static class Option {
        private String label;
        private String value;
    }

    @Data
    public static class Ports {
        private Groups groups;
        private List<PortItem> items;
    }

    @Data
    public static class Groups {
        private GroupConfig top;
        private GroupConfig right;
        private GroupConfig bottom;
        private GroupConfig left;
    }

    @Data
    public static class GroupConfig {
        private String position;
        private GroupAttrs attrs;
    }

    @Data
    public static class GroupAttrs {
        private Circle circle;
    }

    @Data
    public static class Circle {
        private Integer r;
        private Boolean magnet;
        private String stroke;
        private String fill;
        private Integer strokeWidth;
        private String visibility;
    }

    @Data
    public static class PortItem {
        private String group;
        private PortAttrs attrs;
        private String id;
    }

    @Data
    public static class PortAttrs {
        private Circle circle;
    }

    @Data
    public static class Source {
        private String cell;
        private String port;
    }

    @Data
    public static class Target {
        private String cell;
        private String port;
    }

    @Data
    public static class Attrs {
        private Line line;
    }

    @Data
    public static class Line {
        private TargetMarker targetMarker;
        private String strokeDasharray;
    }

    @Data
    public static class TargetMarker {
        private Integer width;
        private Integer height;
    }

    @Data
    public static class OutputDefinition {
        private List<OutputItem> outputs;
    }

    @Data
    public static class OutputItem {
        private String name;
        private String value;
        private String type;
        private Boolean expanded;
        private List<Object> children;
    }

    public Object getOutputs(CellData cellData) {
        if (cellData.getOutputs() instanceof List) {
            return (List<OutputItem>) cellData.getOutputs();
        } else {
            return (OutputDefinition) cellData.getOutputs();
        }
    }


}