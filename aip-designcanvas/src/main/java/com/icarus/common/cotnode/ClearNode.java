package com.icarus.common.cotnode;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.icarus.common.runtime.NodeContext;
import com.icarus.sdk.springboot.base.utils.SpringContextUtils;
import com.icarus.service.FileStorageService;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.util.StringUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.text.SimpleDateFormat;
import java.util.stream.Collectors;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import org.w3c.dom.Document;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

/**
 * 数据清洗节点
 */
@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class ClearNode extends BaseNode {

    private Object result;

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【数据清洗】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
        File sourceFile = null;

        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            nodeContext.setInputs(new JSONObject(resolvedInputs));

            // 解析属性配置
            Properties props = resolveProperties(context, Properties.class);

            // 记录配置到上下文
            JSONObject propertiesJson = new JSONObject();
            propertiesJson.set("dataPreview", props.getDataPreview());
            propertiesJson.set("cleanSteps", props.getCleanSteps());
            propertiesJson.set("inputData", props.getInputData());
            nodeContext.setProperties(propertiesJson);

            // 获取并解析文件
            String filePath = props.getDataPreview() != null ? props.getDataPreview().getFilePath() : null;
            if (StringUtils.hasText(filePath)) {
                // 获取文件对象
                sourceFile = getFileFromPath(filePath);
                if (sourceFile == null) {
                    throw new IllegalArgumentException("无法获取文件: " + filePath);
                }

                // 根据文件类型解析数据
                Object data = parseFileData(sourceFile, props.getDataPreview());

                // 执行清洗步骤
                Object cleanedData = data;
                CleanStep step = props.getCleanSteps();
                cleanedData = executeCleanStep(cleanedData, step);

                result = cleanedData.toString();
                nodeContext.setStatus("completed");

            } else {
                // 优先从属性中获取输入数据
                String inputData = props.getInputData();

                // 如果属性中没有，则尝试从输入参数中获取
                if (inputData == null || inputData.trim().isEmpty()) {
                    inputData = (String) resolvedInputs.get("");
                }

                if (inputData == null) {
                    throw new IllegalArgumentException("未提供输入数据，请通过inputData属性或输入参数提供");
                }

                // 执行清洗步骤
                Object cleanedData = inputData;
                CleanStep step = props.getCleanSteps();
                cleanedData = executeCleanStep(cleanedData, step);

                result = cleanedData;
                nodeContext.setStatus("completed");
            }

            processOutputs(context);

        } catch (Exception e) {
            log.error("数据清洗节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            // 清理临时文件
            if (sourceFile != null && sourceFile.getName().startsWith("temp_")) {
                sourceFile.delete();
            }
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        return context;
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    /**
     * 执行清洗步骤
     */
    private Object executeCleanStep(Object data, CleanStep step) {
        switch (step.getType()) {
            case "BASIC":
                return executeBasicClean(data, step);
            case "CONVERT":
                return executeDataConvert(data, step);
            case "VALIDATE":
                return executeDataValidate(data, step);
            case "ADVANCED":
                return executeAdvancedProcessing(data, step);
            default:
                throw new IllegalArgumentException("不支持的清洗类型: " + step.getType());
        }
    }

    /**
     * 执行基础清洗
     */
    private Object executeBasicClean(Object data, CleanStep step) {
        // 如果是单个字符串，转换为List处理后再转回字符串
        boolean isSingleString = false;
        if (data instanceof String) {
            isSingleString = true;
            data = Collections.singletonList(data);
        }

        Object result;
        switch (step.getOperation()) {
            case "REMOVE_SPACE":
                result = removeSpace(data, step.getConfig());
                break;
            case "REMOVE_DUPLICATE":
                result = removeDuplicate(data, step.getConfig());
                break;
            case "REPLACE_VALUE":
                result = replaceValue(data, step.getConfig());
                break;
            case "TRIM_WHITESPACE":
                result = trimWhitespace(data, step.getConfig());
                break;
            default:
                throw new IllegalArgumentException("不支持的基础清洗操作: " + step.getOperation());
        }

        // 如果原始输入是单个字符串，则返回处理后的第一个元素
        if (isSingleString && result instanceof List && !((List<?>) result).isEmpty()) {
            return ((List<?>) result).get(0);
        }

        return result;
    }

    /**
     * 执行数据转换
     */
    private Object executeDataConvert(Object data, CleanStep step) {
        // 如果是单个字符串，转换为List处理后再转回字符串
        boolean isSingleString = false;
        if (data instanceof String) {
            isSingleString = true;
            data = Collections.singletonList(data);
        }

        Object result;
        switch (step.getOperation()) {
            case "TYPE_CONVERT":
                result = executeTypeConvert(data, step.getConfig());
                break;
            case "DATE_FORMAT":
                result = executeDateProcess(data, step.getConfig());
                break;
            case "DATA_NORMALIZE":
                result = executeNumberProcess(data, step.getConfig());
                break;
            case "STRING_PROCESS":
                result = executeStringProcess(data, step.getConfig());
                break;
            default:
                throw new IllegalArgumentException("不支持的数据转换操作: " + step.getOperation());
        }

        // 如果原始输入是单个字符串，则返回处理后的第一个元素
        if (isSingleString && result instanceof List && !((List<?>) result).isEmpty()) {
            return ((List<?>) result).get(0);
        }

        return result;
    }

    /**
     * 执行数据验证
     */
    private Object executeDataValidate(Object data, CleanStep step) {
        // 如果是单个字符串，转换为List处理后再转回字符串
        boolean isSingleString = false;
        if (data instanceof String) {
            isSingleString = true;
            data = Collections.singletonList(data);
        }

        Object result;
        switch (step.getOperation()) {
            case "NOT_NULL":
                result = validateNotNull(data, step.getConfig());
                break;
            case "RANGE_CHECK":
                result = validateRange(data, step.getConfig());
                break;
            case "FORMAT_CHECK":
                result = validateFormat(data, step.getConfig());
                break;
            case "CUSTOM_RULE":
                result = validateCustom(data, step.getConfig());
                break;
            default:
                throw new IllegalArgumentException("不支持的数据验证操作: " + step.getOperation());
        }

        // 如果原始输入是单个字符串，则返回处理后的第一个元素
        if (isSingleString && result instanceof List && !((List<?>) result).isEmpty()) {
            return ((List<?>) result).get(0);
        }

        return result;
    }

    /**
     * 执行高级处理
     */
    private Object executeAdvancedProcessing(Object data, CleanStep step) {
        // 如果是单个字符串，转换为List处理后再转回字符串
        boolean isSingleString = false;
        if (data instanceof String) {
            isSingleString = true;
            data = Collections.singletonList(data);
        }

        Object result;
        switch (step.getOperation()) {
            case "REGEX_PATTERN":
                result = applyRegexPattern(data, step.getConfig());
                break;
            case "CUSTOM_FUNCTION":
                result = applyCustomFunction(data, step.getConfig());
                break;
            case "CONDITION_FILTER":
                result = applyConditionFilter(data, step.getConfig());
                break;
            case "DATA_AGGREGATION":
                result = applyDataAggregation(data, step.getConfig());
                break;
            default:
                throw new IllegalArgumentException("不支持的高级处理操作: " + step.getOperation());
        }

        // 如果原始输入是单个字符串，则返回处理后的第一个元素
        // 对于过滤操作，可能会返回空列表，此时返回原始字符串
        if (isSingleString && result instanceof List) {
            List<?> resultList = (List<?>) result;
            return !resultList.isEmpty() ? resultList.get(0) : "";
        }

        return result;
    }

    /**
     * 去除空值
     */
    private Object removeSpace(Object data, CleanConfig config) {
        // 直接处理单个字符串
        if (data instanceof String) {
            return processSpaces((String) data, config.getTrimType());
        } else if (data instanceof List) {
            List<?> list = (List<?>) data;

            // 如果配置了删除空值
            if (Boolean.TRUE.equals(config.getRemoveEmpty())) {
                return list.stream()
                        .filter(row -> {
                            if (row == null)
                                return false;
                            if (row instanceof String)
                                return !((String) row).isEmpty();
                            if (row instanceof String[]) {
                                String[] arr = (String[]) row;
                                // 如果配置了删除全空行，则检查是否所有元素都为空
                                if (Boolean.TRUE.equals(config.getRemoveEmptyRows())) {
                                    for (String s : arr) {
                                        if (s != null && !s.trim().isEmpty())
                                            return true;
                                    }
                                    return false;
                                }
                                return true;
                            }
                            return true;
                        })
                        .map(row -> {
                            if (row instanceof String[]) {
                                String[] original = (String[]) row;
                                String[] arr = Arrays.copyOf(original, original.length);
                                for (int i = 0; i < arr.length; i++) {
                                    arr[i] = processSpaces(arr[i], config.getTrimType());
                                }
                                return arr;
                            } else if (row instanceof String) {
                                return processSpaces((String) row, config.getTrimType());
                            }
                            return row;
                        }).collect(Collectors.toList());
            } else {
                // 原有的空格处理逻辑
                return list.stream().map(row -> {
                    if (row instanceof String[]) {
                        String[] original = (String[]) row;
                        String[] arr = Arrays.copyOf(original, original.length);
                        for (int i = 0; i < arr.length; i++) {
                            arr[i] = processSpaces(arr[i], config.getTrimType());
                        }
                        return arr;
                    } else if (row instanceof String) {
                        return processSpaces((String) row, config.getTrimType());
                    }
                    return row;
                }).collect(Collectors.toList());
            }
        }
        return data;
    }

    /**
     * 处理空格
     */
    private String processSpaces(String value, String trimType) {
        if (value == null)
            return null;
        switch (trimType) {
            case "all":
                return value.replaceAll("\\s+", "");
            case "left":
                return value.replaceAll("^\\s+", "");
            case "right":
                return value.replaceAll("\\s+$", "");
            case "both":
                return value.trim();
            default:
                return value;
        }
    }

    /**
     * 去除重复行
     */
    private Object removeDuplicate(Object data, CleanConfig config) {
        if (data instanceof List) {
            List<?> list = (List<?>) data;
            boolean keepFirst = Boolean.TRUE.equals(config.getKeepFirst());

            // 全局去重
            if (keepFirst) {
                // 保留第一个出现的数据
                return list.stream()
                        .map(this::convertToComparable)
                        .distinct()
                        .collect(Collectors.toList());
            } else {
                // 保留最后一个出现的数据
                List<Object> result = new ArrayList<>();
                Set<String> seen = new HashSet<>();

                // 从后向前遍历以保留最后出现的数据
                for (int i = list.size() - 1; i >= 0; i--) {
                    Object item = list.get(i);
                    String key = convertToComparable(item);
                    if (seen.add(key)) {
                        result.add(0, item);
                    }
                }
                return result;
            }
        }
        return data;
    }

    /**
     * 将数据转换为可比较的字符串形式
     */
    private String convertToComparable(Object item) {
        if (item == null) {
            return "";
        }
        if (item instanceof String[]) {
            // 数组类型，将所有元素连接成字符串
            return String.join("|", (String[]) item);
        }
        // 其他类型直接转换为字符串
        return item.toString();
    }

    /**
     * 替换值
     */
    private Object replaceValue(Object data, CleanConfig config) {
        if (data instanceof List) {
            List<?> list = (List<?>) data;
            return list.stream().map(row -> {
                if (row instanceof String[]) {
                    String[] original = (String[]) row;
                    String[] arr = Arrays.copyOf(original, original.length);
                    for (int i = 0; i < arr.length; i++) {
                        arr[i] = replaceValueInString(arr[i], config);
                    }
                    return arr;
                } else if (row instanceof String) {
                    return replaceValueInString((String) row, config);
                }
                return row;
            }).collect(Collectors.toList());
        }
        return data;
    }

    /**
     * 在字符串中替换值
     */
    private String replaceValueInString(String value, CleanConfig config) {
        if (value == null)
            return null;
        String oldValue = config.getOldValue();
        String newValue = config.getNewValue();
        if (oldValue == null || newValue == null)
            return value;

        // 支持正则表达式替换
        if (oldValue.startsWith("regex:")) {
            String regex = oldValue.substring(6);
            return value.replaceAll(regex, newValue);
        }
        return value.replace(oldValue, newValue);
    }

    /**
     * 去除空白
     */
    private Object trimWhitespace(Object data, CleanConfig config) {
        if (data instanceof List) {
            List<?> list = (List<?>) data;
            return list.stream().map(row -> {
                if (row instanceof String[]) {
                    String[] original = (String[]) row;
                    String[] arr = Arrays.copyOf(original, original.length);
                    for (int i = 0; i < arr.length; i++) {
                        if (arr[i] != null) {
                            switch (config.getTrimType()) {
                                case "all":
                                    arr[i] = arr[i].replaceAll("\\s+", " ").trim();
                                    break;
                                case "collapse":
                                    arr[i] = arr[i].replaceAll("\\s+", " ");
                                    break;
                                case "both":
                                default:
                                    arr[i] = arr[i].trim();
                                    break;
                            }
                        }
                    }
                    return arr;
                } else if (row instanceof String) {
                    String str = (String) row;
                    if (str != null) {
                        switch (config.getTrimType()) {
                            case "all":
                                return str.replaceAll("\\s+", " ").trim();
                            case "collapse":
                                return str.replaceAll("\\s+", " ");
                            case "both":
                            default:
                                return str.trim();
                        }
                    }
                }
                return row;
            }).collect(Collectors.toList());
        }
        return data;
    }

    /**
     * 类型转换
     */
    private Object executeTypeConvert(Object data, CleanConfig config) {
        if (data instanceof List) {
            List<?> list = (List<?>) data;
            return list.stream().map(row -> {
                if (row instanceof String[]) {
                    String[] original = (String[]) row;
                    Object[] result = new Object[original.length];
                    for (int i = 0; i < original.length; i++) {
                        result[i] = convertSingleValue(original[i], config);
                    }
                    return result;
                } else if (row instanceof String) {
                    return convertSingleValue((String) row, config);
                }
                return row;
            }).collect(Collectors.toList());
        } else if (data instanceof String) {
            return convertSingleValue((String) data, config);
        }
        return data;
    }

    /**
     * 单值类型转换
     */
    private Object convertSingleValue(String value, CleanConfig config) {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }

        String targetType = config.getTargetType();
        if (targetType == null) {
            return value;
        }

        try {
            switch (targetType.toLowerCase()) {
                case "number":
                case "double":
                    return Double.parseDouble(value.trim());
                case "integer":
                case "int":
                    return Integer.parseInt(value.trim());
                case "boolean":
                    return Boolean.parseBoolean(value.trim()) ||
                            "1".equals(value.trim()) ||
                            "yes".equalsIgnoreCase(value.trim()) ||
                            "true".equalsIgnoreCase(value.trim());
                case "date":
                    // 日期转换在日期处理函数中更详细处理
                    return value;
                default:
                    return value;
            }
        } catch (Exception e) {
            log.warn("类型转换失败: {} -> {}, 错误: {}", value, targetType, e.getMessage());
            return value; // 转换失败返回原值
        }
    }

    /**
     * 日期处理
     */
    private Object executeDateProcess(Object data, CleanConfig config) {
        if (data instanceof List) {
            List<?> list = (List<?>) data;
            return list.stream().map(row -> {
                if (row instanceof String[]) {
                    String[] original = (String[]) row;
                    String[] result = new String[original.length];
                    for (int i = 0; i < original.length; i++) {
                        result[i] = formatDate(original[i], config);
                    }
                    return result;
                } else if (row instanceof String) {
                    return formatDate((String) row, config);
                }
                return row;
            }).collect(Collectors.toList());
        } else if (data instanceof String) {
            return formatDate((String) data, config);
        }
        return data;
    }

    /**
     * 格式化日期
     */
    private String formatDate(String value, CleanConfig config) {
        if (value == null || value.trim().isEmpty()) {
            return value;
        }

        String sourceFormat = config.getSourceFormat();
        String targetFormat = config.getTargetFormat();

        if (sourceFormat == null || targetFormat == null) {
            return value;
        }

        try {
            SimpleDateFormat sourceSdf = new SimpleDateFormat(sourceFormat);
            SimpleDateFormat targetSdf = new SimpleDateFormat(targetFormat);
            Date date = sourceSdf.parse(value.trim());
            return targetSdf.format(date);
        } catch (Exception e) {
            log.warn("日期格式转换失败: {} ({}->{}), 错误: {}",
                    value, sourceFormat, targetFormat, e.getMessage());
            return value; // 转换失败返回原值
        }
    }

    /**
     * 数值处理（包括归一化）
     */
    private Object executeNumberProcess(Object data, CleanConfig config) {
        if (data instanceof List) {
            List<?> list = (List<?>) data;
            return list.stream().map(row -> {
                if (row instanceof String[]) {
                    Object[] original = (Object[]) row;
                    Object[] result = new Object[original.length];
                    for (int i = 0; i < original.length; i++) {
                        result[i] = processNumber(original[i], config);
                    }
                    return result;
                } else {
                    return processNumber(row, config);
                }
            }).collect(Collectors.toList());
        } else {
            return processNumber(data, config);
        }
    }

    /**
     * 处理单个数值
     */
    private Object processNumber(Object value, CleanConfig config) {
        if (value == null) {
            return null;
        }

        // 尝试转换为数值
        Double numValue;
        if (value instanceof Number) {
            numValue = ((Number) value).doubleValue();
        } else {
            try {
                numValue = Double.parseDouble(value.toString().trim());
            } catch (NumberFormatException e) {
                return value; // 不是数字，返回原值
            }
        }

        // 处理单位转换
        if (config.getSourceUnit() != null && config.getTargetUnit() != null) {
            numValue = convertUnit(numValue, config.getSourceUnit(), config.getTargetUnit());
        }

        // 处理归一化
        if (config.getMinValue() != null && config.getMaxValue() != null) {
            numValue = normalize(numValue, config.getMinValue(), config.getMaxValue());
        }

        // 处理小数位数
        if (config.getDecimalPlaces() != null) {
            double factor = Math.pow(10, config.getDecimalPlaces());
            numValue = Math.round(numValue * factor) / factor;
        }

        return numValue;
    }

    /**
     * 单位转换
     */
    private Double convertUnit(Double value, String sourceUnit, String targetUnit) {
        // 实现常见单位转换，这里只是示例
        // 实际应用中可能需要更复杂的单位转换系统

        // 长度单位示例
        if ("mm".equals(sourceUnit) && "cm".equals(targetUnit)) {
            return value / 10.0;
        } else if ("cm".equals(sourceUnit) && "mm".equals(targetUnit)) {
            return value * 10.0;
        } else if ("cm".equals(sourceUnit) && "m".equals(targetUnit)) {
            return value / 100.0;
        } else if ("m".equals(sourceUnit) && "cm".equals(targetUnit)) {
            return value * 100.0;
        }

        // 重量单位示例
        else if ("g".equals(sourceUnit) && "kg".equals(targetUnit)) {
            return value / 1000.0;
        } else if ("kg".equals(sourceUnit) && "g".equals(targetUnit)) {
            return value * 1000.0;
        }

        // 温度单位示例
        else if ("C".equals(sourceUnit) && "F".equals(targetUnit)) {
            return value * 9 / 5.0 + 32;
        } else if ("F".equals(sourceUnit) && "C".equals(targetUnit)) {
            return (value - 32) * 5 / 9.0;
        }

        // 默认不转换
        return value;
    }

    /**
     * 归一化数值到[0,1]范围
     */
    private Double normalize(Double value, Double min, Double max) {
        if (max.equals(min)) {
            return 0.0; // 避免除以零
        }
        return (value - min) / (max - min);
    }

    /**
     * 字符串处理
     */
    private Object executeStringProcess(Object data, CleanConfig config) {
        if (data instanceof List) {
            List<?> list = (List<?>) data;
            return list.stream().map(row -> {
                if (row instanceof String[]) {
                    String[] original = (String[]) row;
                    String[] result = new String[original.length];
                    for (int i = 0; i < original.length; i++) {
                        result[i] = processString(original[i], config);
                    }
                    return result;
                } else if (row instanceof String) {
                    return processString((String) row, config);
                }
                return row;
            }).collect(Collectors.toList());
        } else if (data instanceof String) {
            return processString((String) data, config);
        }
        return data;
    }

    /**
     * 处理单个字符串
     */
    private String processString(String value, CleanConfig config) {
        if (value == null) {
            return null;
        }

        String result = value;

        // 大小写转换
        if ("upper".equals(config.getCaseConversion())) {
            result = result.toUpperCase();
        } else if ("lower".equals(config.getCaseConversion())) {
            result = result.toLowerCase();
        } else if ("capitalize".equals(config.getCaseConversion())) {
            if (!result.isEmpty()) {
                result = Character.toUpperCase(result.charAt(0)) +
                        (result.length() > 1 ? result.substring(1).toLowerCase() : "");
            }
        }

        // 截取字符串
        if (config.getStartIndex() != null && config.getEndIndex() != null) {
            int start = Math.max(0, config.getStartIndex());
            int end = Math.min(result.length(), config.getEndIndex());
            if (start < end) {
                result = result.substring(start, end);
            }
        }

        // 填充字符串
        if (config.getPadChar() != null && config.getPadLength() != null) {
            if ("left".equals(config.getPadDirection())) {
                result = String.format("%" + config.getPadLength() + "s", result)
                        .replace(' ', config.getPadChar().charAt(0));
            } else if ("right".equals(config.getPadDirection())) {
                result = String.format("%-" + config.getPadLength() + "s", result)
                        .replace(' ', config.getPadChar().charAt(0));
            }
        }

        return result;
    }

    /**
     * 应用正则表达式
     */
    private Object applyRegexPattern(Object data, CleanConfig config) {
        if (data instanceof List) {
            List<?> list = (List<?>) data;
            return list.stream().map(row -> {
                if (row instanceof String[]) {
                    String[] original = (String[]) row;
                    String[] result = new String[original.length];
                    for (int i = 0; i < original.length; i++) {
                        result[i] = applyRegexToString(original[i], config);
                    }
                    return result;
                } else if (row instanceof String) {
                    return applyRegexToString((String) row, config);
                }
                return row;
            }).collect(Collectors.toList());
        } else if (data instanceof String) {
            return applyRegexToString((String) data, config);
        }
        return data;
    }

    /**
     * 对字符串应用正则表达式
     */
    private String applyRegexToString(String value, CleanConfig config) {
        if (value == null || config.getPattern() == null) {
            return value;
        }

        String pattern = config.getPattern();
        String replacement = config.getNewValue();

        if ("extract".equals(config.getRegexMode())) {
            // 提取模式
            java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
            java.util.regex.Matcher m = p.matcher(value);
            if (m.find()) {
                return m.group(config.getRegexGroup() != null ? config.getRegexGroup() : 0);
            }
            return "";
        } else if ("match".equals(config.getRegexMode())) {
            // 匹配模式
            return String.valueOf(value.matches(pattern));
        } else {
            // 替换模式
            return value.replaceAll(pattern, replacement != null ? replacement : "");
        }
    }

    /**
     * 应用自定义函数
     */
    private Object applyCustomFunction(Object data, CleanConfig config) {
        if (data instanceof List) {
            List<?> list = (List<?>) data;
            return list.stream().map(row -> {
                if (row instanceof String[]) {
                    Object[] original = (Object[]) row;
                    Object[] result = new Object[original.length];
                    for (int i = 0; i < original.length; i++) {
                        result[i] = evaluateCustomFunction(original[i], config);
                    }
                    return result;
                } else {
                    return evaluateCustomFunction(row, config);
                }
            }).collect(Collectors.toList());
        } else {
            return evaluateCustomFunction(data, config);
        }
    }

    /**
     * 评估自定义函数
     */
    private Object evaluateCustomFunction(Object value, CleanConfig config) {
        // 这里实现简单的自定义函数，实际应用可能需要更复杂的表达式引擎
        String functionType = config.getFunctionType();
        if (functionType == null || value == null) {
            return value;
        }

        // 数学函数
        if ("abs".equals(functionType) && value instanceof Number) {
            return Math.abs(((Number) value).doubleValue());
        } else if ("sqrt".equals(functionType) && value instanceof Number) {
            return Math.sqrt(((Number) value).doubleValue());
        } else if ("round".equals(functionType) && value instanceof Number) {
            return Math.round(((Number) value).doubleValue());
        }

        // 字符串函数
        else if ("length".equals(functionType) && value instanceof String) {
            return ((String) value).length();
        } else if ("reverse".equals(functionType) && value instanceof String) {
            return new StringBuilder((String) value).reverse().toString();
        }

        // 默认返回原值
        return value;
    }

    /**
     * 应用条件过滤
     */
    private Object applyConditionFilter(Object data, CleanConfig config) {
        if (!(data instanceof List)) {
            return data;
        }

        List<?> list = (List<?>) data;
        return list.stream().filter(row -> evaluateCondition(row, config))
                .collect(Collectors.toList());
    }

    /**
     * 评估过滤条件
     */
    private boolean evaluateCondition(Object row, CleanConfig config) {
        // 获取条件参数
        String field = config.getField();
        String operator = config.getOperator();
        String value = config.getFilterValue();

        if (field == null || operator == null) {
            return true; // 没有条件，保留所有数据
        }

        // 获取字段值
        Object fieldValue;
        if (row instanceof String[]) {
            String[] arr = (String[]) row;
            try {
                int index = Integer.parseInt(field);
                if (index >= 0 && index < arr.length) {
                    fieldValue = arr[index];
                } else {
                    return true; // 索引越界，保留数据
                }
            } catch (NumberFormatException e) {
                return true; // 字段不是有效索引，保留数据
            }
        } else if (row instanceof Map) {
            fieldValue = ((Map<?, ?>) row).get(field);
        } else {
            fieldValue = row; // 单值数据
        }

        // 空值处理
        if (fieldValue == null) {
            return "isNull".equals(operator) ||
                    ("equals".equals(operator) && (value == null || "null".equals(value)));
        }

        // 比较操作
        String strValue = fieldValue.toString();
        switch (operator) {
            case "equals":
                return strValue.equals(value);
            case "notEquals":
                return !strValue.equals(value);
            case "contains":
                return strValue.contains(value);
            case "startsWith":
                return strValue.startsWith(value);
            case "endsWith":
                return strValue.endsWith(value);
            case "greaterThan":
                try {
                    return Double.parseDouble(strValue) > Double.parseDouble(value);
                } catch (NumberFormatException e) {
                    return false;
                }
            case "lessThan":
                try {
                    return Double.parseDouble(strValue) < Double.parseDouble(value);
                } catch (NumberFormatException e) {
                    return false;
                }
            case "isNull":
                return strValue.isEmpty();
            case "isNotNull":
                return !strValue.isEmpty();
            case "matches":
                return strValue.matches(value);
            default:
                return true;
        }
    }

    /**
     * 应用数据聚合
     */
    private Object applyDataAggregation(Object data, CleanConfig config) {
        if (!(data instanceof List)) {
            return data;
        }

        List<?> list = (List<?>) data;
        String aggregationType = config.getAggregationType();
        String groupByField = config.getGroupByField();

        if (aggregationType == null) {
            return data;
        }

        // 简单聚合（无分组）
        if (groupByField == null) {
            switch (aggregationType) {
                case "count":
                    return list.size();
                case "sum":
                    return list.stream()
                            .filter(item -> item instanceof Number)
                            .mapToDouble(item -> ((Number) item).doubleValue())
                            .sum();
                case "avg":
                    return list.stream()
                            .filter(item -> item instanceof Number)
                            .mapToDouble(item -> ((Number) item).doubleValue())
                            .average()
                            .orElse(0);
                case "max":
                    return list.stream()
                            .filter(item -> item instanceof Number)
                            .mapToDouble(item -> ((Number) item).doubleValue())
                            .max()
                            .orElse(0);
                case "min":
                    return list.stream()
                            .filter(item -> item instanceof Number)
                            .mapToDouble(item -> ((Number) item).doubleValue())
                            .min()
                            .orElse(0);
                default:
                    return data;
            }
        }

        // 分组聚合
        Map<Object, List<Object>> groups = new HashMap<>();

        // 分组
        for (Object item : list) {
            Object groupKey = extractGroupKey(item, groupByField);
            if (!groups.containsKey(groupKey)) {
                groups.put(groupKey, new ArrayList<>());
            }
            groups.get(groupKey).add(item);
        }

        // 对每个分组应用聚合
        Map<Object, Object> result = new HashMap<>();
        for (Map.Entry<Object, List<Object>> entry : groups.entrySet()) {
            Object groupKey = entry.getKey();
            List<Object> groupItems = entry.getValue();

            switch (aggregationType) {
                case "count":
                    result.put(groupKey, groupItems.size());
                    break;
                case "sum":
                    result.put(groupKey, groupItems.stream()
                            .filter(item -> item instanceof Number)
                            .mapToDouble(item -> ((Number) item).doubleValue())
                            .sum());
                    break;
                case "avg":
                    result.put(groupKey, groupItems.stream()
                            .filter(item -> item instanceof Number)
                            .mapToDouble(item -> ((Number) item).doubleValue())
                            .average()
                            .orElse(0));
                    break;
                case "max":
                    result.put(groupKey, groupItems.stream()
                            .filter(item -> item instanceof Number)
                            .mapToDouble(item -> ((Number) item).doubleValue())
                            .max()
                            .orElse(0));
                    break;
                case "min":
                    result.put(groupKey, groupItems.stream()
                            .filter(item -> item instanceof Number)
                            .mapToDouble(item -> ((Number) item).doubleValue())
                            .min()
                            .orElse(0));
                    break;
                default:
                    result.put(groupKey, groupItems);
            }
        }

        return result;
    }

    /**
     * 提取分组键
     */
    private Object extractGroupKey(Object item, String groupByField) {
        if (item instanceof String[]) {
            String[] arr = (String[]) item;
            try {
                int index = Integer.parseInt(groupByField);
                if (index >= 0 && index < arr.length) {
                    return arr[index];
                }
            } catch (NumberFormatException e) {
                // 忽略
            }
        } else if (item instanceof Map) {
            return ((Map<?, ?>) item).get(groupByField);
        }
        return "default"; // 默认分组
    }

    /**
     * 获取文件对象，优先使用 FileStorageService，失败则尝试直接文件路径
     */
    private File getFileFromPath(String filePath) {
        try {
            // 首先尝试通过 FileStorageService 获取
            FileStorageService service = SpringContextUtils.getBean(FileStorageService.class);
            InputStream inputStream = service.getFile(filePath);
            if (inputStream != null) {
                String fileName = "temp_" + filePath.substring(filePath.lastIndexOf("/") + 1);
                return com.icarus.utils.FileUtil.inputStreamToFile(inputStream, fileName);
            }
        } catch (Exception e) {
            log.debug("通过 FileStorageService 获取文件失败，尝试本地路径");
        }

        // 直接返回本地文件对象
        return new File(filePath);
    }

    /**
     * 根据文件类型解析数据
     */
    private Object parseFileData(File file, DataPreview preview) throws Exception {
        switch (preview.getDataType()) {
            case "CSV":
                return parseCsvFile(file, preview);
            case "JSON":
                return parseJsonFile(file, preview);
            case "XML":
                return parseXmlFile(file, preview);
            case "TEXT":
                return parseTextFile(file, preview);
            default:
                throw new IllegalArgumentException("不支持的文件类型: " + preview.getDataType());
        }
    }

    /**
     * 解析CSV文件
     */
    private List<String[]> parseCsvFile(File file, DataPreview preview) throws IOException {
        List<String[]> data = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            String line;
            int rowCount = 0;
            while ((line = reader.readLine()) != null && rowCount < preview.getRows()) {
                String[] row = line.split(",");
                if (preview.getColumns() != null && preview.getColumns() < row.length) {
                    row = Arrays.copyOf(row, preview.getColumns());
                }
                data.add(row);
                rowCount++;
            }
        }
        return data;
    }

    /**
     * 解析JSON文件
     */
    private JSONObject parseJsonFile(File file, DataPreview preview) throws IOException {
        String content = FileUtils.readFileToString(file, StandardCharsets.UTF_8);
        return JSONUtil.parseObj(content);
    }

    /**
     * 解析XML文件
     */
    private Object parseXmlFile(File file, DataPreview preview) throws IOException {
        try {
            // 创建 DocumentBuilder 实例
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            // 防止 XXE 攻击
            factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            factory.setFeature("http://xml.org/sax/features/external-general-entities", false);
            factory.setFeature("http://xml.org/sax/features/external-parameter-entities", false);

            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(file);

            // 将 Document 转换为 JSONObject
            return xmlToJson(document.getDocumentElement());
        } catch (Exception e) {
            log.error("XML解析失败", e);
            throw new IOException("XML解析失败: " + e.getMessage());
        }
    }

    /**
     * 将 XML 节点转换为 JSON 对象
     */
    private Object xmlToJson(Node node) {
        // 如果是文本节点，直接返回文本内容
        if (node.getNodeType() == Node.TEXT_NODE) {
            String text = node.getTextContent().trim();
            return text.isEmpty() ? null : text;
        }

        // 处理元素节点
        JSONObject json = new JSONObject();

        // 处理属性
        NamedNodeMap attributes = node.getAttributes();
        if (attributes != null) {
            for (int i = 0; i < attributes.getLength(); i++) {
                Node attr = attributes.item(i);
                json.set("@" + attr.getNodeName(), attr.getNodeValue());
            }
        }

        // 处理子节点
        NodeList children = node.getChildNodes();
        List<Object> childrenOfSameName = new ArrayList<>();
        String lastChildName = null;

        for (int i = 0; i < children.getLength(); i++) {
            Node child = children.item(i);
            if (child.getNodeType() == Node.TEXT_NODE) {
                String text = child.getTextContent().trim();
                if (!text.isEmpty()) {
                    json.set("#text", text);
                }
                continue;
            }

            if (child.getNodeType() == Node.ELEMENT_NODE) {
                String childName = child.getNodeName();
                Object childValue = xmlToJson(child);

                // 处理重复的节点名称
                if (childName.equals(lastChildName)) {
                    if (childrenOfSameName.isEmpty()) {
                        // 第一次遇到重复节点，将之前的值添加到列表中
                        childrenOfSameName.add(json.get(childName));
                        childrenOfSameName.add(childValue);
                        json.set(childName, childrenOfSameName);
                    } else {
                        childrenOfSameName.add(childValue);
                    }
                } else {
                    childrenOfSameName = new ArrayList<>();
                    json.set(childName, childValue);
                    lastChildName = childName;
                }
            }
        }

        return json;
    }

    /**
     * 解析文本文件
     */
    private List<String> parseTextFile(File file, DataPreview preview) throws IOException {
        List<String> lines = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            String line;
            int rowCount = 0;
            while ((line = reader.readLine()) != null && rowCount < preview.getRows()) {
                lines.add(line);
                rowCount++;
            }
        }
        return lines;
    }

    /**
     * 非空验证
     */
    private Object validateNotNull(Object data, CleanConfig config) {
        if (data instanceof List) {
            List<?> list = (List<?>) data;
            boolean removeNulls = Boolean.TRUE.equals(config.getRemoveNulls());

            if (removeNulls) {
                // 移除空值
                return list.stream()
                        .filter(item -> {
                            if (item == null)
                                return false;
                            if (item instanceof String)
                                return !((String) item).trim().isEmpty();
                            if (item instanceof String[]) {
                                String[] arr = (String[]) item;
                                for (String s : arr) {
                                    if (s == null || s.trim().isEmpty())
                                        return false;
                                }
                                return true;
                            }
                            return true;
                        })
                        .collect(Collectors.toList());
            } else {
                // 标记空值（例如添加错误信息）
                return list.stream()
                        .map(item -> {
                            if (item == null) {
                                return markWithError(item, "值不能为空", config);
                            }
                            if (item instanceof String && ((String) item).trim().isEmpty()) {
                                return markWithError(item, "值不能为空", config);
                            }
                            if (item instanceof String[]) {
                                String[] arr = (String[]) item;
                                for (int i = 0; i < arr.length; i++) {
                                    if (arr[i] == null || arr[i].trim().isEmpty()) {
                                        arr[i] = config.getErrorMessage() != null ? config.getErrorMessage() : "值不能为空";
                                    }
                                }
                                return arr;
                            }
                            return item;
                        })
                        .collect(Collectors.toList());
            }
        } else if (data == null || (data instanceof String && ((String) data).trim().isEmpty())) {
            return config.getErrorMessage() != null ? config.getErrorMessage() : "值不能为空";
        }

        return data;
    }

    /**
     * 范围验证
     */
    private Object validateRange(Object data, CleanConfig config) {
        if (data instanceof List) {
            List<?> list = (List<?>) data;
            boolean removeInvalid = Boolean.TRUE.equals(config.getRemoveInvalid());

            if (removeInvalid) {
                // 移除不在范围内的值
                return list.stream()
                        .filter(item -> isInRange(item, config))
                        .collect(Collectors.toList());
            } else {
                // 标记不在范围内的值
                return list.stream()
                        .map(item -> isInRange(item, config) ? item : markWithError(item, "值不在有效范围内", config))
                        .collect(Collectors.toList());
            }
        } else {
            if (!isInRange(data, config)) {
                return config.getErrorMessage() != null ? config.getErrorMessage() : "值不在有效范围内";
            }
        }

        return data;
    }

    /**
     * 检查值是否在范围内
     */
    private boolean isInRange(Object value, CleanConfig config) {
        if (value == null)
            return false;

        try {
            if (value instanceof Number) {
                double numValue = ((Number) value).doubleValue();
                boolean minValid = config.getMinValue() == null || numValue >= config.getMinValue();
                boolean maxValid = config.getMaxValue() == null || numValue <= config.getMaxValue();
                return minValid && maxValid;
            } else if (value instanceof String) {
                // 尝试转换为数字
                try {
                    double numValue = Double.parseDouble((String) value);
                    boolean minValid = config.getMinValue() == null || numValue >= config.getMinValue();
                    boolean maxValid = config.getMaxValue() == null || numValue <= config.getMaxValue();
                    return minValid && maxValid;
                } catch (NumberFormatException e) {
                    // 如果不是数字，检查字符串长度
                    int length = ((String) value).length();
                    boolean minValid = config.getMinValue() == null || length >= config.getMinValue();
                    boolean maxValid = config.getMaxValue() == null || length <= config.getMaxValue();
                    return minValid && maxValid;
                }
            } else if (value instanceof String[]) {
                // 对于数组，检查每个元素
                String[] arr = (String[]) value;
                for (String s : arr) {
                    if (!isInRange(s, config)) {
                        return false;
                    }
                }
                return true;
            }
        } catch (Exception e) {
            log.warn("范围检查失败: {}", e.getMessage());
            return false;
        }

        return true;
    }

    /**
     * 格式验证
     */
    private Object validateFormat(Object data, CleanConfig config) {
        if (data instanceof List) {
            List<?> list = (List<?>) data;
            boolean removeInvalid = Boolean.TRUE.equals(config.getRemoveInvalid());

            if (removeInvalid) {
                // 移除格式无效的值
                return list.stream()
                        .filter(item -> hasValidFormat(item, config))
                        .collect(Collectors.toList());
            } else {
                // 标记格式无效的值
                return list.stream()
                        .map(item -> hasValidFormat(item, config) ? item : markWithError(item, "格式无效", config))
                        .collect(Collectors.toList());
            }
        } else {
            if (!hasValidFormat(data, config)) {
                return config.getErrorMessage() != null ? config.getErrorMessage() : "格式无效";
            }
        }

        return data;
    }

    /**
     * 检查值是否符合指定格式
     */
    private boolean hasValidFormat(Object value, CleanConfig config) {
        if (value == null)
            return false;
        if (!(value instanceof String))
            return true; // 非字符串类型默认通过

        String strValue = (String) value;
        String format = config.getFormat();
        String pattern = config.getPattern();

        if (format == null && pattern == null)
            return true;

        try {
            if (pattern != null) {
                // 使用正则表达式验证
                return strValue.matches(pattern);
            }

            if (format != null) {
                switch (format.toLowerCase()) {
                    case "email":
                        return strValue.matches("^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$");
                    case "phone":
                        return strValue.matches("^\\+?[0-9]{10,15}$");
                    case "date":
                        try {
                            new SimpleDateFormat(
                                    config.getSourceFormat() != null ? config.getSourceFormat() : "yyyy-MM-dd")
                                    .parse(strValue);
                            return true;
                        } catch (Exception e) {
                            return false;
                        }
                    case "number":
                        try {
                            Double.parseDouble(strValue);
                            return true;
                        } catch (NumberFormatException e) {
                            return false;
                        }
                    case "url":
                        return strValue.matches("^(https?|ftp)://[^\\s/$.?#].[^\\s]*$");
                    case "ip":
                        return strValue.matches(
                                "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$");
                    default:
                        return true;
                }
            }
        } catch (Exception e) {
            log.warn("格式验证失败: {}", e.getMessage());
            return false;
        }

        return true;
    }

    /**
     * 自定义规则验证
     */
    private Object validateCustom(Object data, CleanConfig config) {
        if (config.getRule() == null)
            return data;

        if (data instanceof List) {
            List<?> list = (List<?>) data;
            boolean removeInvalid = Boolean.TRUE.equals(config.getRemoveInvalid());

            if (removeInvalid) {
                // 移除不符合规则的值
                return list.stream()
                        .filter(item -> evaluateCustomRule(item, config))
                        .collect(Collectors.toList());
            } else {
                // 标记不符合规则的值
                return list.stream()
                        .map(item -> evaluateCustomRule(item, config) ? item : markWithError(item, "不符合自定义规则", config))
                        .collect(Collectors.toList());
            }
        } else {
            if (!evaluateCustomRule(data, config)) {
                return config.getErrorMessage() != null ? config.getErrorMessage() : "不符合自定义规则";
            }
        }

        return data;
    }

    /**
     * 评估自定义规则
     */
    private boolean evaluateCustomRule(Object value, CleanConfig config) {
        if (value == null)
            return false;

        String rule = config.getRule();
        if (rule == null)
            return true;

        try {
            // 这里可以实现简单的规则引擎或表达式计算
            // 示例实现：支持简单的比较操作
            if (value instanceof String) {
                String strValue = (String) value;

                if (rule.startsWith("length >")) {
                    int length = Integer.parseInt(rule.substring(9).trim());
                    return strValue.length() > length;
                } else if (rule.startsWith("length <")) {
                    int length = Integer.parseInt(rule.substring(9).trim());
                    return strValue.length() < length;
                } else if (rule.startsWith("length =")) {
                    int length = Integer.parseInt(rule.substring(9).trim());
                    return strValue.length() == length;
                } else if (rule.startsWith("contains")) {
                    String substring = rule.substring(9).trim().replace("\"", "");
                    return strValue.contains(substring);
                } else if (rule.startsWith("startsWith")) {
                    String prefix = rule.substring(11).trim().replace("\"", "");
                    return strValue.startsWith(prefix);
                } else if (rule.startsWith("endsWith")) {
                    String suffix = rule.substring(9).trim().replace("\"", "");
                    return strValue.endsWith(suffix);
                } else if (rule.startsWith("matches")) {
                    String pattern = rule.substring(8).trim().replace("\"", "");
                    return strValue.matches(pattern);
                }
            } else if (value instanceof Number) {
                double numValue = ((Number) value).doubleValue();

                if (rule.startsWith(">")) {
                    double threshold = Double.parseDouble(rule.substring(1).trim());
                    return numValue > threshold;
                } else if (rule.startsWith("<")) {
                    double threshold = Double.parseDouble(rule.substring(1).trim());
                    return numValue < threshold;
                } else if (rule.startsWith("=")) {
                    double threshold = Double.parseDouble(rule.substring(1).trim());
                    return Math.abs(numValue - threshold) < 0.00001; // 浮点数比较
                }
            }
        } catch (Exception e) {
            log.warn("自定义规则评估失败: {}", e.getMessage());
            return false;
        }

        return true;
    }

    /**
     * 标记错误
     */
    private Object markWithError(Object value, String defaultError, CleanConfig config) {
        String errorMessage = config.getErrorMessage() != null ? config.getErrorMessage() : defaultError;

        // 根据数据类型不同，采用不同的标记方式
        if (value instanceof String[]) {
            String[] arr = (String[]) value;
            String[] result = Arrays.copyOf(arr, arr.length);
            // 在数组最后添加错误信息
            result[result.length - 1] = errorMessage;
            return result;
        } else {
            // 对于其他类型，直接返回错误信息
            return errorMessage;
        }
    }

    @Override
    public String desc() {
        return "";
    }

    /**
     * 数据预览配置
     */
    @Data
    @NoArgsConstructor
    public static class DataPreview {
        private String filePath; // 文件路径
        private String dataType; // 数据类型：CSV/JSON/XML/文本
        private Integer rows; // 行数
        private Integer columns; // 列数
    }

    /**
     * 清洗步骤配置
     */
    @Data
    @NoArgsConstructor
    public static class CleanStep {
        private String type; // 清洗类型：BASIC/CONVERT/VALIDATE
        private String operation; // 具体操作
        private CleanConfig config; // 统一的配置对象
    }

    /**
     * 统一的清洗配置
     */
    @Data
    @NoArgsConstructor
    public static class CleanConfig {
        // 通用字段处理
        private String trimType; // 去除空格类型：all/left/right
        private String oldValue; // 替换值-原值
        private String newValue; // 替换值-新值
        private Boolean keepFirst; // 去重时是否保留第一个
        private Boolean removeEmpty; // 是否删除空值
        private Boolean removeEmptyRows; // 是否删除全空行

        // 类型转换
        private String sourceType; // 源类型：string/number/boolean/date
        private String targetType; // 目标类型：string/number/boolean/date

        // 日期处理
        private String sourceFormat; // 源日期格式
        private String targetFormat; // 目标日期格式

        // 数值处理
        private Integer decimalPlaces; // 小数位数
        private String sourceUnit; // 源单位
        private String targetUnit; // 目标单位
        private Double minValue; // 最小值/归一化最小值
        private Double maxValue; // 最大值/归一化最大值

        // 字符串处理
        private String caseConversion; // 大小写转换：upper/lower/capitalize
        private Integer startIndex; // 截取起始索引
        private Integer endIndex; // 截取结束索引
        private String padChar; // 填充字符
        private Integer padLength; // 填充长度
        private String padDirection; // 填充方向：left/right

        // 正则表达式
        private String pattern; // 正则表达式
        private String regexMode; // 正则模式：extract/match/replace
        private Integer regexGroup; // 提取的分组索引

        // 自定义函数
        private String functionType; // 函数类型
        private String functionParams; // 函数参数

        // 条件过滤
        private String field; // 字段名或索引
        private String operator; // 操作符：equals/notEquals/contains/startsWith/endsWith/greaterThan/lessThan/isNull/isNotNull/matches
        private String filterValue; // 过滤值

        // 数据聚合
        private String aggregationType; // 聚合类型：count/sum/avg/max/min
        private String groupByField; // 分组字段

        // 格式验证
        private String format; // 格式类型：email/phone/date/number/custom
        private String rule; // 自定义规则
        private String errorMessage; // 错误信息

        // 验证选项
        private Boolean removeNulls; // 是否移除空值
        private Boolean removeInvalid; // 是否移除无效值
    }

    /**
     * 节点属性配置
     */
    @Data
    @NoArgsConstructor
    public static class Properties {
        // 数据预览配置
        private String filePath;
        private String dataType;
        private Integer rows;
        private Integer columns;

        // 从上游节点传入的数据
        private String inputData;

        // 清洗步骤直接配置（适配前端传参格式）
        private String type;
        private String operation;
        private CleanConfig config;

        // 获取数据预览配置
        public DataPreview getDataPreview() {
            if (filePath == null && dataType == null && rows == null && columns == null) {
                return null;
            }

            DataPreview preview = new DataPreview();
            preview.setFilePath(filePath);
            preview.setDataType(dataType);
            preview.setRows(rows);
            preview.setColumns(columns);
            return preview;
        }

        // 获取清洗步骤
        public CleanStep getCleanSteps() {
            if (type == null && operation == null) {
                return null;
            }

            CleanStep step = new CleanStep();
            step.setType(type);
            step.setOperation(operation);
            step.setConfig(config);
            return step;
        }
    }

}