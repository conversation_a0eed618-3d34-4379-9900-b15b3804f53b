package com.icarus.common.cotnode;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.icarus.common.runtime.NodeContext;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class FileNode extends BaseNode {
    
    @Getter
    @Setter
    private String result;

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【文件操作】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
        
        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            nodeContext.setInputs(new JSONObject(resolvedInputs));
            
            // 解析属性配置
            Properties resolvedProperties = resolveProperties(context, Properties.class);
            JSONObject propertiesJson = new JSONObject();
            propertiesJson.set("action", resolvedProperties.getAction());
            nodeContext.setProperties(propertiesJson);

            // 从输入参数中获取文件操作所需的值
            String filePath = (String) resolvedInputs.get("filePath");
            String content = (String) resolvedInputs.get("content");
            String targetPath = (String) resolvedInputs.get("targetPath");
            String newName = (String) resolvedInputs.get("newName");
            
            switch (resolvedProperties.getAction()) {
                case "del":
                    File targetFile = new File(filePath);
                    if (!targetFile.exists()) {
                        throw new RuntimeException("文件不存在: " + targetFile.getAbsolutePath());
                    }
                    if (!targetFile.delete()) {
                        throw new RuntimeException("删除文件失败: " + targetFile.getAbsolutePath());
                    }
                    result = "文件删除成功";
                    break;
                    
                case "load":
                    targetFile = new File(filePath);
                    if (!targetFile.exists()) {
                        throw new RuntimeException("文件不存在: " + targetFile.getAbsolutePath());
                    }
                    StringBuilder contentBuilder = new StringBuilder();
                    try (BufferedReader reader = new BufferedReader(new InputStreamReader(
                            new FileInputStream(targetFile), StandardCharsets.UTF_8))) {
                        String line;
                        while ((line = reader.readLine()) != null) {
                            contentBuilder.append(line).append("\n");
                        }
                    }
                    result = contentBuilder.toString();
                    break;
                    
                case "add":
                    if (StrUtil.isBlank(content)) {
                        throw new RuntimeException("文件内容不能为空");
                    }
                    File newFile = new File(filePath);
                    if (newFile.exists()) {
                        throw new RuntimeException("文件已存在: " + newFile.getAbsolutePath());
                    }
                    File newFileDir = newFile.getParentFile();
                    if (!newFileDir.exists() && !newFileDir.mkdirs()) {
                        throw new RuntimeException("创建目标目录失败: " + newFileDir.getAbsolutePath());
                    }
                    try (BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(
                            new FileOutputStream(newFile), StandardCharsets.UTF_8))) {
                        writer.write(content);
                    }
                    result = "文件创建成功";
                    break;

                case "move":
                    if (StrUtil.isBlank(targetPath)) {
                        throw new RuntimeException("目标路径不能为空");
                    }
                    File sourceFile = new File(filePath);
                    if (!sourceFile.exists()) {
                        throw new RuntimeException("源文件不存在: " + sourceFile.getAbsolutePath());
                    }
                    
                    File targetDir = new File(targetPath);
                    if (!targetDir.exists() && !targetDir.mkdirs()) {
                        throw new RuntimeException("创建目标目录失败: " + targetDir.getAbsolutePath());
                    }
                    
                    File destFile = new File(targetDir, sourceFile.getName());
                    if (destFile.exists()) {
                        throw new RuntimeException("目标文件已存在: " + destFile.getAbsolutePath());
                    }
                    
                    if (!sourceFile.renameTo(destFile)) {
                        throw new RuntimeException("移动文件失败");
                    }
                    result = "文件移动成功";
                    break;

                case "rename":
                    if (StrUtil.isBlank(newName)) {
                        throw new RuntimeException("新文件名不能为空");
                    }
                    File oldFile = new File(filePath);
                    if (!oldFile.exists()) {
                        throw new RuntimeException("文件不存在: " + oldFile.getAbsolutePath());
                    }
                    
                    File parentDir = oldFile.getParentFile();
                    File renamedFile = new File(parentDir, newName);
                    if (renamedFile.exists()) {
                        throw new RuntimeException("目标文件名已存在: " + renamedFile.getAbsolutePath());
                    }
                    
                    if (!oldFile.renameTo(renamedFile)) {
                        throw new RuntimeException("重命名文件失败");
                    }
                    result = "文件重命名成功";
                    break;
                    
                default:
                    throw new RuntimeException("不支持的操作类型: " + resolvedProperties.getAction());
            }
            
            nodeContext.setStatus("completed");
            processOutputs(context);

        } catch (Exception e) {
            log.error("文件节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        log.info("文件节点执行结束，节点ID：{}", getId());
        return context;
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    private JSONArray buildFileTree(File directory) {
        JSONArray children = new JSONArray();
        File[] files = directory.listFiles();

        if (files != null) {
            for (File file : files) {
                JSONObject node = new JSONObject();
                node.set("name", file.getName());

                if (file.isDirectory()) {
                    node.set("children", buildFileTree(file));
                }
                children.add(node);
            }
        }

        return children;
    }

    @Data
    @NoArgsConstructor
    public static class Properties {
        private String action;    // 操作类型：del/load/add/move/rename
    }

    @Override
    public String desc() {
        return "文件节点";
    }
}
