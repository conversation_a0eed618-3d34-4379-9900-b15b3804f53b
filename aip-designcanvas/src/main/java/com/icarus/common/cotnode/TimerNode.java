package com.icarus.common.cotnode;

import cn.hutool.json.JSONObject;
import com.icarus.common.runtime.NodeContext;
import com.icarus.service.TimerSchedulerService;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
@Component
public class TimerNode extends BaseNode implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext context) throws BeansException {
        applicationContext = context;
    }

    private static TimerSchedulerService getTimerSchedulerService() {
        return applicationContext.getBean(TimerSchedulerService.class);
    }

    @Getter
    @Setter
    private String triggerId;

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【定时器】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);

        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            nodeContext.setInputs(new JSONObject(resolvedInputs));

            // 解析属性配置
            Properties resolvedProperties = resolveProperties(context, Properties.class);
            JSONObject propertiesJson = new JSONObject();
            propertiesJson.set("id", resolvedProperties.getId());
            propertiesJson.set("userId", resolvedProperties.getUserId());
            propertiesJson.set("name", resolvedProperties.getName());
            propertiesJson.set("timezone", resolvedProperties.getTimezone());
            propertiesJson.set("triggerTimeType", resolvedProperties.getTriggerTimeType());
            propertiesJson.set("triggerTime", resolvedProperties.getTriggerTime());
            propertiesJson.set("cronExpression", resolvedProperties.getCronExpression());
            propertiesJson.set("frequencyType", resolvedProperties.getFrequencyType());
            propertiesJson.set("frequencyDay", resolvedProperties.getFrequencyDay());
            propertiesJson.set("workflowId", resolvedProperties.getWorkflowId());
            nodeContext.setProperties(propertiesJson);

            // 生成触发器ID
            triggerId = generateTriggerId(resolvedProperties);

            // 设置定时任务
            scheduleTask(resolvedProperties, context);

            nodeContext.setStatus("completed");

            // 处理输出
            processOutputs(context);

        } catch (Exception e) {
            log.error("定时器节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        log.info("定时器节点执行结束，节点ID：{}，生成的触发器ID：{}", getId(), triggerId);
        return context;
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("triggerId".equals(propertyName)) {
            return triggerId;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    /**
     * 生成触发器ID
     */
    private String generateTriggerId(Properties properties) {
        // 生成唯一的触发器ID
        String uniqueId = properties.getWorkflowId();
        log.info("生成触发器ID: {}", uniqueId);
        return uniqueId;
    }

    /**
     * 设置定时任务
     */
    private void scheduleTask(Properties properties, Map<String, Object> context) {
        log.info("设置定时任务，时区：{}，触发时间类型：{}", properties.getTimezone(), properties.getTriggerTimeType());

        try {
            // 获取定时器服务
            TimerSchedulerService schedulerService = getTimerSchedulerService();

            // 准备工作流参数
            Map<String, Object> workflowParams = prepareWorkflowParams(properties, context);
            // 根据触发时间类型处理不同的定时策略
            if ("preset".equals(properties.getTriggerTimeType())) {
                // 解析时区
                ZoneId zoneId = ZoneId.of(properties.getTimezone());
                ZonedDateTime now = ZonedDateTime.now(zoneId);
                log.info("当前时区时间: {}", now);

                // 处理预设时间
                String triggerTime = properties.getTriggerTime();
                String frequencyType = properties.getFrequencyType();
                int frequencyDay = properties.getFrequencyDay();

                log.info("使用预设时间模式，触发时间: {}, 频率类型: {}, 频率日期: {}",
                        triggerTime, frequencyType, frequencyDay);

                // 添加预设时间定时任务
                schedulerService.addPresetTask(
                        triggerId,
                        triggerTime,
                        frequencyType,
                        frequencyDay,
                        properties.getWorkflowId(),
                        workflowParams);

            } else if ("cron".equals(properties.getTriggerTimeType())) {
                // 处理Cron表达式
                String cronExpression = properties.getCronExpression();
                log.info("使用Cron表达式模式，表达式: {}", cronExpression);

                // 验证Cron表达式
                if (cronExpression == null || cronExpression.trim().isEmpty()) {
                    throw new RuntimeException("Cron表达式不能为空");
                }

                // 添加Cron定时任务
                schedulerService.addCronTask(
                        triggerId,
                        cronExpression,
                        properties.getWorkflowId(),
                        workflowParams);

            } else {
                throw new RuntimeException("不支持的触发时间类型: " + properties.getTriggerTimeType());
            }

            log.info("成功设置定时任务，触发器ID: {}", triggerId);

        } catch (Exception e) {
            log.error("设置定时任务失败: {}", e.getMessage(), e);
            throw new RuntimeException("设置定时任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 准备传递给工作流的参数
     */
    private Map<String, Object> prepareWorkflowParams(Properties properties, Map<String, Object> context) {
        Map<String, Object> params = new HashMap<>();

        // 添加必要的上下文信息
        params.put("triggerId", triggerId);
        params.put("triggerNodeId", getId());
        params.put("triggerTime", ZonedDateTime.now().toString());
        params.put("userId", properties.getUserId());

        // 添加其他可能需要的参数
        // 这里可以添加从当前上下文提取的关键参数

        return params;
    }

    @Data
    @NoArgsConstructor
    public static class Properties {
        private String id; // 标识符
        private String userId; // 用户ID
        private String name; // 名称
        private String timezone; // 时区，例如：Asia/Shanghai
        private String triggerTimeType; // 触发时间类型：preset(预设时间)或cron(Cron表达式)
        private String triggerTime; // 触发时间(格式: HH:mm:ss)
        private String cronExpression; // Cron表达式(当triggerTimeType为cron时使用)
        private String frequencyType; // 频率类型：daily(每日)、weekly(每周)、monthly(每月)、interval(间隔)
        private int frequencyDay; // 频率日期：对应星期几(1-7)或月份日期(1-31)或间隔天数
        private String workflowId; // 排定工作流ID
        private String workflowPath; // 排定工作流路径
    }

    @Override
    public String desc() {
        return "定时器节点";
    }
}