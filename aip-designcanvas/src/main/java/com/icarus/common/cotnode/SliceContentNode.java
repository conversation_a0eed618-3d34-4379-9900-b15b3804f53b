package com.icarus.common.cotnode;

import cn.hutool.json.JSONObject;
import com.icarus.common.runtime.NodeContext;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class SliceContentNode extends BaseNode {
    @Getter
    @Setter
    private List<String> result;

    private final String URLRegex = "(http|https|ftp)://([^\\s\\u4e00-\\u9fa5])+";

    private final String EmailRegex = "\\w+([-+.']\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*";

    @Override

    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【内容切片】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
        // 解析输入参数
        Map<String, Object> resolvedInputs = resolveAllInputs(context);
        nodeContext.setInputs(new JSONObject(resolvedInputs));
        // 解析属性配置
        Properties resolvedProperties = resolveProperties(context, Properties.class);
        try {
            result = sliceContent(resolvedProperties);
            nodeContext.setStatus("completed");
            processOutputs(context);
        } catch (Exception e) {
            log.error("文件节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }
        log.info("执行【内容切片】节点完成, 当前节点id:{}", getId());
        return context;
    }

    private List<String> sliceContent(Properties properties) {
        String content = properties.getContent();
        int sliceSize = properties.getSliceSize();
        int overlapSize = properties.getOverlapSize();
        String separator = properties.getSeparator();
        if (sliceSize <= 0) {
            throw new IllegalArgumentException("sliceSize must be greater than 0");
        }
        if (overlapSize < 0 || overlapSize >= sliceSize) {
            throw new IllegalArgumentException("overlapSize must be in range [0, sliceSize)");
        }

        if (properties.getIsDeleteURL() != null && properties.getIsDeleteURL()) {
            content = content.replaceAll(URLRegex, "");
            content = content.replaceAll(EmailRegex, "");
        }

        if (properties.getIsCompress() != null && properties.getIsCompress()) {
            content = content.replaceAll("\\s+", " ");
        }

        List<String> slices = new ArrayList<>();
        String[] segments = content.split(separator);
        for (String segment : segments) {
            if (segment.isEmpty()) continue;
            List<String> segmentSlices = sliceSegment(segment, sliceSize, overlapSize);
            slices.addAll(segmentSlices);
        }
        return slices;
    }

    private List<String> sliceSegment(String segment, int sliceSize, int overlapSize) {
        List<String> slices = new ArrayList<>();
        int length = segment.length();

        if (length <= sliceSize) {
            // 分段长度小于等于sliceSize，无需进一步切分
            slices.add(segment);
            return slices;
        }

        // 计算需要的切片数（考虑重叠）
        int step = sliceSize - overlapSize;
        int slicesNeeded = (int) Math.ceil((double) (length - sliceSize) / step) + 1;

        for (int i = 0; i < slicesNeeded; i++) {
            int start = i * step;
            int end = Math.min(start + sliceSize, length);
            // 处理最后一个切片可能的不足
            if (end == length && start > length - sliceSize) {
                // 调整最后一个切片的起始位置，确保覆盖整个文本
                start = length - sliceSize;
            }
            slices.add(segment.substring(start, end));
        }

        return slices;
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    @Override
    public String desc() {
        return "文本切分节点";
    }

    @Data
    @NoArgsConstructor
    public static class Properties {
        private String content;
        private Integer sliceSize;
        private Integer overlapSize;
        private Boolean isCompress;
        private Boolean isDeleteURL;
        private String separator;
    }
}
