package com.icarus.common.cotnode;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.resource.InputStreamResource;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.core.util.ObjUtil;
import com.icarus.common.runtime.NodeContext;
import com.icarus.sdk.springboot.base.utils.SpringContextUtils;
import com.icarus.service.FileStorageService;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.PathNotFoundException;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URI;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * HTTP请求节点
 * @autor ksw
 * @date 2025-02-27
 */
@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class HttpNode extends BaseNode {

    /**
     * 当前节点参数变量引用正则表达式：针对仅引用当前节点变量的匹配规则 {current.*}
     * 1. 参数引用格式：{current.参数名},参数名支持任意字母、数字、下划线、横线
     * 2. 参数嵌套格式：{current.参数名.参数名}
     */
    private final static Pattern CURRENT_PARAMETER_VARIABLE_PATTERN = Pattern
            .compile("^\\{current([._-][a-zA-Z0-9_-]+)+\\}$");

    /**
     * 参数变量引用正则表达式：针对仅引用变量的匹配规则 {参数名}
     * 1. 参数引用格式：{参数名},参数名支持任意字母、数字、下划线、横线
     * 2. 参数嵌套格式：{参数名.参数名},支持多级嵌套，如：{a.b.c}、{result.byname.result}
     */
    private final static Pattern PARAMETER_VARIABLE_PATTERN = Pattern
            .compile("^\\{([a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)*)?\\}$");

    /**
     * 文本参数变量引用正则表达式：针对文本内容中引用变量的匹配规则 任意文本内容{参数名}任意文本内容
     * 1. 参数引用格式：*{参数名}*,参数名支持任意字母、数字、下划线、横线
     * 2. 参数嵌套格式：*{参数名.参数名}*,支持多级嵌套，如：*{a.b.c}*、*{result.byname.result}*
     */
    private final static Pattern TEXT_PARAMETER_VARIABLE_PATTERN = Pattern
            .compile("\\{([a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)*)?\\}");

    @Getter
    @Setter
    private Object result;

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【HTTP请求】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
        // 解析输入参数
        Map<String, Object> resolvedInputs = resolveAllInputs(context);
        nodeContext.setInputs(new JSONObject(resolvedInputs));

        // 解析属性配置
        Properties resolvedProperties = resolveProperties(context, Properties.class);

        JSONObject propertiesJson = new JSONObject();
        propertiesJson.set("method", resolvedProperties.getMethod());
        propertiesJson.set("url", resolvedProperties.getUrl());
        propertiesJson.set("headers", resolvedProperties.getHeaders());
        propertiesJson.set("params", resolvedProperties.getParams());
        propertiesJson.set("body", resolvedProperties.getBody());
        propertiesJson.set("retry", resolvedProperties.getRetry());
        propertiesJson.set("timeout", resolvedProperties.getTimeout());
        propertiesJson.set("ignoreError", resolvedProperties.getIgnoreError());
        nodeContext.setProperties(propertiesJson);

        try {
            int maxRetries = ObjUtil.isNotNull(resolvedProperties.getRetry())
                    && Integer.parseInt(resolvedProperties.getRetry()) > 0 ?
                    Integer.parseInt(resolvedProperties.getRetry()) : 0;
            int currentRetry = 0;
            Exception lastException = null;
            do {
                try {
                    // 发送请求
                    result = sendRequest(resolvedProperties, context);
                    // 请求成功，跳出重试循环
                    break;
                } catch (Exception e) {
                    lastException = e;
                    if (currentRetry == maxRetries) {
                        // 已达到最大重试次数，抛出最后一次异常
                        throw e;
                    }
                    log.error("HTTP请求失败，正在进行第{}次重试，异常信息：{}", currentRetry + 1, e.getMessage());
                    currentRetry++;
                }
            } while (currentRetry <= maxRetries);

            // 请求成功后的处理
            nodeContext.setStatus("completed");

        } catch (Exception e) {
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
            if (ObjUtil.isNotNull(resolvedProperties.getIgnoreError()) && "true".equalsIgnoreCase(resolvedProperties.getIgnoreError())) {
                HashMap<String, String> errorBody = new HashMap<>();
                errorBody.put("errorCode", "HTTP_CALL_ERROR");
                errorBody.put("errorMessage", e.getMessage());
                result = errorBody;
            }
        } finally {
            nodeContext.setOutputs(new JSONObject(result));
            // 处理节点输出，将结果写入上下文
            processOutputs(context);

            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        log.info("HTTP节点执行结束，节点ID：{}", getId());
        return context;
    }

    /**
     * 解析属性配置的值
     * 遍历所有属性，将引用类型的值解析为实际值
     *
     * @param context 流程上下文
     * @return 解析后的属性对象
     */
    @Override
    protected <T> T resolveProperties(Map<String, Object> context, Class<T> propertiesClass) {
        Object properties = this.getProperties();
        if (properties == null) {
            return null;
        }

        // 将properties转换为JSONObject
        JSONObject propsJson = JSONUtil.parseObj(properties);
        JSONObject resolvedProps = new JSONObject();
        log.info("propsJson: {}", propsJson);

        // 遍历所有属性
        for (Map.Entry<String, Object> entry : propsJson.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (value instanceof String) {
                /**
                 * modify by ksw 2025-04-17 统一使用参数解析器解析参数值
                 */
                value = this.parserParameterValue(value.toString(), context, this,propsJson.getStr("method"));

                if ("body".equals(key)) {
                    try {
                        if (value instanceof JSONObject || value instanceof JSONArray) {
                            JSONObject bodyObj = new JSONObject();
                            bodyObj.put("bodyType", "json");
                            bodyObj.put("bodyData", value);
                            resolvedProps.put(key, bodyObj);
                        } else if (StrUtil.contains((String) value, "bodyType")) {
                            Object jsonValue = JSONUtil.parse(value);
                            resolvedProps.put(key, jsonValue);
                        } else {
                            // 尝试将字符串解析为JSON对象

                            Object jsonValue = JSONUtil.parseObj(value);
                            JSONObject bodyObj = new JSONObject();
                            bodyObj.put("bodyType", "json");
                            bodyObj.put("bodyData", jsonValue);
                            resolvedProps.put(key, bodyObj);
                        }
                    } catch (Exception e) {
                        log.warn("body value parser error: {}", value);
                        // 如果解析失败，创建一个新的Map并放入bodyData
                        JSONObject bodyObj = new JSONObject();
                        bodyObj.put("bodyType", "raw-text");
                        bodyObj.put("bodyData", value);
                        resolvedProps.put(key, bodyObj);
                    }
                }
                // 特殊处理headers和params字段，确保它们是List<Parameter>类型
                else if ("headers".equals(key) || "params".equals(key)) {
                    if (value instanceof List) {
                        resolvedProps.put(key, value);
                    }else {
                        try {
                            // 尝试将字符串解析为JSON数组
                            Object jsonValue = JSONUtil.parse((String) value);
                            if (jsonValue instanceof List) {
                                resolvedProps.put(key, jsonValue);
                            } else {
                                log.warn("value array parser failed: {}", value);
                                // 如果不是列表，创建一个空列表
                                resolvedProps.put(key, new ArrayList<>());
                            }
                        } catch (Exception e) {
                            log.warn("value array parser error: {}", value);
                            // 如果解析失败，使用空列表
                            resolvedProps.put(key, new ArrayList<>());
                        }
                    }
                }else if("url".equals( key)){
                    resolvedProps.put(key, value.toString().replace("\"", ""));
                }else {
                    resolvedProps.put(key, value);
                }
            } else {
                // 非字符串类型的值直接使用
                resolvedProps.put(key, value);
            }
        }

        log.info("HttpNode resolvedProps: {}", resolvedProps);

        try {
            // 将解析后的JSON转换为指定的类型
            return JSONUtil.toBean(resolvedProps, propertiesClass);
        } catch (Exception e) {
            log.error("HttpNode 转换Properties对象失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    protected static Object parserParameterValue(String expression, Map<String, Object> context, BaseNode node,String method) {
        // 参数值表达式、流程上下文不能为空、流程变量上下文不能为空
        if (StrUtil.isEmpty(expression) || CollUtil.isEmpty(context) || ObjUtil.isNull(BaseNode.getFlowData(context)))
            return null;

        // 场景一：当前节点参数变量匹配 - 匹配引用当前节点参数值表达式 {current.*}
        Matcher currentMatcher = CURRENT_PARAMETER_VARIABLE_PATTERN.matcher(expression);
        if (currentMatcher.find()) { // 正则匹配成功 表示当前表达式为 {current.*}
            // 获取匹配到的参数表达式
            String expression_ = currentMatcher.group();
            // 提取属性路径，去掉{current.和}
            String property = expression_.substring("{current.".length(), expression_.length() - 1);
            // 获取当前节点的属性值仅支持 {current.*}按照*去获取值,若出现{current.*.*}则会按照*.*去获取值
            return ObjUtil.isNull(node) ? null : node.getCurrentNodeProperty(property);
        }

        // 场景二：变量匹配 - 匹配完整的参数值表达式 {*}、{*.*}
        Matcher matcher = PARAMETER_VARIABLE_PATTERN.matcher(expression);
        if (matcher.find()) { // 正则匹配成功 表示当前表达式为{*}、{*.*}
            // 获取匹配到的参数表达式
            String expression_ = matcher.group();
            // 提取属性路径，去掉{和}
            String path = expression_.substring(1, expression_.length() - 1);

            try {
                // 检查是否包含点号，表示需要访问嵌套属性（如result.byname.result）
                if (path.contains(".")) {
                    // 分离第一级属性名和后续路径
                    String objName = path.substring(0, path.indexOf("."));
                    String propPath = path.substring(path.indexOf(".") + 1);

                    // 先尝试获取第一级对象
                    String objJsonPath = "$." + objName;
                    Object objValue = null;
                    try {
                        objValue = JsonPath.read(BaseNode.getFlowData(context), objJsonPath);
                    } catch (PathNotFoundException e) {
                        log.debug("Object not found: " + objName);
                        throw e; // 重新抛出异常，让外层catch处理
                    }

                    // 特殊处理：如果对象是JSON字符串，先解析成JSON对象再获取属性
                    // 这种情况适用于某些属性值是序列化的JSON字符串的场景
                    if (objValue instanceof String) {
                        String strValue = (String) objValue;
                        if (strValue.trim().startsWith("{") && strValue.trim().endsWith("}")) {
                            try {
                                // 解析JSON字符串为JSONObject
                                JSONObject jsonObj = JSONUtil.parseObj(strValue);
                                // 使用路径获取嵌套属性值，支持多层级如byname.result
                                return jsonObj.getByPath(propPath);
                            } catch (Exception e) {
                                log.debug("Failed to parse JSON string or get property: " + path, e);
                            }
                        }
                    }
                }

                // 如果上面的特殊处理没有返回结果，或者路径不包含点号，则使用标准JsonPath直接访问
                // JsonPath本身支持多层级属性访问，如$.result.byname.result
                String jsonPath = "$." + path;
                return JsonPath.read(BaseNode.getFlowData(context), jsonPath);
            } catch (PathNotFoundException e) {
                log.error("Failed to parse the parameter value: " + expression_, e);
            }
        }

        // 场景三：文本内嵌变量匹配 - 处理文本中包含的参数引用，如"Hello, {name}!"
        Matcher textMatcher = TEXT_PARAMETER_VARIABLE_PATTERN.matcher(expression);
        String textContent = expression;
        boolean matchFound = false;
        while (textMatcher.find()) {
            matchFound = true;
            // 获取匹配到的变量表达式，如{name}、{result.byname.result}
            String variableExpression = textMatcher.group();

            // 子场景一：检查是否为当前节点变量引用 {current.*}
            Matcher curMatcher = CURRENT_PARAMETER_VARIABLE_PATTERN.matcher(variableExpression);
            if (curMatcher.find()) {
                // 获取匹配到的参数表达式
                String curExpression_ = curMatcher.group();
                // 提取属性路径，去掉{current.和}
                String property = curExpression_.substring("{current.".length(), curExpression_.length() - 1);
                // 获取当前节点的属性值
                Object currentNodeProperty = ObjUtil.isNull(node) ? null : node.getCurrentNodeProperty(property);
                if (ObjUtil.isNotNull(currentNodeProperty)) {
                    // 全局替换(将文本中{current.*}替换为具体值)
                    textContent = textContent.replace(curExpression_, JSONUtil.toJsonStr(currentNodeProperty));
                } else {
                    // 移除无法解析的参数表达式
                    textContent = textContent.replace(curExpression_, "");
                }
            } else {
                // 子场景二：处理普通变量引用 {*}、{*.*}
                // 提取属性路径，去掉{和}
                String path = variableExpression.substring(1, variableExpression.length() - 1);
                Object variableValue = null;

                // 处理嵌套属性路径，如result.byname.result
                if (path.contains(".")) {
                    // 分离第一级属性名和后续路径
                    String objName = path.substring(0, path.indexOf("."));
                    String propPath = path.substring(path.indexOf(".") + 1);

                    try {
                        // 先获取第一级对象
                        String objJsonPath = "$." + objName;
                        Object objValue = JsonPath.read(BaseNode.getFlowData(context), objJsonPath);

                        // 特殊处理：如果对象是JSON字符串，先解析成JSON对象再获取属性
                        if (objValue instanceof String) {
                            String strValue = (String) objValue;
                            if (strValue.trim().startsWith("{") && strValue.trim().endsWith("}")) {
                                try {
                                    // 解析JSON字符串为JSONObject
                                    JSONObject jsonObj = JSONUtil.parseObj(strValue);
                                    // 使用路径获取嵌套属性值，支持多层级如byname.result
                                    variableValue = jsonObj.getByPath(propPath);
                                } catch (Exception e) {
                                    log.debug("Failed to parse JSON string or get property: " + path, e);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.debug("Failed to process JSON path: " + path, e);
                    }
                }

                // 如果上面的特殊处理没有获取到值，则使用标准JsonPath直接访问
                if (variableValue == null) {
                    String jsonPath = "$." + path;
                    try {
                        variableValue = JsonPath.read(BaseNode.getFlowData(context), jsonPath);
                    } catch (PathNotFoundException e) {
                        log.error("Failed to parse the parameter value: " + variableExpression, e);
                    }
                }

                // 如果成功获取到值，则替换文本中的变量表达式
                if (ObjUtil.isNotNull(variableValue)) {
                    // 全局替换(将文本中{*}/{*.*}替换为具体值)
                    JSONObject value = JSONUtil.createObj().set("value", variableValue);
                    if (value.get("value") instanceof String &&"post".equals(method.toLowerCase())){
                        textContent = textContent.replace(variableExpression, "\""+value.get("value")+"\"");
                    }else {
                        textContent = textContent.replace(variableExpression, value.getStr("value"));
                    }


                } else {
                    // 将无法解析的参数表达式赋值删除
                    Pattern pattern = Pattern.compile("\"[a-zA-Z0-9]+\"\\s*:\\s*\\{" + path + "},?\\s*");
                    Matcher match = pattern.matcher(textContent);
                    textContent = match.replaceAll("");
                }
            }
        }

        // 如果没有任何匹配且表达式为空，则返回null
        if (!matchFound && StrUtil.isEmpty(textContent)) {
            return null;
        }

        return textContent;
    }

    /**
     * 专门处理 PATCH 请求的方法
     */
    private JSONObject sendPatchRequest(Properties properties, Map<String, Object> context) {
        JSONObject result = new JSONObject();
        String url = properties.getUrl();
        log.info("发送HTTP请求: PATCH {}", url);

        try {
            // 创建 Java 11 HttpClient
            java.net.http.HttpClient client = java.net.http.HttpClient.newBuilder()
                    .followRedirects(java.net.http.HttpClient.Redirect.NORMAL)
                    .build();

            // 准备请求体
            String bodyContent = "{}";
            String contentType = "application/json";

            // 处理请求体
            if (CollUtil.isNotEmpty(properties.getBody())) {
                // 获取请求体类型和数据
                Map<String, Object> body = properties.getBody();
                String bodyType = ObjUtil.isNotNull(body.get("bodyType")) ? body.get("bodyType").toString() : "none";
                Object bodyData = body.get("bodyData");

                if (ObjUtil.isNotNull(bodyData) && !StrUtil.isEmpty(bodyData.toString())) {
                    switch (bodyType.toLowerCase()) {
                        case "json":
                            bodyContent = JSONUtil.toJsonStr(bodyData);
                            contentType = "application/json";
                            break;
                        case "raw-text":
                            bodyContent = bodyData.toString();
                            contentType = "text/plain";
                            break;
                        case "form-data":
                        case "x-www-form-urlencoded":
                            // 构建表单数据
                            Map<String, Object> formParams = new HashMap<>();
                            if (bodyData instanceof List) {
                                List<?> paramList = (List<?>) bodyData;
                                for (Object item : paramList) {
                                    Parameter param = BeanUtil.toBean(item, Parameter.class);
                                    if (ObjUtil.isNotNull(param.getValue())) {
                                        formParams.put(param.getName(), param.resolveValue(context));
                                    }
                                }
                            }
                            bodyContent = JSONUtil.toJsonStr(formParams);  // 简化处理，实际表单数据可能需要特殊编码
                            contentType = bodyType.equalsIgnoreCase("form-data") ?
                                    "multipart/form-data" : "application/x-www-form-urlencoded";
                            break;
                        default:
                            bodyContent = "{}";
                            break;
                    }
                }
            }

            // 构建请求 builder
            java.net.http.HttpRequest.Builder requestBuilder = java.net.http.HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .method("PATCH", java.net.http.HttpRequest.BodyPublishers.ofString(bodyContent))
                    .header("Content-Type", contentType);

            // 添加请求头
            if (CollectionUtil.isNotEmpty(properties.getHeaders())) {
                Map<String, String> headers = properties.getHeaders()
                        .stream()
                        .filter(parameter -> ObjUtil.isNotNull(parameter.getValue()))
                        .collect(Collectors.toMap(Parameter::getName, parameter -> parameter.resolveValue(context).toString()));
                if (CollUtil.isNotEmpty(headers)) {
                    headers.forEach(requestBuilder::header);
                    log.info("请求头: {}", headers);
                }
            }

            // 设置超时
            if (ObjUtil.isNotNull(properties.getTimeout()) && Integer.parseInt(properties.getTimeout()) > 0) {
                int timeoutMillis = Integer.parseInt(properties.getTimeout()) * 1000;
                requestBuilder.timeout(java.time.Duration.ofMillis(timeoutMillis));
                log.info("设置超时时间: {}毫秒", timeoutMillis);
            }

            // 发送请求
            java.net.http.HttpRequest request = requestBuilder.build();
            java.net.http.HttpResponse<String> response = client.send(request, java.net.http.HttpResponse.BodyHandlers.ofString());

            // 处理响应
            log.info("HTTP响应状态码: {}", response.statusCode());
            result.set("status", response.statusCode());

            // 记录响应头
            Map<String, List<String>> responseHeaders = response.headers().map();
            if (!responseHeaders.isEmpty()) {
                log.info("HTTP响应头: {}", responseHeaders);
            }

            // 处理响应体
            String body = response.body();
            if (body == null || body.isEmpty()) {
                log.warn("HTTP响应体为空");
                result.set("body", "");
            } else {
                // 如果响应体太长，只记录部分
                if (body.length() > 1000) {
                    log.info("HTTP请求响应(截断): {}", body.substring(0, 1000) + "...");
                } else {
                    log.info("HTTP请求响应: {}", body);
                }

                if (JSONUtil.isTypeJSONObject(body)) {
                    result.set("body", JSONUtil.parseObj(body, JSONConfig.create()
                            .setIgnoreNullValue(true)
                            .setIgnoreError(true)));
                } else if (JSONUtil.isTypeJSONArray(body)) {
                    result.set("body", JSONUtil.parseArray(body, JSONConfig.create()
                            .setIgnoreNullValue(true)
                            .setIgnoreError(true)));
                } else {
                    result.set("body", body);
                }
            }

            return result;
        } catch (Exception e) {
            log.error("HTTP PATCH请求执行异常: {}", e.getMessage(), e);
            throw new RuntimeException("HTTP PATCH请求执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 发送HTTP请求
     * @param properties
     * @return
     */
    private JSONObject sendRequest(Properties properties, Map<String, Object> context){
        // 构建HTTP请求
        HttpRequest request;
        String method = properties.getMethod().toUpperCase();
        switch (method) {
            case "GET":
                request = HttpRequest.get(properties.getUrl());
                break;
            case "POST":
                request = HttpRequest.post(properties.getUrl());
                break;
            case "PUT":
                request = HttpRequest.put(properties.getUrl());
                break;
            case "DELETE":
                request = HttpRequest.delete(properties.getUrl());
                break;
            case "PATCH":
                // 不使用 hutool 的 HttpRequest.patch，而是使用 Java 11 内置的 HTTP Client
                return sendPatchRequest(properties, context);
            default:
                throw new IllegalArgumentException(String.format("不支持的请求类型[%s]", method));
        }

        // 设置请求头
        if (CollectionUtil.isNotEmpty(properties.getHeaders())) {
            Map<String, String> headers = properties.getHeaders()
                    .stream()
                    .filter(parameter -> ObjUtil.isNotNull(parameter.getValue()))
                    .collect(Collectors.toMap(Parameter::getName, parameter -> parameter.resolveValue(context).toString()));
            if(CollUtil.isNotEmpty(headers)) {
                request.addHeaders(headers);
                log.info("请求头: {}", headers);
            }
        }

        // 设置请求参数
        if (CollectionUtil.isNotEmpty(properties.getParams())) {
            Map<String, Object> parameters = properties.getParams()
                    .stream()
                    .filter(parameter -> ObjUtil.isNotNull(parameter.getValue()))
                    .collect(Collectors.toMap(Parameter::getName, parameter -> parameter.resolveValue(context)));
            if(CollUtil.isNotEmpty(parameters)) {
                request.form(parameters);
                log.info("请求参数: {}", parameters);
            }
        }

        // 设置请求体
        if (CollUtil.isNotEmpty(properties.getBody())
                && !method.equals(HttpMethod.GET.name())
                && !method.equals(HttpMethod.DELETE.name())) {
            setRequestBody(request, properties.getBody(), context);
            log.info("请求体: {}", properties.getBody());
        }

        // 设置超时时间（单位：毫秒）
        if (ObjUtil.isNotNull(properties.getTimeout()) && Integer.parseInt(properties.getTimeout()) > 0) {
            int timeoutMillis = Integer.parseInt(properties.getTimeout()) * 1000;
            request.timeout(timeoutMillis);
            log.info("设置超时时间: {}毫秒", timeoutMillis);
        }

        log.info("发送HTTP请求: {} {}", method, properties.getUrl());
        //配置HttpRequest跟随重定向
        request.setFollowRedirects(true);

        JSONObject result = new JSONObject();

        // 执行请求
        try (HttpResponse response = request.execute()) {
            log.info("HTTP响应状态码: {}", response.getStatus());
            result.set("status", response.getStatus());

            // 记录响应头信息
            Map<String, List<String>> headers = response.headers();
            if (headers != null && !headers.isEmpty()) {
                log.info("HTTP响应头: {}", headers);
            }

            String body = response.body();
            if (body == null || body.isEmpty()) {
                log.warn("HTTP响应体为空");
                result.set("body", "");
            }

            // 如果响应体太长，只记录部分
            if (body.length() > 1000) {
                log.info("HTTP请求响应(截断): {}", body.substring(0, 1000) + "...");
            } else {
                log.info("HTTP请求响应: {}", body);
            }

            if (JSONUtil.isTypeJSONObject(body)){
                result.set("body", JSONUtil.parseObj(body, JSONConfig.create()
                        .setIgnoreNullValue(true)
                        .setIgnoreError(true)));
            } else if (JSONUtil.isTypeJSONArray(body)) {
                result.set("body", JSONUtil.parseArray(body, JSONConfig.create()
                        .setIgnoreNullValue(true)
                        .setIgnoreError(true)));
            } else {
                result.set("body", body);
            }

            return result;
        } catch (Exception e) {
            log.error("HTTP请求执行异常: {}", e.getMessage(), e);
            throw new RuntimeException("HTTP请求执行失败", e);
        }
    }

    /**
     * 设置HTTP请求体
     * @param request HTTP请求对象
     * @param body
     */
    private void setRequestBody(HttpRequest request, Map<String,Object> body, Map<String, Object> context) {
        // 获取请求体类型
        String bodyType = ObjUtil.isNotNull(body.get("bodyType")) ? body.get("bodyType").toString() : "none";
        // 获取请求体数据
        Object bodyData = body.get("bodyData");
        if(ObjUtil.isNull(bodyData) || StrUtil.isEmpty(bodyData.toString())) return;
        switch (bodyType.toLowerCase()) {
            case "none":
                break;
            case "json":
                request.body(JSONUtil.toJsonStr(bodyData), "application/json");
                break;
            case "form-data":
                Map<String, Object> formData = buildFormDataParam(bodyData, context);
                request.contentType("multipart/form-data");
                request.form(formData);
                break;
            case "x-www-form-urlencoded":
                Map<String, Object> urlEncodedParam = buildUrlEncodedParam(bodyData, context);
                request.contentType("application/x-www-form-urlencoded");
                request.form(urlEncodedParam);
                break;
            case "raw-text":
                if (!(bodyData instanceof String)) {
                    throw new IllegalArgumentException("raw text类型的请求体必须是String类型");
                }
                request.body((String) bodyData);
                request.contentType("text/plain");
                break;
            case "binary":
                FileInfo fileInfo = buildBinaryParam(bodyData, context);
                request.header("fileName",  fileInfo.getFileName()); // 将文件名放入请求头
                request.body((byte[]) fileInfo.getFile());
                request.contentType("application/octet-stream");
                break;
            default:
                throw new IllegalArgumentException("不支持的内容类型：" + bodyType);
        }
    }

    /**
     * 构建Binary
     * @param bodyData
     * @param context
     * @return
     */
    private FileInfo buildBinaryParam(Object bodyData, Map<String, Object> context){
        if(!(bodyData instanceof String)){
            throw new IllegalArgumentException("binary类型的请求体数据必须是String类型");
        }
        // 解析引用参数类型例如：{data.*}
        Parameter parameter = new Parameter();
        parameter.setValue(bodyData);
        String filePath = parameter.resolveValue(context).toString();
        FileInfo fileInfo = new FileInfo();
        fileInfo.setFileName(filePath.substring(filePath.lastIndexOf("/") + 1));
        fileInfo.setFile(getFileByte(filePath.toString()));
        return fileInfo;
    }

    /**
     * 构建form-data参数
     * @param bodyData
     * @param context
     * @return
     */
    private Map<String, Object> buildFormDataParam(Object bodyData, Map<String, Object> context) {
        if (!(bodyData instanceof List)) {
            throw new IllegalArgumentException("form-data类型的请求体数据必须是Array类型");
        }
        List<Parameter> parameters = ((List<?>) bodyData).stream()
                .map(item -> BeanUtil.toBean(item, Parameter.class))
                .filter(parameter -> ObjUtil.isNotNull(parameter.getValue()))
                .collect(Collectors.toList());
        return parameters.stream().collect(Collectors.toMap(Parameter::getName, parameter -> {
            if("file".equalsIgnoreCase(parameter.getType())){ // 文件类型处理
                // 获取解析后的参数值
                String path = parameter.resolveValue(context).toString();
                InputStream fileInputStream = getFileInputStream(path);
                return new InputStreamResource(fileInputStream,
                        path.substring(path.lastIndexOf("/")  + 1));
            }else
                return parameter.resolveValue(context); // 支持引用参数值
        }));
    }

    /**
     * 构建x-www-form-urlencoded参数
     * @param bodyData 请求体数据
     * @param context 上下文
     * @return 处理后的参数Map
     */
    private Map<String, Object> buildUrlEncodedParam(Object bodyData, Map<String, Object> context) {
        if (!(bodyData instanceof List)) {
            throw new IllegalArgumentException("x-www-form-urlencoded类型的请求体必须是Array类型");
        }
        List<Parameter> parameters = ((List<?>) bodyData).stream()
                .map(item -> BeanUtil.toBean(item, Parameter.class))
                .filter(parameter -> ObjUtil.isNotNull(parameter.getValue()))
                .collect(Collectors.toList());
        return parameters.stream()
                .collect(Collectors.toMap(
                        Parameter::getName,
                        parameter -> parameter.resolveValue(context)
                ));
    }

    /**
     * 获取文件字节数组
     * @param filePath
     * @return
     */
    private byte[] getFileByte(String filePath){
        FileStorageService service = SpringContextUtils.getBean(FileStorageService.class);
        try (InputStream inputStream = service.getFile(filePath)) {
            byte[] buffer = new byte[8192];
            int bytesRead;
            ByteArrayOutputStream output = new ByteArrayOutputStream();
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                output.write(buffer, 0, bytesRead);
            }
            return output.toByteArray();
        } catch (Exception e) {
            throw new RuntimeException("读取文件失败", e);
        }
    }

    /**
     * 获取文件流
     * @param filePath
     * @return
     */
    private InputStream getFileInputStream(String filePath) {
        FileStorageService service = SpringContextUtils.getBean(FileStorageService.class);
        try {
            return service.getFile(filePath);
        } catch (Exception e) {
            throw new RuntimeException("读取文件失败", e);
        }
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    @Data
    @NoArgsConstructor
    public static class Properties {
        private String method; // 请求类型
        private String url; // 请求地址
        private List<Parameter> headers; // 请求头
        private List<Parameter> params; // 请求参数
        private Map<String, Object> body; // 请求体
        private String ignoreError; // 异常忽略
        private String timeout; // 超时时间
        private String retry; // 重试次数
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FileInfo {
        private String fileName; // 文件名
        private Object file;
    }

    @Override
    public String desc() {
        return "HTTP节点";
    }

} 