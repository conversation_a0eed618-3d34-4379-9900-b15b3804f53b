package com.icarus.common.cotnode.ab.crawler.service;

import cn.hutool.core.util.StrUtil;
import com.icarus.common.cotnode.ab.crawler.dto.BrowserResult;
import com.microsoft.playwright.*;
import com.microsoft.playwright.options.LoadState;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import java.io.File;
import java.lang.reflect.Method;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.ReentrantLock;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Playwright浏览器服务
 * 负责浏览器的按需初始化、页面管理和基本操作
 * 优化版本：懒加载初始化，空闲自动释放，可配置过期时间
 */
@Log4j2
@Service
public class PlaywrightBrowserService {

    // 浏览器实例
    private volatile Playwright playwright;
    private volatile Browser browser;
    private final Map<String, BrowserContext> contexts = new ConcurrentHashMap<>();
    private final Map<String, Page> pages = new ConcurrentHashMap<>();

    // 下载事件监听相关
    private final Map<String, List<Map<String, Object>>> downloadEvents = new ConcurrentHashMap<>();

    // jQuery选择器适配相关的正则表达式
    private static final Pattern CONTAINS_PATTERN = Pattern.compile(":(contains|has-text)\\((['\"])(.*?)\\2\\)");
    private static final Pattern FIRST_PATTERN = Pattern.compile(":first");
    private static final Pattern LAST_PATTERN = Pattern.compile(":last");
    private static final Pattern EQ_PATTERN = Pattern.compile(":eq\\((\\d+)\\)");

    // 懒加载和资源管理相关
    private final ReentrantLock initLock = new ReentrantLock();
    private volatile boolean isInitialized = false;
    private final AtomicLong lastUsedTime = new AtomicLong(0);

    // 可配置的空闲超时时间（分钟）
    @Value("${crawler.playwright.idle-timeout:5}")
    private int idleTimeoutMinutes;

    // 定时检查间隔（分钟）
    @Value("${crawler.playwright.check-interval:2}")
    private int checkIntervalMinutes;

    // 可配置的下载路径
    @Value("${crawler.playwright.download-path:${java.io.tmpdir}/playwright-downloads}")
    private String downloadPath;

    // 可配置的临时文件路径
    @Value("${crawler.playwright.temp-path:${java.io.tmpdir}/crawler_files}")
    private String tempPath;

    // 是否启用临时文件清理
    @Value("${crawler.playwright.cleanup-temp-files:true}")
    private boolean cleanupTempFiles;

    // 浏览器安装目录
    @Value("${crawler.playwright.browsers-path:}")
    private String browsersPath;

    /**
     * 懒加载初始化浏览器
     * 只有在实际需要使用时才初始化
     */
    private void ensureBrowserInitialized() {
        if (!isInitialized) {
            initLock.lock();
            try {
                if (!isInitialized) {
                    log.info("检测到爬虫任务，开始按需初始化 Playwright 浏览器...");
                    long startTime = System.currentTimeMillis();

                    // 设置浏览器安装路径（如果配置了的话）
                    setBrowsersPath();

                    this.playwright = Playwright.create();
                    this.browser = playwright.chromium().launch(new BrowserType.LaunchOptions()
                            .setHeadless(true)
                    );

                    long initTime = System.currentTimeMillis() - startTime;
                    log.info("Playwright 浏览器初始化成功，耗时: {}ms", initTime);

                    // 显示浏览器安装信息
                    showBrowserInstallationInfo();

                    // 清理旧的临时文件（如果启用）
                    if (cleanupTempFiles) {
                        cleanupPlaywrightTempFiles();
                    }

                    isInitialized = true;
                    updateLastUsedTime();
                }
            } catch (Exception e) {
                log.error("初始化 Playwright 浏览器失败", e);
                throw new RuntimeException("Playwright 浏览器初始化失败，爬虫功能不可用", e);
            } finally {
                initLock.unlock();
            }
        } else {
            updateLastUsedTime();
        }
    }

    /**
     * 更新最后使用时间，重置过期计时器
     */
    private void updateLastUsedTime() {
        long currentTime = System.currentTimeMillis();
        lastUsedTime.set(currentTime);
        log.info("更新浏览器最后使用时间: {}", new java.util.Date(currentTime));
    }

    /**
     * 检查是否应该释放资源
     */
    public boolean shouldReleaseResources() {
        if (!isInitialized) {
            return false;
        }

        long idleTime = System.currentTimeMillis() - lastUsedTime.get();
        long idleTimeoutMs = idleTimeoutMinutes * 60 * 1000L;
        return idleTime > idleTimeoutMs;
    }

    /**
     * 定时检查并释放空闲资源
     * 根据配置的检查间隔定时执行
     */
    @Scheduled(fixedRateString = "#{${crawler.playwright.check-interval:2} * 60 * 1000}")
    public void checkAndReleaseIdleResources() {
        if (shouldReleaseResources()) {
            long idleMinutes = (System.currentTimeMillis() - lastUsedTime.get()) / 60000;
            log.info("检测到浏览器空闲超过{}分钟（配置阈值:{}分钟），自动释放资源以节省内存",
                    idleMinutes, idleTimeoutMinutes);
            releaseBrowserResources();
        }
    }

    /**
     * 手动释放浏览器资源
     * 可以在没有爬虫任务时调用以释放内存
     */
    public void releaseBrowserResources() {
        if (!isInitialized) {
            log.info("浏览器未初始化，无需释放资源");
            return;
        }

        initLock.lock();
        try {
            if (isInitialized) {
                log.info("开始释放 Playwright 浏览器资源...");
                long startTime = System.currentTimeMillis();

                cleanupAllResources();

                long cleanupTime = System.currentTimeMillis() - startTime;
                log.info("Playwright 浏览器资源释放完成，耗时: {}ms", cleanupTime);

                isInitialized = false;
                lastUsedTime.set(0);
            }
        } finally {
            initLock.unlock();
        }
    }

    /**
     * 清理所有资源的内部方法
     */
    private void cleanupAllResources() {
        try {
            // 关闭所有页面
            for (Page page : pages.values()) {
                try {
                    if (!page.isClosed()) {
                        page.close();
                    }
                } catch (Exception e) {
                    log.info("关闭页面失败", e);
                }
            }
            pages.clear();

            // 关闭所有上下文
            for (BrowserContext context : contexts.values()) {
                try {
                    context.close();
                } catch (Exception e) {
                    log.info("关闭浏览器上下文失败", e);
                }
            }
            contexts.clear();

            // 清理下载事件
            downloadEvents.clear();

            // 关闭浏览器
            if (browser != null) {
                browser.close();
                browser = null;
            }

            // 关闭 Playwright
            if (playwright != null) {
                playwright.close();
                playwright = null;
            }

            // 清理临时文件（如果启用）
            if (cleanupTempFiles) {
                cleanupPlaywrightTempFiles();
            }

        } catch (Exception e) {
            log.error("清理 Playwright 浏览器资源失败", e);
        }
    }

    /**
     * 获取浏览器状态信息
     */
    public Map<String, Object> getBrowserStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("initialized", isInitialized);
        status.put("pageCount", pages.size());
        status.put("contextCount", contexts.size());
        status.put("idleTimeoutMinutes", idleTimeoutMinutes);
        status.put("checkIntervalMinutes", checkIntervalMinutes);

        if (isInitialized) {
            long idleTime = System.currentTimeMillis() - lastUsedTime.get();
            status.put("idleTimeMinutes", idleTime / 60000);
            status.put("shouldRelease", shouldReleaseResources());
            status.put("lastUsedTime", new java.util.Date(lastUsedTime.get()));
        } else {
            status.put("idleTimeMinutes", 0);
            status.put("shouldRelease", false);
            status.put("lastUsedTime", null);
        }

        return status;
    }

    /**
     * Spring 容器销毁时的清理方法
     */
    @PreDestroy
    public void destroy() {
        log.info("Spring 容器销毁，清理 Playwright 浏览器资源...");
        releaseBrowserResources();
    }



    /**
     * 导航到指定URL
     *
     * @param url 目标URL
     * @param headers HTTP请求头
     * @param timeout 超时设置(毫秒)
     * @return 浏览结果
     */
    public BrowserResult navigate(String url, Map<String, String> headers, Integer timeout) {
        try {
            // 懒加载初始化浏览器，并更新使用时间
            ensureBrowserInitialized();

            String pageId = "default_page";
            Page page = getOrCreatePage(pageId, headers);

            if (timeout != null && timeout > 0) {
                page.setDefaultTimeout(timeout);
            }

            page.navigate(url);
            waitForLoadSafely(page, LoadState.DOMCONTENTLOADED, timeout);

            // 导航成功后再次更新使用时间
            updateLastUsedTime();

            return BrowserResult.success()
                    .put("pageId", pageId)
                    .put("title", page.title())
                    .put("url", page.url());

        } catch (Exception e) {
            log.error("浏览器导航失败: {}", url, e);
            return BrowserResult.error("导航失败: " + e.getMessage());
        }
    }
    
    /**
     * 在iframe内操作
     * 
     * @param pageId 页面ID
     * @param iframeSelector iframe选择器
     * @return 操作结果
     */
    public BrowserResult switchToIframe(String pageId, String iframeSelector) {
        Page page = getPage(pageId);
        if (page == null) {
            return BrowserResult.error("页面不存在: " + pageId);
        }
        
        if (StrUtil.isEmpty(iframeSelector)) {
            return BrowserResult.error("iframe选择器不能为空");
        }
        
        try {
            ElementHandle frameElement = findElementWithAdaptedSelector(page, iframeSelector);
            if (frameElement == null) {
                return BrowserResult.error("未找到iframe元素: " + iframeSelector);
            }
            
            Frame frame = frameElement.contentFrame();
            if (frame == null) {
                return BrowserResult.error("无法获取iframe内容");
            }
            
            // 保存iframe引用，可通过pageId+"_iframe"来访问
            // 这里简化处理，实际可能需要更复杂的iframe管理机制
            
            return BrowserResult.success()
                    .put("frameId", pageId + "_iframe")
                    .put("message", "已切换到iframe: " + iframeSelector);
            
        } catch (Exception e) {
            log.error("切换到iframe失败: {}", iframeSelector, e);
            return BrowserResult.error("切换iframe失败: " + e.getMessage());
        }
    }

    /**
     * 获取页面内容
     */
    public BrowserResult getPageContent(String pageId) {
        ensureBrowserInitialized();

        Page page = getPage(pageId);
        if (page == null) {
            return BrowserResult.error("页面不存在: " + pageId);
        }

        try {
            String content = page.content();
            updateLastUsedTime(); // 更新使用时间

            return BrowserResult.success()
                    .put("content", content)
                    .put("title", page.title())
                    .put("url", page.url());
        } catch (Exception e) {
            log.error("获取页面内容失败", e);
            return BrowserResult.error("获取页面内容失败: " + e.getMessage());
        }
    }

    /**
     * 查找元素
     */
    public BrowserResult findElements(String pageId, String selector) {
        ensureBrowserInitialized();

        Page page = getPage(pageId);
        if (page == null) {
            return BrowserResult.error("页面不存在: " + pageId);
        }

        if (StrUtil.isEmpty(selector)) {
            return BrowserResult.error("选择器不能为空");
        }

        try {
            ElementHandle[] elements = findElementsWithAdaptedSelector(page, selector);
            List<Map<String, String>> elementInfos = new ArrayList<>();

            for (int i = 0; i < elements.length; i++) {
                ElementHandle element = elements[i];
                try {
                    Map<String, String> info = new HashMap<>();
                    info.put("index", String.valueOf(i));

                    // 安全获取文本内容
                    String text = "";
                    try {
                        text = element.textContent();
                    } catch (Exception e) {
                        log.info("获取元素文本失败: {}", e.getMessage());
                    }
                    info.put("text", text);

                    // 获取href属性 - 优先从元素本身获取，如果没有则从子元素中查找
                    String href = "";
                    try {
                        href = element.getAttribute("href");
                        if (StrUtil.isEmpty(href)) {
                            // 尝试在元素内部查找a标签
                            ElementHandle linkElement = element.querySelector("a[href]");
                            if (linkElement != null) {
                                href = linkElement.getAttribute("href");
                                log.info("从子元素中找到链接: {}", href);
                            }
                        }
                    } catch (Exception e) {
                        log.info("获取元素href失败: {}", e.getMessage());
                    }
                    info.put("href", href);

                    // 获取src属性
                    String src = "";
                    try {
                        src = element.getAttribute("src");
                        if (StrUtil.isEmpty(src)) {
                            // 尝试在元素内部查找img标签
                            ElementHandle imgElement = element.querySelector("img[src]");
                            if (imgElement != null) {
                                src = imgElement.getAttribute("src");
                            }
                        }
                    } catch (Exception e) {
                        log.info("获取元素src失败: {}", e.getMessage());
                    }
                    info.put("src", src);

                    // 添加元素标签名，便于调试
                    String tagName = "unknown";
                    try {
                        tagName = element.evaluate("el => el.tagName").toString().toLowerCase();
                    } catch (Exception e) {
                        log.info("获取元素标签名失败: {}", e.getMessage());
                    }
                    info.put("tagName", tagName);

                    elementInfos.add(info);

                    log.info("找到元素 {}: 标签={}, 文本={}, 链接={}",
                        i, info.get("tagName"),
                        info.get("text").length() > 50 ? info.get("text").substring(0, 50) + "..." : info.get("text"),
                        info.get("href"));
                } catch (Exception e) {
                    log.error("处理元素 {} 时发生错误: {}", i, e.getMessage());
                    continue;
                }
            }

            log.info("使用选择器 {} 找到 {} 个元素", selector, elements.length);
            updateLastUsedTime(); // 更新使用时间

            return BrowserResult.success()
                    .put("elements", elementInfos)
                    .put("count", elements.length);
        } catch (Exception e) {
            log.error("查找元素失败: {}", selector, e);
            return BrowserResult.error("查找元素失败: " + e.getMessage());
        }
    }

    /**
     * 点击元素
     */
    public BrowserResult clickElement(String pageId, String selector, Integer index) {
        Page page = getPage(pageId);
        if (page == null) {
            return BrowserResult.error("页面不存在: " + pageId);
        }

        try {
            ElementHandle element = null;
            
            if (StrUtil.isNotEmpty(selector)) {
                element = findElementWithAdaptedSelector(page, selector);
            } else if (index != null && index >= 0) {
                ElementHandle[] elements = page.querySelectorAll("a, button, input[type='button'], input[type='submit']").toArray(new ElementHandle[0]);
                if (index < elements.length) {
                    element = elements[index];
                }
            }
            
            if (element == null) {
                return BrowserResult.error("未找到目标元素");
            }
            
            if (!element.isVisible()) {
                return BrowserResult.error("目标元素不可见");
            }
            
            element.click();
            waitForLoadSafely(page, LoadState.DOMCONTENTLOADED, 30000);
            
            return BrowserResult.success()
                    .put("title", page.title())
                    .put("url", page.url());
                    
        } catch (Exception e) {
            log.error("点击元素失败", e);
            return BrowserResult.error("点击元素失败: " + e.getMessage());
        }
    }

    /**
     * 点击列表项元素 (支持clickListPath选择器)
     * 
     * @param pageId 页面ID
     * @param listSelector 列表选择器
     * @param index 点击第几项(从0开始)
     * @return 点击结果
     */
    public BrowserResult clickListItem(String pageId, String listSelector, int index) {
        Page page = getPage(pageId);
        if (page == null) {
            return BrowserResult.error("页面不存在: " + pageId);
        }
        
        if (StrUtil.isEmpty(listSelector)) {
            return BrowserResult.error("列表选择器不能为空");
        }
        
        try {
            ElementHandle[] listItems = findElementsWithAdaptedSelector(page, listSelector);
            if (listItems.length == 0) {
                return BrowserResult.error("未找到列表项: " + listSelector);
            }
            
            if (index >= listItems.length) {
                return BrowserResult.error("索引超出范围: 最大索引为" + (listItems.length - 1) + "，请求索引为" + index);
            }
            
            // 获取列表项
            ElementHandle item = listItems[index];

            // 尝试获取列表项中的链接元素
            ElementHandle targetElement = item.querySelector("a");
            if (targetElement == null) {
                targetElement = item; // 如果没有找到链接，直接点击列表项
            }

            // 增强的点击逻辑
            try {
                log.info("准备点击元素，索引: {}", index);

                // 1. 滚动到元素位置
                targetElement.scrollIntoViewIfNeeded();
                Thread.sleep(300); // 等待滚动完成

                // 2. 检查元素是否在视口内
                Boolean isInViewport = (Boolean) targetElement.evaluate("el => {\n" +
                    "  const rect = el.getBoundingClientRect();\n" +
                    "  return rect.top >= 0 && rect.left >= 0 && \n" +
                    "         rect.bottom <= window.innerHeight && \n" +
                    "         rect.right <= window.innerWidth;\n" +
                    "}");

                if (!isInViewport) {
                    log.info("元素不在视口内，强制滚动到中心");
                    targetElement.evaluate("el => el.scrollIntoView({behavior: 'auto', block: 'center'})");
                    Thread.sleep(500);
                }

                // 3. 等待一下让页面稳定
                Thread.sleep(200);

                // 4. 尝试点击
                targetElement.click(new ElementHandle.ClickOptions().setTimeout(5000));
                log.info("点击成功");

            } catch (Exception clickError) {
                log.error("常规点击失败，尝试 JavaScript 点击: {}", clickError.getMessage());

                // 备选方案：使用 JavaScript 点击
                try {
                    targetElement.evaluate("el => el.click()");
                    log.info("JavaScript 点击成功");
                } catch (Exception jsError) {
                    log.error("JavaScript 点击也失败: {}", jsError.getMessage());
                    throw clickError; // 抛出原始错误
                }
            }
            
            waitForLoadSafely(page, LoadState.DOMCONTENTLOADED, 30000);
            
            return BrowserResult.success()
                    .put("title", page.title())
                    .put("url", page.url())
                    .put("clickedIndex", index);
                    
        } catch (Exception e) {
            log.error("点击列表项失败: {}[{}]", listSelector, index, e);
            return BrowserResult.error("点击列表项失败: " + e.getMessage());
        }
    }
    
    /**
     * 点击下一页 (支持nextPagePath选择器)
     * 
     * @param pageId 页面ID
     * @param nextPageSelector 下一页选择器
     * @return 操作结果
     */
    public BrowserResult clickNextPage(String pageId, String nextPageSelector) {
        Page page = getPage(pageId);
        if (page == null) {
            return BrowserResult.error("页面不存在: " + pageId);
        }

        if (StrUtil.isEmpty(nextPageSelector)) {
            return BrowserResult.error("下一页选择器不能为空");
        }

        log.info("clickNextPage 接收到选择器: 长度={}, 内容=[{}], 字节数组={}",
                nextPageSelector.length(), nextPageSelector,
                java.util.Arrays.toString(nextPageSelector.getBytes()));

        try {
            ElementHandle nextPageElement = findElementWithAdaptedSelector(page, nextPageSelector);
            if (nextPageElement == null) {
                return BrowserResult.error("未找到下一页元素，可能已经是最后一页");
            }
            
            // 检查元素是否可见
            if (!nextPageElement.isVisible()) {
                return BrowserResult.error("下一页元素不可见");
            }
            
            // 获取当前URL和页码用于比较
            String beforeUrl = page.url();
            
            // 点击下一页
            nextPageElement.click();
            waitForLoadSafely(page, LoadState.DOMCONTENTLOADED, 30000);
            
            // 检查URL是否发生变化
            String afterUrl = page.url();
            boolean pageChanged = !beforeUrl.equals(afterUrl);
            
            return BrowserResult.success()
                    .put("title", page.title())
                    .put("url", page.url())
                    .put("pageChanged", pageChanged)
                    .put("message", pageChanged ? "已成功翻到下一页" : "页面URL未变化，请检查翻页是否成功");
                    
        } catch (Exception e) {
            log.error("点击下一页失败: {}", nextPageSelector, e);
            return BrowserResult.error("点击下一页失败: " + e.getMessage());
        }
    }

    /**
     * 滚动页面
     */
    public BrowserResult scrollPage(String pageId, String direction, Integer distance) {
        Page page = getPage(pageId);
        if (page == null) {
            return BrowserResult.error("页面不存在: " + pageId);
        }

        try {
            int scrollDistance = distance != null ? distance : 0;
            
            if (scrollDistance == 0) {
                if ("down".equalsIgnoreCase(direction)) {
                    page.evaluate("window.scrollBy(0, window.innerHeight)");
                } else {
                    page.evaluate("window.scrollBy(0, -window.innerHeight)");
                }
            } else {
                if ("down".equalsIgnoreCase(direction)) {
                    page.evaluate("window.scrollBy(0, " + scrollDistance + ")");
                } else {
                    page.evaluate("window.scrollBy(0, -" + scrollDistance + ")");
                }
            }
            
            return BrowserResult.success().put("message", "页面已" + direction + "滚动");
        } catch (Exception e) {
            log.error("滚动页面失败", e);
            return BrowserResult.error("滚动页面失败: " + e.getMessage());
        }
    }

    /**
     * 提取文本内容 (支持getTitle、getPubTime、getContent选择器)
     * 
     * @param pageId 页面ID
     * @param selector CSS选择器
     * @return 提取结果
     */
    public BrowserResult extractText(String pageId, String selector) {
        Page page = getPage(pageId);
        if (page == null) {
            return BrowserResult.error("页面不存在: " + pageId);
        }

        try {
            String text;
            // 如果选择器为空，提取整个页面文本
            if (StrUtil.isEmpty(selector)) {
                text = page.textContent("body");
            } else {
                // 处理多选择器情况 (以逗号分隔)
                if (selector.contains(",")) {
                    String[] selectors = selector.split(",");
                    for (String sel : selectors) {
                        sel = sel.trim();
                        try {
                            // 尝试每个选择器
                            ElementHandle element = findElementWithAdaptedSelector(page, sel);
                            if (element != null) {
                                text = element.textContent();
                                if (StrUtil.isNotEmpty(text)) {
                                    return BrowserResult.success()
                                            .put("text", text)
                                            .put("selector", sel);
                                }
                            }
                        } catch (Exception e) {
                            log.info("使用选择器{}提取文本失败，尝试下一个", sel);
                        }
                    }
                    // 所有选择器都失败，返回空字符串
                    return BrowserResult.success().put("text", "");
                } else {
                    // 单个选择器情况
                    ElementHandle element = findElementWithAdaptedSelector(page, selector);
                    text = element != null ? element.textContent() : "";
                }
            }
            
            return BrowserResult.success().put("text", text);
        } catch (Exception e) {
            log.error("提取文本失败: {}", selector, e);
            return BrowserResult.error("提取文本失败: " + e.getMessage());
        }
    }
    
    /**
     * 提取内容HTML (支持getContent选择器)
     * 
     * @param pageId 页面ID
     * @param selector CSS选择器
     * @return 提取结果
     */
    public BrowserResult extractHtml(String pageId, String selector) {
        Page page = getPage(pageId);
        if (page == null) {
            return BrowserResult.error("页面不存在: " + pageId);
        }

        try {
            String html;
            // 如果选择器为空，提取整个页面HTML
            if (StrUtil.isEmpty(selector)) {
                html = page.content();
            } else {
                // 处理多选择器情况 (以逗号分隔)
                if (selector.contains(",")) {
                    String[] selectors = selector.split(",");
                    for (String sel : selectors) {
                        sel = sel.trim();
                        try {
                            String evalResult = page.evaluate("selector => { const el = document.querySelector(selector); return el ? el.innerHTML : ''; }", sel).toString();
                            if (StrUtil.isNotEmpty(evalResult)) {
                                return BrowserResult.success()
                                        .put("html", evalResult)
                                        .put("selector", sel);
                            }
                        } catch (Exception e) {
                            log.info("使用选择器{}提取HTML失败，尝试下一个", sel);
                        }
                    }
                    // 所有选择器都失败，返回空字符串
                    return BrowserResult.success().put("html", "");
                } else {
                    // 单个选择器情况
                    html = page.evaluate("selector => { const el = document.querySelector(selector); return el ? el.innerHTML : ''; }", selector).toString();
                }
            }
            
            return BrowserResult.success().put("html", html);
        } catch (Exception e) {
            log.error("提取HTML内容失败: {}", selector, e);
            return BrowserResult.error("提取HTML内容失败: " + e.getMessage());
        }
    }
    
    /**
     * 提取附件链接 (支持getFile选择器)
     * 
     * @param pageId 页面ID
     * @param fileSelector 附件选择器
     * @return 提取结果
     */
    public BrowserResult extractFiles(String pageId, String fileSelector) {
        Page page = getPage(pageId);
        if (page == null) {
            return BrowserResult.error("页面不存在: " + pageId);
        }
        
        if (StrUtil.isEmpty(fileSelector)) {
            return BrowserResult.error("附件选择器不能为空");
        }

        try {
            List<Map<String, Object>> files = new ArrayList<>();
            log.info("开始提取附件，选择器: {}", fileSelector);

            // 处理多选择器情况 (以逗号分隔)
            if (fileSelector.contains(",")) {
                String[] selectors = fileSelector.split(",");
                log.info("检测到多选择器，共{}个", selectors.length);
                for (String sel : selectors) {
                    try {
                        sel = sel.trim();
                        log.info("使用选择器: {}", sel);
                        ElementHandle[] elements = findElementsWithAdaptedSelector(page, sel);
                        log.info("选择器 {} 找到{}个元素", sel, elements.length);

                        for (ElementHandle element : elements) {
                            Map<String, Object> fileInfo = extractFileInfo(element);
                            if (fileInfo != null && fileInfo.containsKey("url")) {
                                fileInfo.put("selector", sel);
                                files.add(fileInfo);
                                log.info("提取到附件: {} -> {}", fileInfo.get("text"), fileInfo.get("url"));
                            }
                        }
                    } catch (Exception e) {
                        log.error("使用选择器{}提取附件失败", sel, e);
                    }
                }
            } else {
                // 单选择器
                log.info("使用单选择器: {}", fileSelector);
                ElementHandle[] elements = findElementsWithAdaptedSelector(page, fileSelector);
                log.info("选择器 {} 找到{}个元素", fileSelector, elements.length);

                for (ElementHandle element : elements) {
                    Map<String, Object> fileInfo = extractFileInfo(element);
                    if (fileInfo != null && fileInfo.containsKey("url")) {
                        fileInfo.put("selector", fileSelector);
                        files.add(fileInfo);
                        log.info("提取到附件: {} -> {}", fileInfo.get("text"), fileInfo.get("url"));
                    }
                }
            }
            
            return BrowserResult.success()
                    .put("files", files)
                    .put("count", files.size());
                    
        } catch (Exception e) {
            log.error("提取附件失败: {}", fileSelector, e);
            return BrowserResult.error("提取附件失败: " + e.getMessage());
        }
    }
    
    /**
     * 从元素中提取文件信息
     */
    private Map<String, Object> extractFileInfo(ElementHandle element) {
        try {
            Map<String, Object> fileInfo = new HashMap<>();

            // 尝试获取href或src属性作为URL
            String url = element.getAttribute("href");
            if (StrUtil.isEmpty(url)) {
                url = element.getAttribute("src");
            }

            if (StrUtil.isEmpty(url)) {
                log.info("元素没有href或src属性");
                return null;
            }

            String text = element.textContent();
            fileInfo.put("url", url);
            fileInfo.put("text", text);

            // 尝试获取文件类型
            String fileType = "";
            if (url.contains(".")) {
                fileType = url.substring(url.lastIndexOf(".") + 1);
                if (fileType.contains("?")) {
                    fileType = fileType.substring(0, fileType.indexOf("?"));
                }
            }
            fileInfo.put("type", fileType.toLowerCase());

            log.info("提取文件信息: 文本={}, URL={}, 类型={}", text, url, fileType);
            return fileInfo;
        } catch (Exception e) {
            log.info("提取文件信息失败", e);
            return null;
        }
    }

    /**
     * 通过下载事件检测附件链接
     * 这个方法会模拟点击页面上的链接来触发下载事件，从而检测附件
     *
     * @param pageId 页面ID
     * @param linkSelector 链接选择器（可选，如果为空则检测所有链接）
     * @return 检测结果
     */
    public BrowserResult detectAttachmentsByDownload(String pageId, String linkSelector) {
        Page page = getPage(pageId);
        if (page == null) {
            return BrowserResult.error("页面不存在: " + pageId);
        }

        try {
            // 清空之前的下载事件
            List<Map<String, Object>> events = downloadEvents.get(pageId);
            if (events != null) {
                events.clear();
            }

            // 如果没有指定选择器，使用通用的链接选择器
            String selector = StrUtil.isNotEmpty(linkSelector) ? linkSelector : "a[href]";

            // 查找所有链接
            ElementHandle[] linkElements = findElementsWithAdaptedSelector(page, selector);

            List<Map<String, Object>> detectedFiles = new ArrayList<>();

            for (ElementHandle element : linkElements) {
                try {
                    String href = element.getAttribute("href");
                    String text = element.textContent();

                    if (StrUtil.isEmpty(href) || href.startsWith("#") || href.startsWith("javascript:")) {
                        continue; // 跳过无效链接
                    }

                    // 记录点击前的下载事件数量
                    int beforeCount = events != null ? events.size() : 0;

                    // 模拟点击链接（使用JavaScript避免页面跳转）
                    try {
                        page.evaluate("element => { " +
                                "const event = new MouseEvent('click', { " +
                                "  bubbles: true, " +
                                "  cancelable: true, " +
                                "  view: window " +
                                "}); " +
                                "element.dispatchEvent(event); " +
                                "}", element);

                        // 等待一小段时间让下载事件触发
                        Thread.sleep(500);

                        // 检查是否有新的下载事件
                        int afterCount = events != null ? events.size() : 0;
                        if (afterCount > beforeCount) {
                            // 发现了下载事件，说明这是一个附件链接
                            Map<String, Object> file = new HashMap<>();
                            file.put("url", href);
                            file.put("text", StrUtil.isNotEmpty(text) ? text.trim() : "");
                            file.put("detectionMethod", "download_event");

                            // 从下载事件中获取文件名
                            Map<String, Object> downloadEvent = events.get(afterCount - 1);
                            if (downloadEvent.containsKey("suggestedFilename")) {
                                file.put("suggestedFilename", downloadEvent.get("suggestedFilename"));
                            }

                            detectedFiles.add(file);
                            log.info("通过下载事件检测到附件: 文本={}, URL={}", text, href);
                        }

                    } catch (Exception e) {
                        log.info("点击链接失败: {}", href, e);
                    }

                } catch (Exception e) {
                    log.error("处理链接元素失败", e);
                }
            }

            return BrowserResult.success()
                    .put("files", detectedFiles)
                    .put("downloadEvents", events != null ? new ArrayList<>(events) : new ArrayList<>());

        } catch (Exception e) {
            log.error("通过下载事件检测附件失败", e);
            return BrowserResult.error("检测附件失败: " + e.getMessage());
        }
    }

    /**
     * 确保下载目录存在
     */
    private void ensureDownloadDirectoryExists() {
        try {
            Path downloadDir = Paths.get(downloadPath);
            if (!Files.exists(downloadDir)) {
                Files.createDirectories(downloadDir);
                log.info("创建Playwright下载目录: {}", downloadPath);
            }
        } catch (Exception e) {
            log.error("创建下载目录失败: {}, 使用默认路径", downloadPath, e);
        }
    }

    /**
     * 获取配置的下载路径
     * @return 下载路径
     */
    public String getDownloadPath() {
        return downloadPath;
    }

    /**
     * 获取配置的临时文件路径
     * @return 临时文件路径
     */
    public String getTempPath() {
        return tempPath;
    }

    /**
     * 手动清理 Playwright 临时文件
     * 可以在需要释放磁盘空间时调用
     */
    public void manualCleanupTempFiles() {
        if (cleanupTempFiles) {
            log.info("手动触发 Playwright 临时文件清理");
            cleanupPlaywrightTempFiles();
        } else {
            log.error("临时文件清理功能已禁用，请在配置中启用 crawler.playwright.cleanup-temp-files");
        }
    }

    /**
     * 设置浏览器安装路径
     */
    private void setBrowsersPath() {
        try {
            if (browsersPath != null && !browsersPath.trim().isEmpty()) {
                // 确保浏览器安装目录存在
                File browsersDir = new File(browsersPath);
                if (!browsersDir.exists()) {
                    browsersDir.mkdirs();
                    log.info("创建浏览器安装目录: {}", browsersPath);
                }

                // 设置环境变量
                System.setProperty("PLAYWRIGHT_BROWSERS_PATH", browsersPath);
                log.info("设置 Playwright 浏览器安装路径: {}", browsersPath);

                // 检查是否需要安装浏览器
                if (isBrowserInstallationNeeded(browsersDir)) {
                    log.error("检测到浏览器安装目录为空或不完整");
                    log.error("请运行以下命令安装浏览器到指定目录:");
                    log.error("PLAYWRIGHT_BROWSERS_PATH={} mvn exec:java -e -D exec.mainClass=com.microsoft.playwright.CLI -D exec.args=\"install\"", browsersPath);
                }
            } else {
                log.info("使用默认浏览器安装路径");
            }
        } catch (Exception e) {
            log.error("设置浏览器路径失败: {}", e.getMessage());
        }
    }

    /**
     * 检查是否需要安装浏览器
     */
    private boolean isBrowserInstallationNeeded(java.io.File browsersDir) {
        if (!browsersDir.exists() || !browsersDir.isDirectory()) {
            return true;
        }

        java.io.File[] files = browsersDir.listFiles();
        if (files == null || files.length == 0) {
            return true;
        }

        // 检查是否有浏览器目录
        boolean hasChromium = false;
        for (java.io.File file : files) {
            if (file.isDirectory() && file.getName().startsWith("chromium")) {
                hasChromium = true;
                break;
            }
        }

        return !hasChromium;
    }

    /**
     * 显示浏览器安装信息
     */
    private void showBrowserInstallationInfo() {
        try {
            // 检查多个可能的浏览器安装位置
            String userHome = System.getProperty("user.home");
            java.util.List<String> possiblePathsList = new java.util.ArrayList<>();

            // 如果配置了自定义路径，优先检查
            if (browsersPath != null && !browsersPath.trim().isEmpty()) {
                possiblePathsList.add(browsersPath);
            }

            // 添加默认路径
            possiblePathsList.add(userHome + "\\AppData\\Local\\ms-playwright");  // Windows 默认
            possiblePathsList.add(userHome + "\\.cache\\ms-playwright");          // 备选位置
            possiblePathsList.add(System.getProperty("java.io.tmpdir") + "\\ms-playwright"); // 临时位置
            possiblePathsList.add("C:\\ms-playwright");  // 系统级安装

            String[] possiblePaths = possiblePathsList.toArray(new String[0]);

            boolean foundBrowsers = false;
            for (String path : possiblePaths) {
                java.io.File playwrightDir = new java.io.File(path);
                if (playwrightDir.exists()) {
                    log.info("找到 Playwright 浏览器安装目录: {}", path);
                    java.io.File[] browsers = playwrightDir.listFiles();
                    if (browsers != null) {
                        for (java.io.File browser : browsers) {
                            if (browser.isDirectory() && (browser.getName().startsWith("chromium") ||
                                browser.getName().startsWith("firefox") || browser.getName().startsWith("webkit"))) {
                                long sizeInMB = getFolderSize(browser) / (1024 * 1024);
                                log.info("  - {} (大小: {} MB)", browser.getName(), sizeInMB);
                                foundBrowsers = true;
                            }
                        }
                    }
                    break;
                }
            }

            if (!foundBrowsers) {
                log.error("未找到已安装的 Playwright 浏览器");
                log.error("请运行以下命令安装浏览器:");
                log.error("mvn exec:java -e -D exec.mainClass=com.microsoft.playwright.CLI -D exec.args=\"install\"");
            }

            // 显示临时文件目录信息
            String tempDir = System.getProperty("java.io.tmpdir");
            log.info("系统临时文件目录: {}", tempDir);
            checkPlaywrightTempFiles(tempDir);

        } catch (Exception e) {
            log.info("检查浏览器安装路径时出错: {}", e.getMessage());
        }
    }

    /**
     * 检查 Playwright 临时文件
     */
    private void checkPlaywrightTempFiles(String tempDir) {
        try {
            java.io.File tempDirFile = new java.io.File(tempDir);
            java.io.File[] tempFiles = tempDirFile.listFiles((dir, name) ->
                name.startsWith("playwright-java-"));

            if (tempFiles != null && tempFiles.length > 0) {
                log.error("发现 {} 个 Playwright 临时文件夹:", tempFiles.length);
                long totalSize = 0;
                for (java.io.File tempFile : tempFiles) {
                    if (tempFile.isDirectory()) {
                        long size = getFolderSize(tempFile);
                        totalSize += size;
                        log.error("  - {} (大小: {} MB)", tempFile.getName(), size / (1024 * 1024));
                    }
                }
                log.error("临时文件总大小: {} MB", totalSize / (1024 * 1024));
                log.error("建议定期清理这些临时文件以释放磁盘空间");
            }
        } catch (Exception e) {
            log.info("检查临时文件时出错: {}", e.getMessage());
        }
    }

    /**
     * 计算文件夹大小
     */
    private long getFolderSize(java.io.File folder) {
        long size = 0;
        if (folder.isDirectory()) {
            java.io.File[] files = folder.listFiles();
            if (files != null) {
                for (java.io.File file : files) {
                    if (file.isFile()) {
                        size += file.length();
                    } else if (file.isDirectory()) {
                        size += getFolderSize(file);
                    }
                }
            }
        } else {
            size = folder.length();
        }
        return size;
    }

    /**
     * 清理 Playwright 临时文件
     */
    private void cleanupPlaywrightTempFiles() {
        try {
            String tempDir = System.getProperty("java.io.tmpdir");
            java.io.File tempDirFile = new java.io.File(tempDir);

            // 查找所有 playwright-java- 开头的临时目录
            java.io.File[] tempFiles = tempDirFile.listFiles((dir, name) ->
                name.startsWith("playwright-java-"));

            if (tempFiles != null && tempFiles.length > 0) {
                log.info("开始清理 {} 个 Playwright 临时文件夹", tempFiles.length);

                int deletedCount = 0;
                long totalFreedSpace = 0;

                for (java.io.File tempFile : tempFiles) {
                    if (tempFile.isDirectory()) {
                        try {
                            long size = getFolderSize(tempFile);
                            if (deleteDirectory(tempFile)) {
                                deletedCount++;
                                totalFreedSpace += size;
                                log.info("删除临时目录: {} (大小: {} MB)",
                                    tempFile.getName(), size / (1024 * 1024));
                            }
                        } catch (Exception e) {
                            log.error("删除临时目录失败: {} - {}", tempFile.getName(), e.getMessage());
                        }
                    }
                }

                if (deletedCount > 0) {
                    log.info("清理完成：删除了 {} 个临时目录，释放空间 {} MB",
                        deletedCount, totalFreedSpace / (1024 * 1024));
                } else {
                    log.error("未能删除任何临时目录，可能存在权限问题");
                }
            } else {
                log.info("未找到需要清理的 Playwright 临时文件");
            }
        } catch (Exception e) {
            log.error("清理 Playwright 临时文件失败", e);
        }
    }

    /**
     * 递归删除目录
     */
    private boolean deleteDirectory(java.io.File directory) {
        if (directory == null || !directory.exists()) {
            return false;
        }

        if (directory.isDirectory()) {
            java.io.File[] files = directory.listFiles();
            if (files != null) {
                for (java.io.File file : files) {
                    if (!deleteDirectory(file)) {
                        return false;
                    }
                }
            }
        }

        return directory.delete();
    }

    /**
     * 获取或创建页面
     */
    private Page getOrCreatePage(String pageId, Map<String, String> headers) {
        Page page = pages.get(pageId);
        if (page == null || page.isClosed()) {
            // 确保下载目录存在
            ensureDownloadDirectoryExists();

            Browser.NewContextOptions contextOptions = new Browser.NewContextOptions()
                    .setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36");

            // 设置下载路径（如果API支持的话）
            try {
                // 尝试设置下载路径，某些版本的Playwright可能不支持此方法
                contextOptions.setAcceptDownloads(true);
                // 注意：setDownloadsPath 可能在某些版本中不可用，我们通过反射或其他方式处理
                Method setDownloadsPathMethod = contextOptions.getClass().getMethod("setDownloadsPath", java.nio.file.Path.class);
                setDownloadsPathMethod.invoke(contextOptions, Paths.get(downloadPath));
                log.info("设置Playwright下载路径: {}", downloadPath);
            } catch (Exception e) {
                log.info("当前Playwright版本不支持setDownloadsPath，使用默认下载路径: {}", e.getMessage());
                contextOptions.setAcceptDownloads(true);
            }

            if (headers != null && !headers.isEmpty()) {
                contextOptions.setExtraHTTPHeaders(headers);
            }

            BrowserContext context = browser.newContext(contextOptions);
            contexts.put(pageId + "_context", context);

            // 设置下载事件监听
            setupDownloadListener(context, pageId);

            page = context.newPage();

            // 在页面上设置下载事件监听
            setupPageDownloadListener(page, pageId);

            pages.put(pageId, page);
        }
        return page;
    }

    /**
     * 设置下载事件监听器
     */
    private void setupDownloadListener(BrowserContext context, String pageId) {
        // 初始化下载事件列表
        downloadEvents.put(pageId, new CopyOnWriteArrayList<>());

        // 设置下载事件监听
        context.setDefaultTimeout(30000);
    }

    /**
     * 设置页面级别的下载事件监听器
     */
    private void setupPageDownloadListener(Page page, String pageId) {
        try {
            // 监听页面的下载事件
            page.onDownload(download -> {
                try {
                    Map<String, Object> downloadInfo = new HashMap<>();
                    downloadInfo.put("url", download.url());
                    downloadInfo.put("suggestedFilename", download.suggestedFilename());
                    downloadInfo.put("timestamp", System.currentTimeMillis());

                    // 添加到下载事件列表
                    List<Map<String, Object>> events = downloadEvents.get(pageId);
                    if (events != null) {
                        events.add(downloadInfo);
                    }

                    log.info("检测到下载事件: URL={}, 文件名={}", download.url(), download.suggestedFilename());

                    // 取消实际下载（我们只需要检测下载事件）
                    download.cancel();

                } catch (Exception e) {
                    log.error("处理下载事件失败", e);
                }
            });
        } catch (Exception e) {
            log.error("设置下载监听器失败: {}", e.getMessage());
        }
    }

    /**
     * 获取页面（公共方法，供其他服务使用）
     */
    public Page getPage(String pageId) {
        if (StrUtil.isEmpty(pageId)) {
            pageId = "default_page";
        }
        return pages.get(pageId);
    }

    /**
     * 安全等待页面加载
     */
    private void waitForLoadSafely(Page page, LoadState loadState, Integer timeout) {
        try {
            int waitTimeout = timeout != null ? timeout : 30000;
            page.waitForLoadState(loadState, new Page.WaitForLoadStateOptions().setTimeout(waitTimeout));
        } catch (Exception e) {
            log.info("等待页面加载状态失败: {} - {}", loadState, e.getMessage());
        }
    }

    /**
     * 适配jQuery选择器为Playwright可用的选择器
     * 只使用CSS选择器，不使用XPath
     */
    private String adaptSelector(String jQuerySelector) {
        if (StrUtil.isEmpty(jQuerySelector)) {
            return jQuerySelector;
        }

        // 记录原始选择器用于调试
        String originalSelector = jQuerySelector;

        try {
            // 如果是XPath，直接返回，不进行任何转换
            if (jQuerySelector.trim().startsWith("//")) {
                log.info("检测到XPath选择器，保持原样: {}", jQuerySelector);
                return jQuerySelector;
            }

            // 处理:contains选择器 - CSS不支持，标记为特殊选择器
            if (jQuerySelector.contains(":contains(")) {
                log.info("发现:contains选择器，标记为特殊处理: {}", jQuerySelector);
                return "__CONTAINS__" + jQuerySelector;
            }

            String adapted = jQuerySelector.trim();

            // 智能识别选择器类型并适配
            adapted = smartAdaptSelector(adapted);

            // 处理:eq(n)选择器
            Matcher eqMatcher = EQ_PATTERN.matcher(adapted);
            if (eqMatcher.find()) {
                String index = eqMatcher.group(1);
                // 索引+1因为CSS是1-based，而jQuery是0-based
                int cssIndex = Integer.parseInt(index) + 1;
                adapted = eqMatcher.replaceAll(":nth-child(" + cssIndex + ")");
            }

            // 处理:first选择器
            Matcher firstMatcher = FIRST_PATTERN.matcher(adapted);
            if (firstMatcher.find()) {
                adapted = firstMatcher.replaceAll(":first-child");
            }

            // 处理:last选择器
            Matcher lastMatcher = LAST_PATTERN.matcher(adapted);
            if (lastMatcher.find()) {
                adapted = lastMatcher.replaceAll(":last-child");
            }

            log.info("选择器适配: {} -> {}", originalSelector, adapted);
            return adapted;
        } catch (Exception e) {
            log.info("适配选择器失败: {} -> {}", originalSelector, e.getMessage());
            return jQuerySelector; // 出错时返回原始选择器
        }
    }

    /**
     * 智能适配选择器，处理常见的jQuery选择器模式
     */
    private String smartAdaptSelector(String selector) {
        if (StrUtil.isEmpty(selector)) {
            return selector;
        }

        // 如果已经是标准CSS选择器，直接返回
        if (selector.startsWith(".") || selector.startsWith("#") ||
            selector.contains(" ") || selector.contains(">") ||
            selector.contains("+") || selector.contains("~") ||
            selector.contains("[") || selector.contains(":")) {
            return selector;
        }

        // 处理纯文本选择器（可能是类名、ID或标签名）
        // 根据常见模式进行智能判断

        // 1. 如果包含大写字母或驼峰命名，很可能是类名
        if (selector.matches(".*[A-Z].*") || selector.matches(".*[a-z][A-Z].*")) {
            return "." + selector;
        }

        // 2. 如果是常见的HTML标签名，保持原样
        Set<String> commonTags = new HashSet<>(Arrays.asList(
            "div", "span", "p", "a", "img", "ul", "li", "table", "tr", "td", "th",
            "h1", "h2", "h3", "h4", "h5", "h6", "article", "section", "main",
            "header", "footer", "nav", "aside", "form", "input", "button"
        ));

        if (commonTags.contains(selector.toLowerCase())) {
            return selector;
        }

        // 3. 如果包含下划线或连字符，可能是类名或ID
        if (selector.contains("_") || selector.contains("-")) {
            // 尝试作为类名
            return "." + selector;
        }

        // 4. 如果是纯小写字母，可能是标签名
        if (selector.matches("^[a-z]+$")) {
            return selector;
        }

        // 5. 默认情况下，尝试作为类名
        return "." + selector;
    }

    /**
     * 使用选择器查找元素，支持多种选择器方式
     * 只使用CSS选择器和JavaScript处理特殊选择器
     */
    private ElementHandle[] findElementsWithAdaptedSelector(Page page, String selector) {
        if (StrUtil.isEmpty(selector)) {
            return new ElementHandle[0];
        }

        log.info("findElementsWithAdaptedSelector 接收到选择器: 长度={}, 内容=[{}]",
                selector.length(), selector);
        
        // 处理多选择器情况（逗号分隔）- 但排除XPath选择器
        if (selector.contains(",") && !selector.contains("',") && !selector.contains("\",") &&
            !selector.trim().startsWith("//")) {
            List<ElementHandle> allElements = new ArrayList<>();
            String[] selectors = selector.split(",");
            
            for (String sel : selectors) {
                sel = sel.trim();
                try {
                    // 处理包含contains的选择器
                    if (sel.contains(":contains(")) {
                        ElementHandle[] containsElements = findElementsByContains(page, sel);
                        if (containsElements.length > 0) {
                            Collections.addAll(allElements, containsElements);
                        }
                        continue;
                    }
                    
                    // 检查是否为XPath选择器
                    if (sel.trim().startsWith("//")) {
                        // 使用JavaScript处理XPath选择器
                        ElementHandle[] xpathElements = findElementsByXPath(page, sel);
                        if (xpathElements.length > 0) {
                            Collections.addAll(allElements, xpathElements);
                        }
                        continue;
                    }

                    // 标准CSS选择器处理
                    String adaptedSelector = adaptSelector(sel);
                    
                    ElementHandle[] elements = page.querySelectorAll(adaptedSelector).toArray(new ElementHandle[0]);
                    if (elements.length > 0) {
                        Collections.addAll(allElements, elements);
                    }
                } catch (Exception e) {
                    log.info("使用选择器{}查找元素失败: {}", sel, e.getMessage());
                }
            }
            
            return allElements.toArray(new ElementHandle[0]);
        } else {
            // 单个选择器
            try {
                // 检查是否为XPath选择器 - 增强检查
                String trimmedSelector = selector.trim();
                if (trimmedSelector.startsWith("//")) {
                    log.info("检测到XPath选择器，直接处理: 长度={}, 内容=[{}]",
                            trimmedSelector.length(), trimmedSelector);
                    return findElementsByXPath(page, trimmedSelector);
                }

                // 处理包含contains的选择器
                if (selector.contains(":contains(")) {
                    return findElementsByContains(page, selector);
                }

                String adaptedSelector = adaptSelector(selector);
                return page.querySelectorAll(adaptedSelector).toArray(new ElementHandle[0]);
            } catch (Exception e) {
                log.info("使用选择器{}查找元素失败: {}", selector, e.getMessage());
                return new ElementHandle[0];
            }
        }
    }

    /**
     * 查找单个元素
     */
    private ElementHandle findElementWithAdaptedSelector(Page page, String selector) {
        ElementHandle[] elements = findElementsWithAdaptedSelector(page, selector);
        return elements.length > 0 ? elements[0] : null;
    }

    /**
     * 使用JavaScript查找包含指定文本的元素
     */
    private ElementHandle[] findElementsByContains(Page page, String selector) {
        try {
            // 提取选择器的基础部分和包含的文本
            String baseSelector = "*";
            String text = "";
            
            Matcher containsMatcher = CONTAINS_PATTERN.matcher(selector);
            if (containsMatcher.find()) {
                // 提取基础选择器
                baseSelector = selector.substring(0, containsMatcher.start()).trim();
                if (StrUtil.isEmpty(baseSelector)) {
                    baseSelector = "*"; // 默认查询所有元素
                }
                
                // 提取文本
                text = containsMatcher.group(3);
            } else {
                log.info("无法从选择器中提取:contains内容: {}", selector);
                return new ElementHandle[0];
            }
            
            // 这里是编译错误的根本原因 - 使用更简单的方式实现
            // 采用一次性的JavaScript方法，不需要处理JSHandle转换
            Object result = page.evaluate(
                "([selector, text]) => {\n" +
                "  const elements = document.querySelectorAll(selector);\n" +
                "  const matching = Array.from(elements).filter(el => el.textContent.includes(text));\n" +
                "  // 返回匹配元素的选择器路径，用于后续querySelector获取\n" +
                "  return matching.map(el => {\n" +
                "    // 创建唯一ID便于查找\n" +
                "    const id = 'playwright_temp_' + Math.random().toString(36).substr(2, 9);\n" +
                "    el.setAttribute('data-playwright-temp-id', id);\n" +
                "    return id;\n" +
                "  });\n" +
                "}",
                new String[] { baseSelector, text }
            );
            
            // 如果没有结果，直接返回空数组
            if (result == null) {
                return new ElementHandle[0];
            }
            
            // 解析返回的ID列表
            List<ElementHandle> elements = new ArrayList<>();
            List<?> ids;
            
            try {
                ids = (List<?>) result;
            } catch (ClassCastException e) {
                log.info("JavaScript结果转换失败: {}", e.getMessage());
                return new ElementHandle[0];
            }
            
            // 通过临时ID查找元素
            for (Object id : ids) {
                try {
                    String elementId = id.toString();
                    ElementHandle element = page.querySelector("[data-playwright-temp-id='" + elementId + "']");
                    if (element != null) {
                        elements.add(element);
                        // 移除临时ID属性
                        page.evaluate("id => document.querySelector(`[data-playwright-temp-id='${id}']`).removeAttribute('data-playwright-temp-id')", elementId);
                    }
                } catch (Exception e) {
                    log.info("通过ID获取元素失败: {}", e.getMessage());
                }
            }
            
            return elements.toArray(new ElementHandle[0]);
        } catch (Exception e) {
            log.info("JavaScript查找包含文本的元素失败: {}", e.getMessage());
            return new ElementHandle[0];
        }
    }

    /**
     * 使用JavaScript处理XPath选择器
     */
    private ElementHandle[] findElementsByXPath(Page page, String xpath) {
        try {
            // 使用字符串长度和内容检查来调试截断问题
            log.info("使用XPath查找元素，长度: {}, 内容: [{}], 字节数组: {}",
                    xpath.length(), xpath, java.util.Arrays.toString(xpath.getBytes()));

            // 验证XPath格式
            if (!xpath.trim().startsWith("//")) {
                log.error("无效的XPath格式，长度: {}, 内容: [{}]", xpath.length(), xpath);
                return new ElementHandle[0];
            }

            // 额外验证XPath完整性
            if (xpath.contains("contains(") && !xpath.contains(")]")) {
                log.error("XPath选择器可能被截断，缺少结束部分: [{}]", xpath);
                return new ElementHandle[0];
            }

            // 使用JavaScript的document.evaluate处理XPath
            Object result = page.evaluate(
                "function(xpath) {\n" +
                "  try {\n" +
                "    console.log('执行XPath查询:', xpath);\n" +
                "    const xpathResult = document.evaluate(\n" +
                "      xpath,\n" +
                "      document,\n" +
                "      null,\n" +
                "      XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,\n" +
                "      null\n" +
                "    );\n" +
                "    \n" +
                "    console.log('XPath查询结果数量:', xpathResult.snapshotLength);\n" +
                "    \n" +
                "    const elements = [];\n" +
                "    for (let i = 0; i < xpathResult.snapshotLength; i++) {\n" +
                "      const element = xpathResult.snapshotItem(i);\n" +
                "      if (element) {\n" +
                "        const elementInfo = {\n" +
                "          tagName: element.tagName || 'unknown',\n" +
                "          text: (element.textContent || element.innerText || '').trim(),\n" +
                "          href: element.href || element.getAttribute('href') || '',\n" +
                "          index: i\n" +
                "        };\n" +
                "        console.log('找到元素:', elementInfo);\n" +
                "        elements.push(elementInfo);\n" +
                "      }\n" +
                "    }\n" +
                "    \n" +
                "    return { success: true, elements: elements, count: elements.length };\n" +
                "  } catch (e) {\n" +
                "    console.error('XPath执行失败:', e);\n" +
                "    return { success: false, error: e.message };\n" +
                "  }\n" +
                "}",
                xpath
            );

            if (result != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> resultMap = (Map<String, Object>) result;

                if (Boolean.TRUE.equals(resultMap.get("success"))) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> elements = (List<Map<String, Object>>) resultMap.get("elements");

                    log.info("XPath查找到 {} 个元素", elements.size());

                    // 由于我们无法直接返回ElementHandle，这里返回空数组
                    // 但是我们可以记录找到的元素信息
                    for (Map<String, Object> element : elements) {
                        log.info("XPath元素: 标签={}, 文本={}, 链接={}",
                            element.get("tagName"),
                            element.get("text").toString().length() > 50 ?
                                element.get("text").toString().substring(0, 50) + "..." : element.get("text"),
                            element.get("href"));
                    }

                    // 对于XPath，我们需要使用不同的方法
                    // 这里暂时返回空数组，实际的XPath处理在其他地方进行
                    return new ElementHandle[0];
                } else {
                    log.error("XPath执行失败: {}", resultMap.get("error"));
                    return new ElementHandle[0];
                }
            }

            return new ElementHandle[0];
        } catch (Exception e) {
            log.error("XPath查找失败: {} - {}", xpath, e.getMessage());
            return new ElementHandle[0];
        }
    }

    /**
     * 智能检测页面上的所有附件（只检测，不下载）
     * 这个方法专门用于检测附件信息，返回给 CrawlerNode 处理下载和保存
     *
     * @param pageId 页面ID
     * @param baseUrl 基础URL，用于解析相对链接
     * @return 检测结果
     */
    public BrowserResult detectAllAttachments(String pageId, String baseUrl) {
        ensureBrowserInitialized();

        Page page = getPage(pageId);
        if (page == null) {
            return BrowserResult.error("页面不存在: " + pageId);
        }

        try {
            List<Map<String, Object>> allAttachments = new ArrayList<>();
            Set<String> processedUrls = new HashSet<>();

            log.info("开始智能检测页面附件...");

            // 策略1: 检测明显的文件链接（有文件扩展名）
            List<String> fileExtensions = Arrays.asList(
                "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx",
                "txt", "rtf", "zip", "rar", "7z", "xml", "json", "csv"
            );

            for (String ext : fileExtensions) {
                try {
                    String selector = String.format("a[href*='.%s']", ext);
                    Object result = page.evaluate(
                        "(selector) => {\n" +
                        "  const links = Array.from(document.querySelectorAll(selector));\n" +
                        "  return links.map(link => ({\n" +
                        "    href: link.href,\n" +
                        "    text: link.textContent.trim(),\n" +
                        "    title: link.title || ''\n" +
                        "  }));\n" +
                        "}", selector
                    );

                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> links = (List<Map<String, Object>>) result;

                    for (Map<String, Object> link : links) {
                        String href = (String) link.get("href");
                        String text = (String) link.get("text");

                        if (StrUtil.isNotEmpty(href) && !processedUrls.contains(href)) {
                            processedUrls.add(href);

                            Map<String, Object> attachment = new HashMap<>();
                            attachment.put("url", href);
                            attachment.put("text", StrUtil.isNotEmpty(text) ? text.trim() : "");
                            attachment.put("type", ext);
                            attachment.put("detectionMethod", "file_extension");
                            allAttachments.add(attachment);

                            log.info("发现{}文件: {} -> {}", ext.toUpperCase(), text, href);
                        }
                    }
                } catch (Exception e) {
                    log.info("检测{}文件时出错: {}", ext, e.getMessage());
                }
            }

            // 策略2: 检测包含关键词的链接
            try {
                Object result = page.evaluate(
                    "() => {\n" +
                    "  const keywords = ['附件', '下载', '文件', '资料', '材料', '办法', '规则', '细则', '指引', '通知', '公告'];\n" +
                    "  const links = Array.from(document.querySelectorAll('a[href]'));\n" +
                    "  return links.filter(link => {\n" +
                    "    const text = link.textContent.toLowerCase();\n" +
                    "    const href = link.href.toLowerCase();\n" +
                    "    return keywords.some(keyword => text.includes(keyword)) ||\n" +
                    "           href.includes('download') || href.includes('file') || href.includes('attachment') ||\n" +
                    "           text.includes('《') || text.includes('》') ||\n" +
                    "           /\\d{4}/.test(text);\n" +
                    "  }).map(link => ({\n" +
                    "    href: link.href,\n" +
                    "    text: link.textContent.trim(),\n" +
                    "    title: link.title || ''\n" +
                    "  }));\n" +
                    "}"
                );

                @SuppressWarnings("unchecked")
                List<Map<String, Object>> keywordLinks = (List<Map<String, Object>>) result;

                for (Map<String, Object> link : keywordLinks) {
                    String href = (String) link.get("href");
                    String text = (String) link.get("text");

                    if (StrUtil.isNotEmpty(href) && !processedUrls.contains(href)) {
                        processedUrls.add(href);

                        Map<String, Object> attachment = new HashMap<>();
                        attachment.put("url", href);
                        attachment.put("text", StrUtil.isNotEmpty(text) ? text.trim() : "");
                        attachment.put("type", "unknown");
                        attachment.put("detectionMethod", "keyword_based");
                        allAttachments.add(attachment);

                        log.info("发现关键词链接: {} -> {}", text, href);
                    }
                }
            } catch (Exception e) {
                log.info("检测关键词链接时出错: {}", e.getMessage());
            }

            log.info("附件检测完成，共找到{}个附件", allAttachments.size());
            updateLastUsedTime(); // 更新使用时间

            return BrowserResult.success()
                    .put("attachments", allAttachments)
                    .put("count", allAttachments.size());

        } catch (Exception e) {
            log.error("智能检测附件失败", e);
            return BrowserResult.error("检测附件失败: " + e.getMessage());
        }
    }
}
