package com.icarus.common.cotnode;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.icarus.common.cotnode.ab.crawler.dto.CrawlerResult;
import com.icarus.common.cotnode.ab.crawler.service.CrawlerService;
import com.icarus.common.minio.MinioUtil;
import com.icarus.common.runtime.NodeContext;
import com.icarus.entity.CrawlerFile;
import com.icarus.entity.CrawlerSettings;
import com.icarus.entity.CrawlerTask;
import com.icarus.service.CrawlerFileService;
import com.icarus.service.CrawlerSettingsService;
import com.icarus.service.CrawlerTaskService;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 简化版爬虫节点 - 使用Playwright浏览器服务
 * <p>
 * 此版本通过解耦架构，将爬虫核心功能移至CrawlerService和PlaywrightBrowserService，
 * 实现了代码精简化和关注点分离，有利于维护和扩展。
 * </p>
 */
@EqualsAndHashCode(callSuper = true)
@Log4j2
@NoArgsConstructor
@Component
public class CrawlerNode extends BaseNode implements ApplicationContextAware {

    // 静态注入核心实现
    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext context) throws BeansException {
        applicationContext = context;
        log.info("ApplicationContext已设置到CrawlerNode");
    }

    // 静态获取方法
    private static CrawlerSettingsService getCrawlerSettingsService() {
        return applicationContext.getBean(CrawlerSettingsService.class);
    }

    private static CrawlerTaskService getCrawlerTaskService() {
        return applicationContext.getBean(CrawlerTaskService.class);
    }

    private static CrawlerFileService getCrawlerFileService() {
        return applicationContext.getBean(CrawlerFileService.class);
    }
    private static CrawlerService getCrawlerService() {
        return applicationContext.getBean(CrawlerService.class);
    }

    private static MinioUtil getMinioUtil() {
        return applicationContext.getBean(MinioUtil.class);
    }

    // 静态值缓存
    private static String staticMinioBucketName;
    private static String staticTempPath;
    private static CrawlerSettingsService crawlerSettingsService;
    private static CrawlerTaskService crawlerTaskService;
    private static CrawlerFileService crawlerFileService;
    private static MinioUtil minioUtil;
    private static CrawlerService crawlerService;

    @Value("${minio.bucketName:crawler}")
    private String minioBucketName;

    @Value("${crawler.playwright.temp-path:${java.io.tmpdir}/crawler_files}")
    private String tempPath;

    /** 下载统计 */
    private Map<String, Integer> downloadStats = new ConcurrentHashMap<>();

    /** 失败文件列表 */
    private List<Map<String, String>> failedFiles = Collections.synchronizedList(new ArrayList<>());


    @PostConstruct
    public void initStaticValues() {
        staticMinioBucketName = minioBucketName;
        staticTempPath = tempPath;
        // 使用静态方法获取 Bean
        crawlerSettingsService = getCrawlerSettingsService();
        crawlerTaskService = getCrawlerTaskService();
        crawlerFileService = getCrawlerFileService();
        crawlerService = getCrawlerService();
         minioUtil = getMinioUtil();
        log.info("CrawlerNodeSimplified静态值初始化完成，MinIO存储桶：{}", staticMinioBucketName);
    }

    /** 已处理的URL，用于去重 */
    private Set<String> processedUrls = ConcurrentHashMap.newKeySet();

    /** 爬虫节点代码标识 */
    public static final String code = "crawlerNode";

    /**
     * 节点执行主方法
     *
     * @param context 上下文参数
     * @return 更新后的上下文
     */
    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【爬虫】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);

        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            nodeContext.setInputs(new JSONObject(resolvedInputs));

            // 解析属性配置
            Properties resolvedProperties = resolveProperties(context, Properties.class);
            JSONObject propertiesJson = JSONUtil.parseObj(resolvedProperties);
            nodeContext.setProperties(propertiesJson);

            // 获取配置ID
            Long settingsId = resolvedProperties.getSettingsId();
            if (settingsId == null) {
                throw new IllegalArgumentException("爬虫配置ID不能为空");
            }

            // 获取批次ID
            String batchId = resolvedProperties.getBatchId();
            if (StrUtil.isEmpty(batchId)) {
                batchId = "batch_" + IdUtil.fastSimpleUUID();
                log.info("未提供批次ID，自动生成：{}", batchId);
            }

            // 获取爬虫配置
            CrawlerSettings settings = crawlerSettingsService.getById(settingsId);
            if (settings == null) {
                throw new IllegalArgumentException("未找到ID为" + settingsId + "的爬虫配置");
            }
            log.info("成功获取爬虫配置：ID={}，URL={}", settingsId, settings.getCrawlUrl());

            // 查找是否有已存在的任务（增量更新逻辑）
            CrawlerTask existingTask = findExistingTaskBySettings(settingsId);
            CrawlerTask task;

            if (existingTask != null) {
                task = existingTask;
                String taskStatus = task.getStatus();

                if ("completed".equals(taskStatus)) {
                    // 任务已完成，检查是否需要重新执行
                    log.info("发现已完成的任务，任务ID: {}，使用已有批次ID: {}，跳过重复执行", task.getId(), task.getBatchId());

                    // 更新任务信息，记录跳过原因
                    task.setUpdateTime(LocalDateTime.now());
                    task.setErrorMessage("任务已完成，跳过重复执行");
                    crawlerTaskService.updateById(task);

                    // 使用已存在任务的批次ID
                    batchId = task.getBatchId();
                    Map<String, Object> result = createCompletedTaskResult(task);
                    result.put("skipReason", "任务已完成");
                    result.put("skipTime", LocalDateTime.now().toString());
                    addEnhancedOutputs(result, task.getId(), batchId);

                    // 设置节点状态为完成
                    nodeContext.setStatus("completed");
                    log.info("✅ 爬虫节点跳过执行完成 - 任务已完成，任务ID: {}", task.getId());
                    // 处理输出参数
                    processOutputs(context);
                    return context;

                } else if ("running".equals(taskStatus)) {
                    log.info("发现正在运行的任务，任务ID: {}，使用已有批次ID: {}，跳过重复执行", task.getId(), task.getBatchId());

                    // 更新任务信息，记录跳过原因
                    task.setUpdateTime(LocalDateTime.now());
                    task.setErrorMessage("任务正在运行中，跳过重复执行");
                    crawlerTaskService.updateById(task);

                    // 使用已存在任务的批次ID
                    batchId = task.getBatchId();
                    Map<String, Object> result = createRunningTaskResult(task);
                    result.put("skipReason", "任务正在运行中");
                    result.put("skipTime", LocalDateTime.now().toString());
                    addEnhancedOutputs(result, task.getId(), batchId);

                    // 设置节点状态为完成（跳过执行也算完成）
                    nodeContext.setStatus("completed");
                    log.info("✅ 爬虫节点跳过执行完成 - 任务正在运行，任务ID: {}", task.getId());
                    // 处理输出参数
                    processOutputs(context);
                    return context;

                } else if ("failed".equals(taskStatus)) {
                    // 失败的任务重新执行
                    log.info("发现失败的任务，任务ID: {}，使用已有批次ID: {}，重新执行", task.getId(), task.getBatchId());

                    // 使用已存在任务的批次ID
                    batchId = task.getBatchId();
                    task.setStatus("running");
                    task.setStartTime(LocalDateTime.now());
                    task.setUpdateTime(LocalDateTime.now());
                    task.setErrorMessage(null);
                    crawlerTaskService.updateById(task);

                    // 恢复下载统计
                    initializeStats();
                    if (task.getFileCount() != null) downloadStats.put("success", task.getFileCount());
                    if (task.getFailedCount() != null) downloadStats.put("failed", task.getFailedCount());
                    if (task.getPageCount() != null) downloadStats.put("pageCount", task.getPageCount());
                    downloadStats.put("total", (task.getFileCount() != null ? task.getFileCount() : 0) +
                                              (task.getFailedCount() != null ? task.getFailedCount() : 0));
                }
            } else {
                // 创建新任务
                task = new CrawlerTask();
                task.setSettingsId(settingsId);
                task.setBatchId(batchId);
                task.setStatus("running");
                task.setStartTime(LocalDateTime.now());
                task.setPageCount(0);
                task.setFileCount(0);
                task.setFailedCount(0);
                task.setCreateTime(LocalDateTime.now());
                task.setUpdateTime(LocalDateTime.now());
                // 保存任务进度
                task.setLastProcessedUrl(settings.getCrawlUrl()); // 初始URL
                task.setCurrentPage(0); // 从第0页开始
                task.setProcessedUrls(""); // 空的已处理URL列表

                crawlerTaskService.save(task);
                log.info("创建爬虫任务成功: 配置ID={}, 批次ID={}, 任务ID={}", settingsId, batchId, task.getId());

                // 初始化统计信息
                initializeStats();
            }

            Long taskId = task.getId();

            // 执行爬虫任务
            CrawlerResult crawlResult = null;
            try {
                log.info("开始调用CrawlerService执行爬虫任务：配置ID={}，任务ID={}", settingsId, taskId);
                // 使用新的爬虫服务爬取网站
                crawlResult = crawlerService.crawlWebsite(settings, task);
                
                // 检查爬取结果
                if (crawlResult != null && crawlResult.isSuccess()) {
                    // 爬取成功
                    log.info("爬虫任务执行成功，获取到数据: {}", crawlResult.getBoolean("hasData"));

                // 更新任务状态为完成
                task.setStatus("completed");
                task.setEndTime(LocalDateTime.now());
                task.setUpdateTime(LocalDateTime.now());
                    
                    // 分离列表页和详情页内容
                    String fullTextContent = crawlResult.getString("textContent");
                    String listPageContent = extractListPageContent(fullTextContent);
                    String detailPagesContent = extractDetailPagesContent(fullTextContent);

                    // 只在crawler_task.text_content中存储列表页内容
                    if (StrUtil.isNotEmpty(listPageContent)) {
                        task.setTextContent(listPageContent);
                        log.info("保存列表页内容到数据库，长度: {} 字符", listPageContent.length());
                    }

                    // 将详情页内容作为特殊附件存入MinIO（支持增量更新）
                    if (StrUtil.isNotEmpty(detailPagesContent)) {
                        // 检查详情页内容是否已经存在
                        if (!isDetailPagesContentExists(detailPagesContent, task.getId())) {
                            log.info("发现新的详情页内容，开始保存");
                            // 从爬取结果中获取详情页信息，用于生成更有意义的文件名
                            saveDetailPagesAsAttachment(detailPagesContent, task, crawlResult);
                        } else {
                            log.info("详情页内容已存在，跳过保存");
                        }
                    }

                    // 处理爬取的附件
                        @SuppressWarnings("unchecked")
                        List<Map<String, Object>> attachments = (List<Map<String, Object>>) crawlResult.get("attachments");
                        if (CollUtil.isNotEmpty(attachments)) {
                        log.info("爬取到 {} 个附件，开始处理", attachments.size());
                        processAttachments(attachments, task);
                    } else {
                        log.info("未爬取到附件信息");
                    }
                    
                    // 保存页面统计
                    Integer pageCount = crawlResult.getInteger("pageCount");
                    if (pageCount != null) {
                        task.setPageCount(pageCount);
                    } else {
                        task.setPageCount(downloadStats.getOrDefault("pageCount", 0));
                    }
                    
                    // 更新文件统计
                    task.setFileCount(downloadStats.getOrDefault("success", 0));
                    task.setFailedCount(downloadStats.getOrDefault("failed", 0));
                            log.info("爬取统计: 页面数={}, 成功文件数={}, 失败文件数={}", 
                                    task.getPageCount(), task.getFileCount(), task.getFailedCount());
                    
                crawlerTaskService.updateById(task);
                } else {
                    // 爬取失败
                    String errorMessage = crawlResult != null ? crawlResult.getMessage() : "未知错误";
                    log.error("爬虫任务执行失败: {}", errorMessage);
                    
                    // 更新任务状态为失败
                    task.setStatus("failed");
                    task.setEndTime(LocalDateTime.now());
                    task.setUpdateTime(LocalDateTime.now());
                    task.setErrorMessage(errorMessage);
                    crawlerTaskService.updateById(task);
                    
                    throw new RuntimeException("爬虫任务执行失败: " + errorMessage);
                }

                log.info("爬虫任务执行完成，配置ID: {}，批次ID: {}，任务ID: {}", settingsId, batchId, taskId);
            } catch (Exception e) {
                // 更新任务状态为失败
                task.setStatus("failed");
                task.setEndTime(LocalDateTime.now());
                task.setUpdateTime(LocalDateTime.now());
                task.setErrorMessage(e.getMessage());
                crawlerTaskService.updateById(task);

                log.error("爬虫任务执行失败，配置ID: {}，批次ID: {}，任务ID: {}", settingsId, batchId, taskId, e);
                throw e;
            }

            // 设置输出结果
            Map<String, Object> result = crawlResult != null ? 
                    crawlResult.getData() : createResultMap(taskId, batchId, settingsId);
            nodeContext.setStatus("completed");

            // 添加默认输出
            addEnhancedOutputs(result, taskId, batchId);

            // 处理输出参数
            processOutputs(context);

        } catch (Exception e) {
            log.error("爬虫节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        log.info("爬虫节点执行结束，节点ID：{}", getId());
        return context;
    }

    /**
     * 处理爬取到的附件列表（支持增量更新）
     *
     * @param attachments 附件列表
     * @param task 爬虫任务
     */
    private void processAttachments(List<Map<String, Object>> attachments, CrawlerTask task) {
        log.info("开始处理附件列表，总数: {}", attachments.size());

        int newFiles = 0;
        int existingFiles = 0;

        for (Map<String, Object> attachment : attachments) {
            try {
                String fileUrl = (String) attachment.get("url");
                String linkText = (String) attachment.get("text");
                String fileType = (String) attachment.get("type");
                String selector = (String) attachment.get("selector");

                if (StrUtil.isEmpty(fileUrl)) {
                    continue;
                }

                // 根据附件类型决定文件名
                String fileName = determineFileName(fileUrl, linkText, selector);

                // 检查文件是否已经存在（增量更新逻辑）
                if (isFileAlreadyExists(fileUrl, task.getId())) {
                    log.info("文件已存在，跳过下载: {} -> {}", fileName, fileUrl);
                    existingFiles++;
                    continue;
                }

                log.info("发现新文件，开始下载: {} -> {} (选择器: {})", fileUrl, fileName, selector);

                // 下载并保存文件
                downloadAndSaveFile(fileUrl, fileName, fileType, task.getId());
                newFiles++;

            } catch (Exception e) {
                log.error("处理附件失败: {}", e.getMessage());
                Map<String, String> failedFile = new HashMap<>();
                failedFile.put("url", (String) attachment.get("url"));
                failedFile.put("fileName", (String) attachment.get("text"));
                failedFile.put("reason", e.getMessage());
                failedFiles.add(failedFile);

                // 更新下载统计
                downloadStats.put("failed", downloadStats.getOrDefault("failed", 0) + 1);
                downloadStats.put("total", downloadStats.getOrDefault("total", 0) + 1);
            }
        }

        log.info("附件处理完成 - 新下载: {} 个，已存在: {} 个", newFiles, existingFiles);
    }

    /**
     * 根据附件类型决定文件名
     *
     * @param fileUrl 文件URL
     * @param linkText 链接文本
     * @param selector 选择器
     * @return 确定的文件名
     */
    private String determineFileName(String fileUrl, String linkText, String selector) {
        // 如果是a标签（链接），使用链接文本作为文件名
        if (StrUtil.isNotEmpty(selector) && selector.toLowerCase().contains("a")) {
            if (StrUtil.isNotEmpty(linkText)) {
                // 清理链接文本，作为文件名
                String cleanedText = sanitizeFileName(linkText);

                // 如果链接文本没有扩展名，尝试从URL获取扩展名
                if (!cleanedText.contains(".")) {
                    String urlFileName = getFileNameFromUrl(fileUrl);
                    if (urlFileName.contains(".")) {
                        String extension = urlFileName.substring(urlFileName.lastIndexOf('.'));
                        cleanedText += extension;
                    }
                }

                log.info("使用链接文本作为文件名: {} -> {}", linkText, cleanedText);
                return cleanedText;
            }
        }

        // 其他类型的附件使用原始文件名
        String originalFileName = getFileNameFromUrl(fileUrl);
        log.info("使用原始文件名: {}", originalFileName);
        return originalFileName;
    }
    
    /**
     * 下载并保存文件
     *
     * @param fileUrl 文件URL
     * @param fileName 文件名
     * @param fileType 文件类型
     * @param taskId 任务ID
     */
    private void downloadAndSaveFile(String fileUrl, String fileName, String fileType, Long taskId) {
        int retryCount = 3;
        int tryCount = 0;
        Exception lastException = null;

        // 处理文件名
        fileName = sanitizeFileName(fileName);

        while (tryCount <= retryCount) {
            try {
                log.info("下载文件: {} -> {} (尝试 {}/{})", fileUrl, fileName, tryCount + 1, retryCount + 1);

                // 检查并创建临时目录（使用配置的路径）
                if (StrUtil.isEmpty(staticTempPath)) {
                    throw new Exception("临时文件路径配置为空，请检查 crawler.playwright.temp-path 配置");
                }

                log.info("使用临时文件路径: {}", staticTempPath);
                File tempDir = new File(staticTempPath);
                if (!tempDir.exists()) {
                    boolean created = tempDir.mkdirs();
                    if (!created) {
                        throw new Exception("无法创建临时文件目录: " + staticTempPath);
                    }
                    log.info("创建临时文件目录: {}", staticTempPath);
                } else {
                    log.info("临时文件目录已存在: {}", staticTempPath);
                }

                // 创建随机文件夹来保证唯一性，保持原始文件名
                String randomFolder = UUID.randomUUID().toString().substring(0, 8); // 使用8位随机字符串作为文件夹名
                File tempFile = new File(tempDir, fileName); // 临时文件使用原始文件名

                log.info("临时文件路径: {}", tempFile.getAbsolutePath());

                // 确保临时文件的父目录存在
                File parentDir = tempFile.getParentFile();
                if (parentDir != null && !parentDir.exists()) {
                    boolean created = parentDir.mkdirs();
                    if (!created) {
                        throw new Exception("无法创建临时文件的父目录: " + parentDir.getAbsolutePath());
                    }
                }

                // 设置下载连接
                log.info("开始建立连接: {}", fileUrl);
                URL url = new URL(fileUrl);
                HttpURLConnection conn = (HttpURLConnection) url.openConnection();
                conn.setRequestMethod("GET");
                conn.setConnectTimeout(30000);
                conn.setReadTimeout(60000); // 增加读取超时时间

                // 设置更完整的请求头，模拟真实浏览器
                conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
                conn.setRequestProperty("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8");
                conn.setRequestProperty("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
                conn.setRequestProperty("Accept-Encoding", "gzip, deflate");
                conn.setRequestProperty("Connection", "keep-alive");
                conn.setRequestProperty("Upgrade-Insecure-Requests", "1");

                // 设置 Referer 为详情页URL（如果可能的话）
                if (fileUrl.contains("sse.com.cn")) {
                    String referer = fileUrl.substring(0, fileUrl.lastIndexOf('/'));
                    conn.setRequestProperty("Referer", referer);
                    log.info("设置 Referer: {}", referer);
                }

                // 检查响应状态
                log.info("建立连接成功，检查响应状态...");
                int responseCode = conn.getResponseCode();
                String responseMessage = conn.getResponseMessage();
                log.info("HTTP响应: {} {}", responseCode, responseMessage);

                if (responseCode != HttpURLConnection.HTTP_OK) {
                    String errorDetail = String.format("HTTP错误: %d %s", responseCode, responseMessage);

                    // 尝试读取错误响应内容
                    try (InputStream errorStream = conn.getErrorStream()) {
                        if (errorStream != null) {
                            byte[] errorBytes = errorStream.readAllBytes();
                            String errorContent = new String(errorBytes, "UTF-8");
                            if (errorContent.length() > 200) {
                                errorContent = errorContent.substring(0, 200) + "...";
                            }
                            errorDetail += ", 错误内容: " + errorContent;
                        }
                    } catch (Exception e) {
                        log.info("读取错误响应失败: {}", e.getMessage());
                    }

                    throw new Exception(errorDetail);
                }

                // 获取Content-Type和文件大小
                String contentType = conn.getContentType();
                int fileSize = conn.getContentLength();

                // 获取文件类型（如果未提供）
                if (StrUtil.isEmpty(fileType)) {
                    fileType = getFileTypeFromContentType(contentType);
                if (StrUtil.isEmpty(fileType)) {
                    fileType = getFileTypeFromName(fileName);
                    }
                }

                // 下载文件
                try (InputStream in = conn.getInputStream();
                     FileOutputStream out = new FileOutputStream(tempFile)) {
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    int totalRead = 0;

                    while ((bytesRead = in.read(buffer)) != -1) {
                        out.write(buffer, 0, bytesRead);
                        totalRead += bytesRead;
                    }

                    log.info("文件下载成功: {}, 大小: {} 字节", fileName, totalRead);
                }

                // 上传到MinIO，使用随机文件夹保证唯一性
                String objectName = "crawler/" + taskId + "/" + randomFolder + "/" + fileName;
                String minioUrl;

                try (FileInputStream fis = new FileInputStream(tempFile)) {
                    // 使用MinioUtil上传文件
                    byte[] fileBytes = org.apache.commons.io.IOUtils.toByteArray(fis);
                    minioUrl = minioUtil.uploadFile(
                            fileBytes,
                            minioBucketName,
                            objectName,
                            contentType != null ? contentType : "application/octet-stream"
                    );

                    log.info("文件已上传到MinIO: {}", objectName);
                }

                // 保存文件信息到数据库
                CrawlerFile file = new CrawlerFile();
                file.setTaskId(taskId);
                file.setFileName(fileName);
                file.setFileType(fileType);
                file.setFileSize((long) (fileSize > 0 ? fileSize : tempFile.length()));
                file.setMinioUrl(minioUrl);
                file.setOriginalUrl(fileUrl);
                file.setSourceUrl(fileUrl);
                file.setStatus("success");
                file.setCreateTime(LocalDateTime.now());
                file.setUpdateTime(LocalDateTime.now());
                crawlerFileService.save(file);

                // 更新下载统计
                downloadStats.put("success", downloadStats.getOrDefault("success", 0) + 1);
                downloadStats.put("total", downloadStats.getOrDefault("total", 0) + 1);

                // 删除临时文件
                tempFile.delete();

                log.info("文件处理完成: {}", fileName);
                return;

            } catch (Exception e) {
                lastException = e;
                String errorMsg = e.getMessage();
                if (errorMsg == null) {
                    errorMsg = e.getClass().getSimpleName() + ": " + e.toString();
                }
                log.error("下载文件失败 (尝试 {}/{}): {}, 错误类型: {}, 错误信息: {}",
                        tryCount + 1, retryCount + 1, fileUrl, e.getClass().getSimpleName(), errorMsg);
                log.info("下载异常详情", e);
                tryCount++;

                if (tryCount <= retryCount) {
                    try {
                    // 指数退避重试
                        Thread.sleep(1000 * (long) Math.pow(2, tryCount));
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
        }

        // 所有重试都失败
        downloadStats.put("failed", downloadStats.getOrDefault("failed", 0) + 1);
        downloadStats.put("total", downloadStats.getOrDefault("total", 0) + 1);

        // 添加到失败文件列表
        Map<String, String> failedFile = new HashMap<>();
        failedFile.put("url", fileUrl);
        failedFile.put("fileName", fileName);
        failedFile.put("reason", lastException != null ? lastException.getMessage() : "未知错误");
        failedFiles.add(failedFile);

        log.error("下载文件失败，已达到最大重试次数: {}", fileUrl);
    }
    
    /**
     * 从完整文本内容中提取列表页内容
     *
     * @param fullTextContent 完整文本内容
     * @return 列表页内容
     */
    private String extractListPageContent(String fullTextContent) {
        if (StrUtil.isEmpty(fullTextContent)) {
            return "";
        }

        // 查找列表页内容标记
        String listPageMarker = "=== 列表页内容 ===";
        String detailPageMarker = "=== 详情页:";

        int listStart = fullTextContent.indexOf(listPageMarker);
        if (listStart == -1) {
            // 如果没有找到标记，可能整个内容都是列表页内容
            if (!fullTextContent.contains(detailPageMarker)) {
                return fullTextContent.trim();
            }
            return "";
        }

        // 找到列表页内容的开始位置
        listStart += listPageMarker.length();

        // 查找第一个详情页标记的位置
        int detailStart = fullTextContent.indexOf(detailPageMarker, listStart);

        String listContent;
        if (detailStart == -1) {
            // 没有详情页内容，从列表页标记到结尾都是列表页内容
            listContent = fullTextContent.substring(listStart);
        } else {
            // 从列表页标记到第一个详情页标记之间是列表页内容
            listContent = fullTextContent.substring(listStart, detailStart);
        }

        return listContent.trim();
    }

    /**
     * 从完整文本内容中提取详情页内容
     *
     * @param fullTextContent 完整文本内容
     * @return 详情页内容
     */
    private String extractDetailPagesContent(String fullTextContent) {
        if (StrUtil.isEmpty(fullTextContent)) {
            return "";
        }

        // 查找详情页内容标记
        String detailPageMarker = "=== 详情页:";

        int detailStart = fullTextContent.indexOf(detailPageMarker);
        if (detailStart == -1) {
            return "";
        }

        // 从第一个详情页标记到结尾都是详情页内容
        String detailContent = fullTextContent.substring(detailStart);

        return detailContent.trim();
    }

    /**
     * 将详情页内容作为特殊附件保存到MinIO
     *
     * @param detailPagesContent 详情页内容
     * @param task 爬虫任务
     * @param crawlResult 爬取结果，用于提取详情页标题信息
     */
    private void saveDetailPagesAsAttachment(String detailPagesContent, CrawlerTask task, CrawlerResult crawlResult) {
        try {
            if (StrUtil.isEmpty(detailPagesContent)) {
                return;
            }

            log.info("保存详情页内容作为特殊附件，长度: {} 字符", detailPagesContent.length());

            // 创建临时目录（使用配置的路径）
            File tempDir = new File(staticTempPath, "detail_pages");
            if (!tempDir.exists()) {
                tempDir.mkdirs();
                log.info("创建详情页临时目录: {}", tempDir.getAbsolutePath());
            }

            // 生成有意义的文件名，从详情页内容中提取标题（不包含任务ID）
            String cleanFileName = generateCleanDetailPagesFileName(detailPagesContent);

            // 创建随机文件夹来保证唯一性，保持原始文件名
            String randomFolder = UUID.randomUUID().toString().substring(0, 8);
            File tempFile = new File(tempDir, cleanFileName);

            // 保存详情页内容到临时文件
            try (FileOutputStream out = new FileOutputStream(tempFile)) {
                out.write(detailPagesContent.getBytes(StandardCharsets.UTF_8));
            }

            // 生成内容哈希
            String contentMd5 = DigestUtils.md5DigestAsHex(detailPagesContent.getBytes(StandardCharsets.UTF_8));

            // 上传到MinIO，使用随机文件夹保证唯一性
            String objectName = "crawler/" + task.getId() + "/detail_pages/" + randomFolder + "/" + cleanFileName;
            String minioUrl;

            try (FileInputStream fis = new FileInputStream(tempFile)) {
                byte[] fileBytes = org.apache.commons.io.IOUtils.toByteArray(fis);
                minioUrl = minioUtil.uploadFile(
                        fileBytes,
                        minioBucketName,
                        objectName,
                        "text/plain"
                );

                log.info("详情页内容已上传到MinIO: {}", objectName);
            }

            // 保存文件信息到数据库
            CrawlerFile file = new CrawlerFile();
            file.setTaskId(task.getId());
            file.setFileName(cleanFileName); // 使用清理后的文件名
            file.setFileType("txt");
            file.setFileSize((long) detailPagesContent.length());
            file.setMinioUrl(minioUrl);
            file.setTitle("详情页内容合集");
            file.setContentHash(contentMd5);
            file.setStatus("success");
            file.setCreateTime(LocalDateTime.now());
            file.setUpdateTime(LocalDateTime.now());
            crawlerFileService.save(file);

            // 更新下载统计
            downloadStats.put("success", downloadStats.getOrDefault("success", 0) + 1);
            downloadStats.put("total", downloadStats.getOrDefault("total", 0) + 1);

            // 删除临时文件
            tempFile.delete();

            log.info("详情页内容作为特殊附件保存完成");

        } catch (Exception e) {
            log.error("保存详情页内容作为附件失败: {}", e.getMessage());
        }
    }



    /**
     * 从Content-Type获取文件类型
     */
    private String getFileTypeFromContentType(String contentType) {
        if (StrUtil.isEmpty(contentType)) {
            return "";
        }

        // 移除字符集等参数
        if (contentType.contains(";")) {
            contentType = contentType.substring(0, contentType.indexOf(";"));
        }

        // 常见MIME类型映射
        Map<String, String> mimeTypeMap = new HashMap<>();
        mimeTypeMap.put("application/pdf", "pdf");
        mimeTypeMap.put("application/msword", "doc");
        mimeTypeMap.put("application/vnd.openxmlformats-officedocument.wordprocessingml.document", "docx");
        mimeTypeMap.put("application/vnd.ms-excel", "xls");
        mimeTypeMap.put("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "xlsx");
        mimeTypeMap.put("application/vnd.ms-powerpoint", "ppt");
        mimeTypeMap.put("application/vnd.openxmlformats-officedocument.presentationml.presentation", "pptx");
        mimeTypeMap.put("text/plain", "txt");
        mimeTypeMap.put("text/html", "html");
        mimeTypeMap.put("text/xml", "xml");
        mimeTypeMap.put("text/csv", "csv");
        mimeTypeMap.put("image/jpeg", "jpg");
        mimeTypeMap.put("image/png", "png");
        mimeTypeMap.put("image/gif", "gif");
        mimeTypeMap.put("image/bmp", "bmp");
        mimeTypeMap.put("image/webp", "webp");
        mimeTypeMap.put("application/zip", "zip");
        mimeTypeMap.put("application/x-rar-compressed", "rar");
        mimeTypeMap.put("application/x-7z-compressed", "7z");

        return mimeTypeMap.getOrDefault(contentType, contentType.substring(contentType.lastIndexOf('/') + 1));
    }

    /**
     * 从文件名获取文件类型
     */
    private String getFileTypeFromName(String fileName) {
        if (StrUtil.isEmpty(fileName) || !fileName.contains(".")) {
            return "unknown";
        }

        String extension = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();

        // 常见文件扩展名
        Set<String> validExtensions = new HashSet<>(Arrays.asList(
                "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", 
                "jpg", "jpeg", "png", "gif", "bmp", "webp", 
                "txt", "html", "htm", "shtml", "shtm", "xml", "csv", 
                "zip", "rar", "7z"
        ));

        return validExtensions.contains(extension) ? extension : "unknown";
    }

    /**
     * 从URL获取文件名
     */
    private String getFileNameFromUrl(String url) {
        try {
            String fileName = url.substring(url.lastIndexOf('/') + 1);
            
            // 处理URL参数
            if (fileName.contains("?")) {
                fileName = fileName.substring(0, fileName.indexOf("?"));
            }
            
            // 处理URL锚点
            if (fileName.contains("#")) {
                fileName = fileName.substring(0, fileName.indexOf("#"));
            }
            
            // URL解码
            fileName = URLDecoder.decode(fileName, StandardCharsets.UTF_8.name());
            
            // 文件名为空时使用随机UUID
            if (StrUtil.isEmpty(fileName) || fileName.startsWith(".")) {
                String extension = "";
                if (fileName.contains(".")) {
                    extension = fileName.substring(fileName.lastIndexOf('.'));
                }
                fileName = UUID.randomUUID().toString() + extension;
            }
            
            return fileName;
            } catch (Exception e) {
            log.error("从URL获取文件名失败: {}", url, e);
            return UUID.randomUUID().toString();
        }
    }
    
    /**
     * 处理文件名，移除不安全字符
     */
    private String sanitizeFileName(String fileName) {
        if (StrUtil.isEmpty(fileName)) {
            return "unnamed_file";
        }
        
        // 替换文件名中的非法字符
        String sanitized = fileName.replaceAll("[\\\\/:*?\"<>|]", "_");
        
        // 限制文件名长度
        if (sanitized.length() > 255) {
            sanitized = sanitized.substring(0, 255);
        }
        
        return sanitized;
    }

    /**
     * 检查文件是否已经存在（增量更新逻辑）
     *
     * @param fileUrl 文件URL
     * @param taskId 任务ID
     * @return 如果文件已存在返回true，否则返回false
     */
    private boolean isFileAlreadyExists(String fileUrl, Long taskId) {
        try {
            // 使用 MyBatis-Plus 的 LambdaQueryWrapper 进行查询
            com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<CrawlerFile> queryWrapper =
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<>();

            // 方法1: 根据原始URL检查
            queryWrapper.eq(CrawlerFile::getOriginalUrl, fileUrl);
            List<CrawlerFile> existingFilesByUrl = crawlerFileService.list(queryWrapper);
            if (!existingFilesByUrl.isEmpty()) {
                log.info("根据原始URL找到已存在的文件: {}", fileUrl);
                return true;
            }

            // 方法2: 根据源URL检查（兼容性）
            queryWrapper.clear();
            queryWrapper.eq(CrawlerFile::getSourceUrl, fileUrl);
            List<CrawlerFile> existingFilesBySourceUrl = crawlerFileService.list(queryWrapper);
            if (!existingFilesBySourceUrl.isEmpty()) {
                log.info("根据源URL找到已存在的文件: {}", fileUrl);
                return true;
            }

            // 方法3: 根据任务ID和原始URL的组合检查（同一任务内去重）
            queryWrapper.clear();
            queryWrapper.eq(CrawlerFile::getTaskId, taskId)
                        .and(wrapper -> wrapper.eq(CrawlerFile::getOriginalUrl, fileUrl)
                                              .or()
                                              .eq(CrawlerFile::getSourceUrl, fileUrl));
            List<CrawlerFile> existingFilesByTask = crawlerFileService.list(queryWrapper);
            if (!existingFilesByTask.isEmpty()) {
                log.info("根据任务ID和URL找到已存在的文件: taskId={}, url={}", taskId, fileUrl);
                return true;
            }

            return false;

        } catch (Exception e) {
            log.error("检查文件是否存在时发生错误: {}", e.getMessage());
            // 发生错误时，为了安全起见，假设文件不存在，继续下载
            return false;
        }
    }

    /**
     * 检查详情页内容是否已经存在（增量更新逻辑）
     *
     * @param detailPagesContent 详情页内容
     * @param taskId 任务ID
     * @return 如果内容已存在返回true，否则返回false
     */
    private boolean isDetailPagesContentExists(String detailPagesContent, Long taskId) {
        try {
            // 生成内容哈希用于比较
            String contentMd5 = org.springframework.util.DigestUtils.md5DigestAsHex(
                detailPagesContent.getBytes(java.nio.charset.StandardCharsets.UTF_8));

            // 使用 MyBatis-Plus 的 LambdaQueryWrapper 进行查询
            com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<CrawlerFile> queryWrapper =
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<>();

            // 方法1: 根据内容哈希检查
            queryWrapper.eq(CrawlerFile::getContentHash, contentMd5);
            List<CrawlerFile> existingFilesByHash = crawlerFileService.list(queryWrapper);
            if (!existingFilesByHash.isEmpty()) {
                log.info("根据内容哈希找到已存在的详情页内容");
                return true;
            }

            // 方法2: 根据任务ID和文件类型检查（详情页内容通常是txt类型）
            queryWrapper.clear();
            queryWrapper.eq(CrawlerFile::getTaskId, taskId)
                        .eq(CrawlerFile::getFileType, "txt")
                        .like(CrawlerFile::getFileName, "详情页");
            List<CrawlerFile> existingDetailPages = crawlerFileService.list(queryWrapper);
            if (!existingDetailPages.isEmpty()) {
                log.info("根据任务ID找到已存在的详情页内容文件");
                return true;
            }

            return false;

        } catch (Exception e) {
            log.error("检查详情页内容是否存在时发生错误: {}", e.getMessage());
            // 发生错误时，为了安全起见，假设内容不存在，继续保存
            return false;
        }
    }

    /**
     * 添加增强的输出参数，确保包含爬取结果的所有关键信息
     *
     * @param result 爬取结果
     * @param taskId 任务ID
     * @param batchId 批次ID
     */
    private void addEnhancedOutputs(Map<String, Object> result, Long taskId, String batchId) {
        if (CollUtil.isEmpty(outputs)) {
            log.info("添加增强的输出参数");
            // 基本输出
            addOutput("result", result, "Object");
            addOutput("taskId", taskId, "Number");
            addOutput("batchId", batchId, "String");

            // 统计信息
            @SuppressWarnings("unchecked")
            Map<String, Integer> stats = (Map<String, Integer>) result.getOrDefault("downloadStats", downloadStats);
            addOutput("pageCount", stats.getOrDefault("pageCount", 0), "Number");
            addOutput("fileCount", stats.getOrDefault("success", 0), "Number");
            addOutput("failedCount", stats.getOrDefault("failed", 0), "Number");

            // 爬取状态
            addOutput("success", result.getOrDefault("success", false), "Boolean");
            addOutput("hasData", result.getOrDefault("hasData", false), "Boolean");

            // 如果有文本内容，添加文本内容输出
            if (result.containsKey("textContent")) {
                String textContent = (String) result.get("textContent");
                if (StrUtil.isNotEmpty(textContent)) {
                    // 如果文本内容太长，只输出摘要
                    if (textContent.length() > 1000) {
                        addOutput("textContentSummary", textContent.substring(0, 1000) + "...(已截断)", "String");
                    } else {
                        addOutput("textContent", textContent, "String");
                    }
                    log.info("添加文本内容输出，长度：{}", textContent.length());
                }
            }

            // 如果有附件信息，添加附件信息输出
            if (result.containsKey("attachments")) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> attachments = (List<Map<String, Object>>) result.get("attachments");
                if (CollUtil.isNotEmpty(attachments)) {
                    addOutput("attachmentsCount", attachments.size(), "Number");
                    addOutput("attachments", attachments, "Array");
                    log.info("添加附件输出，数量：{}", attachments.size());
                }
            }

            // 如果有失败文件，添加失败文件信息
            if (result.containsKey("failedFiles")) {
                @SuppressWarnings("unchecked")
                List<Map<String, String>> failedFiles = (List<Map<String, String>>) result.get("failedFiles");
                if (CollUtil.isNotEmpty(failedFiles)) {
                    addOutput("failedFiles", failedFiles, "Array");
                    log.info("添加失败文件输出，数量：{}", failedFiles.size());
                }
            }

            // 如果有跳过信息，添加跳过信息输出
            if (result.containsKey("skipReason")) {
                addOutput("skipReason", result.get("skipReason"), "String");
                addOutput("skipTime", result.get("skipTime"), "String");
                addOutput("isSkipped", true, "Boolean");
                log.info("添加跳过信息输出：{}", result.get("skipReason"));
            } else {
                addOutput("isSkipped", false, "Boolean");
            }

            // 添加任务状态信息
            if (result.containsKey("taskStatus")) {
                addOutput("taskStatus", result.get("taskStatus"), "String");
            }

            // 添加执行时间信息
            if (result.containsKey("startTime")) {
                addOutput("startTime", result.get("startTime"), "String");
            }
            if (result.containsKey("endTime")) {
                addOutput("endTime", result.get("endTime"), "String");
            }
        }
    }

    /**
     * 创建结果Map
     *
     * @param taskId 任务ID
     * @param batchId 批次ID
     * @param settingsId 配置ID
     * @return 结果Map
     */
    private Map<String, Object> createResultMap(Long taskId, String batchId, Long settingsId) {
        Map<String, Object> result = new HashMap<>();
        result.put("taskId", taskId);
        result.put("batchId", batchId);
        result.put("settingsId", settingsId);
        result.put("pageCount", downloadStats.get("pageCount"));
        result.put("fileCount", downloadStats.get("success"));
        result.put("failedCount", downloadStats.get("failed"));
        result.put("failedFiles", failedFiles);
        log.info("创建结果Map：任务ID={}，批次ID={}，配置ID={}", taskId, batchId, settingsId);
        return result;
    }

    /**
     * 根据配置ID查找已存在的爬虫任务（增量更新逻辑）
     *
     * @param settingsId 配置ID
     * @return 已存在的任务，如果不存在则返回null
     */
    private CrawlerTask findExistingTaskBySettings(Long settingsId) {
        try {
            log.info("查询已存在的爬虫任务：配置ID={}", settingsId);

            // 使用 MyBatis-Plus 的 LambdaQueryWrapper 进行查询
            LambdaQueryWrapper<CrawlerTask> queryWrapper =
                new LambdaQueryWrapper<>();

            // 查询配置ID相同的任务
            queryWrapper.eq(CrawlerTask::getSettingsId, settingsId)
                        .orderByDesc(CrawlerTask::getCreateTime)
                        .last("LIMIT 1");

            CrawlerTask task = crawlerTaskService.getOne(queryWrapper);
            if (task != null) {
                log.info("找到配置id相同的爬虫任务：ID={}，状态={}，批次ID={}，创建时间={}",
                        task.getId(), task.getStatus(), task.getBatchId(), task.getCreateTime());
            } else {
                log.info("未找到配置id相同的爬虫任务，可以创建新任务");
            }
            return task;
        } catch (Exception e) {
            log.error("查询已存在任务失败", e);
            return null;
        }
    }

    /**
     * 初始化下载统计
     */
    private void initializeStats() {
        downloadStats.put("success", 0);
        downloadStats.put("failed", 0);
        downloadStats.put("pageCount", 0);
        downloadStats.put("total", 0);
    }

    /**
     * 创建已完成任务的结果
     */
    private Map<String, Object> createCompletedTaskResult(CrawlerTask task) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("hasData", true);
        result.put("status", "completed");
        result.put("taskStatus", "completed");
        result.put("message", "任务已完成，跳过重复执行");
        result.put("taskId", task.getId());
        result.put("batchId", task.getBatchId());
        result.put("settingsId", task.getSettingsId());
        result.put("pageCount", task.getPageCount() != null ? task.getPageCount() : 0);
        result.put("fileCount", task.getFileCount() != null ? task.getFileCount() : 0);
        result.put("failedCount", task.getFailedCount() != null ? task.getFailedCount() : 0);
        result.put("startTime", task.getStartTime() != null ? task.getStartTime().toString() : null);
        result.put("endTime", task.getEndTime() != null ? task.getEndTime().toString() : null);
        result.put("createTime", task.getCreateTime() != null ? task.getCreateTime().toString() : null);
        result.put("updateTime", task.getUpdateTime() != null ? task.getUpdateTime().toString() : null);

        // 添加文本内容摘要
        if (StrUtil.isNotEmpty(task.getTextContent())) {
            String textContent = task.getTextContent();
            if (textContent.length() > 500) {
                result.put("textContentSummary", textContent.substring(0, 500) + "...(已截断)");
            } else {
                result.put("textContent", textContent);
            }
        }

        result.put("downloadStats", Map.of(
            "success", task.getFileCount() != null ? task.getFileCount() : 0,
            "failed", task.getFailedCount() != null ? task.getFailedCount() : 0,
            "pageCount", task.getPageCount() != null ? task.getPageCount() : 0,
            "total", (task.getFileCount() != null ? task.getFileCount() : 0) +
                    (task.getFailedCount() != null ? task.getFailedCount() : 0)
        ));
        return result;
    }

    /**
     * 创建正在运行任务的结果
     */
    private Map<String, Object> createRunningTaskResult(CrawlerTask task) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("hasData", false);
        result.put("status", "running");
        result.put("taskStatus", "running");
        result.put("message", "任务正在运行中，跳过重复执行");
        result.put("taskId", task.getId());
        result.put("batchId", task.getBatchId());
        result.put("settingsId", task.getSettingsId());
        result.put("pageCount", task.getPageCount() != null ? task.getPageCount() : 0);
        result.put("fileCount", task.getFileCount() != null ? task.getFileCount() : 0);
        result.put("failedCount", task.getFailedCount() != null ? task.getFailedCount() : 0);
        result.put("startTime", task.getStartTime() != null ? task.getStartTime().toString() : null);
        result.put("endTime", null); // 运行中的任务没有结束时间
        result.put("createTime", task.getCreateTime() != null ? task.getCreateTime().toString() : null);
        result.put("updateTime", task.getUpdateTime() != null ? task.getUpdateTime().toString() : null);

        // 计算运行时长
        if (task.getStartTime() != null) {
            long runningMinutes = java.time.Duration.between(task.getStartTime(), LocalDateTime.now()).toMinutes();
            result.put("runningMinutes", runningMinutes);
            result.put("runningStatus", runningMinutes > 30 ? "可能异常" : "正常运行中");
        }

        result.put("downloadStats", Map.of(
            "success", task.getFileCount() != null ? task.getFileCount() : 0,
            "failed", task.getFailedCount() != null ? task.getFailedCount() : 0,
            "pageCount", task.getPageCount() != null ? task.getPageCount() : 0,
            "total", (task.getFileCount() != null ? task.getFileCount() : 0) +
                    (task.getFailedCount() != null ? task.getFailedCount() : 0)
        ));
        return result;
    }

    @Override
    public String desc() {
        return "爬虫节点 - 使用Playwright浏览器抓取网页内容和附件";
    }

    /**
     * 属性配置类
     */
    @Data
    @NoArgsConstructor
    public static class Properties {
        /** 爬虫配置ID */
        private Long settingsId;
        /** 批次ID，用于断点续爬 */
        private String batchId;
        /** 超时设置（毫秒） */
        private Integer timeout;
        /** 重试次数 */
        private Integer retryCount;
    }

    /**
     * 从详情页内容中生成有意义的文件名（不包含任务ID）
     *
     * @param detailPagesContent 详情页内容
     * @return 生成的文件名
     */
    private String generateCleanDetailPagesFileName(String detailPagesContent) {
        try {
            // 尝试从详情页内容中提取第一个详情页的标题
            String firstDetailTitle = extractFirstDetailTitle(detailPagesContent);

            if (StrUtil.isNotEmpty(firstDetailTitle)) {
                // 清理标题，作为文件名
                String cleanTitle = sanitizeFileName(firstDetailTitle);

                // 限制文件名长度，避免过长
                if (cleanTitle.length() > 100) {
                    cleanTitle = cleanTitle.substring(0, 100);
                }

                return cleanTitle + "_详情页合集.txt";
            }

        } catch (Exception e) {
            log.info("从详情页内容提取标题失败: {}", e.getMessage());
        }

        // 如果提取失败，使用默认文件名
        return "详情页内容合集.txt";
    }

    /**
     * 从详情页内容中生成有意义的文件名
     *
     * @param detailPagesContent 详情页内容
     * @param taskId 任务ID
     * @return 生成的文件名
     */
    private String generateDetailPagesFileName(String detailPagesContent, Long taskId) {
        try {
            // 尝试从详情页内容中提取第一个详情页的标题
            String firstDetailTitle = extractFirstDetailTitle(detailPagesContent);

            if (StrUtil.isNotEmpty(firstDetailTitle)) {
                // 清理标题，作为文件名
                String cleanTitle = sanitizeFileName(firstDetailTitle);

                // 限制文件名长度，避免过长
                if (cleanTitle.length() > 100) {
                    cleanTitle = cleanTitle.substring(0, 100);
                }

                return cleanTitle + "_详情页合集_" + taskId + ".txt";
            }

        } catch (Exception e) {
            log.info("从详情页内容提取标题失败: {}", e.getMessage());
        }

        // 如果提取失败，使用默认文件名
        return "detail_pages_" + taskId + ".txt";
    }

    /**
     * 从详情页内容中提取第一个详情页的标题
     *
     * @param detailPagesContent 详情页内容
     * @return 第一个详情页的标题
     */
    private String extractFirstDetailTitle(String detailPagesContent) {
        if (StrUtil.isEmpty(detailPagesContent)) {
            return "";
        }

        // 查找第一个详情页标记
        String detailPageMarker = "=== 详情页:";
        int detailStart = detailPagesContent.indexOf(detailPageMarker);

        if (detailStart == -1) {
            log.info("未找到详情页标记，尝试从内容中提取标题");
            return extractTitleFromContent(detailPagesContent);
        }

        // 提取标题部分
        int titleStart = detailStart + detailPageMarker.length();
        int titleEnd = detailPagesContent.indexOf("===", titleStart);

        if (titleEnd == -1) {
            // 如果没有找到结束标记，查找换行符
            titleEnd = detailPagesContent.indexOf("\n", titleStart);
            if (titleEnd == -1) {
                titleEnd = Math.min(titleStart + 100, detailPagesContent.length());
            }
        }

        String title = detailPagesContent.substring(titleStart, titleEnd).trim();

        // 移除可能的时间信息
        if (title.contains("发布时间:")) {
            int timeIndex = title.indexOf("发布时间:");
            title = title.substring(0, timeIndex).trim();
        }

        // 如果标题为空或太短，尝试从内容中提取
        if (StrUtil.isEmpty(title) || title.length() < 3) {
            log.info("提取的标题为空或太短: [{}]，尝试从内容中提取", title);
            String contentTitle = extractTitleFromContent(detailPagesContent);
            if (StrUtil.isNotEmpty(contentTitle)) {
                return contentTitle;
            }
        }

        return title;
    }

    /**
     * 从详情页内容中提取标题（备用方法）
     */
    private String extractTitleFromContent(String content) {
        if (StrUtil.isEmpty(content)) {
            return "";
        }

        // 尝试从内容的前几行中提取标题
        String[] lines = content.split("\n");
        for (String line : lines) {
            line = line.trim();
            if (StrUtil.isNotEmpty(line) && line.length() > 5 && line.length() < 200) {
                // 跳过明显不是标题的行
                if (line.startsWith("===") || line.startsWith("发布时间:") ||
                    line.startsWith("来源:") || line.startsWith("http")) {
                    continue;
                }

                // 如果行包含中文且长度合适，可能是标题
                if (line.matches(".*[\u4e00-\u9fa5].*") && line.length() >= 5) {
                    log.info("从内容中提取标题: {}", line);
                    return line;
                }
            }
        }

        return "";
    }

    /**
     * 生成显示用的文件名（去掉任务ID等技术信息）
     *
     * @param fileName 原始文件名
     * @return 显示用文件名
     */
    private String generateDisplayFileName(String fileName) {
        if (StrUtil.isEmpty(fileName)) {
            return "详情页内容合集.txt";
        }

        // 如果是默认格式，返回通用名称
        if (fileName.startsWith("detail_pages_")) {
            return "详情页内容合集.txt";
        }

        // 移除任务ID部分，保留有意义的标题
        String displayName = fileName;
        if (displayName.contains("_详情页合集_")) {
            int index = displayName.indexOf("_详情页合集_");
            displayName = displayName.substring(0, index) + ".txt";
        }

        return displayName;
    }
}