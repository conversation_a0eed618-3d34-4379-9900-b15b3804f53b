package com.icarus.common.cotnode;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.icarus.common.runtime.NodeContext;
import com.icarus.starter.channel.queue.sse.SseEmitterManager;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
@Component
public class SSEMessageNode extends BaseNode {

    @Getter
    @Setter
    private String result;

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【SSE消息】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);

        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            nodeContext.setInputs(new JSONObject(resolvedInputs));

            // 解析属性配置
            Properties resolvedProperties = resolveProperties(context, Properties.class);
            JSONObject propertiesJson = new JSONObject();
            propertiesJson.set("sessionId", resolvedProperties.getSessionId());
            propertiesJson.set("messageContent", resolvedProperties.getMessageContent());
            nodeContext.setProperties(propertiesJson);

            // 获取会话ID
            String sessionId = resolvedProperties.getSessionId();

            // 如果未指定会话ID，尝试从上下文中获取
            if (sessionId == null || sessionId.isEmpty()) {
                // 1. 首先从上下文的request.headers中获取
                Object requestObj = context.get("request");
                if (requestObj != null) {
                    try {
                        // 尝试从RuntimeRequest对象中获取headers
                        Map<String, String> headers = (Map<String, String>) getFieldValue(requestObj, "headers");
                        if (headers != null && headers.containsKey("aip-session")) {
                            sessionId = headers.get("aip-session");
                            log.info("从request.headers获取会话ID: {}", sessionId);
                        }
                    } catch (Exception e) {
                        log.info("从request.headers获取会话ID失败", e);
                    }
                }
                
                // 2. 如果headers中没有，尝试从上下文的sessionId中获取
                if (sessionId == null || sessionId.isEmpty()) {
                    Object contextSessionId = context.get("aip-session");
                    if (contextSessionId != null) {
                        sessionId = contextSessionId.toString();
                        log.info("从上下文获取会话ID: {}", sessionId);
                    } else {
                        throw new RuntimeException("未指定会话ID，且无法从上下文中获取sessionId");
                    }
                }
            }

            // 发送消息
            String messageContent = resolvedProperties.getMessageContent();

            boolean success = sendMessage(sessionId, "message", messageContent);

            if (success) {
                nodeContext.setStatus("completed");
                log.info("SSE消息发送成功，会话ID：{}", sessionId);
            } else {
                nodeContext.setStatus("failed");
                nodeContext.setErrorMessage("消息发送失败，会话ID：" + sessionId);
                log.error("SSE消息发送失败，会话ID：{}", sessionId);
            }

            // 将结果添加到上下文中，供后续节点使用
            context.put("result", result);
            // 处理输出
            processOutputs(context);

        } catch (Exception e) {
            log.error("SSE消息节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
            // 发生异常时也设置结果
            result = "ERROR:" + e.getMessage();
            context.put("result", result);
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        log.info("SSE消息节点执行结束，节点ID：{}，结果：{}", getId(), result);
        return context;
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    /**
     * 发送消息到SSE连接
     * 
     * @param sessionId 会话id
     * @param type      消息类型
     * @param message   消息内容
     * @return 是否发送成功
     */
    private boolean sendMessage(String sessionId, String type, String message) {
        log.info("准备发送消息，会话ID：{}，类型：{}，内容：{}", sessionId, type, message);

        // 获取SSE连接
        SseEmitter sseEmitter = SseEmitterManager.get(sessionId);
        if (sseEmitter == null) {
            log.error("SSE连接不存在，会话ID：{}", sessionId);
            result = "FAILED:CONNECTION_NOT_FOUND";
            return false;
        }

        try {
            JSONObject retJson = new JSONObject();
            retJson.put("type", type);
            retJson.put("content", message);
            // 发送消息
            SseEmitterManager.send(sessionId, JSONUtil.toJsonStr(retJson));

            log.info("消息发送成功，会话ID：{}，类型：{}", sessionId, type);
            result = "SUCCESS";
            return true;

        } catch (Exception e) {
            log.error("消息发送失败，会话ID：{}", sessionId, e);
            // 连接可能已失效，清理资源
            SseEmitterManager.del(sessionId);
            result = "FAILED:" + e.getMessage();
            return false;
        }
    }

    /**
     * 流式发送消息到SSE连接
     * 
     * @param sessionId      会话id
     * @param type           消息类型
     * @param message        完整消息内容
     * @param chunkSize      每次发送的字符数
     * @param streamInterval 发送间隔(毫秒)
     * @return 是否发送成功
     */
    private boolean sendStreamMessage(String sessionId, String type, String message, int chunkSize,
            int streamInterval) {
        log.info("准备流式发送消息，会话ID：{}，类型：{}，总长度：{}", sessionId, type, message.length());

        // 获取SSE连接
        SseEmitter sseEmitter = SseEmitterManager.get(sessionId);
        if (sseEmitter == null) {
            log.error("SSE连接不存在，会话ID：{}", sessionId);
            result = "FAILED:CONNECTION_NOT_FOUND";
            return false;
        }

        // 确保参数有效
        int actualChunkSize = chunkSize > 0 ? chunkSize : 20; // 默认每次20个字符
        int actualInterval = streamInterval > 0 ? streamInterval : 100; // 默认100毫秒

        try {
            // 发送开始标记
            JSONObject startData = new JSONObject();
            startData.put("type", type + "_start");
            startData.put("timestamp", System.currentTimeMillis());
            startData.put("totalLength", message.length());
            SseEmitterManager.send(sessionId, JSONUtil.toJsonStr(startData));

            // 分块发送消息
            int messageLength = message.length();
            int position = 0;
            int chunkIndex = 0;

            while (position < messageLength) {
                int endPosition = Math.min(position + actualChunkSize, messageLength);
                String chunk = message.substring(position, endPosition);

                JSONObject chunkData = new JSONObject();
                chunkData.put("type", type + "_chunk");
                chunkData.put("chunk", chunk);
                chunkData.put("chunkIndex", chunkIndex);
                chunkData.put("position", position);
                chunkData.put("timestamp", System.currentTimeMillis());

                SseEmitterManager.send(sessionId, JSONUtil.toJsonStr(chunkData));
                log.info("发送消息块 {}: 位置 {}-{}, 内容: {}", chunkIndex, position, endPosition, chunk);

                position = endPosition;
                chunkIndex++;

                // 添加发送间隔，避免过快发送
                if (position < messageLength && actualInterval > 0) {
                    Thread.sleep(actualInterval);
                }
            }

            // 发送结束标记
            JSONObject endData = new JSONObject();
            endData.put("type", type + "_end");
            endData.put("timestamp", System.currentTimeMillis());
            endData.put("totalChunks", chunkIndex);
            SseEmitterManager.send(sessionId, JSONUtil.toJsonStr(endData));

            log.info("流式发送消息完成，会话ID：{}，类型：{}，共发送{}块", sessionId, type, chunkIndex);
            result = "SUCCESS:CHUNKS_" + chunkIndex;
            return true;

        } catch (Exception e) {
            log.error("流式发送消息失败，会话ID：{}", sessionId, e);
            // 连接可能已失效，清理资源
            SseEmitterManager.del(sessionId);
            result = "FAILED:" + e.getMessage();
            return false;
        }
    }

    /**
     * 通过反射获取对象的字段值
     * @param obj 对象
     * @param fieldName 字段名
     * @return 字段值
     */
    private Object getFieldValue(Object obj, String fieldName) {
        try {
            // 获取对象的类
            Class<?> clazz = obj.getClass();
            
            // 尝试直接获取字段
            try {
                java.lang.reflect.Field field = clazz.getDeclaredField(fieldName);
                field.setAccessible(true);
                return field.get(obj);
            } catch (NoSuchFieldException e) {
                // 字段不存在，尝试通过getter方法获取
                String getterName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                java.lang.reflect.Method getter = clazz.getMethod(getterName);
                return getter.invoke(obj);
            }
        } catch (Exception e) {
            log.info("获取对象字段值失败: {}.{}", obj.getClass().getName(), fieldName, e);
            return null;
        }
    }

    @Data
    @NoArgsConstructor
    public static class Properties {
        private String sessionId; // 会话ID，如果为空则尝试从上下文中获取
        private String messageContent; // 消息内容
    }

    @Override
    public String desc() {
        return "SSE消息节点";
    }
}