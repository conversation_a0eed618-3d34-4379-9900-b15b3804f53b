package com.icarus.common.cotnode;

import cn.hutool.json.JSONObject;
import com.icarus.common.runtime.NodeContext;
import com.icarus.sdk.springboot.base.utils.SpringContextUtils;
import com.icarus.service.model.FileInfo;
import com.icarus.utils.FileUtil;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.ArchiveEntry;
import org.apache.commons.compress.archivers.ArchiveInputStream;
import org.apache.commons.compress.archivers.zip.ZipArchiveInputStream;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.apache.commons.compress.compressors.gzip.GzipCompressorInputStream;
import org.apache.commons.compress.archivers.sevenz.SevenZFile;
import org.apache.commons.compress.archivers.sevenz.SevenZArchiveEntry;
import com.icarus.service.FileStorageService;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class UnzipNode extends BaseNode {
    
    @Getter
    @Setter
    private String[] result;
    
    // 文件根路径，可以通过配置注入
    private static final String filePath = FileUtil.AI_DESIGNER_PATH.toString();

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【解压缩】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
        File sourceFile = null;
        
        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            if (resolvedInputs == null) {
                throw new IllegalArgumentException("输入参数不能为空");
            }

            // 验证必要的输入参数
            String filePath = (String) resolvedInputs.get("filePath");
            if (filePath == null) {
                throw new IllegalArgumentException("filePath 是必需的输入参数");
            }

            nodeContext.setInputs(new JSONObject(resolvedInputs));
            
            // 获取压缩文件对象
            sourceFile = getFileFromPath(filePath);
            if (sourceFile == null) {
                throw new IllegalArgumentException("无法获取文件: " + filePath);
            }
            
            // 从文件扩展名判断压缩类型
            String fileExtension = getFileExtension(sourceFile.getName());
            if (!isValidCompressionType(fileExtension)) {
                throw new RuntimeException("不支持的压缩文件类型: " + fileExtension);
            }

            // 生成解压目标路径
            String fileName = getFileNameWithoutExtension(sourceFile.getAbsolutePath());
            String date = new java.text.SimpleDateFormat("yyyyMMdd").format(new java.util.Date());
            String targetPath = UnzipNode.filePath + File.separator + date + File.separator + fileName + "_unzip";
            
            // 创建解压目标目录
            File targetDir = new File(targetPath);
            if (!targetDir.exists() && !targetDir.mkdirs()) {
                throw new IOException("无法创建目标目录: " + targetPath);
            }

            // 执行解压缩并获取上传后的文件路径列表
            List<String> uploadedPaths = extractFile(sourceFile, targetPath, fileExtension);
            // 将List转换为数组作为结果
            result = uploadedPaths.toArray(new String[0]);
            
            nodeContext.setStatus("completed");
            processOutputs(context);

        } catch (Exception e) {
            log.error("解压缩节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        log.info("解压缩节点执行结束，节点ID：{}", getId());
        return context;
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    /**
     * 获取文件扩展名（处理大小写）
     */
    private String getFileExtension(String filePath) {
        String extension = filePath.substring(filePath.lastIndexOf(".") + 1).toLowerCase();
        return extension;
    }

    /**
     * 验证压缩类型是否支持
     */
    private boolean isValidCompressionType(String fileExtension) {
        // 使用Set或枚举可能更好，这里为了简单使用equals
        String ext = fileExtension.toLowerCase();
        return ext.equals("zip") || 
               ext.equals("tar") || 
               ext.equals("gz") || 
               ext.equals("7z");
    }

    private List<String> extractFile(File source, String targetDir, String compressionType) throws IOException {
        List<String> uploadedPaths = new ArrayList<>();
        File targetDirFile = new File(targetDir);
        
        // 添加文件检查
        if (source == null || !source.exists()) {
            throw new IOException("源文件不存在或无法访问");
        }
        try {
            // 执行解压缩
            switch (compressionType.toLowerCase()) {
                case "zip":
                    try (FileInputStream fis = new FileInputStream(source);
                         ZipArchiveInputStream zis = new ZipArchiveInputStream(fis)) {
                        extractArchive(zis, targetDir);
                    }
                    break;
                
                case "tar":
                    try (FileInputStream fis = new FileInputStream(source);
                         TarArchiveInputStream tis = new TarArchiveInputStream(fis)) {
                        extractArchive(tis, targetDir);
                    }
                    break;
                
                case "gz":
                case "gzip":
                    try (FileInputStream fis = new FileInputStream(source);
                         GzipCompressorInputStream gzis = new GzipCompressorInputStream(fis);
                         TarArchiveInputStream tis = new TarArchiveInputStream(gzis)) {
                        extractArchive(tis, targetDir);
                    }
                    break;

                case "7z":
                    extract7z(source, targetDir);
                    break;
                
                default:
                    throw new RuntimeException("不支持的压缩格式: " + compressionType);
            }
            
            // 上传解压后的文件到 FileStorageService
            FileStorageService fileStorageService = SpringContextUtils.getBean(FileStorageService.class);
            if (targetDirFile.exists() && targetDirFile.isDirectory()) {
                uploadDirectory(targetDirFile, fileStorageService, uploadedPaths);
            }
            
            return uploadedPaths;
        } finally {
            // 清理临时文件
            if (source != null) {
                source.delete();
            }
            // 清理解压目录
            if (targetDirFile.exists()) {
                FileUtil.deleteDirectory(targetDirFile);
            }
        }
    }

    /**
     * 递归上传目录中的所有文件
     * @param directory 需要上传的目录
     * @param fileStorageService 文件存储服务
     * @param uploadedFiles 已上传文件路径列表
     * @throws IOException IO异常
     */
    private void uploadDirectory(File directory, FileStorageService fileStorageService, List<String> uploadedFiles) throws IOException {
        if (directory == null || !directory.exists()) {
            log.warn("目录不存在: {}", directory);
            return;
        }

        File[] files = directory.listFiles();
        if (files == null) {
            log.warn("无法列出目录内容: {}", directory);
            return;
        }

        String basePath = directory.getAbsolutePath();
        for (File file : files) {
            if (file.isDirectory()) {
                uploadDirectory(file, fileStorageService, uploadedFiles);
            } else {
                uploadSingleFile(file, basePath, fileStorageService, uploadedFiles);
            }
        }
    }

    /**
     * 上传单个文件
     * @param file 需要上传的文件
     * @param basePath 基础路径，用于计算相对路径
     * @param fileStorageService 文件存储服务
     * @param uploadedFiles 已上传文件路径列表
     */
    private void uploadSingleFile(File file, String basePath, FileStorageService fileStorageService, List<String> uploadedFiles) {
        try {
            MultipartFile multipartFile = new MultipartFile() {
                @Override
                public String getName() {
                    return file.getName();
                }

                @Override
                public String getOriginalFilename() {
                    return file.getName();
                }

                @Override
                public String getContentType() {
                    return "application/octet-stream";
                }

                @Override
                public boolean isEmpty() {
                    return file.length() == 0;
                }

                @Override
                public long getSize() {
                    return file.length();
                }

                @Override
                public byte[] getBytes() throws IOException {
                    return Files.readAllBytes(file.toPath());
                }

                @Override
                public InputStream getInputStream() throws IOException {
                    return new FileInputStream(file);
                }

                @Override
                public void transferTo(File dest) throws IOException {
                    Files.copy(file.toPath(), dest.toPath());
                }
            };

            FileInfo resultFile = fileStorageService.uploadFile(multipartFile, "unzip");
            uploadedFiles.add(resultFile.getUrl());
            log.debug("成功上传文件: {} -> {}", file.getName(), resultFile.getUrl());
            
        } catch (Exception e) {
            log.error("上传文件失败: {} - {}", file.getName(), e.getMessage());
        }
    }

    private void extractArchive(ArchiveInputStream ais, String targetDir) throws IOException {
        ArchiveEntry entry;
        while ((entry = ais.getNextEntry()) != null) {
            if (!ais.canReadEntryData(entry)) {
                continue;
            }
            
            File file = new File(targetDir, entry.getName());
            if (entry.isDirectory()) {
                if (!file.isDirectory() && !file.mkdirs()) {
                    throw new IOException("创建目录失败: " + file);
                }
            } else {
                File parent = file.getParentFile();
                if (!parent.isDirectory() && !parent.mkdirs()) {
                    throw new IOException("创建目录失败: " + parent);
                }
                try (OutputStream os = Files.newOutputStream(file.toPath())) {
                    byte[] buffer = new byte[8192];
                    int len;
                    while ((len = ais.read(buffer)) != -1) {
                        os.write(buffer, 0, len);
                    }
                }
            }
        }
    }

    /**
     * 解压7z文件
     */
    private void extract7z(File source, String targetDir) throws IOException {
        try (SevenZFile sevenZFile = new SevenZFile(source)) {
            SevenZArchiveEntry entry;
            while ((entry = sevenZFile.getNextEntry()) != null) {
                if (!entry.isDirectory()) {
                    File outputFile = new File(targetDir, entry.getName());
                    
                    // 创建父目录
                    File parent = outputFile.getParentFile();
                    if (!parent.exists() && !parent.mkdirs()) {
                        throw new IOException("无法创建目录: " + parent.getAbsolutePath());
                    }

                    // 解压文件
                    try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                        byte[] content = new byte[(int) entry.getSize()];
                        sevenZFile.read(content, 0, content.length);
                        fos.write(content);
                    }
                }
            }
        }
    }

    /**
     * 获取文件名（不包含扩展名）
     */
    private String getFileNameWithoutExtension(String filePath) {
        File file = new File(filePath);
        String fileName = file.getName();
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName;
    }

    /**
     * 获取文件对象，优先使用 FileStorageService，失败则尝试直接文件路径
     * @param filePath 文件路径
     * @return File对象
     */
    private File getFileFromPath(String filePath) {
        try {
            // 首先尝试通过 FileStorageService 获取
            FileStorageService service = SpringContextUtils.getBean(FileStorageService.class);
            InputStream inputStream = service.getFile(filePath);
            if (inputStream != null) {
                String fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
                return com.icarus.utils.FileUtil.inputStreamToFile(inputStream, fileName);
            }
        } catch (Exception e) {
            log.debug("通过 FileStorageService 获取文件失败，尝试本地路径");
        }

        // 直接返回本地文件对象
        return new File(filePath);
    }

    // Properties类简化，移除compressionType
    @Data
    @NoArgsConstructor
    public static class Properties {
        // 不再需要compressionType属性
    }

    @Override
    public String desc() {
        return "解压缩节点";
    }

} 