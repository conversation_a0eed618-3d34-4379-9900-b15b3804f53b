package com.icarus.common.cotnode.ab.md;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import com.icarus.common.cotnode.BaseNode;
import com.icarus.common.minio.MinioUtil;
import com.icarus.common.runtime.NodeContext;
import com.icarus.utils.FileContentReader;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.util.*;

/**
 * 文档转Markdown节点
 * 将文档文件（如doc、docx）转换为Markdown格式，并删除原始文档文件
 */
@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class Doc2MdNode extends BaseNode {

    @Getter
    @Setter
    private String result;

    // 支持的文档类型
    private static final Set<String> SUPPORTED_DOC_TYPES = new HashSet<>(Arrays.asList("DOC", "DOCX"));

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【文档转Markdown】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
        File downloadedFile = null;
        File mdFile = null;
        String originalFilePath = null;
        String originalBucketName = null;
        boolean isOriginalFileUrl = false;
        
        try {
            // 解析属性配置
            Properties resolvedProperties = resolveProperties(context, Properties.class);
            JSONObject propertiesJson = new JSONObject();
            propertiesJson.set("filePath", resolvedProperties.getFilePath());
            nodeContext.setProperties(propertiesJson);

            // 获取MinioUtil实例
            MinioUtil minioUtil = SpringUtil.getBean(MinioUtil.class);

            // 验证必要的输入参数
            String filePath = resolvedProperties.getFilePath();
            if (filePath == null) {
                throw new IllegalArgumentException("filePath 是必需的参数");
            }
            
            // 保存原始文件路径，用于后续删除
            originalFilePath = filePath;
            originalBucketName = resolvedProperties.getBucketName();
            if (StrUtil.isEmpty(originalBucketName)) {
                originalBucketName = minioUtil.getMinioConfig().getBucketName();
                resolvedProperties.setBucketName(originalBucketName);
            }

            // 下载文件到本地临时目录
            String executeId = nodeContext.getExecuteId();
            String downloadDir = System.getProperty("java.io.tmpdir") + File.separator + "doc2md-downloads" + File.separator + executeId;
            File downloadDirFile = new File(downloadDir);
            if (!downloadDirFile.exists()) {
                downloadDirFile.mkdirs();
            }

            // 下载文件
            String downloadedFilePath;
            if (minioUtil.isHttpUrl(filePath)) {
                // 从URL中提取文件名和bucket信息
                Map<String, String> bucketAndObjectMap = minioUtil.extractBucketAndObjectFromUrl(filePath);
                String fileName = bucketAndObjectMap.getOrDefault("objectName", null);
                // 如果是URL，保存bucket信息用于后续删除
                if (originalBucketName == null) {
                    originalBucketName = bucketAndObjectMap.getOrDefault("bucketName", null);

                }
                
                if (fileName.contains("/")) {
                    fileName = fileName.substring(fileName.lastIndexOf("/") + 1);
                }
                
                String localFilePath = downloadDir + File.separator + fileName;
                
                // 从HTTP URL下载文件
                log.info("从HTTP URL下载文件: {}", filePath);
                downloadedFilePath = minioUtil.downloadFromUrl(filePath, localFilePath);
                isOriginalFileUrl = true;
            } else {
                // 获取MinIO中的对象名称
                String objectName = filePath;
                
                // 从对象名称中提取文件名
                String fileName = objectName;
                if (objectName.contains("/")) {
                    fileName = objectName.substring(objectName.lastIndexOf("/") + 1);
                }
                
                String localFilePath = downloadDir + File.separator + fileName;
                
                // 从MinIO下载文件
                log.info("从MinIO下载文件: {}", objectName);
                downloadedFilePath = minioUtil.downloadFileToLocal(
                        resolvedProperties.getBucketName(), 
                        objectName, 
                        localFilePath
                );
            }
            
            if (downloadedFilePath == null) {
                throw new IllegalArgumentException("下载文件失败: " + filePath);
            }
            
            downloadedFile = new File(downloadedFilePath);
            
            // 检查文件类型
            String fileType = getFileTypeFromPath(downloadedFile.getName());
            if (!SUPPORTED_DOC_TYPES.contains(fileType)) {
                throw new IllegalArgumentException("不支持的文件类型: " + fileType + "，仅支持DOC、DOCX格式");
            }
            
            // 读取文档内容
            String docContent = FileContentReader.readContent(downloadedFile, fileType, "UTF-8");
            
            // 转换为Markdown格式
            String markdownContent = convertToMarkdown(docContent);
            
            // 创建Markdown文件
            String mdFileName = downloadedFile.getName().substring(0, downloadedFile.getName().lastIndexOf('.')) + ".md";
            String mdFilePath = downloadDir + File.separator + mdFileName;
            mdFile = new File(mdFilePath);
            FileUtil.writeString(markdownContent, mdFile, "UTF-8");
            
            // 上传Markdown文件到MinIO
            String objectName = generateObjectName(mdFileName, executeId);
            
            // 上传文件
            String uploadedPath = minioUtil.uploadFileWithOptions(
                    convertFileToMultipartFile(mdFile),
                    resolvedProperties.getBucketName(),
                    objectName,
                    null,  // 不设置过期时间
                    true,  // 公开访问
                    null   // 不设置文件限制
            );
            
            if (uploadedPath == null) {
                throw new IllegalArgumentException("上传Markdown文件失败");
            }
            
            // 删除原始文档文件
            boolean deleteSuccess = false;
            if (isOriginalFileUrl) {
                log.info("删除原始文档文件(URL): {}", originalFilePath);
                deleteSuccess = minioUtil.deleteFileFromUrl(originalFilePath);
            } else if (originalFilePath != null && originalBucketName != null) {
                log.info("删除原始文档文件: 存储桶={}, 对象={}", originalBucketName, originalFilePath);
                deleteSuccess = minioUtil.deleteFile(originalBucketName, originalFilePath);
            }
            
            if (!deleteSuccess) {
                log.warn("删除原始文档文件失败: {}", originalFilePath);
            } else {
                log.info("原始文档文件删除成功");
            }
            
            // 设置结果
            result = uploadedPath;
            
            // 设置成功状态和输出
            nodeContext.setStatus("completed");
            
            // 处理输出映射
            processOutputs(context);
            
        } catch (Exception e) {
            log.error("文档转Markdown失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            // 清理临时文件
            if (downloadedFile != null) {
                downloadedFile.delete();
            }
            if (mdFile != null) {
                mdFile.delete();
            }
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }
        
        log.info("文档转Markdown节点执行结束，节点ID：{}", getId());
        return context;
    }
    
    /**
     * 将文档内容转换为Markdown格式
     * 
     * @param docContent 文档内容
     * @return Markdown格式的内容
     */
    private String convertToMarkdown(String docContent) {
        if (StringUtils.isBlank(docContent)) {
            return "";
        }
        
        try {
            // 步骤1: 转换文档内容为更丰富的HTML格式
            String htmlContent = convertDocToHtml(docContent);
            
            // 步骤2: 转换HTML为更准确的Markdown
            String markdownContent = convertHtmlToMarkdown(htmlContent);
            
            // 步骤3: 后处理优化Markdown内容
            markdownContent = postProcessMarkdown(markdownContent);
            
            return markdownContent;
        } catch (Exception e) {
            log.error("转换文档到Markdown失败", e);
            throw new RuntimeException("转换文档到Markdown失败: " + e.getMessage());
        }
    }
    
    /**
     * Markdown后处理，优化输出结果
     * 
     * @param markdown 原始Markdown内容
     * @return 优化后的Markdown内容
     */
    private String postProcessMarkdown(String markdown) {
        if (markdown == null) {
            return "";
        }
        
        // 修复连续的空行问题(超过2个空行的情况)
        markdown = markdown.replaceAll("\n{3,}", "\n\n");
        
        // 修复列表项缩进问题
        StringBuilder result = new StringBuilder();
        String[] lines = markdown.split("\n");
        boolean inList = false;
        
        for (int i = 0; i < lines.length; i++) {
            String line = lines[i];
            
            // 检测是否为列表项
            if (line.trim().matches("^[*\\-+]\\s.*") || line.trim().matches("^\\d+\\.\\s.*")) {
                inList = true;
                
                // 确保列表项前有空行(除非前一行也是列表项)
                if (i > 0 && !lines[i-1].trim().matches("^[*\\-+]\\s.*") && 
                    !lines[i-1].trim().matches("^\\d+\\.\\s.*") && 
                    !lines[i-1].trim().isEmpty()) {
                    result.append("\n");
                }
            } else if (inList && !line.trim().isEmpty() && 
                      !line.trim().matches("^\\s+.*") && 
                      i > 0 && (lines[i-1].trim().matches("^[*\\-+]\\s.*") || 
                               lines[i-1].trim().matches("^\\d+\\.\\s.*"))) {
                // 列表结束，确保之后有空行
                inList = false;
                result.append("\n");
            }
            
            result.append(line).append("\n");
        }
        
        return result.toString().trim();
    }
    
    /**
     * 将文档内容转换为HTML格式
     * 使用更结构化的方法处理文档元素
     * 
     * @param docContent 文档内容
     * @return HTML格式的内容
     */
    private String convertDocToHtml(String docContent) throws Exception {
        StringBuilder html = new StringBuilder();
        html.append("<!DOCTYPE html><html><head><meta charset='UTF-8'></head><body>");
        
        // 按段落分割文档内容，保留空行信息
        String[] paragraphs = docContent.split("(\\r?\\n){2,}");
        
        for (String paragraph : paragraphs) {
            paragraph = paragraph.trim();
            
            if (paragraph.isEmpty()) {
                continue;
            }
            
            // 检测文档结构并创建相应的HTML元素
            
            // 处理标题 (基于特征进行检测，如短行、全大写等)
            if (isHeading(paragraph)) {
                int level = determineHeadingLevel(paragraph);
                html.append("<h").append(level).append(">")
                    .append(escapeHtml(paragraph))
                    .append("</h").append(level).append(">");
            }
            // 处理列表 (检测行首的数字、符号等)
            else if (isList(paragraph)) {
                html.append(generateListHtml(paragraph));
            }
            // 处理表格 (检测制表符或固定宽度的文本列)
            else if (isTable(paragraph)) {
                html.append(generateTableHtml(paragraph));
            }
            // 处理引用块
            else if (isQuote(paragraph)) {
                html.append("<blockquote>")
                    .append(escapeHtml(paragraph.replaceAll("^\\s*>\\s*", "")))
                    .append("</blockquote>");
            }
            // 普通段落
            else {
                // 处理内部格式，如粗体、斜体等
                String processedParagraph = processInlineFormats(paragraph);
                html.append("<p>").append(processedParagraph).append("</p>");
            }
        }
        
        html.append("</body></html>");
        return html.toString();
    }
    
    /**
     * 将HTML内容转换为Markdown格式
     * 使用更准确的HTML解析和转换
     * 
     * @param htmlContent HTML内容
     * @return Markdown格式的内容
     */
    private String convertHtmlToMarkdown(String htmlContent) {
        // 使用更健壮的方式处理HTML转Markdown
        
        // 如果在环境中可以使用外部库，可以考虑使用下面的代码替换
        // 使用flexmark-java或其他库(需要添加相应的依赖)
        /*
        try {
            org.jsoup.nodes.Document document = org.jsoup.Jsoup.parse(htmlContent);
            com.vladsch.flexmark.html2md.converter.FlexmarkHtmlConverter converter = 
                com.vladsch.flexmark.html2md.converter.FlexmarkHtmlConverter.builder().build();
            return converter.convert(document);
        } catch (Exception e) {
            log.error("HTML转Markdown失败", e);
            // 如果转换失败，使用备用方法
            return convertHtmlToMarkdownFallback(htmlContent);
        }
        */
        
        // 以下为不依赖外部库的改进版本
        return convertHtmlToMarkdownImproved(htmlContent);
    }
    
    /**
     * 改进的HTML转Markdown方法
     * 更细致地处理HTML元素
     */
    private String convertHtmlToMarkdownImproved(String htmlContent) {
        StringBuilder markdown = new StringBuilder();
        
        // 预处理HTML，移除DOCTYPE和html/head/body标签
        String content = htmlContent.replaceAll("<!DOCTYPE[^>]*>|<html>|</html>|<head>.*?</head>|<body>|</body>", "");
        
        // 处理标题
        content = content.replaceAll("<h1[^>]*>(.*?)</h1>", "# $1\n\n");
        content = content.replaceAll("<h2[^>]*>(.*?)</h2>", "## $1\n\n");
        content = content.replaceAll("<h3[^>]*>(.*?)</h3>", "### $1\n\n");
        content = content.replaceAll("<h4[^>]*>(.*?)</h4>", "#### $1\n\n");
        content = content.replaceAll("<h5[^>]*>(.*?)</h5>", "##### $1\n\n");
        content = content.replaceAll("<h6[^>]*>(.*?)</h6>", "###### $1\n\n");
        
        // 处理段落，保留空行
        content = content.replaceAll("<p[^>]*>(.*?)</p>", "$1\n\n");
        
        // 处理强调格式
        content = content.replaceAll("<strong[^>]*>(.*?)</strong>", "**$1**");
        content = content.replaceAll("<b[^>]*>(.*?)</b>", "**$1**");
        content = content.replaceAll("<em[^>]*>(.*?)</em>", "*$1*");
        content = content.replaceAll("<i[^>]*>(.*?)</i>", "*$1*");
        
        // 处理链接
        content = content.replaceAll("<a\\s+href=[\"'](.*?)[\"'][^>]*>(.*?)</a>", "[$2]($1)");
        
        // 处理列表，支持嵌套
        content = processLists(content);
        
        // 处理引用块
        content = content.replaceAll("<blockquote[^>]*>(.*?)</blockquote>", "> $1\n\n");
        
        // 处理代码块
        content = content.replaceAll("<pre[^>]*><code[^>]*>(.*?)</code></pre>", "```\n$1\n```\n\n");
        
        // 处理内联代码
        content = content.replaceAll("<code[^>]*>(.*?)</code>", "`$1`");
        
        // An IndexOutOfBoundsException occurred in the table processing or somewhere in this method
        // Process tables safely with additional bounds checking
        try {
            content = processTables(content);
        } catch (Exception e) {
            log.warn("处理表格时出错，使用简单表格处理: {}", e.getMessage());
            // Use a simpler method to process tables that avoids the exception
            content = processTablesSimplified(content);
        }
        
        // 处理水平线
        content = content.replaceAll("<hr[^>]*>", "---\n\n");
        
        // 移除剩余的HTML标签
        content = content.replaceAll("<[^>]*>", "");
        
        // 解码HTML实体
        content = decodeHtmlEntities(content);
        
        // 修复空行
        content = content.replaceAll("\n{3,}", "\n\n");
        
        return content.trim();
    }
    
    /**
     * 简化的表格处理方法，避免复杂的数组操作
     */
    private String processTablesSimplified(String content) {
        StringBuilder result = new StringBuilder();
        
        // Find table tags
        int tableStart = content.indexOf("<table");
        
        if (tableStart == -1) {
            return content; // No tables found
        }
        
        int currentPos = 0;
        
        while (tableStart != -1) {
            // Add content before the table
            result.append(content.substring(currentPos, tableStart));
            
            int tableEnd = content.indexOf("</table>", tableStart);
            if (tableEnd == -1) {
                // No closing tag found, add the rest and break
                result.append(content.substring(tableStart));
                break;
            }
            
            // Extract table content
            String tableHtml = content.substring(tableStart, tableEnd + 8);
            
            // Convert to simple markdown table
            String markdownTable = convertTableToMarkdownSimple(tableHtml);
            result.append(markdownTable);
            
            // Move position
            currentPos = tableEnd + 8;
            tableStart = content.indexOf("<table", currentPos);
        }
        
        // Add any remaining content
        if (currentPos < content.length()) {
            result.append(content.substring(currentPos));
        }
        
        return result.toString();
    }
    
    /**
     * 简化版本的表格转换方法，更加健壮地处理边界情况
     */
    private String convertTableToMarkdownSimple(String tableHtml) {
        StringBuilder markdown = new StringBuilder("\n");
        boolean headerProcessed = false;
        
        // Extract rows
        String[] rows = tableHtml.split("<tr[^>]*>");
        
        for (int i = 0; i < rows.length; i++) {
            if (i == 0) continue; // Skip first split result which is empty or contains the table tag
            
            String row = rows[i];
            boolean isHeader = row.contains("<th");
            
            StringBuilder markdownRow = new StringBuilder("| ");
            
            if (isHeader) {
                // Process header cells
                String[] cells = row.split("<th[^>]*>");
                int cellCount = 0;
                
                for (int j = 1; j < cells.length; j++) { // Start from 1 to skip the first empty split
                    String cellContent = "";
                    
                    try {
                        cellContent = cells[j].replaceAll("</th>.*", "").trim();
                    } catch (Exception e) {
                        log.warn("处理表格头单元格时出错: {}", e.getMessage());
                    }
                    
                    markdown.append(cellContent).append(" | ");
                    cellCount++;
                }
                
                // Add separator row after header
                markdown.append("\n| ");
                for (int j = 0; j < cellCount; j++) {
                    markdown.append("--- | ");
                }
                
                headerProcessed = true;
            } else {
                // Process data cells
                String[] cells = row.split("<td[^>]*>");
                
                for (int j = 1; j < cells.length; j++) { // Start from 1 to skip the first empty split
                    String cellContent = "";
                    
                    try {
                        cellContent = cells[j].replaceAll("</td>.*", "").trim();
                    } catch (Exception e) {
                        log.warn("处理表格数据单元格时出错: {}", e.getMessage());
                    }
                    
                    markdown.append(cellContent).append(" | ");
                }
                
                // If there are data rows but no header was found, add a simple header and separator
                if (!headerProcessed && i == 1) { // First data row and no header processed
                    int cellCount = row.split("<td[^>]*>").length - 1;
                    StringBuilder headerRow = new StringBuilder("| ");
                    StringBuilder separatorRow = new StringBuilder("| ");
                    
                    for (int j = 0; j < cellCount; j++) {
                        headerRow.append("列").append(j + 1).append(" | ");
                        separatorRow.append("--- | ");
                    }
                    
                    // Insert header row and separator before the first data row
                    markdown.insert(0, headerRow.toString() + "\n" + separatorRow.toString() + "\n");
                    headerProcessed = true;
                }
            }
            
            markdown.append("\n");
        }
        
        return markdown.toString();
    }
    
    /**
     * 优化的HTML到Markdown表格处理
     * 增加了错误处理和防御性编程
     */
    private String convertTableToMarkdown(String tableHtml) {
        StringBuilder markdown = new StringBuilder();
        
        try {
            // 提取表头
            int theadStart = tableHtml.indexOf("<thead>");
            int theadEnd = tableHtml.indexOf("</thead>");
            
            if (theadStart != -1 && theadEnd != -1) {
                String theadHtml = tableHtml.substring(theadStart, theadEnd + 8);
                String[] headers = theadHtml.split("<th[^>]*>");
                
                if (headers.length > 1) {
                    markdown.append("|");
                    StringBuilder separator = new StringBuilder("|");
                    
                    // 第一个元素通常是空的或只包含标签，跳过它
                    for (int i = 1; i < headers.length; i++) {
                        String headerText = headers[i].replaceAll("</th>.*", "").trim();
                        markdown.append(" ").append(headerText).append(" |");
                        separator.append(" --- |");
                    }
                    
                    markdown.append("\n").append(separator).append("\n");
                }
            }
            
            // 提取表格主体
            int tbodyStart = tableHtml.indexOf("<tbody>");
            int tbodyEnd = tableHtml.indexOf("</tbody>");
            
            if (tbodyStart != -1 && tbodyEnd != -1) {
                String tbodyHtml = tableHtml.substring(tbodyStart, tbodyEnd + 8);
                String[] rows = tbodyHtml.split("<tr[^>]*>");
                
                // 跳过第一个元素，通常是空的或只包含标签
                for (int i = 1; i < rows.length; i++) {
                    String[] cells = rows[i].split("<td[^>]*>");
                    
                    if (cells.length > 1) {
                        markdown.append("|");
                        
                        // 第一个元素通常是空的或只包含标签，跳过它
                        for (int j = 1; j < cells.length; j++) {
                            String cellText = cells[j].replaceAll("</td>.*", "").trim();
                            markdown.append(" ").append(cellText).append(" |");
                        }
                        
                        markdown.append("\n");
                    }
                }
            }
        } catch (Exception e) {
            log.warn("表格转换过程中出现异常: {}", e.getMessage());
            // 出现异常时返回简化的表格
            return "| 内容解析失败，请查看原始文档 |\n| --- |\n";
        }
        
        return markdown.toString();
    }
    
    /**
     * 处理HTML中的列表元素
     */
    private String processLists(String content) {
        // 处理无序列表
        StringBuilder result = new StringBuilder(content);
        
        // 首先替换无序列表开始/结束标签
        String processed = result.toString()
            .replaceAll("<ul[^>]*>", "\n")
            .replaceAll("</ul>", "\n");
        
        // 然后处理列表项
        processed = processed.replaceAll("<li[^>]*>(.*?)</li>", "* $1\n");
        
        // 处理有序列表
        processed = processed.replaceAll("<ol[^>]*>", "\n");
        processed = processed.replaceAll("</ol>", "\n");
        
        // 需要一个计数器来为有序列表项添加数字
        // 简化起见，这里使用正则表达式的替换组
        // 在实际实现中可能需要更复杂的逻辑
        
        return processed;
    }
    
    /**
     * 检测文本是否为标题
     */
    private boolean isHeading(String text) {
        // 短行且可能是全大写或以大写字母开头
        return text.length() < 100 && (text.equals(text.toUpperCase()) || 
                Character.isUpperCase(text.charAt(0)));
    }
    
    /**
     * 确定标题级别
     */
    private int determineHeadingLevel(String text) {
        // 基于文本长度或其他特征确定标题级别
        if (text.length() > 50) {
            return 3;
        } else if (text.length() > 20) {
            return 2;
        }
        return 1;
    }
    
    /**
     * 检测文本是否为列表
     */
    private boolean isList(String text) {
        // 检查是否包含列表项特征
        String[] lines = text.split("\n");
        int listItemCount = 0;
        
        for (String line : lines) {
            if (line.trim().matches("^[0-9]+\\..*") || line.trim().matches("^[•\\-\\*].*")) {
                listItemCount++;
            }
        }
        
        // 如果超过一定比例的行是列表项，则认为是列表
        return listItemCount > 0 && (double)listItemCount / lines.length > 0.5;
    }
    
    /**
     * 生成列表的HTML表示
     */
    private String generateListHtml(String text) {
        String[] lines = text.split("\n");
        StringBuilder html = new StringBuilder();
        boolean inOrderedList = false;
        boolean inUnorderedList = false;
        
        for (String line : lines) {
            String trimmedLine = line.trim();
            
            // 有序列表项
            if (trimmedLine.matches("^[0-9]+\\..*")) {
                if (!inOrderedList) {
                    if (inUnorderedList) {
                        html.append("</ul>");
                        inUnorderedList = false;
                    }
                    html.append("<ol>");
                    inOrderedList = true;
                }
                
                String content = trimmedLine.replaceFirst("^[0-9]+\\.\\s*", "");
                html.append("<li>").append(escapeHtml(content)).append("</li>");
            }
            // 无序列表项
            else if (trimmedLine.matches("^[•\\-\\*].*")) {
                if (!inUnorderedList) {
                    if (inOrderedList) {
                        html.append("</ol>");
                        inOrderedList = false;
                    }
                    html.append("<ul>");
                    inUnorderedList = true;
                }
                
                String content = trimmedLine.replaceFirst("^[•\\-\\*]\\s*", "");
                html.append("<li>").append(escapeHtml(content)).append("</li>");
            }
            // 非列表项
            else if (!trimmedLine.isEmpty()) {
                if (inOrderedList) {
                    html.append("</ol>");
                    inOrderedList = false;
                }
                if (inUnorderedList) {
                    html.append("</ul>");
                    inUnorderedList = false;
                }
                
                html.append("<p>").append(escapeHtml(trimmedLine)).append("</p>");
            }
        }
        
        // 关闭未关闭的列表
        if (inOrderedList) {
            html.append("</ol>");
        }
        if (inUnorderedList) {
            html.append("</ul>");
        }
        
        return html.toString();
    }
    
    /**
     * 检测文本是否为表格
     */
    private boolean isTable(String text) {
        // 简单的表格检测逻辑
        // 检查是否有制表符或固定宽度的列
        String[] lines = text.split("\n");
        
        // 至少需要2行才可能是表格
        if (lines.length < 2) {
            return false;
        }
        
        // 检查是否包含制表符
        boolean hasTabs = false;
        for (String line : lines) {
            if (line.contains("\t")) {
                hasTabs = true;
                break;
            }
        }
        
        // 如果有制表符，很可能是表格
        if (hasTabs) {
            return true;
        }
        
        // 检查是否有对齐的空格模式，这可能表示是表格
        // 简化处理，这里只检查每行是否有类似数量的空格分隔符
        try {
            // 找出最短行的长度，避免越界
            int minLength = Integer.MAX_VALUE;
            for (String line : lines) {
                if (line.length() < minLength) {
                    minLength = line.length();
                }
            }
            
            // 如果最短行长度为0，不可能是表格
            if (minLength == 0) {
                return false;
            }
            
            // 创建安全的数组大小
            int[] spacePositions = new int[minLength];
            
            // 统计每个位置的空格出现次数
            for (String line : lines) {
                for (int i = 0; i < minLength && i < line.length(); i++) {
                    if (line.charAt(i) == ' ') {
                        spacePositions[i]++;
                    }
                }
            }
            
            // 查找高频空格位置
            int columnCount = 0;
            for (int count : spacePositions) {
                if (count >= lines.length * 0.7) { // 如果70%的行在该位置有空格
                    columnCount++;
                }
            }
            
            // 如果至少有2列，可能是表格
            return columnCount >= 2;
        } catch (Exception e) {
            log.warn("检测表格结构时出错: {}", e.getMessage());
            return false; // 出错时保守地返回false
        }
    }
    
    /**
     * 生成表格的HTML表示
     */
    private String generateTableHtml(String text) {
        String[] lines = text.split("\n");
        StringBuilder html = new StringBuilder("<table>");
        
        // 添加表头（第一行）
        html.append("<thead><tr>");
        String[] headers = lines[0].split("\t");
        if (headers.length <= 1) {
            // 如果没有制表符，尝试按空格拆分
            headers = splitBySpacePattern(lines[0], lines);
        }
        
        for (String header : headers) {
            html.append("<th>").append(escapeHtml(header.trim())).append("</th>");
        }
        html.append("</tr></thead>");
        
        // 添加表格内容
        html.append("<tbody>");
        for (int i = 1; i < lines.length; i++) {
            html.append("<tr>");
            String[] cells = lines[i].split("\t");
            if (cells.length <= 1) {
                // 如果没有制表符，尝试按空格拆分
                cells = splitBySpacePattern(lines[i], lines);
            }
            
            for (String cell : cells) {
                html.append("<td>").append(escapeHtml(cell.trim())).append("</td>");
            }
            html.append("</tr>");
        }
        html.append("</tbody></table>");
        
        return html.toString();
    }
    
    /**
     * 根据空格模式分割文本行
     */
    private String[] splitBySpacePattern(String line, String[] allLines) {
        // 查找所有行中空格的共同位置
        List<Integer> splitPositions = findCommonSpacePositions(allLines);
        
        if (splitPositions.isEmpty()) {
            return new String[]{line.trim()};
        }
        
        // 根据分割位置拆分行
        List<String> parts = new ArrayList<>();
        int start = 0;
        
        for (int pos : splitPositions) {
            if (pos > start && pos < line.length()) {
                parts.add(line.substring(start, pos).trim());
                start = pos;
            }
        }
        
        // 添加最后一部分
        if (start < line.length()) {
            parts.add(line.substring(start).trim());
        }
        
        return parts.toArray(new String[0]);
    }
    
    /**
     * 查找所有行中空格的共同位置
     */
    private List<Integer> findCommonSpacePositions(String[] lines) {
        List<Integer> positions = new ArrayList<>();
        
        // 找出最短行的长度
        int minLength = Integer.MAX_VALUE;
        for (String line : lines) {
            minLength = Math.min(minLength, line.length());
        }
        
        // 查找所有行在同一位置都是空格的位置
        for (int i = 0; i < minLength; i++) {
            boolean isCommonSpace = true;
            for (String line : lines) {
                if (i >= line.length() || line.charAt(i) != ' ') {
                    isCommonSpace = false;
                    break;
                }
            }
            
            if (isCommonSpace) {
                positions.add(i);
            }
        }
        
        return positions;
    }
    
    /**
     * 检测文本是否为引用块
     */
    private boolean isQuote(String text) {
        // 检查是否以引用符号开头
        String[] lines = text.split("\n");
        int quoteLineCount = 0;
        
        for (String line : lines) {
            if (line.trim().startsWith(">")) {
                quoteLineCount++;
            }
        }
        
        // 如果超过一定比例的行是引用，则认为是引用块
        return quoteLineCount > 0 && (double)quoteLineCount / lines.length > 0.5;
    }
    
    /**
     * 处理内联格式，如粗体、斜体等
     */
    private String processInlineFormats(String text) {
        // 这里可以增加对内联格式的检测和转换
        // 例如：检测特殊符号包围的文本并转换为HTML标签
        
        // 简单示例：将星号包围的文本视为斜体
        text = text.replaceAll("\\*(.*?)\\*", "<em>$1</em>");
        
        // 将双星号包围的文本视为粗体
        text = text.replaceAll("\\*\\*(.*?)\\*\\*", "<strong>$1</strong>");
        
        // 更多格式处理...
        
        return escapeHtml(text);
    }
    
    /**
     * 从文件路径获取文件类型
     * 
     * @param fileName 文件名
     * @return 文件类型（大写）
     */
    private String getFileTypeFromPath(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return fileName.substring(lastDotIndex + 1).toUpperCase();
        }
        return "";
    }
    
    /**
     * 生成对象名称
     * 
     * @param originalFilename 原始文件名
     * @param executeId 执行ID
     * @return 对象名称
     */
    private String generateObjectName(String originalFilename, String executeId) {
        return String.format("files/%s/%s/%s",
                LocalDate.now(),
                executeId,
                originalFilename);
    }
    
    /**
     * 将File转换为MultipartFile
     * 
     * @param file 文件
     * @return MultipartFile对象
     */
    private MultipartFile convertFileToMultipartFile(File file) {
        return new MultipartFile() {
            @Override
            public String getName() {
                return file.getName();
            }

            @Override
            public String getOriginalFilename() {
                return file.getName();
            }

            @Override
            public String getContentType() {
                return MediaType.TEXT_MARKDOWN_VALUE;
            }

            @Override
            public boolean isEmpty() {
                return file.length() == 0;
            }

            @Override
            public long getSize() {
                return file.length();
            }

            @Override
            public byte[] getBytes() throws IOException {
                return FileUtil.readBytes(file);
            }

            @Override
            public InputStream getInputStream() throws IOException {
                return new FileInputStream(file);
            }

            @Override
            public void transferTo(File dest) throws IOException, IllegalStateException {
                FileUtil.copy(file, dest, true);
            }
        };
    }
    
    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }
    
    @Data
    @NoArgsConstructor
    public static class Properties {
        /**
         * 文件路径（MinIO对象路径或URL）
         */
        private String filePath;
        
        /**
         * 存储桶名称
         */
        private String bucketName;
    }

    /**
     * 转义HTML特殊字符
     * 
     * @param text 原始文本
     * @return 转义后的文本
     */
    private String escapeHtml(String text) {
        return text.replace("&", "&amp;")
                   .replace("<", "&lt;")
                   .replace(">", "&gt;")
                   .replace("\"", "&quot;")
                   .replace("'", "&#39;");
    }

    /**
     * 处理HTML中的表格元素
     */
    private String processTables(String content) {
        // 这是一个简化的表格处理方法
        // 实际实现可能需要更复杂的解析逻辑
        
        // 匹配表格结构
        int tableStart = content.indexOf("<table");
        while (tableStart != -1) {
            int tableEnd = content.indexOf("</table>", tableStart);
            if (tableEnd == -1) break;
            
            // 提取表格HTML
            String tableHtml = content.substring(tableStart, tableEnd + 8);
            
            // 转换为Markdown表格
            String markdownTable = convertTableToMarkdown(tableHtml);
            
            // 替换原始表格
            content = content.substring(0, tableStart) + markdownTable + content.substring(tableEnd + 8);
            
            // 查找下一个表格
            tableStart = content.indexOf("<table", tableStart + markdownTable.length());
        }
        
        return content;
    }
    
    /**
     * 解码HTML实体
     */
    private String decodeHtmlEntities(String content) {
        return content.replace("&amp;", "&")
                     .replace("&lt;", "<")
                     .replace("&gt;", ">")
                     .replace("&quot;", "\"")
                     .replace("&#39;", "'")
                     .replace("&nbsp;", " ");
    }

    @Override
    public String desc() {
        return "文档转MD";
    }
} 