package com.icarus.common.cotnode;

import cn.hutool.json.JSONObject;
import com.icarus.common.runtime.NodeContext;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class ExtractTextNode extends BaseNode {

    @Getter
    @Setter
    private String result;

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【提取图片文字】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);

        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            if (resolvedInputs == null) {
                throw new IllegalArgumentException("输入参数不能为空");
            }

            // 解析属性配置
            ExtractTextNode.Properties resolvedProperties = resolveProperties(context, ExtractTextNode.Properties.class);
            JSONObject propertiesJson = new JSONObject();
            propertiesJson.set("gitUrl", resolvedProperties.getGitUrl());
            propertiesJson.set("localPath", resolvedProperties.getLocalPath());
            propertiesJson.set("branchName", resolvedProperties.getBranchName());
            propertiesJson.set("operation", resolvedProperties.getOperation());
            propertiesJson.set("commitMessage", resolvedProperties.getCommitMessage());
            propertiesJson.set("username", resolvedProperties.getUsername());
            propertiesJson.set("password", resolvedProperties.getPassword());
            nodeContext.setProperties(propertiesJson);

            // 验证必要的输入参数
            String operation = resolvedProperties.getOperation();
            String localPath = resolvedProperties.getLocalPath();

            if (operation == null || operation.trim().isEmpty()) {
                throw new IllegalArgumentException("operation 是必需的输入参数");
            }
            if (localPath == null || localPath.trim().isEmpty()) {
                throw new IllegalArgumentException("localPath 是必需的输入参数");
            }

            // 记录输入参数到上下文
            nodeContext.setInputs(new JSONObject(resolvedInputs));

            // 根据操作类型执行相应的git命令
//            result = executeGitOperation(resolvedProperties);

            // 设置成功状态和输出
            nodeContext.setStatus("completed");

            // 处理输出映射
            processOutputs(context);

        } catch (Exception e) {
            log.error("Git操作失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        log.info("Git操作节点执行结束，节点ID：{}", getId());
        return context;
    }
    @Data
    @NoArgsConstructor
    public static class Properties {
        private String gitUrl;          // git项目地址
        private String branchName;      // 分支名称
        private String localPath;       // git本地路径
        private String operation;       // 操作方式：pull、clone、commit、push、status、add、checkout、branch、fetch
        private String commitMessage;   // 提交信息
        private String username;        // 用户名（用于HTTPS认证）
        private String password;        // 密码（用于HTTPS认证）
        private String remoteHost;      //远程host
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    @Override
    public String desc() {
        return "Git操作节点";
    }
}