package com.icarus.common.cotnode.ab.agent;

import cn.hutool.json.JSONObject;
import com.icarus.common.cotnode.BaseNode;
import com.icarus.common.runtime.NodeContext;
import com.icarus.sdk.client.utils.LLMClientUtil;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class LLMNode extends BaseNode {

    @Getter
    @Setter
    private String result;

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【大语言模型】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
        
        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            nodeContext.setInputs(new JSONObject(resolvedInputs));
            
            // 解析属性配置
            Properties resolvedProperties = resolveProperties(context, Properties.class);
            JSONObject propertiesJson = new JSONObject();
            propertiesJson.set("model", resolvedProperties.getModel());
            propertiesJson.set("systemPrompt", resolvedProperties.getSystemPrompt());
            propertiesJson.set("question", resolvedProperties.getQuestion());
            propertiesJson.set("outputFormat", resolvedProperties.getOutputFormat());
            nodeContext.setProperties(propertiesJson);
            if (resolvedProperties != null) {
                // 根据模型名称路由到对应的处理器
                String modelName = resolvedProperties.getModel();
                // 确认输出格式
                boolean simple = "default".equalsIgnoreCase(resolvedProperties.getOutputFormat()) ? false : true;
                result = LLMClientUtil.builder()
                        .modelName(modelName)
                        .systemPrompt(resolvedProperties.getSystemPrompt())
                        .chat(resolvedProperties.getQuestion())
                        .removeThinkTAG()
                        .runChatCompletion();

                // 设置结果并更新状态
                nodeContext.setStatus("completed");
                // 处理输出参数
                processOutputs(context);
            }

        } catch (Exception e) {
            log.error("LLM节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage("调用大模型报错,请排查日志");
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        log.info("LLM节点执行结束，节点ID：{}", getId());
        return context;
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    @Data
    @NoArgsConstructor
    public static class Properties {
        private String model;
        private String systemPrompt;
        private String question;
        private String outputFormat; // 输出格式
    }

    @Override
    public String desc() {
        return "大语言模型节点";
    }
}