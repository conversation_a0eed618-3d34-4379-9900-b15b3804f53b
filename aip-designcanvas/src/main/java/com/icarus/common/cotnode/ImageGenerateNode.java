package com.icarus.common.cotnode;

import cn.hutool.json.JSONObject;
import com.icarus.common.runtime.NodeContext;
import com.icarus.dto.ModelRequest;
import com.icarus.dto.ModelResponse;
import com.icarus.service.ModelService;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.Map;
import java.util.UUID;
import java.util.HashMap;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 图片生成节点
 * 使用 deepseek 模型生成图片
 */
@EqualsAndHashCode(callSuper = true)
@Slf4j
public class ImageGenerateNode extends BaseNode {

    @Autowired
    private ModelService modelService;
    
    /** 节点执行结果 */
    @Getter
    @Setter
    private String result;

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【ImageGenerate】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
        
        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            nodeContext.setInputs(new JSONObject(resolvedInputs));
            
            // 解析属性配置
            Properties resolvedProperties = resolveProperties(context, Properties.class);
            JSONObject propertiesJson = new JSONObject();
            propertiesJson.set("prompt", resolvedProperties.getPrompt());
            nodeContext.setProperties(propertiesJson);

            // 获取提示词，优先使用输入参数，其次使用属性配置
            String prompt = (String) resolvedInputs.get("prompt");
            if (prompt == null || prompt.trim().isEmpty()) {
                prompt = resolvedProperties.getPrompt();
            }
            
            if (prompt == null || prompt.trim().isEmpty()) {
                throw new IllegalArgumentException("Prompt cannot be null or empty");
            }

            // 调用模型生成图片
            String imageBase64 = generateImage(prompt);
            result = imageBase64;
            
            // 构建输出结果
            JSONObject outputJson = new JSONObject();
            outputJson.set("imageBase64", imageBase64);
            nodeContext.setOutputs(outputJson);
            
            // 设置执行状态为完成
            nodeContext.setStatus("completed");
            
            // 处理输出参数
            processOutputs(context);

        } catch (Exception e) {
            log.error("ImageGenerate 节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        return context;
    }
    
    /**
     * 调用模型生成图片
     */
    private String generateImage(String prompt) {
        try {
            // 构建模型请求
            ModelRequest modelRequest = new ModelRequest();
            modelRequest.setRequestId(UUID.randomUUID().toString());
            // 使用图片生成模型
            // TODO:需要修改为图片模型，暂无
            modelRequest.setModelName("");  // 或其他支持图片生成的模型
            modelRequest.setPrompt(prompt);
            
            // 设置图片生成相关参数
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("width", 512);  // 图片宽度
            parameters.put("height", 512); // 图片高度
            parameters.put("num_inference_steps", 50); // 推理步数
            parameters.put("guidance_scale", 7.5);     // 引导比例
            parameters.put("output_format", "base64"); // 指定输出格式为base64
            modelRequest.setParameters(parameters);
            
            // 调用模型
            // TODO,获取模型响应
            ModelResponse modelResponse = null;
            
            if (modelResponse.getError() != null) {
                throw new RuntimeException("Model error: " + modelResponse.getError());
            }
            
            // 确保返回的是base64格式的图片数据
            String base64Image = modelResponse.getResult();
            if (!base64Image.startsWith("data:image")) {
                base64Image = "data:image/png;base64," + base64Image;
            }
            
            return base64Image;
            
        } catch (Exception e) {
            log.error("Failed to generate image", e);
            throw new RuntimeException("Failed to generate image: " + e.getMessage());
        }
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    @Override
    public String desc() {
        return "图片模型生成节点";
    }

    /**
     * 节点配置属性类
     */
    @Data
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Properties {
        /** 图片生成提示词 */
        private String prompt;
    }
} 