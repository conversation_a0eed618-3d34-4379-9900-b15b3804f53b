package com.icarus.common.cotnode.ab.agent;

import cn.hutool.json.JSONObject;
import com.icarus.common.cotnode.BaseNode;
import com.icarus.common.cotnode.DatabaseNode;
import com.icarus.common.runtime.NodeContext;
import com.icarus.entity.DesignEmbeddingDetail;
import com.icarus.entity.DesignEmbeddingFile;
import com.icarus.sdk.springboot.base.utils.SpringContextUtils;
import com.icarus.service.EmbeddingDatabaseService;
import com.icarus.utils.JsonUtils;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import java.math.BigDecimal;
import java.sql.*;
import java.util.*;

import com.icarus.enums.DatabaseType;

@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class EmbeddingDatabaseNode extends BaseNode {

    private EmbeddingDatabaseService embeddingDatabaseService;

    private String nodeName = "向量存储";

    @Getter
    @Setter
    private String result;

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        String nodeId = getId();
        log.info("开始执行【{}】节点, 当前节点id:{}", nodeName, nodeId);

        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);

        try {
            embeddingDatabaseService = SpringContextUtils.getBean(EmbeddingDatabaseService.class);
            
            // 解析属性配置
            Properties properties = resolveProperties(context, Properties.class);
            nodeContext.setProperties(new JSONObject(properties));

            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            // 如果resolvedInputs为空，使用context中的flowData.data
            if (resolvedInputs == null || resolvedInputs.isEmpty()) {
                JSONObject flowData = (JSONObject) context.get("flowData");
                if (flowData != null && flowData.containsKey("data")) {
                    resolvedInputs = new HashMap<>();
                    Map<String, Object> dataMap = (Map<String, Object>) flowData.get("data");
                    resolvedInputs.putAll(dataMap);
                    log.info("使用flowData.data作为输入数据");
                }
            }
            nodeContext.setInputs(new JSONObject(resolvedInputs));

            // 解析属性配置
            EmbeddingDatabaseNode.Properties resolvedProperties = resolveProperties(context, EmbeddingDatabaseNode.Properties.class);
            JSONObject propertiesJson = new JSONObject();
            propertiesJson.set("dbType", resolvedProperties.getDbType());
            propertiesJson.set("jdbcUrl", resolvedProperties.getJdbcUrl());
            propertiesJson.set("username", resolvedProperties.getUsername());
            propertiesJson.set("password", resolvedProperties.getPassword());
            propertiesJson.set("operation", resolvedProperties.getOperation());
            propertiesJson.set("fileName", resolvedProperties.getFileName());
            propertiesJson.set("fileUrl", resolvedProperties.getFileUrl());
            propertiesJson.set("model", resolvedProperties.getModel());
            propertiesJson.set("content", resolvedProperties.getContent());
            propertiesJson.set("embeddings", resolvedProperties.getEmbeddings());

            nodeContext.setProperties(propertiesJson);
            
            // 构建数据库连接属性
            DatabaseNode.Properties dbProperties = new DatabaseNode.Properties();
            dbProperties.setDbType(resolvedProperties.getDbType());
            dbProperties.setJdbcUrl(resolvedProperties.getJdbcUrl());
            dbProperties.setUsername(resolvedProperties.getUsername());
            dbProperties.setPassword(resolvedProperties.getPassword());

            if (!dbProperties.isValid()) {
                throw new RuntimeException("数据库配置信息无效");
            }

            try (Connection connection = getConnection(dbProperties)) {
                log.info("数据库连接成功: {}", getDatabaseInfo(connection));
                connection.setAutoCommit(false);
                try {
                    // 根据操作类型执行不同的操作
                    String operation = properties.getOperation();
                    if ("insert".equalsIgnoreCase(operation)) {
                        // 插入操作
                        DesignEmbeddingFile file = new DesignEmbeddingFile();
                        file.setFileUrl(properties.getFileUrl());
                        file.setFileName(properties.getFileName());
                       
                        // 使用获取到的 service
                        String fileId = embeddingDatabaseService.insertFileInfo(file);
                        
                        // 使用传入的数据库连接处理 detail 数据
                        List<DesignEmbeddingDetail> details = new ArrayList<>();
                        DesignEmbeddingDetail detail = new DesignEmbeddingDetail();
                        detail.setFileId(fileId);
                        detail.setSplitTitle("splitTitle");
                        detail.setContext(properties.getContent());
                        detail.setEmbedding(properties.getEmbeddings());
                        detail.setModel(properties.getModel());
                        details.add(detail);
                        embeddingDatabaseService.insertEmbeddingDetails(connection, details);
                        result = "插入成功";
                    } else if ("delete".equalsIgnoreCase(operation)) {
                        // 删除操作
                        embeddingDatabaseService.deleteByFileId(connection, properties.getFileId());
                        result = "删除成功";
                    } else if ("query".equalsIgnoreCase(operation)) {
                        // 查询操作
                        DesignEmbeddingFile  data = embeddingDatabaseService.queryEmbeddingData(
                            connection, properties.getFileId()
                        );
//                        List<DesignEmbeddingFile>  results = embeddingDatabaseService.embeddingData(
//                            connection, "[0.32702282,-0.09291819,-0.17593707,-0.23012148,-0.18094933,-0.08688869,-0.20900176,-0.50322306,-0.0028327436,-0.04382044,0.34599286,-0.051783703,0.4307058,-0.3369474,0.11477966,0.3948665,0.0057297186,0.16326228,-0.30014595,-0.42594773,0.085997134,0.35630763,0.012462746,-0.022331906,-0.4193407,0.18218926,0.18120751,0.2902892,0.21342728,-0.27098763,-0.35977092,0.030544357,0.014208955,-0.30986878,-0.46138546,-0.027225865,-0.44515365,0.30633911,0.34591797,-0.45048252,-0.055644702,0.12624015,0.19785772,-0.39967325,-0.07912832,0.16123815,-0.5222738,-0.31523734,0.22455646,0.045448124,0.09420594,-0.3489184,-0.20156993,-0.047579862,-0.586626,0.21806315,0.23454191,0.1571549,-0.017464858,-0.34320146,-0.13072556,1.9064759,-0.033976242,-0.11395275,-0.41774103,-0.26454324,-0.051011283,-0.07631271,-0.3203197,-0.14421861,0.019416898,0.27082,-0.065279566,-0.120219834,-0.12359307,-0.23369783,-0.30855298,0.29643387,0.35390943,0.08076117,0.085342534,0.16526127,0.23930922,-0.14406212,0.07444397,-0.58902276,0.36895102,9.700126,-0.3785555,-0.030795125,-0.27401882,-0.22172245,-0.101139426,0.29859307,-0.41433105,0.05489512,-0.07924803,-0.09642556,-0.024217816,0.24240223,0.41886905,-0.17118038,-0.44962946,0.17070003,-0.6419282,0.14393823,-0.26730007,-0.6319442,-0.14782366,-0.22259413,-0.041916996,-0.21843186,0.10300925,0.16816916,-0.21054713,-0.18549424,-0.20473996,0.2799398,0.06840645,0.19705309,0.12617058,0.057904426,0.13092576,-0.21749902,-0.18541221,0.1364276,0.2795652,-0.039577957,0.27861008,0.16078816,-0.23679657,-0.10525228,-0.026627274,0.21626289,-0.14453611,0.23029837,0.12694484,-0.2533135,-0.2505394,-0.06637453,0.47500008,-0.17038493,0.31514427,-0.14984182,0.35506317,-0.3141906,-0.1251233,0.08088345,-0.19134669,-0.114848286,0.3667744,0.03701585,-0.12393927,-0.39014414,-0.13161932,-0.04553888,0.37397197,-0.12523785,0.2998101,-0.30631694,-0.14930145,-0.27819985,-0.30067933,-0.14865603,-0.22100168,-0.27993128,0.2818985,0.15151009,-0.079084925,0.43264744,-0.23293775,-0.122078754,0.09042083,0.56266975,0.30675483,-0.19954462,0.11316147,0.25021884,-0.16533832,0.45358384,0.31014186,-0.028605815,-0.025724942,-0.20571724,0.21693829,0.18340984,0.08586143,0.16935821,0.07566102,-0.23607448,-0.012400339,0.4395455,-0.3461165,-0.05048808,-0.45421636,-0.40998292,-0.20943679,0.09568601,0.14565636,-0.03005313,-0.47294122,0.15336356,-0.04513302,-0.041026533,0.19945164,0.24555948,0.16724104,0.459768,0.0011102463,0.23465995,-0.4338948,0.45947245,0.07686161,0.3920092,-0.041422337,-0.1113844,-0.30296463,0.29241717,0.24829508,0.26923695,0.5446917,0.23149912,-0.05476406,-0.14346308,0.040374514,-0.07635721,0.3644359,-0.08198369,-0.06264022,0.13838857,-0.8193986,0.07433833,-0.1318118,-0.25763455,-0.16127086,-0.08676001,-0.23628616,-0.10794564,-0.43630454,-0.057436932,-0.000590043,0.07815874,-0.1519485,-0.14994629,-0.10303786,0.09147748,0.014618052,-0.20390137,0.2457909,-0.09331978,-0.10656365,-0.22777157,-0.017354367,-0.5298629,0.15085025,-0.30406186,0.16463302,-0.09346414,0.12405689,0.061359446,0.15889816,-0.013292551,0.2723625,-0.6052914,-0.13319427,0.34623674,0.2543823,-0.27831873,-0.06672592,-0.12642638,-0.06265744,0.2523534,-0.433956,-0.0707057,0.016240431,0.31763926,0.021152169,0.17869605,0.2930975,-0.1008963,0.14638044,0.0064445017,-0.7577811,0.31251925,0.48928183,0.22576475,-0.27678522,-0.07821782,-0.020520937,-0.23275565,-0.33560458,0.08513891,-0.19674526,0.12255141,-0.14241561,0.4813908,0.18111105,-0.25701702,-0.26962188,0.04585463,-0.15229823,0.105527475,-0.10379846,0.16250876,0.34749693,-0.04273891,-0.20633523,0.03442067,-0.22111718,0.29099414,-0.08423224,-0.33817017,0.080266185,-0.35216135,0.5223806,-0.41355172,0.13494067,-0.16315395,-0.056539062,0.13749804,0.3288479,-0.32811365,0.00458253,-0.1970115,0.29943392,0.06715598,0.402288,0.06883446,-0.5043907,0.0010665732,0.25801837,0.29438823,-0.3068147,-0.10902677,-0.46463862,0.06495463,0.5163806,0.28691366,-0.292227,0.19366227,0.15942787,0.119004466,-0.4535464,-0.48878536,0.15506841,-0.07383451,0.10876051,0.20669183,-0.1341842,-0.7074352,0.21054658,0.45669475,0.14786464,-0.5432498,0.21215713,-0.12740725,-0.018255765,-0.07409685,0.09114507,0.37348318,-0.017540894,0.114626735,-0.38843295,0.18639155,-0.015674027,-0.15196647,0.015753638,0.27442992,-0.62206435,-0.10456128,0.19584396,0.65692526,0.37692833,-0.28544718,0.27752241,0.12305555,0.22900757,0.02194863,-0.12515596,-0.16291063,0.09776135,0.24822499,0.2653782,0.07550528,0.10648693,-0.12534003,0.1531607,-0.05000206,0.40784794,0.06628251,0.06410697,-0.25835901,0.35113928,-0.2227404,-0.1640296,-0.109545246,0.37841052,0.025836056,-0.09672778,0.053659428,0.045754198,0.25037113,0.106156036,-3.0699046,0.15550354,-0.1775798,0.21006937,-0.14139551,0.11306573,0.35945338,-0.7406417,-0.25860575,0.0074043097,-0.40218255,0.11385413,-0.081947565,-0.31600228,0.027331475,0.017128117,0.28937688,0.30662563,-0.02165612,0.35756615,0.053890824,0.22427857,0.49394336,-0.26350832,-0.24436264,-0.15452209,0.4107982,-0.15413442,-0.45393595,-0.13252603,0.28118184,0.2963652,0.011367513,-0.38796306,-0.17808196,-0.12613407,-0.2518701,0.05136614,-0.100000724,0.24197191,0.07116092,0.010758771,-0.007412668,-0.076082915,-0.5758488,-0.44598925,-0.48223278,-0.0719586,-0.37231636,0.06975035,0.041919585,0.012739256,-0.5150361,-0.12056093,-0.005149979,-0.12337359,-0.24435277,-0.04522021,0.004877851,0.13882738,-0.29880318,-0.5398156,0.22492176,0.1871221,0.17940201,0.3543049,-0.06807818,-0.1557427,-0.25889313,-0.20601161,0.09612787,0.08230925,0.44294757,0.31853497,0.44661897,-0.32254466,0.381504,-0.14035338,-0.17715596,-0.9225853,0.11671647,0.27130592,0.22714357,0.06403612,-0.058074046,-0.29125848,-0.069337204,-0.2762376,0.14947903,-0.03175186,-0.12779953,0.02405593,-0.12158774,0.13321564,-0.13089438,0.2283405,0.0037013267,-0.536575,-0.37661934,0.106862344,0.47580332,0.23029032,0.029756138,0.020673895,-0.2654332,-0.037768908,0.19401243,0.15141585,-0.3605817,0.29859126,0.22138298,-0.053316552,-0.33333063,0.19343923,0.15840153,-0.05512869,-0.18602905,0.27021626,0.1244675,0.072551526,-0.20821448,0.105394095,-0.29222825,-0.300493,0.12224194,-0.0031692102,-0.01772085,-0.19053495,-0.2601339,-0.45036265,0.022905098,-0.35771877,-0.077266544,0.026714785,0.3786922,0.18861124,0.29857695,-0.025299935,0.15306808,0.1277095,-0.010200821,0.059410654,0.20478071,0.36352554,0.12250519,0.31212488,-0.012835224,-0.51942956,-0.28600484,0.12473529,-0.65366703,-0.20141211,0.019903505,-0.19661413,-0.3446746,-0.10712648,0.030209593,-0.34519017,0.29417363,0.19891366,0.06710657,-0.051331438,-0.17425624,0.059522383,-0.16796063,-0.21381845,-0.30581552,-0.13879031,-0.42088488,-0.084740706,0.25509152,-0.25716883,-0.6127707,-0.32569957,0.34686396,-0.46695897,0.2354335,0.12970681,-0.22668828,-0.25232702,-0.0037287015,0.017805874,-0.0589823,0.12185094,0.08880202,-0.4503554,0.31293035,0.0866098,0.18641976,0.120724596,-0.017010942,0.079610035,-0.043837164,-0.24389029,-0.3115353,0.27838045,-0.08641423,-0.45172286,0.04059088,0.20699507,-0.3510452,0.07727282,-0.3227334,0.100375734,0.048501093,-0.6239607,-0.14158128,0.23019204,-0.3469524,0.028373642,0.087595955,-0.13944045,-0.052625626,0.31367603,0.14650944,-0.29595074,-0.12398106,-0.013180106,0.13998169,0.07857685,-0.51282626,-0.34008473,0.19708881,-0.50120836,-0.07062209,0.01734418,-0.10531833,-0.07136136,0.066757664,-0.11582109,-0.19627842,0.18866268,-0.011838154,-0.31201422,0.29068765,-0.27838555,-0.3137444,-0.16892694,-0.38372964,-0.13637999,-0.13017713,-0.56472385,-0.5091175,-0.12185359,-0.18766104,-0.06496574,-0.074292876,-0.66788465,-0.087785915,0.30119556,-0.19155395,0.09553256,0.24901716,-0.4989664,-0.22748218,-0.12959048,0.00016578092,-0.3547397,0.020772614,0.034905307,0.33500597,-0.39213708,-0.04745104,0.21676888,0.33704895,0.12070339,0.26488793,0.074174926,-0.084736146,0.56541604,0.22343858,0.38917315,0.40910172,0.13879777,0.053964958,-0.0077265548,-0.23823966,0.17402841,-0.42628804,0.36987966,0.0023761292,-0.012986622,0.05745293,-0.19445199,-0.10761247,-0.07230764,-0.17014559,-0.5288467,-0.35630092,0.04031262,-0.3569976,-0.08281669,-0.13509816,0.07290183,-0.25746816,0.3709457,0.3305575,-0.56236494,-0.029887289,-0.1247107,0.07606899,0.4146455,-0.33931267,-0.027071897,0.14000262,-0.50115407,-0.16986163,-0.53526384,-0.60960174,0.5209437,-0.029750576,0.19240776,0.15050764,-0.4572817,-0.120703526,-0.43915746,0.25845084,-0.03366015,0.2704875,-0.39391226,0.30276898,0.09318737,-0.547949,0.13455302,-0.1640001,0.21919754,-0.07301049,-0.2698792,-0.3160823,0.6807377,-0.47147486,0.03895535,0.107072845,-0.03241845,-0.487734,-0.4162395,0.028920153,-0.018955486,0.28157446,0.07660768,0.26993725,-0.344249,-0.016091054,0.4742658,0.46377814,0.20798421,0.07604971,-0.11219782,-0.017101616,-0.04900954,-0.80889183,0.17011133,-0.15462846,-0.08506279,0.46628332,-0.49509698,0.07616233,0.12850784,-0.17083639,0.35759458,-0.41451094,-0.081673935,0.021696595,0.0035329822,0.1461107,-0.049061142,0.34261873,-0.25965795,0.4143527,-0.28820226,0.11858615,-0.5772511,0.19450717,-0.064790584,0.31375974,0.13534035,-0.20135677,-0.47099724,0.2953123,-0.18083096,-0.03407657,-0.30735844,0.27824613,0.23263547,0.38854545,0.09555461,0.10572002,-0.17863694,0.18052843,0.019895831,-0.19277477,0.04740014,-0.11142827,-0.2459982,0.32398456,-0.035323538,-0.10807063,-0.096111536,0.00014304736,0.09155499,-0.34181392,-0.3292024,0.16447814,-0.18645608,-0.040536746,-0.48778456,0.024740228,-0.2037241,-0.16245402,0.26789373,0.09538195,0.22779489,0.5173563,0.2434085,-0.035020262,0.4096449,-0.043607507,0.06399914,-0.11991063,-0.22346316,0.25628582,0.28814617,0.53872466,-0.105221614,0.10604309,0.084749624,0.07992738,-0.4092666,-0.24215624,-0.1711139,-0.08828098,0.03783313,-0.2899429,0.22128914,-0.18101907,0.16857772,0.15492456,-0.22101025,0.042708684,0.17619137,0.34564644,-0.24941908,-0.13474052,-0.073532216,0.23693515,-0.16671672,-0.0031410013,-0.041314557,-0.19842297,0.072541915,0.19495511,-0.23347785,-0.6275362,0.1621947,-0.23060599,0.2964092,-0.23989446,-0.06003143,-0.072306775,-0.25781012,0.09714805,-0.22418335,0.342186,0.09549918,-0.3597548,-0.016701648,0.2455675,0.008731908,0.028012643,0.24488762,0.40244323,0.3906162,-0.02657676,-0.47937092,-0.3957802,-0.2461211,-0.23644623,-0.10171658,0.29695496,0.15263715,0.023223063,-0.0123764435,-0.27690494,-0.28665328,-0.35560215,0.19981375,-0.07073452,-0.16658513,-0.4722944,0.22089614,0.22696717,-0.3153974,0.30487835,0.27377903,-0.5418056,-0.13632493,0.34409901,0.11442503,0.08880308,0.00021535633,-0.046278257,0.18675233,0.17386167,0.12396855,-0.035575766,0.5570707,-0.035976626,-0.07970604,-0.34042698,0.18939675,-0.15168585,-0.1187026,0.15491594,-0.08638326,0.21534719,-0.3062625,0.07151943,0.09974127,0.07904114,-0.09994833,-0.09557189,-0.05841426,0.19337499,0.12378034,-0.34155008,-0.58592886,-0.5428449,-0.059897423,-0.25642574,0.020373467,0.02645498,0.0806549,0.030711677,0.48272124,-0.22275749,0.030444136,0.16261815,-0.3732439,0.2457299,-0.44682276,-0.040257405,-0.6387556,0.10653368,-0.04949314,0.25196958,-0.18648687,-0.20896707,-0.33860824,-0.43401936,-0.043518864,-0.22562096,-0.09301452,-0.77498376,-0.032147884,-0.42837024,0.0019275056,-0.11590536,-0.31422296,-0.050066117,0.20801137,0.22581092,0.024503754,-0.4576961,-0.09796239,-0.11548714,0.10246396,-0.1421237,-0.25728527,-0.0437758,0.029126754,0.005045714,-0.22966252,0.13107306,0.08520882,-0.23104826,0.40270805,0.28053293,-0.1939313,-0.07182836,0.36748955,0.32620564,-0.12868606,0.05025834,-0.09380641,0.004639008,-0.33295763,0.0928801,0.04718858,-0.28185618,0.06499024,0.24986392,0.24343209,-0.09379206,-0.09119811,0.26064584,-0.10233303,0.14273101,0.09052454,-0.07412716,-0.30033764,-0.11433455,-0.03565462,-0.091267616,-0.527915,-0.3060312,-0.028404566,0.50296086,-0.25998366,0.043824837,0.34636196,0.59383655,0.07237865,-0.09183758,0.020439276,-0.033348672,0.02569972,0.000396544,-0.58058375,-0.00032423978,0.091367446,-0.39866507]"
//                        );
                        result = JsonUtils.objectToJson(data);
                    }
                    connection.commit();
                    nodeContext.setStatus("completed");
                    processOutputs(context);
                } catch (Exception e) {
                    connection.rollback();
                    throw e;
                } finally {
                    connection.setAutoCommit(true);
                }
            }

        } catch (Exception e) {
            log.error(nodeName + "节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        log.info("{}节点执行结束，节点ID：{}", nodeName, nodeId);
        return context;
    }

    /**
     * 获取数据库连接
     */
    private Connection getConnection(DatabaseNode.Properties properties) throws SQLException {
        DatabaseType dbType = DatabaseType.fromString(properties.getDbType());
        if (dbType == DatabaseType.unknown) {
            dbType = detectDatabaseTypeFromUrl(properties.getJdbcUrl());
        }
        log.info("数据库类型: {}, 驱动类: {}", dbType.getType(), dbType.getDriverClass());

        try {
            Class.forName(dbType.getDriverClass());
        } catch (ClassNotFoundException e) {
            log.error("数据库驱动加载失败: {}", dbType.getDriverClass(), e);
            throw new SQLException("数据库驱动未找到: " + dbType.getDriverClass());
        }

        return DriverManager.getConnection(
            properties.getJdbcUrl(),
            properties.getUsername(),
            properties.getPassword()
        );
    }

    /**
     * 从JDBC URL中检测数据库类型
     */
    private DatabaseType detectDatabaseTypeFromUrl(String jdbcUrl) {
        if (StringUtils.isBlank(jdbcUrl)) {
            return DatabaseType.unknown;
        }

        String lowerUrl = jdbcUrl.toLowerCase();
        if (lowerUrl.contains(":postgresql:")) {
            return DatabaseType.postgresql;
        }

        log.error("无法从JDBC URL识别数据库类型: {}", jdbcUrl);
        return DatabaseType.unknown;
    }

    /**
     * 获取数据库信息
     */
    private String getDatabaseInfo(Connection connection) {
        try {
            DatabaseMetaData metaData = connection.getMetaData();
            return String.format("Database: %s, Version: %s, Driver: %s %s",
                    metaData.getDatabaseProductName(),
                    metaData.getDatabaseProductVersion(),
                    metaData.getDriverName(),
                    metaData.getDriverVersion());
        } catch (SQLException e) {
            log.error("获取数据库信息失败", e);
            return "Unknown database";
        }
    }

    // 格式化为pgvector可接受的格式
    public String formatForPgVector(List<Double> vector) {
        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < vector.size(); i++) {
            if (i > 0) {
                sb.append(",");
            }
            // 使用BigDecimal转换，避免科学记数法
            BigDecimal bd = new BigDecimal(vector.get(i).toString());
            sb.append(bd.toPlainString());
        }
        sb.append("]");
        return sb.toString();
    }

    @Override
    public String desc() {
        return "向量库节点";
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    @Data
    @NoArgsConstructor
    public static class Properties {
        /**
         * 操作类型：insert/delete/query
         */
        private String operation = "insert";

        private String dbType;
        
        private String jdbcUrl;
        
        private String username;
        
        private String password;
        
        /**
         * 文件ID（用于删除和查询操作）
         */
        private String fileId;

        /**
         * 文件名（用于插入操作）
         */
        private String fileName;

        /**
         * 文件路径（用于插入操作）
         */
        private String fileUrl;

        /**
         * 向量模型（用于插入操作）
         */
        private String model;

        /**
         * 文本
         */
        private String content;


        /**
         * 向量
         */
        private String embeddings;
    }
} 