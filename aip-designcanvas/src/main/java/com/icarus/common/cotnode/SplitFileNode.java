package com.icarus.common.cotnode;

import cn.hutool.json.JSONObject;
import com.icarus.common.runtime.NodeContext;
import com.icarus.sdk.springboot.base.utils.SpringContextUtils;
import com.icarus.service.FileStorageService;
import com.icarus.utils.FileUtil;
import com.icarus.utils.PdfHandleUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.Loader;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.xwpf.usermodel.*;
import org.apache.pdfbox.pdmodel.PDDocument;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.*;


@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class SplitFileNode extends BaseNode {

    private List<Map<String, String>> result;

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【文件拆分】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
        File file = null;

        try {
            // 解析属性配置
            Properties resolvedProperties = resolveProperties(context, Properties.class);
            nodeContext.setProperties(new JSONObject(resolvedProperties));

            // 验证必要的属性参数
            String filePath = resolvedProperties.getFilePath();
            if (StringUtils.isBlank(filePath)) {
                throw new IllegalArgumentException("filePath 是必需的属性参数");
            }

            // 记录输入参数到上下文
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            if (resolvedInputs != null) {
                nodeContext.setInputs(new JSONObject(resolvedInputs));
            }

            // 获取文件
            file = getFileFromPath(filePath);
            if (file == null) {
                throw new IllegalArgumentException("无法获取文件: " + filePath);
            }

            // 根据文件类型进行拆分
            String fileName = file.getName().toLowerCase();
            result = splitFile(file, fileName, resolvedProperties);

            // 设置成功状态和输出
            nodeContext.setStatus("completed");
            // 处理输出映射
            processOutputs(context);

        } catch (Exception e) {
            log.error("文件拆分失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            if (file != null) {
                file.delete();
            }
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        log.info("文件拆分节点执行结束，节点ID：{}", getId());
        return context;
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    /**
     * 从文件路径获取文件对象
     */
    private File getFileFromPath(String filePath) {
        try {
            // 首先尝试通过 FileStorageService 获取
            FileStorageService service = SpringContextUtils.getBean(FileStorageService.class);
            InputStream inputStream = service.getFile(filePath);
            if (inputStream != null) {
                String fileName = "temp_" + filePath.substring(filePath.lastIndexOf("/") + 1);
                return FileUtil.inputStreamToFile(inputStream, fileName);
            }
        } catch (Exception e) {
            log.warn("通过 FileStorageService 获取文件失败: {}", e.getMessage());
        }

        // FileStorageService 获取失败，尝试直接文件路径
        try {
            File directFile = new File(filePath);
            if (directFile.exists() && directFile.isFile()) {
                return directFile;
            }
        } catch (Exception e) {
            log.warn("通过直接文件路径获取文件失败: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 根据文件类型选择拆分方法
     */
    private List<Map<String, String>> splitFile(File file, String fileName, Properties properties) throws Exception {
        if (fileName.endsWith(".doc")) {
            return splitDoc(file, properties);
        } else if (fileName.endsWith(".docx")) {
            return splitDocx(file, properties);
        } else if (fileName.endsWith(".xlsx") || fileName.endsWith(".xls")) {
            return splitExcel(file, properties);
        } else if (fileName.endsWith(".pdf")) {
            return splitPdf(file, properties);
        } else {
            throw new IllegalArgumentException("不支持的文件类型: " + fileName);
        }
    }

    /**
     * 拆分DOC文件
     */
    private List<Map<String, String>> splitDoc(File file, Properties properties) throws Exception {
        List<Map<String, String>> chunks = new ArrayList<>();
        try (FileInputStream fis = new FileInputStream(file);
             HWPFDocument doc = new HWPFDocument(fis)) {

            String fullText = doc.getDocumentText();
            int charsPerChunk = properties.getCharsPerChunk();

            // 直接按字符数拆分
            for (int i = 0; i < fullText.length(); i += charsPerChunk) {
                Map<String, String> chunk = new HashMap<>();
                chunk.put("content", fullText.substring(i,
                        Math.min(i + charsPerChunk, fullText.length())).trim());
                chunk.put("index", String.valueOf((i / charsPerChunk) + 1));
                chunks.add(chunk);
            }
        }
        return chunks;
    }

    /**
     * 拆分DOCX文件
     */
    private List<Map<String, String>> splitDocx(File file, Properties properties) throws Exception {
        List<Map<String, String>> chunks = new ArrayList<>();
        try (FileInputStream fis = new FileInputStream(file);
             XWPFDocument document = new XWPFDocument(fis)) {

            // 收集所有段落文本
            List<String> paragraphs = new ArrayList<>();
            for (XWPFParagraph para : document.getParagraphs()) {
                String text = para.getText().trim();
                if (StringUtils.isNotBlank(text)) {
                    paragraphs.add(text);
                }
            }

            // 应用范围限制
            int startIndex = properties.getStartNum() - 1;
            int endIndex = Math.min(properties.getEndNum() - 1, paragraphs.size() - 1);

            // 按字符数拆分
            StringBuilder currentChunk = new StringBuilder();
            int charsPerChunk = properties.getCharsPerChunk();
            int chunkIndex = 1;

            for (int i = startIndex; i <= endIndex; i++) {
                String paragraph = paragraphs.get(i);
                if (currentChunk.length() + paragraph.length() > charsPerChunk) {
                    // 保存当前块
                    if (currentChunk.length() > 0) {
                        Map<String, String> chunk = new HashMap<>();
                        chunk.put("content", currentChunk.toString().trim());
                        chunk.put("index", String.valueOf(chunkIndex++));
                        chunks.add(chunk);
                        currentChunk = new StringBuilder();
                    }
                }
                currentChunk.append(paragraph).append("\n");
            }

            // 保存最后一块
            if (currentChunk.length() > 0) {
                Map<String, String> chunk = new HashMap<>();
                chunk.put("content", currentChunk.toString().trim());
                chunk.put("index", String.valueOf(chunkIndex));
                chunks.add(chunk);
            }
        }
        return chunks;
    }

    /**
     * 拆分Excel文件
     */
    private List<Map<String, String>> splitExcel(File file, Properties properties) throws Exception {
        List<Map<String, String>> chunks = new ArrayList<>();
        try (FileInputStream fis = new FileInputStream(file);
             Workbook workbook = WorkbookFactory.create(fis)) {

            String splitLevel = properties.getSplitLevel();
            int startIndex = properties.getStartNum() - 1; // Excel索引从0开始
            int endIndex;

            switch (splitLevel) {
                case "sheet":
                    // 按工作表拆分
                    endIndex = Math.min(properties.getEndNum() - 1, workbook.getNumberOfSheets() - 1);
                    for (int i = startIndex; i <= endIndex; i++) {
                        Sheet sheet = workbook.getSheetAt(i);
                        StringBuilder sheetContent = new StringBuilder();
                        for (Row row : sheet) {
                            for (Cell cell : row) {
                                sheetContent.append(getCellValue(cell)).append("\t");
                            }
                            sheetContent.append("\n");
                        }
                        Map<String, String> chunk = new HashMap<>();
                        chunk.put("content", sheetContent.toString().trim());
                        chunk.put("index", sheet.getSheetName());
                        chunks.add(chunk);
                    }
                    break;

                case "row":
                    // 按行数拆分
                    int rowsPerChunk = properties.getRowsPerChunk();
                    for (Sheet sheet : workbook) {
                        int totalRows = sheet.getPhysicalNumberOfRows();
                        endIndex = Math.min(properties.getEndNum(), totalRows);

                        for (int i = startIndex; i < endIndex; i += rowsPerChunk) {
                            StringBuilder chunkContent = new StringBuilder();
                            int endRow = Math.min(i + rowsPerChunk, endIndex);

                            for (int rowNum = i; rowNum < endRow; rowNum++) {
                                Row row = sheet.getRow(rowNum);
                                if (row != null) {
                                    for (Cell cell : row) {
                                        chunkContent.append(getCellValue(cell)).append("\t");
                                    }
                                    chunkContent.append("\n");
                                }
                            }

                            Map<String, String> chunk = new HashMap<>();
                            chunk.put("content", chunkContent.toString().trim());
                            chunk.put("index", sheet.getSheetName() + "_" + ((i / rowsPerChunk) + 1));
                            chunks.add(chunk);
                        }
                    }
                    break;

                default:
                    throw new IllegalArgumentException("不支持的拆分级别: " + splitLevel);
            }
        }
        return chunks;
    }

    /**
     * 拆分PDF文件
     */
    private List<Map<String, String>> splitPdf(File file, Properties properties) throws Exception {
        try (PDDocument document = Loader.loadPDF(file)) {
            switch (PdfHandleUtil.PDFSplitMode.valueOf(properties.getSplitMode())) {
                case BY_FONTSIZE:
                    return PdfHandleUtil.splitByFontSize(document, properties.getFontSizeThreshold());
                case BY_PAGE:
                    return PdfHandleUtil.splitByPage(document, properties.getStartNum(), properties.getEndNum());
                case BY_CHAPTER:
                    return PdfHandleUtil.splitByChapter(document, properties.getChapterPattern(),
                        properties.getStartNum(), properties.getEndNum());
                default:
                    throw new IllegalArgumentException("不支持的PDF拆分模式");
            }
        }
    }

    /**
     * 获取Excel单元格的值
     */
    private String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                }
                return String.valueOf(cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    return String.valueOf(cell.getNumericCellValue());
                } catch (Exception e) {
                    return cell.getStringCellValue();
                }
            default:
                return "";
        }
    }

    @Override
    public String desc() {
        return "文本拆分";
    }

    @Data
    @NoArgsConstructor
    public static class Properties {
        /**
         * Excel拆分级别：sheet(工作表), row(行)
         */
        private String splitLevel = "sheet";

        private String filePath = "";

        /**
         * Excel按行拆分时每块的行数
         */
        private Integer rowsPerChunk = 2;

        /**
         * 按字符拆分时每块的大小
         */
        private Integer charsPerChunk = 500;

        /**
         * PDF拆分模式
         */
        private String splitMode = "BY_PAGE";

        /**
         * 字体大小阈值（用于BY_FONTSIZE模式）
         */
        private float fontSizeThreshold = 12.0f;

        /**
         * 开始数值
         */
        private int startNum = 1;

        /**
         * 结束数值
         */
         private int endNum = Integer.MAX_VALUE;

        /**
         * 章节匹配模式（用于BY_CHAPTER模式）
         */
        private String chapterPattern = "(?i)chapter\\s+\\d+|第[一二三四五六七八九十百千万]+章";

        /**
         * 获取范围值
         */
        public int[] getRange() {
            return new int[]{startNum, endNum};
        }
    }
}