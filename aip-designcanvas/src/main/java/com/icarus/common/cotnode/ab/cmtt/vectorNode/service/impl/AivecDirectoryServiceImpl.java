package com.icarus.common.cotnode.ab.cmtt.vectorNode.service.impl;

import com.icarus.common.cotnode.ab.cmtt.vectorNode.entity.AivecDirectory;
import com.icarus.common.cotnode.ab.cmtt.vectorNode.mapper.AivecDirectoryMapper;
import com.icarus.common.cotnode.ab.cmtt.vectorNode.service.IAivecDirectoryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 目录树表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Service
public class AivecDirectoryServiceImpl extends ServiceImpl<AivecDirectoryMapper, AivecDirectory> implements IAivecDirectoryService {

}
