package com.icarus.common.cotnode.ab.cmtt.vectorNode.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.icarus.common.cotnode.ab.cmtt.vectorNode.entity.AivecChunk;

import java.util.List;

/**
 * <p>
 * 支持QA格式的文件切片表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
public interface IAivecChunkService extends IService<AivecChunk> {

    /**
     * 基于向量相似度搜索切片
     *
     * @param queryVector 查询向量
     * @param limit 返回结果数量
     * @param threshold 相似度阈值（可选）
     * @return 相似度排序后的切片列表
     */
    List<AivecChunk> searchSimilarVectors(String queryVector, Integer limit, Float threshold,String baseId,String taskId);

    List<AivecChunk> getChunksBySystemId(String systemId);
}
