package com.icarus.common.cotnode.ab.agent;

import cn.hutool.json.JSONObject;
import com.icarus.common.cotnode.BaseNode;
import com.icarus.common.runtime.NodeContext;
import com.icarus.sdk.client.utils.LLMClientUtil;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * Embedding节点
 * <AUTHOR>
 * @date 2025-02-27
 */
@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class EmbeddingNode extends BaseNode {

    @Getter
    @Setter
    private String result;

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【Embedding】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);

        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            nodeContext.setInputs(new JSONObject(resolvedInputs));

            // 解析属性配置
            Properties resolvedProperties = resolveProperties(context, Properties.class);
            JSONObject propertiesJson = new JSONObject();
            propertiesJson.set("model", resolvedProperties.getModel());
            propertiesJson.set("content", resolvedProperties.getContent());
            nodeContext.setProperties(propertiesJson);

            // 使用Properties中的content执行embedding
            if (resolvedProperties != null) {
                result = LLMClientUtil.embedding(resolvedProperties.getModel(), resolvedProperties.getContent());
                // 设置结果并更新状态
                nodeContext.setStatus("completed");
                // 处理输出参数
                processOutputs(context);
            }

        } catch (Exception e) {
            log.error("Embedding节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        log.info("Embedding节点执行结束，节点ID：{}", getId());
        return context;
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    @Data
    @NoArgsConstructor
    public static class Properties {
        private String model;
        private String content;
    }

    @Override
    public String desc() {
        return "向量节点";
    }

} 