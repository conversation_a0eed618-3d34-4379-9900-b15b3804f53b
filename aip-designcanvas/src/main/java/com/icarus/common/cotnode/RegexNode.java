package com.icarus.common.cotnode;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.icarus.common.runtime.NodeContext;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 正则匹配节点
 */
@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class RegexNode extends BaseNode {

    // 添加结果字段
    private Map<String, Object> result = new HashMap<>();
    private boolean hasMatch = false;
    // 按名称索引的匹配结果
    private Map<String, Object> namedResults = new HashMap<>();

    public static final String code = "regexNode";

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【正则匹配】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);

        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            nodeContext.setInputs(new JSONObject(resolvedInputs));

            // 解析属性配置
            Properties resolvedProperties = resolveProperties(context, Properties.class);
            JSONObject propertiesJson = new JSONObject();

            // 处理前端表格格式的配置
            List<RegexConfig> regexConfigs = parseRegexConfigs(resolvedProperties.getRegexConfigs());
            propertiesJson.set("regexConfigs", regexConfigs);
            nodeContext.setProperties(propertiesJson);

            // 获取输入文本
            String inputText = resolvedProperties.getInputText();
            if (inputText == null || inputText.trim().isEmpty()) {
                throw new IllegalArgumentException("输入文本不能为空");
            }

            // 按优先级排序正则配置
            if (regexConfigs != null && !regexConfigs.isEmpty()) {
                regexConfigs.sort((c1, c2) -> {
                    Integer p1 = c1.getPriority() != null ? c1.getPriority() : Integer.MAX_VALUE;
                    Integer p2 = c2.getPriority() != null ? c2.getPriority() : Integer.MAX_VALUE;
                    return p1.compareTo(p2);
                });

                // 遍历所有正则配置
                hasMatch = false;
                result.clear();
                namedResults.clear();

                List<Map<String, Object>> allResults = new ArrayList<>();

                for (RegexConfig config : regexConfigs) {
                    try {
                        Pattern pattern = Pattern.compile(config.getRegexExpression());
                        Matcher matcher = pattern.matcher(inputText);

                        // 存储当前正则表达式的所有匹配结果
                        List<String> matchResults = new ArrayList<>();
                        boolean foundMatch = false;
                        int matchCount = 0;

                        // 查找所有匹配项
                        while (matcher.find()) {
                            matchCount++;
                            String matchedText = matcher.group();
                            matchResults.add(matchedText);
                            foundMatch = true;

                            // 创建单个匹配结果
                            Map<String, Object> matchResult = new HashMap<>();
                            matchResult.put("name", config.getName());
                            matchResult.put("result", matchedText);
                            matchResult.put("comment", config.getComment());
                            matchResult.put("priority",
                                    config.getPriority() != null ? config.getPriority() : Integer.MAX_VALUE);
                            matchResult.put("index", matchCount); // 添加匹配序号

                            allResults.add(matchResult);

                            log.info("正则匹配成功，名称：{}，优先级：{}，结果：{}，序号：{}",
                                    config.getName(), config.getPriority(), matchedText, matchCount);
                        }

                        if (foundMatch) {
                            hasMatch = true;

                            // 将所有匹配结果存储到namedResults中
                            if (matchResults.size() == 1) {
                                // 单个结果直接存储
                                namedResults.put(config.getName(), matchResults.get(0));
                            } else if (matchResults.size() > 1) {
                                // 多个结果存储为列表
                                namedResults.put(config.getName(), matchResults);
                                // 同时提供单独的访问方式，例如 name_1, name_2 等
                                for (int i = 0; i < matchResults.size(); i++) {
                                    namedResults.put(config.getName() + "_" + (i + 1), matchResults.get(i));
                                }
                            }

                            // 检查是否需要跳出
                            if ("结果非空".equals(config.getExitCondition()) && !matchResults.isEmpty()) {
                                log.info("匹配结果非空，满足跳出条件，停止后续匹配");
                                break;
                            }
                        }
                    } catch (Exception e) {
                        log.error("正则表达式 '{}' 执行失败: {}", config.getRegexExpression(), e.getMessage());
                    }
                }

                // 将所有结果封装到result中
                result.put("matches", allResults);
                result.put("hasMatch", hasMatch);
                result.put("byName", namedResults); // 添加按名称索引的结果

                // 添加匹配统计信息
                result.put("totalMatches", allResults.size());
                result.put("uniquePatterns", namedResults.size());

                if (!hasMatch) {
                    log.warn("所有正则表达式都没有匹配成功");
                }
            } else {
                log.warn("没有配置正则表达式");
                result.put("matches", new ArrayList<>());
                result.put("hasMatch", false);
                result.put("byName", new HashMap<>()); // 添加空的按名称索引结果
                result.put("totalMatches", 0);
                result.put("uniquePatterns", 0);
            }

            nodeContext.setStatus("completed");

            // 添加默认输出
            if (CollUtil.isEmpty(outputs)) {
                addOutput("result", result, "Object");
                addOutput("hasMatch", hasMatch, "Boolean");
                addOutput("totalMatches", result.get("totalMatches"), "Number");

                // 为每个匹配结果添加单独的输出，方便下游节点直接引用
                for (Map.Entry<String, Object> entry : namedResults.entrySet()) {
                    addOutput(entry.getKey(), entry.getValue(),
                            entry.getValue() instanceof List ? "Array" : "String");
                }
            }

            // 处理输出参数
            processOutputs(context);

        } catch (Exception e) {
            log.error("正则匹配节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        log.info("正则匹配节点执行结束，节点ID：{}", getId());
        return context;
    }

    /**
     * 解析前端表格格式的正则配置
     * 支持两种格式：
     * 1. 直接的RegexConfig列表
     * 2. 包含columns和tableData的表格格式
     */
    private List<RegexConfig> parseRegexConfigs(Object configObj) {
        List<RegexConfig> configs = new ArrayList<>();

        if (configObj == null) {
            return configs;
        }

        try {
            // 转换为JSONObject以便处理
            JSONObject jsonObj;
            if (configObj instanceof JSONObject) {
                jsonObj = (JSONObject) configObj;
            } else {
                jsonObj = JSONUtil.parseObj(configObj);
            }

            // 检查是否是表格格式（包含tableData）
            if (jsonObj.containsKey("tableData")) {
                JSONArray tableData = jsonObj.getJSONArray("tableData");
                for (int i = 0; i < tableData.size(); i++) {
                    JSONObject rowData = tableData.getJSONObject(i);
                    RegexConfig config = new RegexConfig();

                    // 设置配置属性
                    config.setName(rowData.getStr("name"));
                    config.setRegexExpression(rowData.getStr("regexExpression"));
                    config.setComment(rowData.getStr("comment", ""));
                    config.setExitCondition(rowData.getStr("exitCondition", ""));
                    config.setGlobalMatch(rowData.getBool("globalMatch", true)); // 默认启用全局匹配

                    // 处理优先级，可能是字符串或数字
                    Object priorityObj = rowData.get("priority");
                    if (priorityObj != null) {
                        if (priorityObj instanceof Number) {
                            config.setPriority(((Number) priorityObj).intValue());
                        } else {
                            try {
                                config.setPriority(Integer.parseInt(priorityObj.toString()));
                            } catch (NumberFormatException e) {
                                log.warn("无法解析优先级: {}", priorityObj);
                                config.setPriority(Integer.MAX_VALUE);
                            }
                        }
                    } else {
                        config.setPriority(Integer.MAX_VALUE);
                    }

                    configs.add(config);
                }
            } else if (configObj instanceof List) {
                // 直接是配置列表
                List<?> configList = (List<?>) configObj;
                for (Object item : configList) {
                    configs.add(JSONUtil.toBean(JSONUtil.parseObj(item), RegexConfig.class));
                }
            }

            log.info("解析到 {} 个正则配置", configs.size());
        } catch (Exception e) {
            log.error("解析正则配置失败", e);
        }

        return configs;
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        } else if ("hasMatch".equals(propertyName)) {
            return hasMatch;
        } else if ("totalMatches".equals(propertyName)) {
            return result.get("totalMatches");
        } else if (namedResults.containsKey(propertyName)) {
            // 支持直接通过名称获取匹配结果
            return namedResults.get(propertyName);
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    @Data
    @NoArgsConstructor
    public static class RegexConfig {
        private String name; // 正则名称
        private String comment; // 备注说明
        private String regexExpression; // 正则表达式
        private Integer priority; // 优先级
        private String exitCondition; // 跳出条件
        private Boolean globalMatch; // 是否全局匹配(查找所有匹配项)
    }

    @Data
    @NoArgsConstructor
    public static class Properties {
        private String inputText; // 输入文本
        private Object regexConfigs; // 正则配置列表，可能是表格格式
    }

    @Override
    public String desc() {
        return "正则匹配节点";
    }
}