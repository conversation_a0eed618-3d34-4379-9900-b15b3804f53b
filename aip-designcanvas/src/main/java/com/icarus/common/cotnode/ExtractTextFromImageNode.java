package com.icarus.common.cotnode;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.icarus.common.minio.MinioUtil;
import com.icarus.common.runtime.NodeContext;
import com.icarus.config.UrlConfigHolder;
import io.minio.messages.Item;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class ExtractTextFromImageNode extends BaseNode {

    @Getter
    @Setter
    private String result;

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【提取图片文字】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);

        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            if (resolvedInputs == null) {
                throw new IllegalArgumentException("输入参数不能为空");
            }

            // 解析属性配置
            ExtractTextFromImageNode.Properties resolvedProperties = resolveProperties(context, ExtractTextFromImageNode.Properties.class);
            JSONObject propertiesJson = new JSONObject();
            propertiesJson.set("model", resolvedProperties.getModel());
            propertiesJson.set("systemPrompt", resolvedProperties.getSystemPrompt());
            propertiesJson.set("question", resolvedProperties.getQuestion());
            propertiesJson.set("imagePath", resolvedProperties.getImagePath());
            nodeContext.setProperties(propertiesJson);

            // 记录输入参数到上下文
            nodeContext.setInputs(new JSONObject(resolvedInputs));

            // 验证必要的输入参数
            String modelName = resolvedProperties.getModel();
            String imagePath = resolvedProperties.getImagePath();
            String question = resolvedProperties.getQuestion();

            if (StrUtil.isEmpty(imagePath)) {
                throw new IllegalArgumentException("图片路径不能为空");
            }
            if (StrUtil.isEmpty(question)) {
                throw new IllegalArgumentException("提问内容不能为空");
            }

            // 获取MinioUtil实例
            //todo 后续应改为从上下文中获取imagePath,删除minioUtil，使用minio节点调用
            MinioUtil minioUtil = SpringUtil.getBean(MinioUtil.class);
            String bucketName = minioUtil.getMinioConfig().getBucketName();

            // 提取图片文件夹中的所有图片
            result = extractTextFromImages(minioUtil, bucketName, imagePath, modelName,
                    resolvedProperties.getSystemPrompt(), question);

            // 设置成功状态和输出
            nodeContext.setStatus("completed");

            // 处理输出映射
            processOutputs(context);

        } catch (Exception e) {
            log.error("提取图片文字节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        log.info("提取图片文字节点执行结束，节点ID：{}", getId());
        return context;
    }
    @Data
    @NoArgsConstructor
    public static class Properties {
        private String model = "qwen25_vl_7b";
        private String systemPrompt;
        private String question;
        private String imagePath;

    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    /**
     * 从MinIO文件夹中提取所有图片并调用VL模型进行文字提取
     *
     * @param minioUtil MinIO工具类
     * @param bucketName 存储桶名称
     * @param folderPath 文件夹路径
     * @param modelName 模型名称
     * @param systemPrompt 系统提示词
     * @param question 用户问题
     * @return 提取的文字结果
     */
    private String extractTextFromImages(MinioUtil minioUtil, String bucketName, String folderPath,
                                       String modelName, String systemPrompt, String question) {
        try {
            // 确保文件夹路径以/结尾
            if (!folderPath.endsWith("/")) {
                folderPath += "/";
            }

            // 列出文件夹中的所有对象
            List<Item> items = minioUtil.listObjects(bucketName, folderPath, false);

            if (items.isEmpty()) {
                log.warn("文件夹中没有找到任何文件: {}", folderPath);
                return "文件夹中没有找到任何图片文件";
            }

            // 过滤出图片文件
            List<String> imageUrls = new ArrayList<>();
            for (Item item : items) {
                String objectName = item.objectName();
                if (isImageFile(objectName)) {
                    // 生成图片的访问URL
                    String imageUrl = minioUtil.getFileUrl(bucketName, objectName);
//                    imageUrl =  StrUtil.subBefore(imageUrl, "?", false);
                    // 生成临时访问URL
                    imageUrls.add(objectName);
                    log.info("找到图片文件: {}", objectName);
                }
            }

            if (imageUrls.isEmpty()) {
                log.warn("文件夹中没有找到图片文件: {}", folderPath);
                return "文件夹中没有找到图片文件";
            }

            // 遍历所有图片，调用VL模型提取文字
            StringBuilder allResults = new StringBuilder();
            List<String> localFilePaths = new ArrayList<>();
            
            try {
                for (int i = 0; i < imageUrls.size(); i++) {
                    String imageUrl = imageUrls.get(i);
                    log.info("正在处理第{}张图片: {}", i + 1, imageUrl);

                    try {
                        // 下载图片到本地根目录
//                        String objectName = extractObjectNameFromUrl(imageUrl);
                        String localFilePath = downloadImageToRootFolder(minioUtil, bucketName, imageUrl,folderPath);
                        localFilePaths.add(localFilePath);
                        
                        // 使用本地文件路径调用VL模型
//                        String localFileUrl = "file://" + localFilePath;"aip-biggroup-starter", "src", "main", "resources", "static",folderPath
                        String localFileUrl = UrlConfigHolder.getMyServerUrl()+folderPath + localFilePath;
                        String extractedText = callVisionModel(modelName, systemPrompt, question, localFileUrl);
                        allResults.append("图片 ").append(i + 1).append(" 提取结果:\n");
                        allResults.append(extractedText).append("\n\n");
                    } catch (Exception e) {
                        log.error("处理图片失败: {}", imageUrl, e);
                        allResults.append("图片 ").append(i + 1).append(" 处理失败: ").append(e.getMessage()).append("\n\n");
                    }
                }
            } finally {
                // 清理所有本地文件
                cleanupLocalFiles(localFilePaths);
            }

            return allResults.toString().trim();

        } catch (Exception e) {
            log.error("提取图片文字失败", e);
            throw new RuntimeException("提取图片文字失败: " + e.getMessage(), e);
        }
    }

    /**
     * 判断是否为图片文件
     *
     * @param fileName 文件名
     * @return 是否为图片文件
     */
    private boolean isImageFile(String fileName) {
        if (StrUtil.isEmpty(fileName)) {
            return false;
        }
        String lowerFileName = fileName.toLowerCase();
        return lowerFileName.endsWith(".jpg") || lowerFileName.endsWith(".jpeg") ||
               lowerFileName.endsWith(".png") || lowerFileName.endsWith(".gif") ||
               lowerFileName.endsWith(".bmp") || lowerFileName.endsWith(".webp");
    }

    /**
     * 调用视觉语言模型进行图片文字提取
     *
     * @param modelName 模型名称
     * @param systemPrompt 系统提示词
     * @param question 用户问题
     * @param localFileUrl 本地图片文件URL
     * @return 提取的文字
     */
    private String callVisionModel(String modelName, String systemPrompt, String question, String localFileUrl) {
        try {
            // 构建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.set("max_tokens", 1024);
            requestBody.set("presence_penalty", 1.03);
            requestBody.set("frequency_penalty", 1.0);
            requestBody.set("seed", null);
            requestBody.set("temperature", 0.5);
            requestBody.set("top_p", 0.95);
            requestBody.set("stream", false);
            requestBody.set("model", "qwen25_vl_7b");
            
            // 构建messages数组
            JSONArray messages = new JSONArray();
            JSONObject message = new JSONObject();
            message.set("role", "user");
            
            // 构建content数组
            JSONArray content = new JSONArray();
            
            // 添加文本内容
            JSONObject textContent = new JSONObject();
            textContent.set("type", "text");
            textContent.set("text", question);
            content.add(textContent);
            
            // 添加图片内容
            JSONObject imageContent = new JSONObject();
            imageContent.set("type", "image_url");
            imageContent.set("image_url", localFileUrl);
            content.add(imageContent);
            
            message.set("content", content);
            messages.add(message);
            requestBody.set("messages", messages);
            
            // 创建HTTP客户端
            HttpClient client = HttpClient.newBuilder()
                    .connectTimeout(Duration.ofSeconds(30))
                    .build();
            
            // 构建HTTP请求
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create("http://10.0.76.30:30000/v1/chat/completions"))
                    .header("Authorization", "Bearer sk-TFhyy2kUSgAdk3do4b294b1097Ec4fCe9bD7Ee6a1dD64518")
                    .header("Content-Type", "application/json")
                    .timeout(Duration.ofMinutes(5))
                    .POST(HttpRequest.BodyPublishers.ofString(requestBody.toString()))
                    .build();
            
            // 发送请求并获取响应
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            
            // 检查响应状态
            if (response.statusCode() != 200) {
                log.error("HTTP请求失败，状态码: {}, 响应: {}", response.statusCode(), response.body());
                throw new RuntimeException("HTTP请求失败，状态码: " + response.statusCode());
            }
            
            // 解析响应
            JSONObject responseJson = JSONUtil.parseObj(response.body());
            log.info("VL模型响应: {}", responseJson.toString());
            
            // 提取结果
            JSONArray choices = responseJson.getJSONArray("choices");
            if (choices != null && !choices.isEmpty()) {
                JSONObject firstChoice = choices.getJSONObject(0);
                JSONObject message_response = firstChoice.getJSONObject("message");
                if (message_response != null) {
                    String result = message_response.getStr("content");
                    log.info("提取的文字内容: {}", result);
                    return result;
                }
            }
            
            throw new RuntimeException("无法从响应中提取文字内容");
            
        } catch (Exception e) {
            log.error("调用视觉模型失败", e);
            throw new RuntimeException("调用视觉模型失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 将MinIO图片下载到启动类所在module的src/main/resources/static/目录下
     *
     * @param minioUtil  MinIO工具类
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @param folderPath
     * @return 本地文件路径
     */
    private String downloadImageToRootFolder(MinioUtil minioUtil, String bucketName, String objectName, String folderPath) {
        try {
            // 获取项目根目录
            String rootDir = System.getProperty("user.dir");
            
            // 创建启动类所在module的src/main/resources/static目录
            Path staticDir = Paths.get(rootDir, "aip-biggroup-starter", "src", "main", "resources", "static",folderPath);
            if (!Files.exists(staticDir)) {
                Files.createDirectories(staticDir);
                log.info("创建static目录: {}", staticDir.toString());
            }
            
            // 提取文件名
            String fileName = objectName;
            if (objectName.contains("/")) {
                fileName = objectName.substring(objectName.lastIndexOf("/") + 1);
                fileName =  StrUtil.subBefore(fileName, "?", false);
            }
            if (fileName.isEmpty()) {
                fileName = "image_" + System.currentTimeMillis() + ".png";
            }
            
            // 构建本地文件路径
            Path localFilePath = staticDir.resolve(fileName);
            
            // 下载文件
            try (InputStream inputStream = minioUtil.downloadFile(bucketName, objectName);
                 FileOutputStream outputStream = new FileOutputStream(localFilePath.toFile())) {
                
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                
                log.info("图片下载成功: {} -> {}", objectName, localFilePath.toString());
                return fileName;
            }
            
        } catch (Exception e) {
            log.error("下载图片到本地失败: {}", objectName, e);
            throw new RuntimeException("下载图片到本地失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 清理本地文件
     * 
     * @param localFilePaths 本地文件路径列表
     */
    private void cleanupLocalFiles(List<String> localFilePaths) {
        for (String filePath : localFilePaths) {
            try {
                Path path = Paths.get(filePath);
                if (Files.exists(path)) {
                    Files.delete(path);
                    log.info("删除本地文件: {}", filePath);
                }
            } catch (IOException e) {
                log.warn("删除本地文件失败: {}", filePath, e);
            }
        }
        
        // 尝试删除空的static目录（通常不建议删除static目录）
        try {
            String rootDir = System.getProperty("user.dir");
            Path staticDir = Paths.get(rootDir, "aip-biggroup-starter", "src", "main", "resources", "static");
            if (Files.exists(staticDir) && Files.list(staticDir).findAny().isEmpty()) {
                // 注意：通常不删除static目录，因为它是项目的标准目录结构
                log.info("static目录为空，但保留目录结构: {}", staticDir.toString());
            }
        } catch (IOException e) {
            log.warn("检查static目录失败", e);
        }
    }
    
    /**
     * 从MinIO URL中提取objectName
     * 
     * @param imageUrl 图片URL
     * @return objectName
     */
    private String extractObjectNameFromUrl(String imageUrl) {
        try {
            // MinIO URL格式通常为: http://host:port/bucket/objectName
            // 或者: http://host:port/minio/bucket/objectName
            String[] parts = imageUrl.split("/");
            
            // 找到bucket名称的位置
            MinioUtil minioUtil = SpringUtil.getBean(MinioUtil.class);
            String bucketName = minioUtil.getMinioConfig().getBucketName();
            
            int bucketIndex = -1;
            for (int i = 0; i < parts.length; i++) {
                if (bucketName.equals(parts[i])) {
                    bucketIndex = i;
                    break;
                }
            }
            
            if (bucketIndex == -1 || bucketIndex >= parts.length - 1) {
                throw new RuntimeException("无法从URL中提取objectName: " + imageUrl);
            }
            
            // 拼接bucket后面的所有部分作为objectName
            StringBuilder objectName = new StringBuilder();
            for (int i = bucketIndex + 1; i < parts.length; i++) {
                if (i > bucketIndex + 1) {
                    objectName.append("/");
                }
                objectName.append(parts[i]);
            }
            
            return objectName.toString();
            
        } catch (Exception e) {
            log.error("从URL提取objectName失败: {}", imageUrl, e);
            throw new RuntimeException("从URL提取objectName失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String desc() {
        return "提取图片文字节点";
    }
}