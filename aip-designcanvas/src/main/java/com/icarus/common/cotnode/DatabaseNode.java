package com.icarus.common.cotnode;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.icarus.common.runtime.NodeContext;
import com.icarus.enums.DatabaseType;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class DatabaseNode extends BaseNode {

	@Getter
	@Setter
	private String result;

	private String nodeName = "数据库";

	/**
	 * 执行数据库节点操作
	 * 支持SQL查询和更新操作，查询结果将转换为JSON格式输出
	 *
	 * @param context    节点上下文
	 */
	@Override
	public Map<String, Object> execute(Map<String, Object> context) {

		String nodeId = getId();

		log.info("开始执行【{}】节点, 当前节点id:{}", nodeName, nodeId);

		NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);

		// 解析输入参数
		//Map<String, Object> resolvedInputs = resolveAllInputs(context);
		//nodeContext.setInputs(new JSONObject(resolvedInputs));

		// 解析属性配置并记录到上下文
		Properties resolvedProperties = resolveProperties(context, Properties.class);
		String sql = resolvedProperties.getSql();
		JSONObject propertiesJson = new JSONObject();
		propertiesJson.set("dbType", resolvedProperties.getDbType());
		propertiesJson.set("jdbcUrl", resolvedProperties.getJdbcUrl());
		propertiesJson.set("username", resolvedProperties.getUsername());
		propertiesJson.set("password", resolvedProperties.getPassword());
		propertiesJson.set("sql", sql);
		nodeContext.setInputs(propertiesJson);
		nodeContext.setProperties(propertiesJson);

		try {
			// 获取数据库连接
			try (Connection connection = getConnection(resolvedProperties)) {
				log.info("数据库连接成功: {}", getDatabaseInfo(connection));

				if (!StringUtils.isBlank(sql)) {
					log.info("准备执行SQL: {}", sql);
					// 判断SQL类型并执行
					String sqlType = determineSqlType(sql);
					log.info("SQL类型: {}", sqlType);

					result = executeSql(connection, sql, sqlType);
				} else {
					result = "连接成功";
				}

				JSONObject output = new JSONObject();
				output.set("connected", true);

				if (isJsonObjectString(result)) {
					output.set("result", JSONUtil.parseObj(result));
				} else if (isJsonArrString(result)) {
					output.set("result", JSONUtil.parseArray(result));
				} else {
					output.set("result", "");
				}
				nodeContext.setOutputs(output);

				result = output.toString();
				// 设置结果并更新状态
				nodeContext.setStatus("completed");
				// 将数据库信息绑定到上下文
				//JSONObject flowData = (JSONObject) context.getOrDefault("flowData", new JSONObject());
				//setValueByPath(flowData, "dbInfo", resolvedProperties);

				processOutputs(context);
			}
		} catch (Exception e) {
			log.error(nodeName + "节点执行失败", e);
			nodeContext.setStatus("failed");
			nodeContext.setErrorMessage(e.getMessage());
		} finally {
			nodeContext.setEndTime(System.currentTimeMillis());
			updateNodeContext(context, nodeId, nodeContext);
		}
		log.info("{}节点执行结束，节点ID：{}", nodeName, nodeId);
		return context;
	}

	/**
	 * 执行SQL语句并返回结果
	 */
	String executeSql(Connection connection, String sql, String sqlType) throws SQLException {
		switch (sqlType) {
			case "SELECT":
				try (ResultSet rs = connection.createStatement().executeQuery(sql)) {
					return convertResultSetToJson(rs);
				}
			case "INSERT":
			case "UPDATE":
			case "DELETE":
				int updateCount = connection.createStatement().executeUpdate(sql);
				log.info("执行DML操作，影响行数: {}", updateCount);
				return String.format("{\"affectedRows\": %d}", updateCount);
			case "CREATE":
			case "ALTER":
			case "DROP":
			case "TRUNCATE":
				boolean success = connection.createStatement().execute(sql);
				success = true; // 就算执行成功，它也默认是false。只要没抛异常，就算成功。
				log.info("执行DDL操作，执行结果: {}", success);
				return String.format("{\"success\": %b}", success);
			default:
				return executeOtherSql(connection, sql);
		}
	}

	/**
	 * 执行其他类型的SQL语句
	 */
	private String executeOtherSql(Connection connection, String sql) throws SQLException {
		boolean hasResults = connection.createStatement().execute(sql);
		if (hasResults) {
			try (ResultSet rs = connection.createStatement().getResultSet()) {
				return convertResultSetToJson(rs);
			}
		} else {
			int updateCount = connection.createStatement().getUpdateCount();
			if (updateCount >= 0) {
				return String.format("{\"affectedRows\": %d}", updateCount);
			}
			return "{\"success\": true}";
		}
	}

	/**
	 * 判断SQL语句类型
	 * 
	 * @param sql SQL语句
	 * @return SQL类型（SELECT, INSERT, UPDATE, DELETE, CREATE, ALTER, DROP, TRUNCATE等）
	 */
	String determineSqlType(String sql) {
		if (sql == null || sql.trim().isEmpty()) {
			return "UNKNOWN";
		}

		// 去除注释和多余空白
		String normalizedSql = sql.replaceAll("--.*$", "") // 删除单行注释
				.replaceAll("/\\*.*?\\*/", "") // 删除多行注释
				.trim()
				.replaceAll("\\s+", " ")
				.toUpperCase();

		// 获取第一个词，通常是SQL命令类型
		String firstWord = normalizedSql.split("\\s+")[0];

		log.info("SQL语句首个关键字: {}", firstWord);

		switch (firstWord) {
			case "SELECT":
			case "INSERT":
			case "UPDATE":
			case "DELETE":
			case "CREATE":
			case "ALTER":
			case "DROP":
			case "TRUNCATE":
				return firstWord;
			case "MERGE":
				return "UPDATE";
			case "CALL":
				return "PROCEDURE";
			default:
				return "OTHER";
		}
	}

	/**
	 * 获取数据库连接
	 * 根据配置的属性创建数据库连接，自动识别数据库类型并加载相应驱动
	 *
	 * @return 数据库连接对象
	 * @throws SQLException 如果连接创建失败或配置无效
	 */
	Connection getConnection(Properties properties) throws SQLException {
		if (properties == null || !properties.isValid()) {
			log.error("数据库配置无效: {}", properties);
			throw new SQLException("数据库配置无效");
		}

		DatabaseType dbType = DatabaseType.fromString(properties.getDbType());
		if (dbType == DatabaseType.unknown) {
			dbType = detectDatabaseTypeFromUrl(properties.getJdbcUrl());
		}
		//properties.setSql(dbType.getTestSql());
		log.info("数据库类型: {}, 驱动类: {}", dbType.getType(), dbType.getDriverClass());

		try {
			// 加载数据库驱动
			Class.forName(dbType.getDriverClass());
		} catch (ClassNotFoundException e) {
			log.error("数据库驱动加载失败: {}", dbType.getDriverClass(), e);
			throw new SQLException("数据库驱动未找到: " + dbType.getDriverClass());
		}
		log.info("正在建立数据库连接, 类型: {}, URL: {},username:{},,password:[}", dbType.getType(), properties.getJdbcUrl(),properties.getUsername(),properties.getPassword());
		return DriverManager.getConnection(
				properties.getJdbcUrl(),
				properties.getUsername(),
				properties.getPassword());
	}

	/**
	 * 从JDBC URL中检测数据库类型
	 *
	 * @param jdbcUrl JDBC连接URL
	 * @return 数据库类型
	 */
	private DatabaseType detectDatabaseTypeFromUrl(String jdbcUrl) {
		if (StringUtils.isBlank(jdbcUrl)) {
			return DatabaseType.unknown;
		}

		String lowerUrl = jdbcUrl.toLowerCase();
		if (lowerUrl.contains(":mysql:")) {
			return DatabaseType.mysql;
		} else if (lowerUrl.contains(":postgresql:")) {
			return DatabaseType.postgresql;
		} else if (lowerUrl.contains(":oracle:")) {
			return DatabaseType.oracle;
		} else if (lowerUrl.contains(":sqlserver:")) {
			return DatabaseType.sqlserver;
		} else if (lowerUrl.contains(":db2:")) {
			return DatabaseType.db2;
		}

		log.error("无法从JDBC URL识别数据库类型: {}", jdbcUrl);
		return DatabaseType.unknown;
	}

	/**
	 * 将ResultSet转换为JSON字符串
	 * 将查询结果集转换为标准的JSON数组格式，每行数据作为一个JSON对象
	 *
	 * @param rs 查询结果集
	 * @return JSON格式的字符串
	 * @throws SQLException 如果结果集处理过程中发生错误
	 */
	private String convertResultSetToJson(ResultSet rs) throws SQLException {
		ResultSetMetaData metaData = rs.getMetaData();
		int columnCount = metaData.getColumnCount();
		List<Map<String, Object>> rows = new ArrayList<>();

		log.info("开始转换查询结果，列数: {}", columnCount);

		while (rs.next()) {
			Map<String, Object> row = new HashMap<>();
			for (int i = 1; i <= columnCount; i++) {
				String columnName = metaData.getColumnLabel(i);
				Object value = rs.getObject(i);
				row.put(columnName, value);
			}
			rows.add(row);
		}

		log.info("查询结果转换完成，总行数: {}", rows.size());
		return JSONUtil.toJsonStr(rows);
	}

	/**
	 * 获取当前数据库产品信息
	 *
	 * @param connection 数据库连接
	 * @return 数据库产品信息字符串
	 */
	String getDatabaseInfo(Connection connection) {
		try {
			DatabaseMetaData metaData = connection.getMetaData();
			return String.format("Database: %s, Version: %s, Driver: %s %s",
					metaData.getDatabaseProductName(),
					metaData.getDatabaseProductVersion(),
					metaData.getDriverName(),
					metaData.getDriverVersion());
		} catch (SQLException e) {
			log.error("获取数据库信息失败", e);
			return "Unknown database";
		}
	}

	@Override
	protected Object getCurrentNodeProperty(String propertyName) {
		// 根据属性名返回对应的值
		if ("result".equals(propertyName)) {
			return JSONUtil.parseObj(result);
		}
		return super.getCurrentNodeProperty(propertyName);
	}

	@Override
	public String desc() {
		return null;
	}

	@Data
	@NoArgsConstructor
	public static class Properties {
		/**
		 * sql
		 */
		private String sql;
		/**
		 * 数据库类型 (mysql, postgresql, oracle, sqlserver, db2)
		 */
		private String dbType;

		/**
		 * JDBC URL
		 * 示例：
		 * MySQL: **********************************
		 * PostgreSQL: ***************************************
		 * Oracle: ***************************************
		 * SQLServer: ***************************************************
		 * DB2: *********************************
		 */
		private String jdbcUrl;

		/**
		 * 数据库用户名
		 */
		private String username;

		/**
		 * 数据库密码
		 */
		private String password;

		/**
		 * 验证配置是否有效
		 * 检查所有必要的配置项是否都已设置
		 *
		 * @return 如果所有必要的配置项都已设置则返回true，否则返回false
		 */
		public boolean isValid() {
			return StringUtils.isNotBlank(jdbcUrl)
					&& StringUtils.isNotBlank(username)
					&& StringUtils.isNotBlank(password);
		}
	}
}
