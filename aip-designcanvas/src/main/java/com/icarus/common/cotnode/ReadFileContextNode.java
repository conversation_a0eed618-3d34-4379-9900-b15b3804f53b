package com.icarus.common.cotnode;

import cn.hutool.json.JSONObject;
import com.icarus.common.runtime.NodeContext;
import com.icarus.sdk.springboot.base.utils.SpringContextUtils;
import com.icarus.service.FileStorageService;
import com.icarus.utils.FileContentReader;
import com.icarus.utils.FileUtil;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class ReadFileContextNode extends BaseNode {

    @Getter
    @Setter
    private String result;

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【文件读取】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
        File file = null;
        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            if (resolvedInputs == null) {
                throw new IllegalArgumentException("输入参数不能为空");
            }

            // 验证必要的输入参数
            String filePath = (String) resolvedInputs.get("filePath");
            if (filePath == null) {
                throw new IllegalArgumentException("filePath 是必需的输入参数");
            }
            // 记录输入参数到上下文
            nodeContext.setInputs(new JSONObject(resolvedInputs));

            // 解析属性配置
            Properties resolvedProperties = resolveProperties(context, Properties.class);
            JSONObject propertiesJson = new JSONObject();
            propertiesJson.set("charset", resolvedProperties.getCharset());
            nodeContext.setProperties(propertiesJson);

            // 读取文件 - 增加兼容处理
            file = getFileFromPath(filePath);
            if (file == null) {
                throw new IllegalArgumentException("无法获取文件: " + filePath);
            }

            String fileName = file.getName();
            String fileType = getFileTypeFromPath(fileName);
            // 读取文件内容
            result = readFileContent(file, fileType, resolvedProperties.getCharset());
            // 设置成功状态和输出
            nodeContext.setStatus("completed");
            // 处理输出映射
            processOutputs(context);

        } catch (Exception e) {
            log.error("文件读取失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            if (file != null) {
                file.delete();
            }
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        log.info("文件读取节点执行结束，节点ID：{}", getId());
        return context;
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    private String getFileTypeFromPath(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return fileName.substring(lastDotIndex + 1).toUpperCase();
        }
        return "TXT"; // 默认文件类型
    }

    private String readFileContent(File file, String fileType, String charset) throws Exception {
        // 如果没有指定字符集，先尝试检测文件编码
        if (StringUtils.isBlank(charset)) {
            try {
                // 尝试检测文件编码
                charset = FileUtil.detectFileEncoding(file);
                log.debug("检测到文件编码: {}", charset);
            } catch (Exception e) {
                log.warn("无法检测文件编码，使用默认UTF-8编码: {}", e.getMessage());
                charset = StandardCharsets.UTF_8.name();
            }
        }
        
        // 读取文件内容
        return FileContentReader.readContent(file, fileType, charset);
    }

    /**
     * 获取文件对象，优先使用 FileStorageService，失败则尝试直接文件路径
     * @param filePath 文件路径
     * @return File对象
     */
    private File getFileFromPath(String filePath) {
        try {
            // 首先尝试通过 FileStorageService 获取
            FileStorageService service = SpringContextUtils.getBean(FileStorageService.class);
            InputStream inputStream = service.getFile(filePath);
            if (inputStream != null) {
                String fileName = "temp_" + filePath.substring(filePath.lastIndexOf("/") + 1);
                return com.icarus.utils.FileUtil.inputStreamToFile(inputStream, fileName);
            }
        } catch (Exception e) {
            log.warn("通过 FileStorageService 获取文件失败: {}", e.getMessage());
        }

        // FileStorageService 获取失败，尝试直接文件路径
        try {
            File directFile = new File(filePath);
            if (directFile.exists() && directFile.isFile()) {
                return directFile;
            }
        } catch (Exception e) {
            log.warn("通过直接文件路径获取文件失败: {}", e.getMessage());
        }

        // 如果都失败了，返回null
        return null;
    }

    @Data
    @NoArgsConstructor
    public static class Properties {
        /**
         * 字符编码，默认自动检测，如果检测失败则使用UTF-8
         */
        private String charset;
    }

    @Override
    public String desc() {
        return "读取文件内容节点";
    }

}