package com.icarus.common.cotnode.midware;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.icarus.common.cotnode.BaseNode;
import com.icarus.common.runtime.NodeContext;
import com.icarus.config.AdvancedConfigDeserializer;
import com.icarus.config.ConditionListDeserializer;
import com.icarus.config.MapDeserializer;
import com.icarus.utils.JsonPathParser;
import com.icarus.utils.JsonUtils;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.icarus.common.enums.ElasticsearchOperationEnum;
import com.icarus.common.enums.ElasticsearchOperatorEnum;
import com.icarus.common.enums.ElasticsearchLogicEnum;
import com.icarus.common.enums.ElasticsearchSortOrderEnum;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.action.bulk.BulkItemResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * Elasticsearch操作节点
 * 支持ES的基本操作：查询、新增、更新、删除、批量处理
 * 提供灵活的查询条件配置和高级查询特性
 * dym
 */
@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class ElasticsearchNode extends BaseNode implements ApplicationContextAware {
    
    /** 最大批量处理数量 */
    private static final int MAX_BULK_SIZE = 1000;
    
    /** ES操作超时时间(毫秒) */
    private static final int OPERATION_TIMEOUT = 30000;
    
    /** 节点执行结果 */
    @Getter
    @Setter
    private String result;
    
    /** 静态 ApplicationContext */
    private static ApplicationContext staticApplicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        staticApplicationContext = applicationContext;
        log.info("ES节点: ApplicationContext 注入成功");
    }

    /**
     * 获取ES客户端实例
     */
    private RestHighLevelClient getEsClient() {
        if (staticApplicationContext == null) {
            throw new IllegalStateException("ApplicationContext未初始化");
        }
        
        try {
            RestHighLevelClient client = staticApplicationContext.getBean(RestHighLevelClient.class);
            if (client == null) {
                throw new IllegalStateException("无法获取ES客户端");
            }
            log.debug("成功获取ES客户端: {}", client);
            return client;
        } catch (Exception e) {
            log.error("获取ES客户端失败: {}", e.getMessage(), e);
            throw new IllegalStateException("获取ES客户端失败: " + e.getMessage());
        }
    }

    /**
     * 执行节点的主要逻辑
     * 1. 解析输入参数和配置
     * 2. 执行ES操作
     * 3. 处理结果和异常
     */
    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【Elasticsearch】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
        
        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            nodeContext.setInputs(new JSONObject(resolvedInputs));
            
            // 解析属性配置
            Properties resolvedProperties = resolveProperties(context, Properties.class);
            JSONObject propertiesJson = new JSONObject();
            propertiesJson.set("operationType", resolvedProperties.getOperationType());
            propertiesJson.set("indexName", resolvedProperties.getIndexName());
            propertiesJson.set("conditions", resolvedProperties.getConditions());
            propertiesJson.set("advancedConfig", resolvedProperties.getAdvancedConfig());
            nodeContext.setProperties(propertiesJson);

            // 执行ES操作并获取结果
            result = executeEsOperation(resolvedProperties);
            
            // 设置执行状态为完成
            nodeContext.setStatus("completed");
            
            // 处理输出参数
            processOutputs(context);

        } catch (Exception e) {
            log.error("Elasticsearch节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        return context;
    }

    /**
     * 根据操作类型执行相应的ES操作
     * @param properties 节点配置属性
     * @return 操作结果的JSON字符串
     */
    private String executeEsOperation(Properties properties) throws Exception {
        ElasticsearchOperationEnum operation = ElasticsearchOperationEnum.getByCode(
            properties.getOperationType().toLowerCase()
        );
        
        switch (operation) {
            case SEARCH:
                return executeSearch(properties);
            case INSERT:
                return executeInsert(properties);
            case UPDATE:
                return executeUpdate(properties);
            case DELETE:
                return executeDelete(properties);
            case BULK:
                return executeBulk(properties);
            default:
                throw new IllegalArgumentException("不支持的操作类型: " + operation.getCode());
        }
    }

    /**
     * 执行ES查询操作
     * 支持复杂的查询条件、分页、排序等特性
     */
    private String executeSearch(Properties properties) throws Exception {
        // 参数校验
        validateSearchParams(properties);
        
        SearchRequest searchRequest = new SearchRequest(properties.getIndexName());
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.timeout(TimeValue.timeValueMillis(OPERATION_TIMEOUT));
        
        // 构建查询条件
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if (properties.getConditions() != null && !properties.getConditions().isEmpty()) {
            boolQuery = buildQueryConditions(properties.getConditions());
        }
        sourceBuilder.query(boolQuery);
        
        // 处理高级配置
        handleAdvancedConfig(sourceBuilder, properties.getAdvancedConfig());
        
        // 执行查询
        searchRequest.source(sourceBuilder);
        SearchResponse response = executeWithRetry(() -> 
            getEsClient().search(searchRequest, RequestOptions.DEFAULT));
            
        // 处理查询结果
        return formatSearchResponse(response);
    }

    /**
     * 校验查询参数
     */
    private void validateSearchParams(Properties properties) {
        if (StringUtils.hasText(properties.getIndexName())) {
            try {
                GetIndexRequest request = new GetIndexRequest(properties.getIndexName());
                boolean exists = getEsClient().indices().exists(request, RequestOptions.DEFAULT);
                if (!exists) {
                    throw new IllegalArgumentException("索引不存在: " + properties.getIndexName());
                }
            } catch (IOException e) {
                throw new RuntimeException("检查索引失败", e);
            }
        }
    }

    /**
     * 处理高级配置（分页、排序）
     */
    private void handleAdvancedConfig(SearchSourceBuilder sourceBuilder, AdvancedConfig config) {
        if (config != null) {
            // 设置分页
            sourceBuilder.from(Math.max(0, config.getFrom()));
            sourceBuilder.size(Math.min(10000, config.getSize())); // ES默认限制10000
            
            // 设置排序
            if (StringUtils.hasText(config.getSortField())) {
                ElasticsearchSortOrderEnum sortOrder = ElasticsearchSortOrderEnum.getByCode(
                    config.getSortOrder()
                );
                sourceBuilder.sort(config.getSortField(), sortOrder.getSortOrder());
            }
        }
    }

    /**
     * 格式化查询结果
     */
    private String formatSearchResponse(SearchResponse response) {
        Map<String, Object> result = new HashMap<>();
        result.put("total", response.getHits().getTotalHits().value);
        result.put("took", response.getTook().getMillis());
        
        // 提取文档内容
        List<Map<String, Object>> documents = new ArrayList<>();
        Arrays.stream(response.getHits().getHits()).forEach(hit -> {
            Map<String, Object> doc = new HashMap<>();
            // 添加文档ID和得分
            doc.put("_id", hit.getId());
            doc.put("_score", hit.getScore());
            
            // 添加文档源数据
            Map<String, Object> sourceAsMap = hit.getSourceAsMap();
            if (sourceAsMap != null) {
                doc.putAll(sourceAsMap);
            }
            
            // 添加高亮结果（如果有）
            if (hit.getHighlightFields() != null && !hit.getHighlightFields().isEmpty()) {
                Map<String, Object> highlights = new HashMap<>();
                hit.getHighlightFields().forEach((key, value) -> 
                    highlights.put(key, value.getFragments()[0].string())
                );
                doc.put("highlights", highlights);
            }
            
            documents.add(doc);
        });
        result.put("documents", documents);
        
        log.debug("查询结果: {}", result);  // 添加日志
        return JsonUtils.objectToJson(result);  // 使用 JSONUtil 转换为 JSON 字符串
    }

    /**
     * 构建查询条件
     */
    private BoolQueryBuilder buildQueryConditions(List<Condition> conditions) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        
        for (Condition condition : conditions) {
            // 处理条件组
            if (condition.getGroup() != null && !condition.getGroup().isEmpty()) {
                BoolQueryBuilder groupQuery = buildQueryConditions(condition.getGroup());
                if (ElasticsearchLogicEnum.OR.getCode().equalsIgnoreCase(condition.getLogic())) {
                    boolQuery.should(groupQuery);
                } else {
                    boolQuery.must(groupQuery);
                }
                continue;
            }

            // 构建单个条件的查询
            BoolQueryBuilder singleQuery = buildSingleCondition(condition);
            if (singleQuery != null) {
                ElasticsearchLogicEnum logic = ElasticsearchLogicEnum.getByCode(condition.getLogic());
                if (ElasticsearchLogicEnum.OR.equals(logic)) {
                    boolQuery.should(singleQuery);
                } else {
                    boolQuery.must(singleQuery);
                }
            }
        }
        
        return boolQuery;
    }

    /**
     * 构建单个查询条件
     */
    private BoolQueryBuilder buildSingleCondition(Condition condition) {
        if (StringUtils.hasText(condition.getField())) {
            BoolQueryBuilder singleQuery = QueryBuilders.boolQuery();
            ElasticsearchOperatorEnum operator = ElasticsearchOperatorEnum.getByCode(
                condition.getOperator().toLowerCase()
            );
            
            switch (operator) {
                case MATCH:
                    if (condition.getValue() != null) {
                        singleQuery.must(QueryBuilders.matchQuery(condition.getField(), condition.getValue()));
                    }
                    break;
                
                case TERM:
                    if (condition.getValue() != null) {
                        singleQuery.must(QueryBuilders.termQuery(condition.getField(), condition.getValue()));
                    }
                    break;
                
                case RANGE:
                    if (condition.getRange() != null) {
                        RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery(condition.getField());
                        if (condition.getRange().getGte() != null) {
                            rangeQuery.gte(condition.getRange().getGte());
                        }
                        if (condition.getRange().getLte() != null) {
                            rangeQuery.lte(condition.getRange().getLte());
                        }
                        singleQuery.must(rangeQuery);
                    }
                    break;
                
                case WILDCARD:
                    if (condition.getValue() != null) {
                        singleQuery.must(QueryBuilders.wildcardQuery(
                            condition.getField(), 
                            condition.getValue().toString()
                        ));
                    }
                    break;
                
                case PREFIX:
                    if (condition.getValue() != null) {
                        singleQuery.must(QueryBuilders.prefixQuery(
                            condition.getField(), 
                            condition.getValue().toString()
                        ));
                    }
                    break;
                
                case EXISTS:
                    singleQuery.must(QueryBuilders.existsQuery(condition.getField()));
                    break;
                
                default:
                    throw new IllegalArgumentException("不支持的操作符: " + operator.getCode());
            }
            
            return singleQuery;
        }
        return null;
    }

    /**
     * 执行文档插入操作
     */
    private String executeInsert(Properties properties) throws Exception {
        IndexRequest request = new IndexRequest(properties.getIndexName())
            .source(properties.getDocument());
        return getEsClient().index(request, RequestOptions.DEFAULT).getId();
    }

    /**
     * 执行文档更新操作
     */
    private String executeUpdate(Properties properties) throws Exception {
        UpdateRequest request = new UpdateRequest(
            properties.getIndexName(),
            properties.getDocumentId()
        ).doc(properties.getDocument());
        return getEsClient().update(request, RequestOptions.DEFAULT).getId();
    }

    /**
     * 执行文档删除操作
     */
    private String executeDelete(Properties properties) throws Exception {
        DeleteRequest request = new DeleteRequest(
            properties.getIndexName(),
            properties.getDocumentId()
        );
        return getEsClient().delete(request, RequestOptions.DEFAULT).getId();
    }

    /**
     * 执行批量操作
     */
    private String executeBulk(Properties properties) throws Exception {
        // 参数校验
        if (properties.getBulkDocuments() == null || properties.getBulkDocuments().isEmpty()) {
            throw new IllegalArgumentException("批量文档列表不能为空");
        }
        if (properties.getBulkDocuments().size() > MAX_BULK_SIZE) {
            throw new IllegalArgumentException("批量文档数量超过限制: " + MAX_BULK_SIZE);
        }

        // 构建批量请求
        BulkRequest bulkRequest = new BulkRequest();
        bulkRequest.timeout(TimeValue.timeValueMillis(OPERATION_TIMEOUT));
        
        for (Map<String, Object> doc : properties.getBulkDocuments()) {
            bulkRequest.add(new IndexRequest(properties.getIndexName()).source(doc));
        }

        // 执行批量操作
        BulkResponse response = executeWithRetry(() -> 
            getEsClient().bulk(bulkRequest, RequestOptions.DEFAULT));

        // 处理响应结果
        JSONObject result = new JSONObject()
            .set("took", response.getTook().getMillis())
            .set("errors", response.hasFailures())
            .set("total", properties.getBulkDocuments().size());
            
        if (response.hasFailures()) {
            List<String> failures = new ArrayList<>();
            for (BulkItemResponse item : response.getItems()) {
                if (item.isFailed()) {
                    failures.add(item.getFailureMessage());
                }
            }
            result.set("failures", failures);
        }
        
        return result.toString();
    }

    /**
     * 带重试的ES操作执行
     */
    private <T> T executeWithRetry(ThrowingSupplier<T> supplier) throws Exception {
        int maxRetries = 3;
        int retryCount = 0;
        Exception lastException = null;
        
        while (retryCount < maxRetries) {
            try {
                return supplier.get();
            } catch (Exception e) {
                lastException = e;
                retryCount++;
                if (retryCount < maxRetries) {
                    log.warn("ES操作失败，准备第{}次重试: {}", retryCount, e.getMessage());
                    Thread.sleep(1000 * retryCount); // 递增重试延迟
                }
            }
        }
        
        throw new RuntimeException("ES操作重试失败", lastException);
    }

    @FunctionalInterface
    interface ThrowingSupplier<T> {
        T get() throws Exception;
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    /**
     * 节点配置属性类
     */
    @Data
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Properties {
        /** 操作类型：search, insert, update, delete, bulk */
        private String operationType;
        
        /** 索引名称 */
        private String indexName;
        
        /** 查询条件列表 */
        @JsonDeserialize(using = ConditionListDeserializer.class)
        private List<Condition> conditions;
        
        /** 高级配置（分页、排序等） */
        @JsonDeserialize(using = AdvancedConfigDeserializer.class)
        private AdvancedConfig advancedConfig;
        
        /** 文档内容（用于insert/update） */
        @JsonDeserialize(using = MapDeserializer.class)
        private Map<String, Object> document;
        
        /** 文档ID（用于update/delete） */
        private String documentId;
        
        /** 批量操作的文档列表 */
        @JsonDeserialize(contentUsing = MapDeserializer.class)
        private List<Map<String, Object>> bulkDocuments;
    }

    /**
     * 查询条件类
     */
    @Data
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Condition {
        /** 字段名 */
        private String field;
        
        /** 操作符：match, term, range等 */
        private String operator;
        
        /** 字段值 */
        private Object value;
        
        /** 范围查询的值 */
        private Range range;
        
        /** 逻辑关系：AND/OR，默认为AND */
        private String logic = "AND";
        
        /** 条件组，支持嵌套条件 */
        @JsonDeserialize(using = ConditionListDeserializer.class)
        private List<Condition> group;
    }

    /**
     * 范围查询值类
     */
    @Data
    @NoArgsConstructor
    public static class Range {
        /** 大于等于 */
        private Object gte;
        
        /** 小于等于 */
        private Object lte;
    }

    /**
     * 高级配置类
     */
    @Data
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AdvancedConfig {
        /** 分页起始位置 */
        private Integer from = 0;
        
        /** 每页大小 */
        private Integer size = 10;
        
        /** 排序字段 */
        private String sortField;
        
        /** 排序方式：asc/desc */
        private String sortOrder = "asc";

        // 添加一个字符串构造器
        public AdvancedConfig(String jsonStr) {
            try {
                ObjectMapper mapper = new ObjectMapper();
                AdvancedConfig config = mapper.readValue(jsonStr, AdvancedConfig.class);
                this.from = config.getFrom();
                this.size = config.getSize();
                this.sortField = config.getSortField();
                this.sortOrder = config.getSortOrder();
            } catch (Exception e) {
                // 使用默认值
                this.from = 0;
                this.size = 10;
                this.sortOrder = "asc";
            }
        }
    }

    protected <T> T resolveProperties(Map<String, Object> context, Class<T> propertiesClass) {
        Object properties = this.getProperties();
        if (properties == null) {
            return null;
        }

        // 将properties转换为JSONObject
        JSONObject propsJson = JSONUtil.parseObj(properties);
        JSONObject resolvedProps = new JSONObject();
        // 遍历所有属性
        for (Map.Entry<String, Object> entry : propsJson.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (value instanceof String) {
                String strValue = (String) value;
                StringBuilder result = new StringBuilder();
                int start = 0;
                int bracketStart;

                // 处理字符串中的所有引用
                while ((bracketStart = strValue.indexOf("{", start)) != -1) {
                    // 添加引用前的普通字符串
                    result.append(strValue.substring(start, bracketStart));

                    int bracketEnd = strValue.indexOf("}", bracketStart);
                    if (bracketEnd == -1) break;

                    // 获取引用路径
                    String path = strValue.substring(bracketStart + 1, bracketEnd);

                    // 从flowData中获取引用值
                    Object flowData = context.get("flowData");
                    if (flowData != null) {
                        Object resolvedValue = JsonPathParser.parseJsonPath(JSONUtil.parseObj(flowData), path);
                        result.append(resolvedValue != null ? resolvedValue : "");
                    }

                    start = bracketEnd + 1;
                }

                // 添加最后一段普通字符串
                if (start < strValue.length()) {
                    result.append(strValue.substring(start));
                }

                resolvedProps.put(key, result.toString());
            } else {
                // 非字符串类型的值直接使用
                resolvedProps.put(key, value);
            }
        }

        // 将解析后的JSON转换为指定的类型
        return JsonUtils.jsonToObject(resolvedProps.toString(),propertiesClass);
    }

    @Override
    public String desc() {
        return "ES节点";
    }
} 