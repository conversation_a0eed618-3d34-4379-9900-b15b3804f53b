package com.icarus.common.cotnode;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.icarus.common.runtime.NodeContext;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

import java.io.File;
import java.util.Map;


@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class GitPullNode extends BaseNode {

    @Getter
    @Setter
    private String result;

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【拉取git仓库】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
        File file = null;
        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            if (resolvedInputs == null) {
                throw new IllegalArgumentException("输入参数不能为空");
            }
            // 解析属性配置
            GitPullNode.Properties resolvedProperties = resolveProperties(context, GitPullNode.Properties.class);
            JSONObject propertiesJson = new JSONObject();
            propertiesJson.set("gitUrl", resolvedProperties.getGitUrl());
            propertiesJson.set("localPath", resolvedProperties.getLocalPath());
            propertiesJson.set("branchName", resolvedProperties.getBranchName());
            nodeContext.setProperties(propertiesJson);

            // 验证必要的输入参数
            String gitUrl = resolvedProperties.getGitUrl();
            String localPath = resolvedProperties.getLocalPath();
            String branchName = resolvedProperties.getBranchName();
            if (gitUrl == null) {
                throw new IllegalArgumentException("gitUrl 是必需的输入参数");
            }
            if (localPath == null) {
                throw new IllegalArgumentException("localPath 是必需的输入参数");
            }
            // 记录输入参数到上下文
            nodeContext.setInputs(new JSONObject(resolvedInputs));

            // 读取文件内容
            result = cloneWithSystemGit(localPath, gitUrl, branchName);
            // 设置成功状态和输出
            nodeContext.setStatus("completed");
////            JSONObject object = new JSONObject();
////            object.set("result", result);
//            nodeContext.setOutputs(new JSONObject(JSONUtil.toJsonStr(result)));
            // 处理输出映射
            processOutputs(context);

        } catch (Exception e) {
            log.error("git拉取失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            if (file != null) {
                file.delete();
            }
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        log.info("拉取git仓库节点执行结束，节点ID：{}", getId());
        return context;
    }

    private String cloneWithSystemGit(String localPath, String gitUrl, String branchName) {
        try {
            // 检查本地路径是否存在，如果存在则先删除
            File localDir = new File(localPath);
            if (localDir.exists()) {
                deleteDirectory(localDir);
            }
            
            // 构建git clone命令
            ProcessBuilder processBuilder;
            if (branchName != null && !branchName.trim().isEmpty()) {
                // 克隆指定分支
                processBuilder = new ProcessBuilder("git", "clone", "-b", branchName, gitUrl, localPath);
            } else {
                // 克隆默认分支
                processBuilder = new ProcessBuilder("git", "clone", gitUrl, localPath);
            }
            
            // 设置工作目录
            processBuilder.directory(new File(System.getProperty("user.dir")));
            
            // 重定向错误流到标准输出
            processBuilder.redirectErrorStream(true);
            
            // 执行命令
            Process process = processBuilder.start();
            
            // 读取输出
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), "UTF-8"))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                    log.info("Git output: {}", line);
                }
            }
            
            // 等待进程完成
            int exitCode = process.waitFor();
            
            if (exitCode == 0) {
                String successMsg = "Git仓库克隆成功，本地路径: " + localPath;
                log.info(successMsg);
                return successMsg + "\n" + output.toString();
            } else {
                String errorMsg = "Git仓库克隆失败，退出码: " + exitCode;
                log.error(errorMsg);
                log.error("Git输出: {}", output.toString());
                throw new RuntimeException(errorMsg + "\n" + output.toString());
            }
            
        } catch (IOException e) {
            String errorMsg = "执行git命令时发生IO异常: " + e.getMessage();
            log.error(errorMsg, e);
            throw new RuntimeException(errorMsg, e);
        } catch (InterruptedException e) {
            String errorMsg = "Git命令执行被中断: " + e.getMessage();
            log.error(errorMsg, e);
            Thread.currentThread().interrupt();
            throw new RuntimeException(errorMsg, e);
        }
    }

    /**
     * 递归删除目录
     */
    private void deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
            directory.delete();
        }
    }

    @Data
    @NoArgsConstructor
    public static class Properties {

        private String gitUrl;
        private String branchName;
        private String localPath;

    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    @Override
    public String desc() {
        return "拉取git仓库节点";
    }

}