package com.icarus.common.cotnode;

import cn.hutool.json.JSONObject;
import com.icarus.common.runtime.NodeContext;
import com.icarus.utils.JsonUtils;
import com.jcraft.jsch.*;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.client.WebSocketClient;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.io.ByteArrayOutputStream;

/**
 * @ClassName ServerNode
 * @Description
 * <AUTHOR>
 * @Date 2025/3/6
 * @Version 1.0
 **/
@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class ServerNode extends BaseNode {

    @Getter
    @Setter
    private String result;

    private WebSocketClient webSocketClient;
    private CompletableFuture<String> wsResponseFuture;

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行 [服务器连接] 节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
        
        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            nodeContext.setInputs(new JSONObject(resolvedInputs));
            
            // 解析属性配置
            Properties resolvedProperties = resolveProperties(context, Properties.class);
            // 目前只支持SSH连接
            resolvedInputs.put("connectionType","SSH");
            JSONObject propertiesJson = new JSONObject();
            propertiesJson.set("script", resolvedProperties.getScript());
            
            nodeContext.setProperties(propertiesJson);
            // 获取要执行的shell命令
            String script = resolvedProperties.getScript();
            Connection connection = JsonUtils.jsonToObject(JsonUtils.objectToJson(resolvedInputs), Connection.class);
            // 根据连接类型执行不同的处理逻辑
            switch (((String)resolvedInputs.get("connectionType")).toUpperCase()) {
                case "SSH":
                    result = executeSSHCommands(null, script, connection);
                    break;
                default:
                    throw new IllegalArgumentException("不支持的连接类型: " + resolvedInputs.get("connectionType"));
            }

            // 设置结果并更新状态
            nodeContext.setStatus("completed");
            
            // 处理输出参数
            processOutputs(context);

        } catch (Exception e) {
            log.error("服务器节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        log.info("服务器节点执行结束，节点ID：{}", getId());
        return context;
    }

    /**
     * SSH方式连接服务器，执行shell命令并获取返回结果
     * @param commands      命令集合
     * @param script        脚本
     * @param connection    服务器相关参数
     * @return
     */
    private String executeSSHCommands(List<String> commands, String script, Connection connection) {
        JSch jsch = new JSch();
        Session session = null;
        StringBuilder output = new StringBuilder();
        try {
            // 创建SSH会话
            session = createSSHSession(jsch, connection);
            output.append("连接建立成功 \n");
            // 执行shell命令
            if (!CollectionUtils.isEmpty(commands)) {
                for (String command : commands) {
                    output.append(executeCommand(session, command)).append("\n");
                }
            } else if (script != null && !script.isEmpty()) {
                output.append(executeCommand(session, script));
            }

        } catch (JSchException e) {
            log.error("SSH连接或执行出错", e);
            throw new RuntimeException("SSH执行失败: " + e.getMessage());
        } finally {
            if (session != null && session.isConnected()) {
                session.disconnect();
            }
        }

        return output.toString();
    }

    /**
     * 创建SSH会话
     * @param jsch
     * @param connection
     */
    private Session createSSHSession(JSch jsch, Connection connection) throws JSchException {
        Session session = null;
        try {
            session = jsch.getSession(connection.getUsername(), connection.getHost(), connection.getPort());
            session.setPassword(connection.getPassword());

            // 配置SSH连接
            java.util.Properties config = new java.util.Properties();
            config.put("StrictHostKeyChecking", "no");
            // 添加更多安全配置
            config.put("PreferredAuthentications", "password,keyboard-interactive,publickey");
            config.put("MaxAuthTries", "3");
            session.setConfig(config);

            // 设置超时
            int timeout = connection.getTimeout() != null ? connection.getTimeout() : 30000;
            session.setTimeout(timeout);
            session.connect(timeout);

            log.info("SSH连接成功建立: {}@{}", connection.getUsername(), connection.getHost());
            return session;
        } catch (JSchException e) {
            disconnectSession(session);
            throw e;
        }
    }

    /**
     * 断开SSH会话
     * @param session
     */
    private void disconnectSession(Session session) {
        try {
            if (session != null && session.isConnected()) {
                session.disconnect();
                log.debug("SSH会话已断开");
            }
        } catch (Exception e) {
            log.warn("断开SSH会话时发生错误", e);
        }
    }

    /**
     * 执行shell命令
     * @param session
     * @param command
     * @return
     * @throws JSchException
     */
    private String executeCommand(Session session, String command) throws JSchException {
        ChannelExec channel = null;
        try {
            channel = (ChannelExec) session.openChannel("exec");
            channel.setCommand(command);

            // 使用带缓冲的流处理输出
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ByteArrayOutputStream errorStream = new ByteArrayOutputStream();
            channel.setOutputStream(outputStream);
            channel.setErrStream(errorStream);
            // 设置环境变量
            channel.setEnv("LANG", "en_US.UTF-8");
            // 连接并等待执行完成
            channel.connect(30000);
            // 使用更精确的等待机制
            int exitStatus = waitForCompletion(channel);
            String output = outputStream.toString("UTF-8");
            String error = errorStream.toString("UTF-8");
            // 检查执行结果
            if (exitStatus != 0) {
                log.warn("命令执行返回非零状态: {}, 错误输出: {}", exitStatus, error);
                throw new RuntimeException("命令执行失败，退出状态: " + exitStatus);
            }
            if (!error.isEmpty()) {
                log.warn("命令执行有错误输出: {}", error);
            }
            return output;
        } catch (Exception e) {
            throw new JSchException("命令执行失败: " + e.getMessage());
        } finally {
            disconnectChannel(channel);
        }
    }

    private void disconnectChannel(ChannelExec channel) {
        try {
            if (channel != null) {
                if (channel.isConnected()) {
                    channel.disconnect();
                }
                log.debug("SSH通道已关闭");
            }
        } catch (Exception e) {
            log.warn("关闭SSH通道时发生错误", e);
        }
    }

    private int waitForCompletion(ChannelExec channel) throws InterruptedException {
        long startTime = System.currentTimeMillis();
        // 30秒超时
        long timeout = 30000;

        while (!channel.isClosed()) {
            if (System.currentTimeMillis() - startTime > timeout) {
                throw new RuntimeException("命令执行超时");
            }
            Thread.sleep(100);
        }

        return channel.getExitStatus();
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    @Override
    public String desc() {
        return null;
    }

    @Data
    @NoArgsConstructor
    public static class Properties {
        private String script;

        private String language = "shell";

    }

    @Data
    @NoArgsConstructor
    public static class Connection {
        /**
         * 基础连接配置 SSH, HTTP/HTTPS, WEBSOCKET, UDP等，目前只支持SSH
         */
        private String connectionType;
        /**
         * 服务器地址
         */
        private String host;
        /**
         * 端口号
         */
        private Integer port;
        /**
         * 用户名
         */
        private String username;
        /**
         * 密码
         */
        private String password;
        private Integer timeout;
        private String script;

    }

} 