package com.icarus.common.cotnode.flow;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.icarus.common.cotnode.BaseNode;
import com.icarus.common.pojo.AIPFlowDefinition;
import com.icarus.common.runtime.AIPFlowRuntime;
import com.icarus.common.runtime.NodeContext;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import java.util.*;

/**
 * LoopControlNode节点
 * <AUTHOR>
 * @date 2025-04-15
 */
@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class LoopControlNode extends BaseNode {
    
    // 添加结果字段
    private List<Object> result = new ArrayList<>();
    
    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【循环控制】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
        
        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            nodeContext.setInputs(new JSONObject(resolvedInputs));
            
            // 解析属性配置
            Properties resolvedProperties = resolveProperties(context, Properties.class);
            JSONObject propertiesJson = new JSONObject();
            propertiesJson.set("type", resolvedProperties.getType());
            propertiesJson.set("times", resolvedProperties.getTimes());
            propertiesJson.set("array", resolvedProperties.getArray());
            propertiesJson.set("loopContent", resolvedProperties.getLoopContent());
            nodeContext.setProperties(propertiesJson);

            // 获取或初始化循环数据
            switch (resolvedProperties.getType()){
                case "TIME":
                    Integer times = ObjUtil.isNotNull(resolvedProperties.getTimes()) ? resolvedProperties.getTimes() : 1;
                    for(int i = 0; i < times; i++){
                        // 设置遍历元素
                        JSONObject flowData = getFlowData(context);
                        flowData.put("loop-index", i + 1); // 设置当前循环下标, 循环体中的节点可通过{loop-index}获取下标（TODO 这种方式存在隐患：例如并行多个循环节点的情况）
                        // 执行循环内容
                        Object output = executeLoopContent(context, resolvedProperties.getLoopContent());
                        // 将循环结果添加到结果列表中
                        result.add(output);
                    }
                    break;
                case "ARRAY":
                    List<?> arrays = convertToList(resolvedProperties.getArray());
                    for(int i = 0; i < arrays.size(); i++){
                        // 设置遍历元素
                        JSONObject flowData = getFlowData(context);
                        flowData.put("loop-index", i + 1); // 设置当前循环下标, 循环体中的节点可通过{loop-index}获取下标（TODO 这种方式存在隐患：例如并行多个循环节点的情况）
                        flowData.put("loop-item", arrays.get(i)); // 设置当前循环元素, 循环体中的节点可通过{loop-item}获取元素（TODO 这种方式存在隐患：例如并行多个循环节点的情况）
                        // 执行循环内容
                        Object output = executeLoopContent(context, resolvedProperties.getLoopContent());
                        // 将循环结果添加到结果列表中
                        result.add(output);
                    }
                    break;
                default:
                    throw new IllegalArgumentException("不支持的循环类型" + resolvedProperties.getType());
            }
            
            // 更新节点状态
            nodeContext.setStatus("completed");
            // 处理输出参数
            processOutputs(context);
        } catch (Exception e) {
            log.error("循环控制节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }
        log.info("循环控制节点执行结束，节点ID：{}", getId());
        return context;
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    /**
     * 将对象转为List
     * @param array
     * @return
     */
    private List<?> convertToList(Object array) {
        if (array == null) {
            return null;
        }
        if (array instanceof List) {
            return (List<?>) array;
        }
        if (array instanceof Object[]) {
            return Arrays.asList((Object[]) array);
        }
        if (array instanceof String) {
            // 尝试解析JSON数组
            try {
                return JSONUtil.parseArray(array.toString());
            } catch (Exception e) {
                log.warn("Failed to parse array string: {}", array);
                return null;
            }
        }
        return null;
    }

    /**
     * 执行循环内容
     *  - 循环体中的内容要求
     *    - 第一个节点：只能是一个节点
     *    - 最后一个节点：只能是一个节点
     * @param context
     * @param loopContent
     */
    private Object executeLoopContent(Map<String, Object> context, Object loopContent) {
        try {
            // 将 loopContent 转换为 Map
            Map<String, Object> loopContentMap = JSONUtil.toBean(JSONUtil.toJsonStr(loopContent), Map.class);
            
            // 从loopContent获取canvas和当前节点
            if (loopContentMap != null && loopContentMap.containsKey("canvas")) {
                // 获取canvas并转换为FlowDefinition
                String canvasJson = JSONUtil.parse(loopContentMap.get("canvas")).toString();
                AIPFlowDefinition cotMap = JSONUtil.toBean(canvasJson, AIPFlowDefinition.class);
                // 创建新的COTRuntime实例
                AIPFlowRuntime AIPFlowRuntime = new AIPFlowRuntime(cotMap);
                // 执行循环体
                AIPFlowRuntime.executeFlow(context);
                // 获取循环体最后一个节点的输出内容
                return ((NodeContext) context.get(AIPFlowRuntime.getEndNode().getId())).getOutputs();
            } else {
                throw new RuntimeException("循环内容中未找到必要的canvas配置");
            }
        } catch (Exception e) {
            log.error("执行循环内容失败", e);
            throw new RuntimeException("执行循环内容失败: " + e.getMessage());
        }
    }

    @Data
    @NoArgsConstructor
    public static class Properties {
        private String type; // 循环类型[ARRAY:循环数组/TIME:指定循环次数]
        private Integer times; // 循环次数:循环类型为TIME时使用
        private Object array; // 循环数组:循环类型为ARRAY时使用
        private Object loopContent; // 循环内容配置
    }

    @Override
    public String desc() {
        return "循环控制节点";
    }
}

