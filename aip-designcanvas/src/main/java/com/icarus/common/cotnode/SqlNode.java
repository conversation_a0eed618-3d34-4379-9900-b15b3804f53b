package com.icarus.common.cotnode;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.icarus.common.runtime.NodeContext;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.sql.*;
import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class SqlNode extends DatabaseNode {

	@Getter
	@Setter
	private String result;

	private String nodeName = "sql执行节点";

	/**
	 * 执行数据库节点操作
	 * 支持SQL查询和更新操作，查询结果将转换为JSON格式输出
	 *
	 * @param context    节点上下文
	 */
	@Override
	public Map<String, Object> execute(Map<String, Object> context) {

		String nodeId = getId();
		log.info("开始执行【{}】节点, 当前节点id:{}", nodeName, nodeId);

		NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
		//JSONObject flowData = (JSONObject) context.getOrDefault("flowData", new JSONObject());

		// 解析输入参数
		Map<String, Object> resolvedInputs = resolveAllInputs(context);
		JSONObject input = new JSONObject(resolvedInputs);

		// 解析属性配置并记录到上下文
		Properties dbInfo = getDbInfo(context);

		input.set("dbType", dbInfo.getDbType());
		input.set("jdbcUrl", dbInfo.getJdbcUrl());
		input.set("username", dbInfo.getUsername());
		input.set("password", dbInfo.getPassword());
		nodeContext.setProperties(input);// 给后续的SQL节点使用

		Properties resolvedProperties = resolveProperties(context, Properties.class);
		String sql = resolvedProperties.getSql();

		input.set("sql", sql);
		nodeContext.setInputs(input);



		try {
			if (StringUtils.isBlank(sql)) {
				throw new IllegalArgumentException("SQL语句不能为空");
			}
			// 获取数据库连接
			try (Connection connection = getConnection(dbInfo)) {
				log.info("数据库连接成功: {}", getDatabaseInfo(connection));

				log.info("准备执行SQL: {}", sql);
				// 判断SQL类型并执行
				String sqlType = determineSqlType(sql);
				log.info("SQL类型: {}", sqlType);

				result = executeSql(connection, sql, sqlType);
				JSONObject resultJson = new JSONObject();
				resultJson.set("sqlType", sqlType);

				if (result.startsWith("[")){
					JSONArray arr = JSONUtil.parseArray(result);
					resultJson.set("data", arr);
				} else if (result.startsWith("{")){
					JSONObject obj = JSONUtil.parseObj(result);
					resultJson.set("data", obj);
				}

				nodeContext.setOutputs(resultJson);
				// 设置结果并更新状态
				nodeContext.setStatus("completed");
				// 处理输出参数
				processOutputs(context);
			}
		} catch (Exception e) {
			log.error(nodeName + "节点执行失败", e);
			nodeContext.setStatus("failed");
			nodeContext.setErrorMessage(e.getMessage());
		} finally {
			nodeContext.setEndTime(System.currentTimeMillis());
			updateNodeContext(context, nodeId, nodeContext);
		}
		log.info("{}节点执行结束，节点ID：{}", nodeName, nodeId);
		return context;
	}

	private Properties getDbInfo(Map<String, Object> context) {
		List<String> ids = getPrevNodeIds();
		if (ids == null || ids.isEmpty()) {
			//JSONObject flowData = (JSONObject) context.getOrDefault("flowData", new JSONObject());
			//return JSONUtil.toBean((JSONObject)flowData.get("dbInfo"), Properties.class);
			throw new RuntimeException("找不到数据库节点");
		}
		// getPrevNodeIds 这个方法似乎只能拿到上一个节点的id。
		/*NodeContext dbNodeContext = null;
		for (int i = ids.size() - 1; i >= 0; i--) {
			String nodeId = ids.get(i);
			NodeContext nodeContext = getNodeContext(context, nodeId);
			if (DatabaseNode.class.getSimpleName().equals(nodeContext.getNodeType())) {
				dbNodeContext = nodeContext;
				break;
			}
		}*/
		NodeContext dbNodeContext = getNodeContext(context, ids.get(0));
		if (dbNodeContext == null) {
			throw new RuntimeException("找不到数据库节点");
		}
		JSONObject dbInfo = new JSONObject(dbNodeContext.getProperties());
		return dbInfo.toBean(Properties.class);
	}

	@Override
	protected Object getCurrentNodeProperty(String propertyName) {
		// 根据属性名返回对应的值
		if ("result".equals(propertyName)) {
			return result;
		}
		return super.getCurrentNodeProperty(propertyName);
	}

	@Override
	public String desc() {
		return "sql执行节点";
	}

}
