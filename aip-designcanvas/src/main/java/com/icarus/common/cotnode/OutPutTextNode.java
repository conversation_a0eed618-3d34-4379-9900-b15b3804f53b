package com.icarus.common.cotnode;

import cn.hutool.json.JSONObject;
import com.icarus.common.runtime.NodeContext;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class OutPutTextNode extends BaseNode{
    @Getter
    @Setter
    private String result;


    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【OutPutTextNode】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
        try{
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            nodeContext.setInputs(new JSONObject(resolvedInputs));

            // 设置结果并更新状态
            result = str(resolvedInputs);
            nodeContext.setStatus("completed");
            // 处理输出参数
            processOutputs(context);
        }catch (Exception e) {
            log.error("输出字符串节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }
        log.info("输出字符串节点执行结束，节点ID：{}", nodeId);
        return context;
    }

    private String str(Map<String, Object> inputs) {
        String text = "";
        for (Map.Entry<String,Object> entry : inputs.entrySet()){
            text +=  entry.getValue() != null ?  entry.getValue().toString() : "";
        }

        return text;
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    @Override
    public String desc() {
        return "输出字符串节点";
    }
}
