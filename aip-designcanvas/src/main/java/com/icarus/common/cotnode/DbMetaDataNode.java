package com.icarus.common.cotnode;

import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.icarus.common.runtime.NodeContext;
import com.icarus.service.impl.DesignDataSourceServiceImpl;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * 数据库元数据节点
 * 根据数据源代码获取数据库表信息，并返回JSON字符串
 */
@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class DbMetaDataNode extends BaseNode {

    @Getter
    @Setter
    private String result;

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【数据库元数据】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);

        try {
            // 解析属性配置
            Properties resolvedProperties = resolveProperties(context, Properties.class);
            String dataSourceCode = resolvedProperties.getDataSourceCode();

            if (dataSourceCode == null || dataSourceCode.isEmpty()) {
                throw new IllegalArgumentException("dataSourceCode 是必需的参数");
            }

            // 获取DesignDataSourceServiceImpl实例
            DesignDataSourceServiceImpl dataSourceService = SpringUtil.getBean(DesignDataSourceServiceImpl.class);

            // 调用服务获取表信息
            Object tablesInfo = dataSourceService.getTablesByDataSourceCode(dataSourceCode);

            // 转换为JSON字符串
            result = JSONUtil.toJsonStr(tablesInfo);

            // 设置成功状态
            nodeContext.setStatus("completed");

            // 处理输出映射
            processOutputs(context);

        } catch (Exception e) {
            log.error("获取数据库元数据失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        log.info("数据库元数据节点执行结束，节点ID：{}", getId());
        return context;
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    @Override
    public String desc() {
        return "数据库元数据节点";
    }

    @Data
    @NoArgsConstructor
    public static class Properties {
        /**
         * 数据源代码
         */
        private String dataSourceCode;
    }
} 