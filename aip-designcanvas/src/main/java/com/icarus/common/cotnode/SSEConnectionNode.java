package com.icarus.common.cotnode;

import cn.hutool.json.JSONObject;
import com.icarus.common.runtime.NodeContext;
import com.icarus.starter.channel.queue.sse.SseEmitterHeartbeat;
import com.icarus.starter.channel.queue.sse.SseEmitterManager;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.HashMap;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
@Component
public class SSEConnectionNode extends BaseNode {

    @Getter
    @Setter
    private String result;

    @Getter
    @Setter
    private SseEmitter sseEmitter;

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【SSE连接】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);

        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            nodeContext.setInputs(new JSONObject(resolvedInputs));

            // 解析属性配置
            Properties resolvedProperties = resolveProperties(context, Properties.class);
            JSONObject propertiesJson = new JSONObject();
            propertiesJson.set("userId", resolvedProperties.getUserId());
            propertiesJson.set("sessionId", resolvedProperties.getSessionId());
            nodeContext.setProperties(propertiesJson);

            // 设置会话ID
            result = resolvedProperties.getSessionId();
            if (result == null || result.isEmpty()) {
                // 如果没有提供会话ID，则生成一个唯一的会话ID
                result = generateSessionId(resolvedProperties);
            }

            // 创建并管理SSE连接
            setupSseConnection(resolvedProperties);

            // 将会话ID添加到上下文中，供后续节点使用
            context.put("result", result);

            nodeContext.setStatus("completed");

            // 处理输出
            processOutputs(context);

        } catch (Exception e) {
            log.error("SSE连接节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        log.info("SSE连接节点执行结束，节点ID：{}，会话ID：{}", getId(), result);
        return context;
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        } else if ("sseEmitter".equals(propertyName)) {
            return sseEmitter;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    /**
     * 生成会话ID
     */
    private String generateSessionId(Properties properties) {
        // 生成唯一的会话ID
        String uniqueId = properties.getUserId() + "_" + System.currentTimeMillis();
        log.info("生成会话ID: {}", uniqueId);
        return uniqueId;
    }

    /**
     * 设置SSE连接
     */
    private void setupSseConnection(Properties properties) {
        log.info("设置SSE长连接，会话ID：{}", result);

        try {
            // 检查是否已存在连接
            sseEmitter = SseEmitterManager.get(result);

            // 如果连接不存在，创建新连接
            if (sseEmitter == null) {
                sseEmitter = SseEmitterManager.init(result);
                SseEmitterManager.put(result, sseEmitter);

                // 启动心跳
                SseEmitterHeartbeat.startHeartbeat();

                // 发送初始化消息
                sendInitMessage();

                log.info("成功创建SSE连接，会话ID：{}", result);
            } else {
                log.info("SSE连接已存在，会话ID：{}", result);
            }
        } catch (Exception e) {
            log.error("设置SSE连接失败: {}", e.getMessage(), e);
            throw new RuntimeException("设置SSE连接失败: " + e.getMessage(), e);
        }
    }

    /**
     * 发送初始化消息
     */
    private void sendInitMessage() {
        try {
            Map<String, Object> data = new HashMap<>();
            data.put("type", "init");
            data.put("message", "SSE连接已建立");
            data.put("timestamp", System.currentTimeMillis());

            sseEmitter.send(SseEmitter.event().data(data));
            log.info("发送初始化消息成功，会话ID：{}", result);
        } catch (Exception e) {
            log.error("发送初始化消息失败，会话ID：{}", result, e);
            SseEmitterManager.del(result);
            sseEmitter = null;
            throw new RuntimeException("发送初始化消息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 关闭SSE连接
     */
    public void closeConnection() {
        if (sseEmitter != null) {
            try {
                sseEmitter.complete();
                log.info("关闭SSE连接，会话ID：{}", result);
            } catch (Exception e) {
                log.error("关闭SSE连接失败，会话ID：{}", result, e);
            } finally {
                SseEmitterManager.del(result);
                sseEmitter = null;
            }
        }
    }

    @Data
    @NoArgsConstructor
    public static class Properties {
        private String userId; // 用户ID
        private String sessionId; // 会话ID，如果为空则自动生成
    }

    @Override
    public String desc() {
        return "SSE连接节点";
    }
}