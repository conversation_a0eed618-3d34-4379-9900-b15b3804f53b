package com.icarus.common.cotnode.ab.crawler.dto;

import java.util.HashMap;
import java.util.Map;

/**
 * 浏览器操作结果封装类
 */
public class BrowserResult {
    private boolean success;
    private String message;
    private Map<String, Object> data;

    private BrowserResult(boolean success, String message) {
        this.success = success;
        this.message = message;
        this.data = new HashMap<>();
    }

    public static BrowserResult success() {
        return new BrowserResult(true, "操作成功");
    }

    public static BrowserResult success(String message) {
        return new BrowserResult(true, message);
    }

    public static BrowserResult error(String message) {
        return new BrowserResult(false, message);
    }

    public BrowserResult put(String key, Object value) {
        this.data.put(key, value);
        return this;
    }

    public boolean isSuccess() {
        return success;
    }

    public String getMessage() {
        return message;
    }

    public Map<String, Object> getData() {
        return data;
    }

    @SuppressWarnings("unchecked")
    public <T> T get(String key) {
        return (T) data.get(key);
    }

    public String getString(String key) {
        Object value = data.get(key);
        return value != null ? value.toString() : null;
    }

    public Integer getInteger(String key) {
        Object value = data.get(key);
        if (value instanceof Integer) {
            return (Integer) value;
        } else if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
}
