package com.icarus.common.cotnode;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.icarus.common.runtime.NodeContext;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import java.security.MessageDigest;
import java.util.*;

/**
 * 文本审核节点
 * 基于易盾文本审核API实现内容安全检测
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class TextAuditNode extends BaseNode {

    /**
     * 审核结果：通过、不通过、需要人工复审
     */
    @Getter
    @Setter
    private String auditResult;

    /**
     * 审核后的文本内容（如果不通过则返回拒绝文本）
     */
    @Getter
    @Setter
    private String resultText;

    /**
     * 审核详细信息
     */
    @Getter
    @Setter
    private String auditDetails;

    /**
     * 易盾文本审核配置参数
     */
    @Value("${yidun.text.secret-id:}")
    private String secretId;

    @Value("${yidun.text.secret-key:}")
    private String secretKey;

    @Value("${yidun.text.business-id:}")
    private String businessId;

    @Value("${yidun.text.api-url:}")
    private String apiUrl;

    /**
     * 审核不通过时的默认回复文本
     */
    private static final String DEFAULT_REJECT_TEXT = "您好！本模型主要用于特定的业务处理、风险识别以及知识库建设，还可为专业人员提供文件法规查询参考，您的问题不在服务范围内，无法提供解答。";

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【文本审核】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
        
        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            nodeContext.setInputs(new JSONObject(resolvedInputs));
            log.info("文本审核节点输入参数: {}", resolvedInputs);
            
            // 解析属性配置
            Properties resolvedProperties = resolveProperties(context, Properties.class);
            JSONObject propertiesJson = new JSONObject();
            propertiesJson.set("inputText", resolvedProperties.getInputText());
            propertiesJson.set("rejectText", resolvedProperties.getRejectText());
            propertiesJson.set("enableAudit", resolvedProperties.getEnableAudit());
            nodeContext.setProperties(propertiesJson);
            
            if (resolvedProperties != null) {
                String inputText = resolvedProperties.getInputText();
                log.info("待审核文本长度: {}", inputText != null ? inputText.length() : 0);
                
                // 检查是否启用审核
                if (!resolvedProperties.getEnableAudit()) {
                    log.info("文本审核功能已禁用，直接通过");
                    auditResult = "PASS";
                    resultText = inputText;
                    auditDetails = "审核功能已禁用";
                } else {
                    // 执行文本审核
                    boolean auditPassed = performTextAudit(inputText);
                    
                    if (auditPassed) {
                        log.info("文本审核通过");
                        auditResult = "PASS";
                        resultText = inputText;
                        auditDetails = "审核通过";
                    } else {
                        log.info("文本审核不通过，返回拒绝文本");
                        auditResult = "REJECT";
                        // 使用配置的拒绝文本，如果为空则使用默认文本
                        resultText = StrUtil.isNotBlank(resolvedProperties.getRejectText()) 
                                   ? resolvedProperties.getRejectText() 
                                   : DEFAULT_REJECT_TEXT;
                        auditDetails = "审核不通过，内容可能包含敏感信息";
                    }
                }
                
                // 设置结果并更新状态
                nodeContext.setStatus("completed");
                log.info("文本审核完成，结果: {}", auditResult);
                
                // 处理输出参数
                processOutputs(context);
            }

        } catch (Exception e) {
            log.error("文本审核节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage("文本审核执行失败: " + e.getMessage());
            
            // 审核失败时的处理
            auditResult = "ERROR";
            resultText = DEFAULT_REJECT_TEXT;
            auditDetails = "审核服务异常: " + e.getMessage();
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        log.info("文本审核节点执行结束，节点ID：{}", getId());
        return context;
    }

    /**
     * 执行文本审核
     * 
     * @param text 待审核的文本
     * @return true-审核通过，false-审核不通过
     */
    private boolean performTextAudit(String text) {
        if (StrUtil.isBlank(text)) {
            log.info("待审核文本为空，直接通过");
            return true;
        }

        // 检查配置参数
        if (StrUtil.hasBlank(secretId, secretKey, businessId, apiUrl)) {
            log.error("易盾文本审核配置参数不完整，跳过审核");
            return true;
        }

        try {
            log.info("开始调用易盾文本审核API");
            
            // 构建请求参数
            Map<String, Object> params = buildAuditParams(text);
            
            // 发送HTTP请求
            HttpResponse response = HttpRequest.post(apiUrl)
                    .form(params)
                    .timeout(10000) // 10秒超时
                    .execute();
            
            if (!response.isOk()) {
                log.error("易盾API请求失败，HTTP状态码: {}", response.getStatus());
                return false;
            }
            
            String responseBody = response.body();
            log.debug("易盾API响应: {}", responseBody);
            
            // 解析响应结果
            return parseAuditResponse(responseBody);
            
        } catch (Exception e) {
            log.error("调用易盾文本审核API异常", e);
            return false;
        }
    }

    /**
     * 构建审核请求参数
     * 
     * @param text 待审核文本
     * @return 请求参数Map
     */
    private Map<String, Object> buildAuditParams(String text) {
        Map<String, Object> params = new HashMap<>();
        
        // 1.设置公共参数
        params.put("secretId", secretId);
        params.put("businessId", businessId);
        params.put("version", "v5.3");
        params.put("timestamp", String.valueOf(System.currentTimeMillis()));
        params.put("nonce", String.valueOf(new Random().nextInt()));
        params.put("signatureMethod", "MD5");

        // 2.设置私有参数
        params.put("dataId", UUID.randomUUID().toString());
        params.put("content", text);

        // 3.预处理参数（过滤空值）
        params = pretreatmentParams(params);

        // 4.生成签名
        String signature = generateSignature(secretKey, params);
        params.put("signature", signature);
        
        log.debug("构建审核请求参数完成，参数数量: {}", params.size());
        return params;
    }

    /**
     * 预处理参数，过滤空值和signature字段
     * 
     * @param params 原始参数
     * @return 处理后的参数
     */
    private Map<String, Object> pretreatmentParams(Map<String, Object> params) {
        Map<String, Object> result = new HashMap<>(params.size());
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            // 过滤空值与原始参数中的signature，避免签名串与实际请求参数不一致导致签名校验失败
            if (entry.getValue() != null && !"signature".equals(entry.getKey())) {
                result.put(entry.getKey(), entry.getValue());
            }
        }
        return result;
    }

    /**
     * 生成签名
     * 
     * @param secretKey 密钥
     * @param params 参数Map
     * @return 签名字符串
     */
    private String generateSignature(String secretKey, Map<String, Object> params) {
        try {
            // 1. 参数排序
            TreeMap<String, Object> sortedParams = new TreeMap<>(params);
            
            // 2. 拼接参数字符串
            StringBuilder paramStr = new StringBuilder();
            for (Map.Entry<String, Object> entry : sortedParams.entrySet()) {
                paramStr.append(entry.getKey()).append(entry.getValue());
            }
            
            // 3. 添加密钥
            paramStr.append(secretKey);
            
            // 4. MD5加密
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(paramStr.toString().getBytes("UTF-8"));
            
            // 5. 转换为16进制字符串
            StringBuilder hexStr = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexStr.append('0');
                }
                hexStr.append(hex);
            }
            
            return hexStr.toString();
        } catch (Exception e) {
            log.error("生成签名失败", e);
            throw new RuntimeException("生成签名失败", e);
        }
    }

    /**
     * 解析审核响应结果
     * 
     * @param responseBody 响应体
     * @return true-审核通过，false-审核不通过
     */
    private boolean parseAuditResponse(String responseBody) {
        try {
            JSONObject responseObj = JSONUtil.parseObj(responseBody);
            int code = responseObj.getInt("code");
            String msg = responseObj.getStr("msg");

            if (code != 200) {
                log.error("易盾审核请求失败: code={}, msg={}", code, msg);
                return false;
            }

            // 获取审核结果
            JSONObject result = responseObj.getJSONObject("result");
            if (result == null) {
                log.error("易盾审核响应格式异常，缺少result字段");
                return false;
            }
            
            JSONObject antispam = result.getJSONObject("antispam");
            if (antispam == null) {
                log.error("易盾审核响应格式异常，缺少antispam字段");
                return false;
            }
            
            String taskId = antispam.getStr("taskId");
            int suggestion = antispam.getInt("suggestion");
            long censorTime = antispam.getLong("censorTime");
            JSONArray labels = antispam.getJSONArray("labels");

            // 记录详细的审核信息
            auditDetails = String.format("taskId: %s, suggestion: %d, censorTime: %d, labels: %s", 
                                        taskId, suggestion, censorTime, labels);

            // 根据suggestion判断审核结果
            switch (suggestion) {
                case 0:
                    log.info("文本审核通过 - taskId: {}, 审核时间: {}", taskId, censorTime);
                    return true;
                case 1:
                    log.info("文本需人工复审 - taskId: {}, 审核时间: {}, 分类信息: {}", 
                            taskId, censorTime, labels);
                    return false;
                case 2:
                    log.info("文本审核不通过 - taskId: {}, 审核时间: {}, 分类信息: {}", 
                            taskId, censorTime, labels);
                    return false;
                default:
                    log.info("未知的审核结果 - suggestion: {}", suggestion);
                    return false;
            }
        } catch (Exception e) {
            log.error("解析易盾审核响应失败", e);
            return false;
        }
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        switch (propertyName) {
            case "auditResult":
                return auditResult;
            case "resultText":
                return resultText;
            case "auditDetails":
                return auditDetails;
            default:
                return super.getCurrentNodeProperty(propertyName);
        }
    }

    /**
     * 节点属性配置类
     */
    @Data
    @NoArgsConstructor
    public static class Properties {
        /**
         * 待审核的文本内容
         */
        private String inputText;
        
        /**
         * 审核不通过时返回的文本（可选，为空时使用默认文本）
         */
        private String rejectText;
        
        /**
         * 是否启用审核功能（默认启用）
         */
        private Boolean enableAudit = true;
    }

    @Override
    public String desc() {
        return "文本审核节点";
    }
}
