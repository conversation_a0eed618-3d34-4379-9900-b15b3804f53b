package com.icarus.common.cotnode;

import cn.hutool.json.JSONObject;
import com.icarus.common.runtime.NodeContext;
import com.icarus.dto.RuntimeRequest;
import com.icarus.utils.FileUtil;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
@Deprecated
public class UploaderNode extends BaseNode {
    
    @Getter
    @Setter
    private String result;

    private String nodeName = "文件上传";
    private static final String uploadFilePath = FileUtil.AI_DESIGNER_PATH.toString() + File.separator + "uploadFiles";

    private List<File> tempFiles = new ArrayList<>();
    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【{}】节点, 当前节点id:{}", nodeName, getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
        
        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            nodeContext.setInputs(new JSONObject(resolvedInputs));

            // 获取 request 对象并进行空值检查
            RuntimeRequest requestObj = (RuntimeRequest) context.get("request");
            MultipartFile[] files = null;

            if (files == null) {
                throw new IllegalArgumentException("未找到上传文件数据");
            }

            log.info("接收到文件个数: {}", files.length);

            // 如果目标目录为空，使用当前服务器路径
            String targetDirectory = (String) resolvedInputs.get("targetDirectory");
            if (targetDirectory == null || targetDirectory.trim().isEmpty()) {
                targetDirectory = uploadFilePath;
                log.info("未指定目标目录，使用默认路径：{}", targetDirectory);
            }

            // 确保目标目录存在
            File targetDir = new File(targetDirectory);
            if (!targetDir.exists()) {
                if (!targetDir.mkdirs()) {
                    throw new IOException("无法创建目标目录: " + targetDirectory);
                }
            }

            copy(files, targetDirectory);

            // 设置处理结果
            nodeContext.setStatus("completed");
            // 处理输出
            processOutputs(context);

        } catch (Exception e) {
            log.error("文件上传节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            // 删除临时文件
            deleteTmpFile();
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        log.info("文件上传节点执行结束，节点ID：{}", getId());
        return context;
    }

    private void copy(MultipartFile[] files, String targetDirectory) throws IOException {

        List<File> copyFiles = new ArrayList<>();
        for (MultipartFile mf : files) {

            // 生成新的文件名（保留原文件名，添加时间戳后缀）
            String originalFileName = mf.getOriginalFilename();
            String fileExtension = getFileExtension(originalFileName);
            String nameWithoutExtension = getFileNameWithoutExtension(originalFileName);

            // 生成时间戳后缀，格式：yyyyMMddHHmmss
            String timePrefix = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String newFileName = nameWithoutExtension + "_" + timePrefix +
                    (fileExtension.isEmpty() ? "" : "." + fileExtension);
            Path targetPath = Paths.get(targetDirectory, newFileName);

            // 复制文件到目标位置
            mf.transferTo(targetPath);
            Resource r = mf.getResource();
            if (r.exists() && r.isReadable() && r.isFile()) {
                tempFiles.add(r.getFile());
            }
            copyFiles.add(targetPath.toFile());
        }

        // 设置结果
        result = String.valueOf(copyFiles);
    }

    /*@PostConstruct
    public void initUploadFile() {
        File uploadFile = new File(uploadFilePath);
        if (!uploadFile.exists()) {
            if (uploadFile.mkdirs()) {
                //log.info("【{}】创建目录成功：{}", nodeName, uploadFilePath);
            } else {
                log.error("【{}】创建目录失败：{}", nodeName, uploadFilePath);
                throw new RuntimeException(String.format("创建上传目录失败，[%s]节点不可用", nodeName));
            }
        } else {
            //log.info("【{}】创建目录已存在：{}", nodeName, uploadFilePath);

        }
    }*/

    private void deleteTmpFile() {
        if (tempFiles != null && !tempFiles.isEmpty()) {
            for (File file : tempFiles) {
                if (!file.delete()) {
                    log.info("临时文件删除失败:{}", file);
                }
            }
        }

    }

    private String getFileExtension(String fileName) {
        int lastIndexOf = fileName.lastIndexOf(".");
        if (lastIndexOf == -1) {
            return "";
        }
        return fileName.substring(lastIndexOf + 1).toLowerCase();
    }

    private String getFileNameWithoutExtension(String fileName) {
        int lastIndexOf = fileName.lastIndexOf(".");
        if (lastIndexOf == -1) {
            return fileName;
        }
        return fileName.substring(0, lastIndexOf);
    }

    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.2f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", size / (1024.0 * 1024));
        } else {
            return String.format("%.2f GB", size / (1024.0 * 1024 * 1024));
        }
    }

    private String getFileType(String extension) {
        if (extension == null || extension.isEmpty()) {
            return "未知类型";
        }
        return extension.toLowerCase();
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    @Override
    public String desc() {
        return "文件上传节点";
    }

}