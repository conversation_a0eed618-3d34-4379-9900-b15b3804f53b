package com.icarus.common.cotnode.flow;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.icarus.common.cotnode.BaseNode;
import com.icarus.common.runtime.NodeContext;
import com.icarus.sdk.client.utils.LLMClientUtil;
import com.icarus.sdk.prompt.PromptTemplate;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class IntentionRecognitionNode extends BaseNode {

    @Getter
    @Setter
    private String result;

    private String nodeName = "意图识别";

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {

        String nodeId = getId();

        log.info("开始执行【{}】节点, 当前节点id:{}", nodeName, nodeId);

        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);

        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);

            JSONObject intput = new JSONObject(resolvedInputs);

            // 解析属性配置
            Properties resolvedProperties = resolveProperties(context, Properties.class);

            String model = resolvedProperties.getModel();
            String systemPrompt = resolvedProperties.getSystemPrompt();
            String question = resolvedProperties.getQuestion();
            String intentionString = resolvedProperties.getIntentionMatch();
            if (intentionString == null || intentionString.trim().isEmpty()) {
                throw new IllegalArgumentException("意图列表不能为空");
            }

            // 处理意图数据
            String intentionStr = processIntentionData(intentionString, intput);

            intput.set("model", model);
            intput.set("systemPrompt", systemPrompt);
            intput.set("question", question);

            nodeContext.setInputs(intput);

            systemPrompt = systemPrompt == null ? "" : systemPrompt;

            if (!StringUtils.hasText(model)) {
                throw new IllegalArgumentException("模型参数不能为空");
            }

            if (!StringUtils.hasText(question)) {
                throw new IllegalArgumentException("问题不能为空");
            }

            JSONObject propertiesJson = new JSONObject();
            propertiesJson.set("model", model);
            propertiesJson.set("systemPrompt", systemPrompt);
            propertiesJson.set("question", question);
            propertiesJson.set("intentionMatch", intput.get("intentionMatch"));
            nodeContext.setProperties(propertiesJson);

            // 构建提示词并执行
            String prompt = intentionPrompt(
                    "systemPrompt", systemPrompt,
                    "question", question,
                    "intentions", intentionStr);

            log.info("prompt:\n{}", prompt);

            result = LLMClientUtil.builder()
                    .modelName(model)
                    .chat(prompt)
                    .runChatCompletion();

            result = extractJSON(result);

            log.info("result:\n{}", result);

            JSONObject outputObj = JSONUtil.parseObj(result);

            nodeContext.setOutputs(outputObj);
            // 设置结果并更新状态
            nodeContext.setStatus("completed");
            // 处理输出参数
            processOutputs(context);

        } catch (Exception e) {
            log.error(nodeName + "节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        log.info("【{}】节点执行结束，节点ID：{}", nodeName, nodeId);
        return context;
    }

    /**
     * 处理意图数据，支持多种格式输入
     * 
     * @param intentionString 意图字符串
     * @param input           输入对象，用于存储处理后的意图数据
     * @return 格式化后的意图字符串
     */
    private String processIntentionData(String intentionString, JSONObject input) {
        // 如果是JSON数组格式
        if (intentionString.trim().startsWith("[")) {
            try {
                // 尝试解析为Intention对象列表
                List<Intention> intentions = JSONUtil.toList(intentionString, Intention.class);
                if (intentions != null && !intentions.isEmpty()) {
                    input.set("intentionMatch", intentions);
                    return JSONUtil.toJsonStr(intentions);
                }
            } catch (Exception e) {
                log.info("无法解析为Intention对象列表，尝试其他格式解析");
                // 解析失败，尝试其他格式
            }

            try {
                // 尝试解析为通用JSON数组
                JSONArray jsonArray = JSONUtil.parseArray(intentionString);
                if (jsonArray != null && !jsonArray.isEmpty()) {
                    // 检查是否需要转换为Intention格式
                    List<Intention> convertedIntentions = convertToIntentionList(jsonArray);
                    if (convertedIntentions != null && !convertedIntentions.isEmpty()) {
                        input.set("intentionMatch", convertedIntentions);
                        return JSONUtil.toJsonStr(convertedIntentions);
                    } else {
                        // 直接使用原始数组
                        input.set("intentionMatch", jsonArray);
                        return jsonArray.toString();
                    }
                }
            } catch (Exception e) {
                log.info("无法解析为通用JSON数组，使用原始字符串");
                // 解析失败，使用原始字符串
            }
        }

        // 如果不是JSON格式或解析失败，直接使用原始字符串
        input.set("intentionMatch", intentionString);
        return intentionString;
    }

    /**
     * 尝试将通用JSON数组转换为Intention对象列表
     * 
     * @param jsonArray JSON数组
     * @return Intention对象列表，如果无法转换则返回null
     */
    private List<Intention> convertToIntentionList(JSONArray jsonArray) {
        List<Intention> result = new ArrayList<>();
        boolean canConvert = true;

        // 检查数组中的每个元素是否可以转换为Intention对象
        for (int i = 0; i < jsonArray.size(); i++) {
            Object item = jsonArray.get(i);
            if (item instanceof JSONObject) {
                JSONObject jsonObj = (JSONObject) item;
                Intention intention = new Intention();

                // 尝试获取id和value字段
                if (jsonObj.containsKey("id")) {
                    try {
                        intention.setId(jsonObj.getLong("id"));
                    } catch (Exception e) {
                        canConvert = false;
                        break;
                    }
                } else {
                    // 如果没有id字段，使用索引作为id
                    intention.setId(i + 1);
                }

                // 尝试获取value字段
                if (jsonObj.containsKey("value")) {
                    intention.setValue(jsonObj.getStr("value"));
                } else if (jsonObj.containsKey("name")) {
                    // 尝试使用name字段作为value
                    intention.setValue(jsonObj.getStr("name"));
                } else if (jsonObj.containsKey("text")) {
                    // 尝试使用text字段作为value
                    intention.setValue(jsonObj.getStr("text"));
                } else {
                    // 如果没有合适的字段，使用整个对象的字符串表示
                    intention.setValue(jsonObj.toString());
                }

                result.add(intention);
            } else if (item instanceof String) {
                // 如果是字符串，直接创建Intention对象
                Intention intention = new Intention();
                intention.setId(i + 1);
                intention.setValue((String) item);
                result.add(intention);
            } else {
                // 其他类型，尝试转换为字符串
                Intention intention = new Intention();
                intention.setId(i + 1);
                intention.setValue(String.valueOf(item));
                result.add(intention);
            }
        }

        return canConvert ? result : null;
    }

    private String formatIntentions(List<Intention> intentions) {
        return JSONUtil.toJsonStr(intentions);
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    public String intentionPrompt(String... kvs) throws IOException {
        PromptTemplate promptTemplate = PromptTemplate.builder("prompts/IntentionRecognition.groovy", "intention");

        Map<String, String> ptMap = new HashMap<>();

        for (int i = 0; i < kvs.length; i += 2) {
            ptMap.put(kvs[i], kvs[i + 1]);
        }
        return promptTemplate.format(ptMap).build();
    }

    private String extractJSON(String result) {

        int endIdx = result.indexOf("</think>");
        if (endIdx > -1) {
            result = result.substring(endIdx + 8).trim();
        }

        if (result.startsWith("```json") && result.endsWith("```")) {
            result = result.substring(7, result.length() - 3).trim();
        }

        return result;
    }

    @Override
    public String desc() {
        return "意图识别节点";
    }

    @Data
    @NoArgsConstructor
    public static class Properties {
        private String model;
        private String systemPrompt;
        private String question;
        private String intentionMatch;
    }

    @Data
    @NoArgsConstructor
    public static class Intention {
        private String value;
        private long id;
    }
}