package com.icarus.common.cotnode.ab.cmtt.vectorNode.service.impl;

import com.icarus.common.cotnode.ab.cmtt.vectorNode.entity.AivecFile;
import com.icarus.common.cotnode.ab.cmtt.vectorNode.mapper.AivecFileMapper;
import com.icarus.common.cotnode.ab.cmtt.vectorNode.service.IAivecFileService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 文件表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Service
public class AivecFileServiceImpl extends ServiceImpl<AivecFileMapper, AivecFile> implements IAivecFileService {

}
