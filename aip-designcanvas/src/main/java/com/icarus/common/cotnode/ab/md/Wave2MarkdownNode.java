package com.icarus.common.cotnode.ab.md;

import cn.hutool.json.JSONObject;
import com.alibaba.dashscope.audio.asr.translation.TranslationRecognizerParam;
import com.alibaba.dashscope.audio.asr.translation.TranslationRecognizerRealtime;
import com.alibaba.dashscope.audio.asr.translation.results.TranslationRecognizerResult;
import com.alibaba.dashscope.common.ResultCallback;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.icarus.common.cotnode.BaseNode;
import com.icarus.common.runtime.NodeContext;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import java.io.*;
import java.nio.ByteBuffer;
import java.nio.file.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

/**
 * Wave2Markdown 节点
 * 将 wave 音频文件转换为 markdown 文档
 */
@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class Wave2MarkdownNode extends BaseNode {

    @Value("${dashscope.api-key}")
    private String apiKey;

    @Value("${file.upload.base-path}")
    private String uploadBasePath;

    /** 节点执行结果 */
    @Getter
    @Setter
    private String result;

    private ExecutorService executorService = Executors.newFixedThreadPool(5);

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【Wave2Markdown】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);

        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            nodeContext.setInputs(new JSONObject(resolvedInputs));

            // 解析属性配置
            Properties resolvedProperties = resolveProperties(context, Properties.class);
            JSONObject propertiesJson = new JSONObject();
            propertiesJson.set("wavePaths", resolvedProperties.getWavePaths());
            nodeContext.setProperties(propertiesJson);

            // 执行转换操作，处理多个文件
            List<String> markdownPaths = convertWaveFilesToMarkdown(resolvedProperties.getWavePaths());

            // 构建结果JSON
            JSONObject resultJson = new JSONObject();
            resultJson.set("total", markdownPaths.size());
            resultJson.set("paths", markdownPaths);
            result = resultJson.toString();

            // 设置执行状态为完成
            nodeContext.setStatus("completed");

            // 处理输出参数
            processOutputs(context);

        } catch (Exception e) {
            log.error("Wave2Markdown 节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        return context;
    }

    /**
     * 将多个 wave 文件转换为 markdown
     */
    public List<String> convertWaveFilesToMarkdown(List<String> wavePaths) throws Exception {
        List<String> markdownPaths = new ArrayList<>();
        List<Future<String>> futures = new ArrayList<>();

        // 创建输出目录
        String dirName = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        Path outputDir = Paths.get(uploadBasePath, dirName);
        Files.createDirectories(outputDir);

        // 提交所有识别任务
        for (int i = 0; i < wavePaths.size(); i++) {
            final String wavePath = wavePaths.get(i);
            final int index = i;

            Future<String> future = executorService.submit(() -> {
                // 执行语音识别
                String recognizedText = recognizeSpeech(Paths.get(wavePath));

                // 生成并保存 markdown
                String markdown = generateMarkdown(recognizedText, Paths.get(wavePath).getFileName().toString());
                return saveMarkdownFile(markdown, outputDir, index + 1);
            });

            futures.add(future);
        }

        // 等待所有任务完成
        for (Future<String> future : futures) {
            try {
                String markdownPath = future.get(5, TimeUnit.MINUTES);
                markdownPaths.add(markdownPath);
            } catch (Exception e) {
                log.error("处理音频文件失败", e);
            }
        }

        return markdownPaths;
    }

    /**
     * 使用通义千问的 ASR 功能识别语音
     */
    private String recognizeSpeech(Path wavePath) throws Exception {
        final StringBuilder result = new StringBuilder();
        final CountDownLatch finishLatch = new CountDownLatch(1);

        // 创建识别参数
        TranslationRecognizerParam param = TranslationRecognizerParam.builder()
                .apiKey(apiKey)
                .model("gummy-realtime-v1")
                .format("wav")
                .sampleRate(16000)
                .transcriptionEnabled(true)
                .sourceLanguage("auto")
                .translationEnabled(false)
                .build();

        // 创建识别器
        TranslationRecognizerRealtime recognizer = new TranslationRecognizerRealtime();

        // 设置回调
        ResultCallback<TranslationRecognizerResult> callback = new ResultCallback<TranslationRecognizerResult>() {
            @Override
            public void onEvent(TranslationRecognizerResult response) {
                if (response.getTranscriptionResult() != null) {
                    if (response.isSentenceEnd()) {
                        result.append(response.getTranscriptionResult().getText()).append(" ");
                    }
                }
            }

            @Override
            public void onComplete() {
                log.debug("语音识别完成");
                finishLatch.countDown();
            }

            @Override
            public void onError(Exception e) {
                log.error("语音识别失败", e);
                finishLatch.countDown();
            }
        };

        try {
            recognizer.call(param, callback);

            // 分块读取音频文件
            try (FileInputStream fis = new FileInputStream(wavePath.toFile())) {
                byte[] buffer = new byte[3200];
                int bytesRead;

                while ((bytesRead = fis.read(buffer)) != -1) {
                    ByteBuffer audioData;
                    if (bytesRead < buffer.length) {
                        audioData = ByteBuffer.wrap(Arrays.copyOf(buffer, bytesRead));
                    } else {
                        audioData = ByteBuffer.wrap(buffer);
                    }
                    recognizer.sendAudioFrame(audioData);
                    Thread.sleep(100);
                }
            }

            recognizer.stop();

            if (!finishLatch.await(1, TimeUnit.MINUTES)) {
                throw new RuntimeException("语音识别超时");
            }
            
            return result.toString();
            
        } catch (Exception e) {
            throw new RuntimeException("语音识别失败", e);
        }
    }
    
    /**
     * 生成 markdown 文档
     */
    private String generateMarkdown(String text, String originalFileName) {
        StringBuilder markdown = new StringBuilder();
        
        // 添加标题
        markdown.append("# 语音转文字记录\n\n");
        
        // 添加原始文件信息
        markdown.append("## 原始文件\n");
        markdown.append(String.format("`%s`\n\n", originalFileName));
        
        // 添加时间戳
        markdown.append("## 转换时间\n");
        markdown.append(String.format("`%s`\n\n", LocalDateTime.now()));
        
        // 添加识别文本
        markdown.append("## 识别文本\n");
        markdown.append(String.format("```\n%s\n```\n\n", text));
        
        return markdown.toString();
    }
    
    /**
     * 保存 markdown 文件并返回访问路径
     */
    private String saveMarkdownFile(String content, Path dirPath, int index) throws IOException {
        // 生成文件名
        String fileName = String.format("output_%02d.md", index);
        
        // 保存文件
        Path filePath = dirPath.resolve(fileName);
        Files.write(filePath, content.getBytes());
        
        // 构建访问路径
        // 假设基础URL是 /markdown/
        String accessPath = "/markdown/" + dirPath.getFileName().toString() + "/" + fileName;
        
        return accessPath;
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    @Override
    public String desc() {
        return "wave转md节点";
    }

    /**
     * 节点配置属性类
     */
    @Data
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Properties {
        /** wave 文件路径列表 */
        private List<String> wavePaths;
    }
}
