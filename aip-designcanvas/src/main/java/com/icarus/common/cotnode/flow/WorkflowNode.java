package com.icarus.common.cotnode.flow;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.icarus.common.cotnode.BaseNode;
import com.icarus.common.pojo.AIPFlowDefinition;
import com.icarus.common.runtime.AIPFlowRuntime;
import com.icarus.common.runtime.NodeContext;
import com.icarus.dto.RuntimeRequest;
import com.icarus.entity.Canvas;
import com.icarus.sdk.springboot.base.utils.SpringContextUtils;
import com.icarus.service.CanvasService;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工作流节点
 * @autor ksw
 * @date 2025-03-02
 */
@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class WorkflowNode extends BaseNode {

    @Getter
    @Setter
    private Map<String, Object> result;

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【工作流】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);

        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            nodeContext.setInputs(new JSONObject(resolvedInputs));

            // 解析属性配置
            WorkflowNode.Properties resolvedProperties = resolveProperties(context, WorkflowNode.Properties.class);
            JSONObject propertiesJson = new JSONObject();
            propertiesJson.set("id", resolvedProperties.getProcessId());
            nodeContext.setProperties(propertiesJson);

            if (StrUtil.isEmpty(resolvedProperties.getProcessId())) {
                log.warn("Workflow节点[{}]未配置子流程,请检查节点配置.", getId());
            }else {
                /**
                 * 解析并执行子流程
                 * 1. 获取子流程画布
                 * 2. 初始化节点间关系
                 * 3. 执行子流程（构建子流程上下文、入参设置、执行）
                 * 4. 获取子流程执行结果作为工作流节点的输出
                 */
                CanvasService canvasService = SpringContextUtils.getBean(CanvasService.class);
                // 获取子流程画布
                Canvas canvas = canvasService.getById(resolvedProperties.getProcessId());
                if(ObjUtil.isNull(canvas))
                    throw new NullPointerException(String.format("未获取到子流程[%s]", resolvedProperties.getProcessId()));
                // 将子流程画布转为流程定义
                AIPFlowDefinition AIPFlowDefinition = JSONUtil.toBean(canvas.getContent(), AIPFlowDefinition.class);
                // 构建COTRuntime(构建节点间关系)
                AIPFlowRuntime AIPFlowRuntime = new AIPFlowRuntime(AIPFlowDefinition);
                // 执行子流程
                Map<String, Object> subContext = executeSubFlow(AIPFlowRuntime, context);
                // 获取子流程输出 用作当前工作流节点的输出（这种方式仅支持使用${current.*}的方式获取参数值）
                result = getSubFlowOutputs(AIPFlowRuntime, subContext);
            }
            // 设置结果并更新状态
            nodeContext.setStatus("completed");
            // 处理输出参数
            processOutputs(context);
        } catch (Exception e) {
            log.error("Workflow节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }
        log.info("Workflow节点执行结束，节点ID：{}", getId());
        return context;
    }

    /**
     * 获取子流程的输出
     * @param AIPFlowRuntime 执行器
     * @param context 子流程上下文
     * @return Map 子流程输出
     */
    private Map<String, Object> getSubFlowOutputs(AIPFlowRuntime AIPFlowRuntime, Map<String, Object> context){
        // 获取结束节点
        AIPFlowDefinition.Cell endNode = AIPFlowRuntime.getEndNode();
        if(ObjUtil.isNotNull(endNode)){
            String id = endNode.getId();
            NodeContext nodeContext = (NodeContext) context.get(id);
            return nodeContext.getOutputs();
        }else{
            return Collections.emptyMap();
        }
    }

    /**
     * 执行子流程
     * @param AIPFlowRuntime 子流程运行时
     * @param parentContext 父流程上下文
     * @return Map 返回子流程上下文
     */
    private Map<String, Object> executeSubFlow(AIPFlowRuntime AIPFlowRuntime, Map<String, Object> parentContext) {
        RuntimeRequest request = new RuntimeRequest();

        // 获取子流程开始节点定义的"输入参数"（注意：这个方法命名可能误导）
        JSONObject obj = (JSONObject) AIPFlowRuntime.getStartNode().getData().getOutputs();
        JSONArray dataArray = obj.getJSONArray("data");

        if (dataArray == null || dataArray.isEmpty()) {
            return AIPFlowRuntime.executeFlow(request); // 没有输入参数直接执行
        }

        // 将 JSONArray 转换为 List<Parameter>
        List<Parameter> inputs = dataArray.stream()
                .map(item -> JSONUtil.toBean((JSONObject) item, Parameter.class))
                .collect(Collectors.toList());

        // 获取主流程传入的参数列表
        List<Parameter> mainInputs = getInputs();
        if (CollUtil.isEmpty(mainInputs)) {
            return AIPFlowRuntime.executeFlow(request); // 主流程无输入参数
        }

        // 构建子流程需要的参数数据（按顺序映射）
        Map<String, Object> data = new HashMap<>();
        int index = 0;

        for (Parameter subParam : inputs) {
            if (index >= mainInputs.size()) break;

            Parameter mainParam = mainInputs.get(index);
            String paramName = subParam.getName(); // 子流程期望的参数名
            Object value = mainParam.resolveValue(parentContext); // 解析主流程参数值

            if (StrUtil.isNotEmpty(paramName) && value != null) {
                data.put(paramName, value);
            }

            index++;
        }

        // 设置请求头和其他信息
        request.setHeaders((Map<String, String>) obj.get("Headers"));
        request.setCommons(obj.get("commons"));
        request.setData(data);

        return AIPFlowRuntime.executeFlow(request);
    }

    /**
     * 处理节点输出，将结果写入上下文 (覆盖父类方法支持获取子流程的输出{sub.*})
     * @param context 流程上下文
     */
    @Override
    protected void processOutputs(Map<String, Object> context) {
        List<Parameter> outputs = getOutputs();
        if (outputs != null && !outputs.isEmpty()) {
            JSONObject flowData = (JSONObject) context.getOrDefault("flowData", new JSONObject());
            Map<String, Object> processedOutputs = new HashMap<>();
            for (Parameter output : outputs) {
                String sourcePath = output.getValue().toString();
                String targetPath = output.getName();
                Object value;
                // 处理参数引用
                if(sourcePath.startsWith("{") && sourcePath.endsWith("}")) {
                    // 去掉花括号，获取实际路径
                    String propertyPath = sourcePath.substring(1, sourcePath.length() - 1);
                    // 从子流程输出中获取值
                    value = getSubFlowOutputsProperty(propertyPath);
                } else {
                    value = sourcePath; // 返回原值
                }
                
                // 设置输出值到flowData和processedOutputs
                setValueByPath(flowData, targetPath, value);
                processedOutputs.put(targetPath, value);
            }
            // 更新flowData
            context.put("flowData", flowData);
            // 更新NodeContext
            String nodeId = getId();
            NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
            nodeContext.setOutputs(new JSONObject(processedOutputs));
            updateNodeContext(context, nodeId, nodeContext);
        }
    }

    /**
     * 获取子流程输出的指定属性
     * @param propertyName 属性路径，支持嵌套路径如"current.result"
     * @return 属性值
     */
    private Object getSubFlowOutputsProperty(String propertyName) {
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }

        // 处理嵌套路径，如 "current.result"
        if (propertyName.contains(".")) {
            String[] paths = propertyName.split("\\.");

            // 获取第一级属性
            Object currentValue = result;

            // 如果是 Map 类型，继续获取嵌套属性
            if (currentValue instanceof Map) {
                Map<String, Object> currentMap = (Map<String, Object>) currentValue;

                // 如果是 "current.result" 这种形式，直接取 Map 中的唯一值
                if (paths.length == 2 && "result".equals(paths[1])) {
                    if (currentMap.size() == 1) {
                        return currentMap.values().iterator().next();
                    } else {
                        log.warn("Map contains multiple values, cannot determine the result value.");
                        return null;
                    }
                } else {
                    for (int i = 1; i < paths.length; i++) {
                        if (currentMap.containsKey(paths[i])) {
                            Object nextValue = currentMap.get(paths[i]);
                            if (i == paths.length - 1) {
                                // 最后一级属性，直接返回值
                                return nextValue;
                            } else if (nextValue instanceof Map) {
                                // 继续遍历下一级
                                currentMap = (Map<String, Object>) nextValue;
                            } else {
                                // 路径未到最后，但值不是 Map 类型，无法继续遍历
                                return null;
                            }
                        } else {
                            // 路径不存在
                            return null;
                        }
                    }
                }
            } else if ("inputs".equals(paths[1]) && "current".equals(paths[0])) {
                // 特殊处理 current.inputs 的情况，直接返回 result 中的 inputs 值
                return result.get("inputs");
            }
            return null;
        } else {
            // 简单属性直接返回
            return result.get(propertyName);
        }
    }
    
    @Data
    @NoArgsConstructor
    public static class Properties {
        private String processId; // 画布ID(子流程ID)
    }

    @Override
    public String desc() {
        return "工作流节点";
    }

}
