package com.icarus.common.cotnode;

import cn.hutool.json.JSONObject;
import com.icarus.common.runtime.NodeContext;
import com.icarus.sdk.client.api.completion.chat.ChatMessage;
import com.icarus.sdk.client.utils.LLMClientUtil;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * VLM图片分析节点
 * 通过VLM模型读取图片中的内容，支持多张图片同时分析
 * 入参：图片URL数组和prompt提示词
 * 输出：String类型的描述结果
 */
@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class VlmImageAnalysisNode extends BaseNode {

    @Getter
    @Setter
    private String result;

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【VLM图片分析】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
        
        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            nodeContext.setInputs(new JSONObject(resolvedInputs));
            
            // 解析属性配置
            Properties resolvedProperties = resolveProperties(context, Properties.class);
            JSONObject propertiesJson = new JSONObject();
            propertiesJson.set("modelName", resolvedProperties.getModelName());
            propertiesJson.set("prompt", resolvedProperties.getPrompt());
            propertiesJson.set("imageUrls", resolvedProperties.getImageUrls());
            propertiesJson.set("maxTokens", resolvedProperties.getMaxTokens());
            propertiesJson.set("temperature", resolvedProperties.getTemperature());
            propertiesJson.set("topP", resolvedProperties.getTopP());
            nodeContext.setProperties(propertiesJson);
            
            // 验证必要的输入参数
            if (resolvedProperties.getPrompt() == null || resolvedProperties.getPrompt().trim().isEmpty()) {
                throw new IllegalArgumentException("prompt 是必需的参数");
            }
            
            if (resolvedProperties.getImageUrls() == null || resolvedProperties.getImageUrls().isEmpty()) {
                throw new IllegalArgumentException("imageUrls 是必需的参数，至少需要一张图片");
            }
            
            // 调用VLM模型进行图片分析
            result = performVlmAnalysis(resolvedProperties);
            
            if (result == null || result.trim().isEmpty()) {
                throw new IllegalArgumentException("VLM模型返回结果为空");
            }
            
            // 设置成功状态和输出
            nodeContext.setStatus("completed");
            
            // 处理输出映射
            processOutputs(context);
            
        } catch (Exception e) {
            log.error("VLM图片分析失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }
        
        log.info("VLM图片分析节点执行结束，节点ID：{}", getId());
        return context;
    }

    /**
     * 执行VLM图片分析
     * 
     * @param properties 配置属性
     * @return 分析结果
     */
    private String performVlmAnalysis(Properties properties) {
        try {
            log.info("开始调用VLM模型进行图片分析，模型：{}，图片数量：{}", 
                    properties.getModelName(), properties.getImageUrls().size());
            
            // 创建ChatMessage并添加提示词
            ChatMessage msg = new ChatMessage(properties.getPrompt());
            
            // 为每张图片添加URL
            for (String imageUrl : properties.getImageUrls()) {
                if (imageUrl != null && !imageUrl.trim().isEmpty()) {
                    msg.attachImageUrl(imageUrl.trim());
                    log.info("已添加图片URL: {}", imageUrl);
                }
            }
            
            // 调用LLMClientUtil进行分析
            String answer = LLMClientUtil.builder()
                    .modelName(properties.getModelName())
                    .maxTokens(properties.getMaxTokens())
                    .temperature(properties.getTemperature())
                    .topP(properties.getTopP())
                    .chat(msg)
                    .runChatCompletion();
            
            log.info("VLM模型分析完成，结果长度：{}", answer != null ? answer.length() : 0);
            return answer;
            
        } catch (Exception e) {
            log.error("调用VLM模型失败", e);
            throw new RuntimeException("VLM模型调用失败: " + e.getMessage(), e);
        }
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    @Override
    public String desc() {
        return "VLM图片分析节点";
    }

    /**
     * 节点配置属性类
     */
    @Data
    @NoArgsConstructor
    public static class Properties {
        /**
         * VLM模型名称，默认为VL_7B
         */
        private String modelName = "VL_7B";
        
        /**
         * 提示词，用于指导模型如何分析图片
         */
        private String prompt;
        
        /**
         * 图片URL数组
         */
        private List<String> imageUrls;
        
        /**
         * 最大token数，默认1024
         */
        private Integer maxTokens = 1024;
        
        /**
         * 温度参数，控制输出的随机性，默认0.5
         */
        private Double temperature = 0.5;
        
        /**
         * TopP参数，控制输出的多样性，默认0.95
         */
        private Double topP = 0.95;
    }
}