package com.icarus.common.cotnode;

import cn.hutool.json.JSONObject;
import com.icarus.common.runtime.NodeContext;
import com.jcraft.jsch.ChannelExec;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.api.CloneCommand;
import org.eclipse.jgit.api.PullCommand;
import org.eclipse.jgit.api.PushCommand;
import org.eclipse.jgit.api.AddCommand;
import org.eclipse.jgit.api.CommitCommand;
import org.eclipse.jgit.api.CheckoutCommand;
import org.eclipse.jgit.api.CreateBranchCommand;
import org.eclipse.jgit.api.FetchCommand;
import org.eclipse.jgit.api.Status;
import org.eclipse.jgit.api.StatusCommand;
import org.eclipse.jgit.api.errors.GitAPIException;
import org.eclipse.jgit.transport.UsernamePasswordCredentialsProvider;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.Map;
import java.util.Set;

@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class GitOperateNode extends BaseNode {

    @Getter
    @Setter
    private String result;

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【Git操作】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);

        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            if (resolvedInputs == null) {
                throw new IllegalArgumentException("输入参数不能为空");
            }

            // 解析属性配置
            GitOperateNode.Properties resolvedProperties = resolveProperties(context, GitOperateNode.Properties.class);
            JSONObject propertiesJson = new JSONObject();
            propertiesJson.set("gitUrl", resolvedProperties.getGitUrl());
            propertiesJson.set("localPath", resolvedProperties.getLocalPath());
            propertiesJson.set("branchName", resolvedProperties.getBranchName());
            propertiesJson.set("operation", resolvedProperties.getOperation());
            propertiesJson.set("commitMessage", resolvedProperties.getCommitMessage());
            propertiesJson.set("username", resolvedProperties.getUsername());
            propertiesJson.set("password", resolvedProperties.getPassword());
            nodeContext.setProperties(propertiesJson);

            // 验证必要的输入参数
            String operation = resolvedProperties.getOperation();
            String localPath = resolvedProperties.getLocalPath();

            if (operation == null || operation.trim().isEmpty()) {
                throw new IllegalArgumentException("operation 是必需的输入参数");
            }
            if (localPath == null || localPath.trim().isEmpty()) {
                throw new IllegalArgumentException("localPath 是必需的输入参数");
            }

            // 记录输入参数到上下文
            nodeContext.setInputs(new JSONObject(resolvedInputs));

            // 根据操作类型执行相应的git命令
            result = executeGitOperation(resolvedProperties);

            // 设置成功状态和输出
            nodeContext.setStatus("completed");

            // 处理输出映射
            processOutputs(context);

        } catch (Exception e) {
            log.error("Git操作失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        log.info("Git操作节点执行结束，节点ID：{}", getId());
        return context;
    }

    public String executeGitOperation(Properties properties) {
        String operation = properties.getOperation().toLowerCase().trim();

        switch (operation) {
            case "clone":
                return cloneRepository(properties);
            case "cloneOnRemote":
                return cloneOnRemoteServer(properties);
            case "fetch":
                return fetchRepository(properties);
            case "pull":
                return pullRepository(properties);
            case "commit":
                return commitChanges(properties);
            case "push":
                return pushChanges(properties);
            case "status":
                return getStatus(properties);
            case "add":
                return addFiles(properties);
            case "checkout":
                return checkoutBranch(properties);
            case "createBranch":
                return createBranch(properties);
            default:
                throw new IllegalArgumentException("不支持的Git操作: " + operation);
        }
    }

    private String cloneRepository(Properties properties) {
        try {
            String gitUrl = properties.getGitUrl();
            String localPath = properties.getLocalPath();
            String branchName = properties.getBranchName();

            if (gitUrl == null || gitUrl.trim().isEmpty()) {
                throw new IllegalArgumentException("clone操作需要gitUrl参数");
            }

            // 检查本地路径是否存在，如果存在则先删除
            File localDir = new File(localPath);
            if (localDir.exists()) {
                deleteDirectory(localDir);
            }else{
                if (!localDir.mkdirs()) { // 创建完整路径
                    throw new IOException("无法创建目录: " + localDir.getAbsolutePath());
                }
            }


            CloneCommand cloneCommand = Git.cloneRepository()
                    .setURI(gitUrl)
                    .setDirectory(localDir);

            // 设置分支
            if (branchName != null && !branchName.trim().isEmpty()) {
                cloneCommand.setBranch(branchName);
            }

            // 设置认证
            setCredentials(cloneCommand, properties);

            try (Git git = cloneCommand.call()) {
                String successMsg = "Git仓库克隆成功，本地路径: " + localPath;
                log.info(successMsg);
                return successMsg;
            }

        } catch (Exception e) {
            log.error("克隆仓库失败: ", e);
            throw new RuntimeException("克隆仓库失败: " + e.getMessage(), e);
        }
    }

    private String fetchRepository(Properties properties) {
        try {
            String localPath = properties.getLocalPath();
            File localDir = new File(localPath);

            if (!localDir.exists() || !new File(localDir, ".git").exists()) {
                throw new IllegalArgumentException("指定路径不是有效的Git仓库: " + localPath);
            }

            try (Git git = Git.open(localDir)) {
                FetchCommand fetchCommand = git.fetch();
                setCredentials(fetchCommand, properties);

                fetchCommand.call();

                String successMsg = "Git fetch执行成功";
                log.info(successMsg);
                return successMsg;
            }

        } catch (GitAPIException | IOException e) {
            throw new RuntimeException("fetch失败: " + e.getMessage(), e);
        }
    }

    private String cloneOnRemoteServer(Properties properties) {
        String remoteHost = properties.getRemoteHost(); // 格式：host:port（例如 "************:22"）
        String gitUrl = properties.getGitUrl();
        String localPath = properties.getLocalPath();
        String branchName = properties.getBranchName();
        String username = properties.getUsername();
        String password = properties.getPassword();

        if (remoteHost == null || gitUrl == null || localPath == null) {
            throw new IllegalArgumentException("remoteHost/gitUrl/localPath 参数缺失");
        }

        Session session = null;
        try {
            // 解析主机和端口
            String host = remoteHost.split(":")[0];
            int port = remoteHost.contains(":") ? Integer.parseInt(remoteHost.split(":")[1]) : 22;

            // 创建 SSH 会话
            JSch jSch = new JSch();
            session = jSch.getSession(username, host, port);
            session.setPassword(password);

            // 忽略 StrictHostKeyChecking（生产环境应配置 KnownHosts）
            session.setConfig("StrictHostKeyChecking", "no");
            session.connect(30000); // 连接超时时间

            // 构建 Git 命令
            StringBuilder gitCommand = new StringBuilder();
            gitCommand.append("mkdir -p ").append(localPath).append(" && cd ").append(localPath).append(" && git clone ");
            if (branchName != null && !branchName.isEmpty()) {
                gitCommand.append("--branch ").append(branchName).append(" ");
            }
            gitCommand.append(gitUrl).append(" .");

            // 执行远程命令
            ChannelExec channel = (ChannelExec) session.openChannel("exec");
            channel.setCommand(gitCommand.toString());

            // 捕获输出流和错误流
            ByteArrayOutputStream successStream = new ByteArrayOutputStream();
            ByteArrayOutputStream errorStream = new ByteArrayOutputStream();
            channel.setOutputStream(successStream);
            channel.setErrStream(errorStream);

            channel.connect(30000);

            // 等待命令执行完成
            while (!channel.isClosed()) {
                Thread.sleep(1000);
            }

            String successOutput = successStream.toString();
            String errorOutput = errorStream.toString();
            int exitCode = channel.getExitStatus();

            if (exitCode != 0) {
                String errorMsg = "远程克隆失败，错误信息: " + errorOutput;
                log.error(errorMsg);
                throw new RuntimeException(errorMsg);
            }

            String successMsg = "远程 Git 克隆成功，路径: " + localPath + "\n输出: " + successOutput;
            log.info(successMsg);
            return successMsg;

        } catch (JSchException | InterruptedException e) {
            throw new RuntimeException("SSH 执行失败: " + e.getMessage(), e);
        } finally {
            if (session != null && session.isConnected()) {
                session.disconnect();
            }
        }
    }

    private String pullRepository(Properties properties) {
        try {
            String localPath = properties.getLocalPath();
            File localDir = new File(localPath);

            if (!localDir.exists() || !new File(localDir, ".git").exists()) {
                throw new IllegalArgumentException("指定路径不是有效的Git仓库: " + localPath);
            }

            try (Git git = Git.open(localDir)) {
                PullCommand pullCommand = git.pull();
                setCredentials(pullCommand, properties);

                pullCommand.call();

                String successMsg = "Git pull执行成功";
                log.info(successMsg);
                return successMsg;
            }

        } catch (GitAPIException | IOException e) {
            throw new RuntimeException("拉取代码失败: " + e.getMessage(), e);
        }
    }

    private String commitChanges(Properties properties) {
        try {
            String localPath = properties.getLocalPath();
            String commitMessage = properties.getCommitMessage();
            File localDir = new File(localPath);

            if (!localDir.exists() || !new File(localDir, ".git").exists()) {
                throw new IllegalArgumentException("指定路径不是有效的Git仓库: " + localPath);
            }

            if (commitMessage == null || commitMessage.trim().isEmpty()) {
                throw new IllegalArgumentException("commit操作需要commitMessage参数");
            }

            try (Git git = Git.open(localDir)) {
                CommitCommand commitCommand = git.commit()
                        .setMessage(commitMessage);

                // 设置作者信息（可选）
                String username = properties.getUsername();
                if (username != null && !username.trim().isEmpty()) {
                    commitCommand.setAuthor(username, username + "@example.com");
                }

                commitCommand.call();

                String successMsg = "Git commit执行成功: " + commitMessage;
                log.info(successMsg);
                return successMsg;
            }

        } catch (GitAPIException | IOException e) {
            throw new RuntimeException("提交更改失败: " + e.getMessage(), e);
        }
    }

    private String pushChanges(Properties properties) {
        try {
            String localPath = properties.getLocalPath();
            File localDir = new File(localPath);

            if (!localDir.exists() || !new File(localDir, ".git").exists()) {
                throw new IllegalArgumentException("指定路径不是有效的Git仓库: " + localPath);
            }

            try (Git git = Git.open(localDir)) {
                PushCommand pushCommand = git.push();
                setCredentials(pushCommand, properties);

                pushCommand.call();

                String successMsg = "Git push执行成功";
                log.info(successMsg);
                return successMsg;
            }

        } catch (GitAPIException | IOException e) {
            throw new RuntimeException("推送更改失败: " + e.getMessage(), e);
        }
    }

    private String getStatus(Properties properties) {
        try {
            String localPath = properties.getLocalPath();
            File localDir = new File(localPath);

            if (!localDir.exists() || !new File(localDir, ".git").exists()) {
                throw new IllegalArgumentException("指定路径不是有效的Git仓库: " + localPath);
            }

            try (Git git = Git.open(localDir)) {
                StatusCommand statusCommand = git.status();
                Status status = statusCommand.call();

                StringBuilder statusInfo = new StringBuilder();
                statusInfo.append("Git状态信息:\n");

                Set<String> added = status.getAdded();
                if (!added.isEmpty()) {
                    statusInfo.append("已添加的文件: ").append(added).append("\n");
                }

                Set<String> changed = status.getChanged();
                if (!changed.isEmpty()) {
                    statusInfo.append("已修改的文件: ").append(changed).append("\n");
                }

                Set<String> removed = status.getRemoved();
                if (!removed.isEmpty()) {
                    statusInfo.append("已删除的文件: ").append(removed).append("\n");
                }

                Set<String> untracked = status.getUntracked();
                if (!untracked.isEmpty()) {
                    statusInfo.append("未跟踪的文件: ").append(untracked).append("\n");
                }

                if (status.isClean()) {
                    statusInfo.append("工作目录是干净的\n");
                }

                String result = statusInfo.toString();
                log.info(result);
                return result;
            }

        } catch (GitAPIException | IOException e) {
            throw new RuntimeException("获取状态失败: " + e.getMessage(), e);
        }
    }

    private String addFiles(Properties properties) {
        try {
            String localPath = properties.getLocalPath();
            File localDir = new File(localPath);

            if (!localDir.exists() || !new File(localDir, ".git").exists()) {
                throw new IllegalArgumentException("指定路径不是有效的Git仓库: " + localPath);
            }

            try (Git git = Git.open(localDir)) {
                AddCommand addCommand = git.add()
                        .addFilepattern(".");

                addCommand.call();

                String successMsg = "Git add执行成功";
                log.info(successMsg);
                return successMsg;
            }

        } catch (GitAPIException | IOException e) {
            throw new RuntimeException("添加文件失败: " + e.getMessage(), e);
        }
    }

    private String checkoutBranch(Properties properties) {
        try {
            String localPath = properties.getLocalPath();
            String branchName = properties.getBranchName();
            File localDir = new File(localPath);

            if (!localDir.exists() || !new File(localDir, ".git").exists()) {
                throw new IllegalArgumentException("指定路径不是有效的Git仓库: " + localPath);
            }

            if (branchName == null || branchName.trim().isEmpty()) {
                throw new IllegalArgumentException("checkout操作需要branchName参数");
            }

            try (Git git = Git.open(localDir)) {
                CheckoutCommand checkoutCommand = git.checkout()
                        .setName(branchName);

                checkoutCommand.call();

                String successMsg = "Git checkout执行成功，切换到分支: " + branchName;
                log.info(successMsg);
                return successMsg;
            }

        } catch (GitAPIException | IOException e) {
            throw new RuntimeException("切换分支失败: " + e.getMessage(), e);
        }
    }

    private String createBranch(Properties properties) {
        try {
            String localPath = properties.getLocalPath();
            String branchName = properties.getBranchName();
            File localDir = new File(localPath);

            if (!localDir.exists() || !new File(localDir, ".git").exists()) {
                throw new IllegalArgumentException("指定路径不是有效的Git仓库: " + localPath);
            }

            if (branchName == null || branchName.trim().isEmpty()) {
                throw new IllegalArgumentException("branch操作需要branchName参数");
            }

            try (Git git = Git.open(localDir)) {
                CreateBranchCommand createBranchCommand = git.branchCreate()
                        .setName(branchName);

                createBranchCommand.call();

                // 创建后自动切换到新分支
                git.checkout().setName(branchName).call();

                String successMsg = "Git分支创建成功并切换到: " + branchName;
                log.info(successMsg);
                return successMsg;
            }

        } catch (GitAPIException | IOException e) {
            throw new RuntimeException("创建分支失败: " + e.getMessage(), e);
        }
    }

    private void setCredentials(Object command, Properties properties) {
        String username = properties.getUsername();
        String password = properties.getPassword();

        if (username != null && !username.trim().isEmpty() &&
                password != null && !password.trim().isEmpty()) {
            // 使用用户名密码认证（HTTP/HTTPS）
            UsernamePasswordCredentialsProvider credentialsProvider =
                    new UsernamePasswordCredentialsProvider(username, password);

            if (command instanceof CloneCommand) {
                ((CloneCommand) command).setCredentialsProvider(credentialsProvider);
            } else if (command instanceof PullCommand) {
                ((PullCommand) command).setCredentialsProvider(credentialsProvider);
            } else if (command instanceof PushCommand) {
                ((PushCommand) command).setCredentialsProvider(credentialsProvider);
            } else if (command instanceof FetchCommand) {
                ((FetchCommand) command).setCredentialsProvider(credentialsProvider);
            }
        }
    }

    /**
     * 递归删除目录
     */
    private void deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
            directory.delete();
        }
    }

    @Data
    @NoArgsConstructor
    public static class Properties {
        private String gitUrl;          // git项目地址
        private String branchName;      // 分支名称
        private String localPath;       // git本地路径
        private String operation;       // 操作方式：pull、clone、commit、push、status、add、checkout、branch、fetch
        private String commitMessage;   // 提交信息
        private String username;        // 用户名（用于HTTPS认证）
        private String password;        // 密码（用于HTTPS认证）
        private String remoteHost;      //远程host
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    @Override
    public String desc() {
        return "Git操作节点";
    }
}