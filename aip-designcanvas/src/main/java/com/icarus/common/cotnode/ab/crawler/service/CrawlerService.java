package com.icarus.common.cotnode.ab.crawler.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.icarus.common.cotnode.ab.crawler.dto.BrowserResult;
import com.icarus.common.cotnode.ab.crawler.dto.CrawlerResult;
import com.icarus.entity.CrawlerSettings;
import com.icarus.entity.CrawlerTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URL;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import com.microsoft.playwright.Page;

/**
 * 爬虫服务类
 * 负责具体的爬虫逻辑实现
 */
@Slf4j
@Service
public class CrawlerService {

    @Autowired
    private PlaywrightBrowserService browserService;


    /**
     * 爬取网站
     */
    public CrawlerResult crawlWebsite(CrawlerSettings settings, CrawlerTask task) {
        String url = StrUtil.isNotEmpty(task.getLastProcessedUrl()) ?
                task.getLastProcessedUrl() : settings.getCrawlUrl();

        if (StrUtil.isEmpty(url)) {
            return CrawlerResult.error("爬取URL不能为空");
        }

        int maxPages = settings.getMaxPages() != null ? settings.getMaxPages() : 10;
        int pageCount = task.getCurrentPage() != null ? task.getCurrentPage() : 0;
        StringBuilder allTextContent = new StringBuilder();
        List<Map<String, Object>> allAttachmentInfos = new ArrayList<>();
        Set<String> processedUrls = new HashSet<>();

        try {
            // 准备请求头
            Map<String, String> headers = null;
            if (StrUtil.isNotEmpty(settings.getHeaders())) {
                headers = JSONUtil.toBean(settings.getHeaders(), Map.class);
            }

            // 爬取列表页
            while (StrUtil.isNotEmpty(url) && pageCount < maxPages) {
                log.info("爬取列表页：{}", url);

                // 导航到页面
                BrowserResult navResult = browserService.navigate(url, headers, settings.getTimeout());
                if (!navResult.isSuccess()) {
                    return CrawlerResult.error("无法访问页面: " + navResult.getMessage());
                }

                String pageId = navResult.getString("pageId");
                
                // 处理iframe (如果配置了)
                if (StrUtil.isNotEmpty(settings.getIframe())) {
                    BrowserResult iframeResult = browserService.switchToIframe(pageId, settings.getIframe());
                    if (iframeResult.isSuccess()) {
                        log.info("已切换到iframe: {}", settings.getIframe());
                        // 更新pageId为iframe引用
                        pageId = iframeResult.getString("frameId");
                    } else {
                        log.info("切换iframe失败: {}", iframeResult.getMessage());
                    }
                }
                
                // 执行页面滚动，处理懒加载内容
                handleLazyLoading(pageId);

                // 提取列表页文本 - 避免使用详情页选择器，防止内容重复
                String pageText = extractListPageText(pageId, settings);
                if (StrUtil.isNotEmpty(pageText)) {
                    allTextContent.append("=== 列表页内容 ===\n");
                    allTextContent.append(pageText).append("\n\n");
                    log.info("提取到列表页文本内容，长度: {} 字符", pageText.length());
                } else {
                    log.info("列表页未提取到文本内容");
                }

                // 获取列表项
                BrowserResult listResult = browserService.findElements(pageId, settings.getClickListPath());
                if (!listResult.isSuccess() || ((List<?>)listResult.get("elements")).isEmpty()) {
                    log.info("未找到列表项，选择器: {}", settings.getClickListPath());
                    // 尝试自动检测列表项
                    listResult = detectListItems(pageId);
                    if (!listResult.isSuccess()) {
                        log.info("无法自动检测列表项，尝试直接查找所有链接");
                        // 最后尝试查找所有a标签
                        listResult = browserService.findElements(pageId, "a[href]");
                        if (listResult.isSuccess()) {
                            log.info("找到所有链接作为列表项");
                        }
                    }
                }

                @SuppressWarnings("unchecked")
                List<Map<String, String>> listItems = (List<Map<String, String>>) listResult.get("elements");
                log.info("获取到{}个列表项", listItems.size());

                // 处理详情页
                for (int i = 0; i < listItems.size(); i++) {
                    Map<String, String> item = listItems.get(i);
                    String detailUrl = item.get("href");
                    String itemText = item.get("text");

                    log.info("处理列表项 {}/{}: 文本={}, 链接={}", i+1, listItems.size(), itemText, detailUrl);

                    // 如果列表项本身没有href，尝试查找其中的a标签
                    if (StrUtil.isEmpty(detailUrl)) {
                        // 尝试在列表项中查找a标签
                        BrowserResult linkResult = findLinkInListItem(pageId, settings.getClickListPath(), i);
                        if (linkResult.isSuccess()) {
                            detailUrl = linkResult.getString("href");
                            log.info("在列表项中找到链接: {}", detailUrl);
                        }
                    }

                    if (StrUtil.isNotEmpty(detailUrl)) {
                        // 转换为绝对URL
                        detailUrl = resolveUrl(detailUrl, url);

                        // 跳过已处理的URL
                        if (processedUrls.contains(detailUrl)) {
                            log.info("跳过已处理的URL: {}", detailUrl);
                            continue;
                        }

                        // 处理详情页
                        CrawlerResult detailResult = null;

                        // 根据配置选择处理方式
                        if (shouldOpenDetailInSamePage(settings)) {
                            // 在当前页面打开详情 - 使用智能点击逻辑
                            log.info("在当前页面打开详情页: {}", detailUrl);
                            BrowserResult clickResult = smartClickListItem(pageId, settings, i, detailUrl);
                            if (clickResult.isSuccess()) {
                                // 等待页面内容加载完成
                                waitForDetailPageLoad(pageId);
                                detailResult = processDetailPageInSamePage(pageId, settings, headers);
                                // 返回列表页面
                                browserService.navigate(url, headers, settings.getTimeout());
                                // 等待列表页重新加载
                                waitForListPageLoad(pageId);
                            } else {
                                log.info("智能点击列表项失败: {}", clickResult.getMessage());
                                // 如果点击失败，尝试直接导航到详情页
                                if (StrUtil.isNotEmpty(detailUrl)) {
                                    log.info("尝试直接导航到详情页: {}", detailUrl);
                                    detailResult = processDetailPageInNewTab(pageId, detailUrl, settings, headers);
                                }
                            }
                        } else {
                            // 在新页面处理详情
                            log.info("在新页面处理详情页: {}", detailUrl);
                            detailResult = processDetailPage(detailUrl, settings, headers);
                        }
                        
                        if (detailResult != null && detailResult.isSuccess()) {
                            // 记录已处理的URL
                            processedUrls.add(detailUrl);

                            // 合并结果
                            String detailText = detailResult.getString("textContent");
                            String title = detailResult.getString("title");
                            String publishTime = detailResult.getString("publishTime");

                            if (StrUtil.isNotEmpty(detailText)) {
                                allTextContent.append("=== 详情页: ").append(title).append(" ===\n");
                                if (StrUtil.isNotEmpty(publishTime)) {
                                    allTextContent.append("发布时间: ").append(publishTime).append("\n");
                                }
                                allTextContent.append(detailText).append("\n\n");
                                log.info("提取到详情页文本内容，标题: {}, 长度: {} 字符", title, detailText.length());
                            } else {
                                log.info("详情页未提取到文本内容: {}", detailUrl);
                            }

                            @SuppressWarnings("unchecked")
                            List<Map<String, Object>> attachments = (List<Map<String, Object>>) detailResult.get("attachments");
                            if (attachments != null && !attachments.isEmpty()) {
                                allAttachmentInfos.addAll(attachments);
                                log.info("详情页提取到 {} 个附件", attachments.size());
                            }
                        } else {
                            log.info("详情页处理失败: {}, 错误: {}", detailUrl,
                                    detailResult != null ? detailResult.getMessage() : "未知错误");
                        }
                    }
                }

                // 查找下一页
                String nextPageUrl = findNextPageUrl(pageId, settings.getNextPagePath(), url);
                
                // 检查是否已循环爬取相同页面
                if (url.equals(nextPageUrl)) {
                    log.info("检测到循环爬取相同页面，终止爬取");
                    break;
                }
                
                url = nextPageUrl;
                pageCount++;
                
                // 更新任务进度
                task.setLastProcessedUrl(url);
                task.setCurrentPage(pageCount);
                
                if (url == null) {
                    log.info("没有下一页，爬取完成");
                    break;
                }
            }

            log.info("列表页爬取完成，共爬取{}页", pageCount);

            String finalTextContent = allTextContent.toString().trim();
            boolean hasData = StrUtil.isNotEmpty(finalTextContent) || !allAttachmentInfos.isEmpty();

            log.info("爬取结果统计: 文本长度={} 字符, 附件数量={}, 有数据={}",
                    finalTextContent.length(), allAttachmentInfos.size(), hasData);

            if (!hasData) {
                log.info("警告：未爬取到任何内容！请检查选择器配置");
                log.info("配置信息 - 列表选择器: {}, 内容选择器: {}, 下一页选择器: {}",
                        settings.getClickListPath(), settings.getGetContent(), settings.getNextPagePath());
            }

            return CrawlerResult.success()
                    .put("textContent", finalTextContent)
                    .put("attachments", allAttachmentInfos)
                    .put("pageCount", pageCount)
                    .put("fileCount", allAttachmentInfos.size())
                    .put("failedCount", 0) // 这里只是爬取，实际下载在CrawlerNode中处理
                    .put("hasData", hasData);

        } catch (Exception e) {
            log.error("爬取网站出错: {}", url, e);
            return CrawlerResult.error("爬取网站出错: " + e.getMessage());
        }
    }
    
    /**
     * 处理懒加载内容
     * 通过多次滚动页面来触发懒加载
     */
    private void handleLazyLoading(String pageId) {
        log.info("处理页面懒加载内容...");
        
        // 获取初始内容高度
        int initialHeight = getPageHeight(pageId);
        int lastHeight = initialHeight;
        int scrollCount = 0;
        int maxScrolls = 10; // 最大滚动次数
        
        // 滚动直到内容不再增加或达到最大滚动次数
        while (scrollCount < maxScrolls) {
            // 向下滚动一屏
            browserService.scrollPage(pageId, "down", null);
            
            // 等待内容加载
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
            
            // 获取滚动后的内容高度
            int currentHeight = getPageHeight(pageId);
            
            log.info("滚动次数: {}, 初始高度: {}, 当前高度: {}", scrollCount + 1, lastHeight, currentHeight);
            
            // 如果高度没有增加，可能已经到底部
            if (currentHeight <= lastHeight) {
                if (scrollCount > 0) { // 至少滚动一次
                    break;
                }
            }
            
            lastHeight = currentHeight;
            scrollCount++;
        }
        
        log.info("懒加载处理完成，共滚动{}次，内容高度从{}增加到{}", scrollCount, initialHeight, lastHeight);
        
        // 滚动回页面顶部，方便后续操作
        browserService.scrollPage(pageId, "up", lastHeight);
    }

    /**
     * 尝试点击展开内容的按钮
     * 兼容现有逻辑，不影响正常流程
     */
    private void tryExpandContent(String pageId) {
        try {
            log.info("尝试查找并点击展开内容按钮");

            // 常见的展开按钮选择器
            String[] expandSelectors = {
                ".show-more", ".expand", ".read-more", ".view-more",
                "[class*='show-more']", "[class*='expand']", "[class*='read-more']",
                "button:contains('显示更多')", "button:contains('展开全文')",
                "button:contains('查看更多')", "a:contains('更多')"
            };

            for (String selector : expandSelectors) {
                BrowserResult result = browserService.clickElement(pageId, selector, null);
                if (result.isSuccess()) {
                    log.info("成功点击展开按钮: {}", selector);
                    // 等待内容加载
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                    break; // 只点击第一个找到的按钮
                }
            }
        } catch (Exception e) {
            log.info("尝试展开内容时出现异常（不影响主流程）: {}", e.getMessage());
        }
    }

    /**
     * 提取结构化内容（表格、列表等）
     * 兼容现有逻辑，增强内容提取能力
     */
    private String extractStructuredContent(String pageId, String contentSelector) {
        try {
            StringBuilder structuredContent = new StringBuilder();

            // 如果有指定的内容选择器，在其范围内查找结构化内容
            String baseSelector = StrUtil.isNotEmpty(contentSelector) ? contentSelector + " " : "";

            // 提取表格内容
            BrowserResult tableResult = browserService.extractHtml(pageId, baseSelector + "table");
            if (tableResult.isSuccess()) {
                String tableHtml = tableResult.getString("html");
                if (StrUtil.isNotEmpty(tableHtml)) {
                    structuredContent.append("【表格数据】\n").append(tableHtml).append("\n\n");
                }
            }

            // 提取列表内容
            BrowserResult listResult = browserService.extractHtml(pageId, baseSelector + "ul, ol");
            if (listResult.isSuccess()) {
                String listHtml = listResult.getString("html");
                if (StrUtil.isNotEmpty(listHtml)) {
                    structuredContent.append("【列表数据】\n").append(listHtml).append("\n\n");
                }
            }

            // 提取代码块内容
            BrowserResult codeResult = browserService.extractHtml(pageId, baseSelector + "pre, code");
            if (codeResult.isSuccess()) {
                String codeHtml = codeResult.getString("html");
                if (StrUtil.isNotEmpty(codeHtml)) {
                    structuredContent.append("【代码内容】\n").append(codeHtml).append("\n\n");
                }
            }

            return structuredContent.toString().trim();

        } catch (Exception e) {
            log.info("提取结构化内容时出现异常（不影响主流程）: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 获取页面高度
     */
    private int getPageHeight(String pageId) {
        try {
            Page page = browserService.getPage(pageId);
            if (page == null) {
                return 0;
            }
            Object result = page.evaluate("() => document.body.scrollHeight");
            return Integer.parseInt(result.toString());
        } catch (Exception e) {
            log.info("获取页面高度失败: {}", e.getMessage());
            return 0;
        }
    }
    
    /**
     * 判断是否应该在同一页面打开详情
     * 优化：增加智能判断逻辑
     */
    private boolean shouldOpenDetailInSamePage(CrawlerSettings settings) {
        // 1. 如果明确配置了点击路径，使用配置
        if (StrUtil.isNotEmpty(settings.getClickListPath()) && StrUtil.isEmpty(settings.getIframe())) {
            return true;
        }

        // 2. 如果没有配置点击路径，但有iframe配置，不在同页面打开
        if (StrUtil.isNotEmpty(settings.getIframe())) {
            return false;
        }

        // 3. 默认行为：尝试智能检测是否应该在同页面打开
        // 如果页面有明显的列表结构，默认尝试在同页面打开
        return true; // 默认尝试在同页面打开详情
    }
    
    /**
     * 自动检测列表项
     */
    private BrowserResult detectListItems(String pageId) {
        log.info("尝试自动检测列表项...");

        // 常见列表选择器，按优先级排序
        String[] commonSelectors = {
            // 新闻类网站常用
            ".news-item a", ".article-item a", ".list-item a",
            ".item a", "li a", "tr a",
            // 通用列表
            "article a", ".content-item a", ".result-item a",
            // 表格行
            "tbody tr", "table tr",
            // 通用容器
            ".item", ".list-item", "li", "article"
        };

        for (String selector : commonSelectors) {
            BrowserResult result = browserService.findElements(pageId, selector);
            if (result.isSuccess()) {
                @SuppressWarnings("unchecked")
                List<Map<String, String>> elements = (List<Map<String, String>>) result.get("elements");

                // 过滤掉没有href的元素
                List<Map<String, String>> validElements = new ArrayList<>();
                for (Map<String, String> element : elements) {
                    String href = element.get("href");
                    String text = element.get("text");
                    if (StrUtil.isNotEmpty(href) && StrUtil.isNotEmpty(text) && text.trim().length() > 2) {
                        validElements.add(element);
                    }
                }

                if (validElements.size() >= 2) { // 至少要有2个有效项才认为是列表
                    log.info("检测到可能的列表项: {}, 有效数量: {}/{}", selector, validElements.size(), elements.size());

                    // 创建新的结果对象
                    BrowserResult validResult = BrowserResult.success()
                            .put("elements", validElements)
                            .put("count", validElements.size());
                    return validResult;
                }
            }
        }

        // 尝试智能分析页面结构
        try {
            Page page = browserService.getPage(pageId);
            if (page != null) {
                // 使用JavaScript分析页面中的链接分布
                Object analysisResult = page.evaluate(
                    "() => {\n" +
                    "  const links = Array.from(document.querySelectorAll('a[href]'));\n" +
                    "  const linkGroups = new Map();\n" +
                    "  \n" +
                    "  // 按父元素分组\n" +
                    "  links.forEach(link => {\n" +
                    "    const parent = link.parentElement;\n" +
                    "    if (parent) {\n" +
                    "      const parentKey = parent.tagName + (parent.className ? '.' + parent.className.split(' ').join('.') : '');\n" +
                    "      if (!linkGroups.has(parentKey)) {\n" +
                    "        linkGroups.set(parentKey, []);\n" +
                    "      }\n" +
                    "      linkGroups.get(parentKey).push({\n" +
                    "        href: link.href,\n" +
                    "        text: link.textContent.trim(),\n" +
                    "        parent: parentKey\n" +
                    "      });\n" +
                    "    }\n" +
                    "  });\n" +
                    "  \n" +
                    "  // 找到最大的链接组\n" +
                    "  let maxGroup = [];\n" +
                    "  let maxGroupKey = '';\n" +
                    "  \n" +
                    "  for (const [key, group] of linkGroups) {\n" +
                    "    if (group.length > maxGroup.length && group.length >= 3) {\n" +
                    "      maxGroup = group;\n" +
                    "      maxGroupKey = key;\n" +
                    "    }\n" +
                    "  }\n" +
                    "  \n" +
                    "  return {\n" +
                    "    groupKey: maxGroupKey,\n" +
                    "    links: maxGroup\n" +
                    "  };\n" +
                    "}"
                );

                if (analysisResult != null) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> analysis = (Map<String, Object>) analysisResult;
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> smartLinks = (List<Map<String, Object>>) analysis.get("links");

                    if (smartLinks != null && smartLinks.size() >= 2) {
                        // 转换格式
                        List<Map<String, String>> convertedLinks = new ArrayList<>();
                        for (Map<String, Object> link : smartLinks) {
                            Map<String, String> converted = new HashMap<>();
                            converted.put("href", (String) link.get("href"));
                            converted.put("text", (String) link.get("text"));
                            convertedLinks.add(converted);
                        }

                        log.info("智能分析检测到列表项: {}, 数量: {}", analysis.get("groupKey"), smartLinks.size());
                        return BrowserResult.success()
                                .put("elements", convertedLinks)
                                .put("count", convertedLinks.size());
                    }
                }
            }
        } catch (Exception e) {
            log.info("智能分析失败: {}", e.getMessage());
        }

        return BrowserResult.error("无法自动检测列表项");
    }

    /**
     * 在列表项中查找链接
     */
    private BrowserResult findLinkInListItem(String pageId, String listSelector, int index) {
        try {
            Page page = browserService.getPage(pageId);
            if (page == null) {
                return BrowserResult.error("页面不存在");
            }

            // 使用JavaScript在指定的列表项中查找a标签
            Object result = page.evaluate(
                "([selector, index]) => {\n" +
                "  try {\n" +
                "    const listItems = document.querySelectorAll(selector);\n" +
                "    if (index >= listItems.length) {\n" +
                "      return { error: '索引超出范围' };\n" +
                "    }\n" +
                "    \n" +
                "    const item = listItems[index];\n" +
                "    const link = item.querySelector('a[href]');\n" +
                "    \n" +
                "    if (link) {\n" +
                "      return {\n" +
                "        href: link.href,\n" +
                "        text: link.textContent.trim()\n" +
                "      };\n" +
                "    } else {\n" +
                "      return { error: '未找到链接' };\n" +
                "    }\n" +
                "  } catch (e) {\n" +
                "    return { error: e.message };\n" +
                "  }\n" +
                "}",
                new String[] { listSelector, String.valueOf(index) }
            );

            if (result != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> resultMap = (Map<String, Object>) result;

                if (resultMap.containsKey("error")) {
                    return BrowserResult.error(resultMap.get("error").toString());
                } else {
                    return BrowserResult.success()
                            .put("href", resultMap.get("href"))
                            .put("text", resultMap.get("text"));
                }
            }

            return BrowserResult.error("JavaScript执行失败");
        } catch (Exception e) {
            log.info("在列表项中查找链接失败: {}", e.getMessage());
            return BrowserResult.error("查找链接失败: " + e.getMessage());
        }
    }

    /**
     * 在同一页面处理详情页（优化版：避免重复提取列表内容）
     */
    private CrawlerResult processDetailPageInSamePage(String pageId, CrawlerSettings settings, Map<String, String> headers) {
        try {
            log.info("在当前页面处理详情内容");

            Page page = browserService.getPage(pageId);
            if (page == null) {
                return CrawlerResult.error("页面不存在");
            }

            String currentUrl = page.url();
            log.info("当前页面URL: {}", currentUrl);

            // 验证页面是否已经跳转到详情页
            boolean isDetailPage = isDetailPageLoaded(pageId, settings);
            if (!isDetailPage) {
                log.error("页面可能未正确跳转到详情页，当前URL: {}", currentUrl);

                // 再次等待并检查
                try {
                    Thread.sleep(3000);
                    page.waitForLoadState(com.microsoft.playwright.options.LoadState.NETWORKIDLE);
                    isDetailPage = isDetailPageLoaded(pageId, settings);
                } catch (Exception e) {
                    log.info("等待页面加载失败: {}", e.getMessage());
                }

                if (!isDetailPage) {
                    log.error("确认页面未跳转到详情页，当前URL: {}", page.url());
                    return CrawlerResult.error("页面未跳转到详情页");
                }
            }

            log.info("确认已跳转到详情页，开始提取内容");

            // 提取标题 - 使用更精确的选择器
            log.info("开始提取标题，选择器: {}", settings.getGetTitle());
            String title = extractDetailContent(pageId, settings.getGetTitle(), "标题");
            log.info("标题提取结果: [{}]", title);

            // 提取发布时间
            String publishTime = extractDetailContent(pageId, settings.getGetPubTime(), "发布时间");

            // 提取内容 - 确保只提取详情页内容，避免列表页内容混入
            String content = extractDetailPageContent(pageId, settings);

            // 提取附件
            log.info("开始提取附件，选择器: {}, 当前URL: {}", settings.getGetFile(), currentUrl);
            List<Map<String, Object>> attachments = extractAttachments(pageId, settings.getGetFile(), currentUrl);
            log.info("附件提取完成，找到 {} 个附件", attachments.size());

            // 验证提取的内容是否有效且不同于列表页
            if (StrUtil.isEmpty(content) && StrUtil.isEmpty(title)) {
                log.error("详情页内容和标题都为空，可能页面未正确加载");
                return CrawlerResult.error("详情页内容提取失败，页面可能未正确加载");
            }

            // 检查内容是否与列表页重复（简单检查长度）
            if (content != null && content.length() > 5000) {
                log.error("详情页内容过长({} 字符)，可能包含列表页内容，尝试重新提取", content.length());
                content = extractPureDetailContent(pageId, settings);
            }

            log.info("详情页内容提取完成 - 标题: {}, 内容长度: {}, 附件数: {}",
                    title, content != null ? content.length() : 0, attachments.size());

            return CrawlerResult.success()
                    .put("title", title)
                    .put("publishTime", publishTime)
                    .put("textContent", content)
                    .put("attachments", attachments);

        } catch (Exception e) {
            log.error("处理同页详情失败", e);
            return CrawlerResult.error("处理同页详情失败: " + e.getMessage());
        }
    }

    /**
     * 提取纯净的详情页内容，排除所有可能的列表页元素
     */
    private String extractPureDetailContent(String pageId, CrawlerSettings settings) {
        try {
            Page page = browserService.getPage(pageId);
            if (page == null) {
                return "";
            }

            // 使用JavaScript提取纯净的详情页内容
            Object result = page.evaluate(
                "([contentSelector, listSelector]) => {\n" +
                "  try {\n" +
                "    // 首先尝试使用配置的内容选择器\n" +
                "    if (contentSelector) {\n" +
                "      const contentElement = document.querySelector(contentSelector);\n" +
                "      if (contentElement) {\n" +
                "        return contentElement.innerText || contentElement.textContent || '';\n" +
                "      }\n" +
                "    }\n" +
                "    \n" +
                "    // 如果没有配置选择器，智能查找详情内容\n" +
                "    const detailSelectors = [\n" +
                "      '.allZoom', '.content', '.article-content', '.detail-content',\n" +
                "      '#content', '#article', '.post-content', 'article',\n" +
                "      '.main-content', '.text-content'\n" +
                "    ];\n" +
                "    \n" +
                "    for (const selector of detailSelectors) {\n" +
                "      const element = document.querySelector(selector);\n" +
                "      if (element) {\n" +
                "        const text = element.innerText || element.textContent || '';\n" +
                "        if (text.length > 100 && text.length < 10000) {\n" +
                "          return text;\n" +
                "        }\n" +
                "      }\n" +
                "    }\n" +
                "    \n" +
                "    // 最后尝试从body中提取，但排除列表和导航元素\n" +
                "    const bodyClone = document.body.cloneNode(true);\n" +
                "    \n" +
                "    // 移除列表和导航元素\n" +
                "    const excludeSelectors = [\n" +
                "      listSelector,\n" +
                "      '.sse_list_1', '.list', '.menu', '.nav', '.navigation',\n" +
                "      '.sidebar', '.pagination', '.breadcrumb', 'header', 'footer'\n" +
                "    ].filter(s => s);\n" +
                "    \n" +
                "    excludeSelectors.forEach(sel => {\n" +
                "      const elements = bodyClone.querySelectorAll(sel);\n" +
                "      elements.forEach(el => el.remove());\n" +
                "    });\n" +
                "    \n" +
                "    const text = bodyClone.innerText || bodyClone.textContent || '';\n" +
                "    return text.length > 100 ? text : '';\n" +
                "    \n" +
                "  } catch (e) {\n" +
                "    console.error('提取详情内容失败:', e);\n" +
                "    return '';\n" +
                "  }\n" +
                "}",
                new String[] { settings.getGetContent(), settings.getClickListPath() }
            );

            String extractedText = result != null ? result.toString().trim() : "";

            if (StrUtil.isNotEmpty(extractedText)) {
                log.info("提取到纯净详情内容，长度: {}", extractedText.length());
                return formatExtractedText(extractedText);
            }

            return "";

        } catch (Exception e) {
            log.info("提取纯净详情内容失败: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 检查是否已经加载到详情页
     */
    private boolean isDetailPageLoaded(String pageId, CrawlerSettings settings) {
        try {
            Page page = browserService.getPage(pageId);
            if (page == null) {
                return false;
            }

            // 检查URL是否发生变化（不再是列表页）
            String currentUrl = page.url();
            log.info("检查详情页加载状态，当前URL: {}", currentUrl);

            // 检查是否存在详情页特有的元素
            if (StrUtil.isNotEmpty(settings.getGetTitle())) {
                try {
                    String title = extractContent(pageId, settings.getGetTitle());
                    if (StrUtil.isNotEmpty(title) && title.length() > 5) {
                        log.info("找到详情页标题: {}", title.substring(0, Math.min(title.length(), 50)));
                        return true;
                    }
                } catch (Exception e) {
                    log.info("提取标题失败: {}", e.getMessage());
                }
            }

            if (StrUtil.isNotEmpty(settings.getGetContent())) {
                try {
                    String content = extractContent(pageId, settings.getGetContent());
                    if (StrUtil.isNotEmpty(content) && content.length() > 100) {
                        log.info("找到详情页内容，长度: {}", content.length());
                        return true;
                    }
                } catch (Exception e) {
                    log.info("提取内容失败: {}", e.getMessage());
                }
            }

            // 检查页面是否包含详情页特征
            try {
                Object result = page.evaluate(
                    "() => {\n" +
                    "  // 检查是否有详情页特征元素\n" +
                    "  const detailIndicators = [\n" +
                    "    '.article', '.content', '.detail', '.main-content',\n" +
                    "    'article', '#content', '#article', '.post'\n" +
                    "  ];\n" +
                    "  \n" +
                    "  for (const selector of detailIndicators) {\n" +
                    "    const element = document.querySelector(selector);\n" +
                    "    if (element && element.textContent.length > 200) {\n" +
                    "      return true;\n" +
                    "    }\n" +
                    "  }\n" +
                    "  \n" +
                    "  return false;\n" +
                    "}"
                );

                if (Boolean.TRUE.equals(result)) {
                    log.info("通过页面特征检测确认为详情页");
                    return true;
                }
            } catch (Exception e) {
                log.info("页面特征检测失败: {}", e.getMessage());
            }

            return false;
        } catch (Exception e) {
            log.info("检查详情页加载状态失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 提取详情页特定内容，避免列表页内容混入
     */
    private String extractDetailContent(String pageId, String selector, String contentType) {
        if (StrUtil.isEmpty(selector)) {
            log.info("{}选择器为空", contentType);

            // 如果是标题，尝试使用常见的标题选择器
            if ("标题".equals(contentType)) {
                return extractTitleWithFallback(pageId);
            }

            return "";
        }

        try {
            String content = extractContent(pageId, selector);
            if (StrUtil.isNotEmpty(content)) {
                log.info("成功提取{}: {}", contentType, content.length() > 50 ? content.substring(0, 50) + "..." : content);
                return content;
            } else {
                log.info("使用选择器{}提取{}为空", selector, contentType);

                // 如果是标题且提取失败，尝试备用方法
                if ("标题".equals(contentType)) {
                    return extractTitleWithFallback(pageId);
                }
            }
        } catch (Exception e) {
            log.info("提取{}失败: {}", contentType, e.getMessage());

            // 如果是标题且提取失败，尝试备用方法
            if ("标题".equals(contentType)) {
                return extractTitleWithFallback(pageId);
            }
        }

        return "";
    }

    /**
     * 使用备用方法提取标题
     */
    private String extractTitleWithFallback(String pageId) {
        // 常见的标题选择器
        String[] titleSelectors = {
            "h1", "h2", ".title", ".article-title", ".content-title",
            ".news-title", ".detail-title", "title", ".page-title"
        };

        for (String selector : titleSelectors) {
            try {
                String title = extractContent(pageId, selector);
                if (StrUtil.isNotEmpty(title) && title.trim().length() > 2) {
                    log.info("使用备用选择器{}提取到标题: {}", selector, title.substring(0, Math.min(title.length(), 50)));
                    return title.trim();
                }
            } catch (Exception e) {
                log.info("备用选择器{}提取标题失败: {}", selector, e.getMessage());
            }
        }

        // 最后尝试使用页面标题
        try {
            Page page = browserService.getPage(pageId);
            if (page != null) {
                String pageTitle = page.title();
                if (StrUtil.isNotEmpty(pageTitle) && pageTitle.trim().length() > 2) {
                    log.info("使用页面标题: {}", pageTitle);
                    return pageTitle.trim();
                }
            }
        } catch (Exception e) {
            log.info("获取页面标题失败: {}", e.getMessage());
        }

        log.error("所有标题提取方法都失败");
        return "";
    }

    /**
     * 提取详情页内容，使用多种策略避免列表页内容混入
     */
    private String extractDetailPageContent(String pageId, CrawlerSettings settings) {
        String content = "";

        // 策略1：使用配置的内容选择器
        if (StrUtil.isNotEmpty(settings.getGetContent())) {
            content = extractVisibleText(pageId, settings.getGetContent());
            if (StrUtil.isNotEmpty(content)) {
                log.info("使用配置选择器提取到内容，长度: {}", content.length());
                return content;
            }
        }

        // 策略2：尝试常见的详情页内容选择器
        String[] detailSelectors = {
            ".article-content", ".content", ".detail-content",
            "#content", "#article", ".post-content",
            "article", ".main-content", ".text-content"
        };

        for (String selector : detailSelectors) {
            content = extractVisibleText(pageId, selector);
            if (StrUtil.isNotEmpty(content) && content.length() > 100) { // 确保内容足够长
                log.info("使用选择器 {} 提取到内容，长度: {}", selector, content.length());
                return content;
            }
        }

        // 策略3：如果以上都失败，使用智能提取但排除列表区域
        content = extractVisibleTextExcludingList(pageId, settings.getClickListPath());

        return content;
    }

    /**
     * 提取可见文本但排除列表区域，避免重复内容
     */
    private String extractVisibleTextExcludingList(String pageId, String listSelector) {
        try {
            Page page = browserService.getPage(pageId);
            if (page == null) {
                return "";
            }

            // 使用JavaScript提取内容但排除列表区域
            Object result = page.evaluate(
                "(listSelector) => {\n" +
                "  try {\n" +
                "    // 克隆body元素\n" +
                "    const bodyClone = document.body.cloneNode(true);\n" +
                "    \n" +
                "    // 移除列表区域\n" +
                "    if (listSelector) {\n" +
                "      const listElements = bodyClone.querySelectorAll(listSelector);\n" +
                "      listElements.forEach(el => el.remove());\n" +
                "    }\n" +
                "    \n" +
                "    // 移除导航、菜单等元素\n" +
                "    const excludeSelectors = [\n" +
                "      'nav', '.nav', '.navigation', '.menu',\n" +
                "      'header', '.header', 'footer', '.footer',\n" +
                "      '.sidebar', '.pagination', '.breadcrumb'\n" +
                "    ];\n" +
                "    \n" +
                "    excludeSelectors.forEach(sel => {\n" +
                "      const elements = bodyClone.querySelectorAll(sel);\n" +
                "      elements.forEach(el => el.remove());\n" +
                "    });\n" +
                "    \n" +
                "    // 提取文本内容\n" +
                "    return bodyClone.innerText || bodyClone.textContent || '';\n" +
                "  } catch (e) {\n" +
                "    return '';\n" +
                "  }\n" +
                "}",
                listSelector
            );

            String extractedText = result != null ? result.toString() : "";
            return formatExtractedText(extractedText);

        } catch (Exception e) {
            log.info("排除列表区域提取文本失败: {}", e.getMessage());
            return "";
        }
    }



    /**
     * 处理详情页
     */
    private CrawlerResult processDetailPage(String detailUrl, CrawlerSettings settings, Map<String, String> headers) {
        try {
            log.info("处理详情页: {}", detailUrl);

            // 导航到详情页
            BrowserResult navResult = browserService.navigate(detailUrl, headers, settings.getTimeout());
            if (!navResult.isSuccess()) {
                return CrawlerResult.error("无法访问详情页: " + navResult.getMessage());
            }

            String pageId = navResult.getString("pageId");
            
            // 处理iframe (如果配置了)
            if (StrUtil.isNotEmpty(settings.getIframe())) {
                BrowserResult iframeResult = browserService.switchToIframe(pageId, settings.getIframe());
                if (iframeResult.isSuccess()) {
                    log.info("已切换到iframe: {}", settings.getIframe());
                    pageId = iframeResult.getString("frameId");
                } else {
                    log.info("切换iframe失败: {}", iframeResult.getMessage());
                }
            }
            
            // 执行页面滚动，处理懒加载内容
            handleLazyLoading(pageId);

            // 尝试点击"显示更多"或"展开全文"按钮（如果存在）
            tryExpandContent(pageId);

            // 提取标题
            String title = extractContent(pageId, settings.getGetTitle());
            
            // 如果选择器未提取到标题，尝试使用页面标题
            if (StrUtil.isEmpty(title)) {
                BrowserResult state = browserService.getPageContent(pageId);
                if (state.isSuccess()) {
                    title = state.getString("title");
                }
            }

            // 提取发布时间
            String publishTime = extractContent(pageId, settings.getGetPubTime());
            
            // 尝试从时间元素提取
            if (StrUtil.isEmpty(publishTime)) {
                List<String> timeSelectors = Arrays.asList(
                    "time", ".time", ".date", ".datetime", 
                    "[datetime]", ".pub-date", ".publish-time"
                );
                
                for (String selector : timeSelectors) {
                    String time = extractContent(pageId, selector);
                    if (StrUtil.isNotEmpty(time)) {
                        publishTime = time;
                        break;
                    }
                }
            }
            
            // 提取时间戳格式 (2022-01-01 12:34:56)
            if (StrUtil.isEmpty(publishTime)) {
                String pageContent = browserService.getPageContent(pageId).getString("content");
                if (StrUtil.isNotEmpty(pageContent)) {
                    Pattern pattern = Pattern.compile("\\d{4}-\\d{2}-\\d{2}\\s\\d{2}:\\d{2}(:\\d{2})?");
                    Matcher matcher = pattern.matcher(pageContent);
                    if (matcher.find()) {
                        publishTime = matcher.group();
                    }
                }
            }

            // 提取内容 - 使用增强的可见文本提取方法
            String content = extractVisibleText(pageId, settings.getGetContent());
            
            // 如果内容为空，尝试自动检测主内容区域
            if (StrUtil.isEmpty(content)) {
                List<String> contentSelectors = Arrays.asList(
                    "article", ".article", ".content", ".main-content", 
                    "#content", "#main", ".text", ".article-content"
                );
                
                for (String selector : contentSelectors) {
                    content = extractVisibleText(pageId, selector);
                    if (StrUtil.isNotEmpty(content)) {
                        log.info("通过选择器自动检测到内容区域: {}", selector);
                        break;
                    }
                }
                
                // 如果还是没有内容，提取整个页面文本
                if (StrUtil.isEmpty(content)) {
                    content = extractVisibleText(pageId, null);
                }
            }

            // 尝试提取结构化内容（如表格、列表等）
            String structuredContent = extractStructuredContent(pageId, settings.getGetContent());
            if (StrUtil.isNotEmpty(structuredContent)) {
                content = content + "\n\n=== 结构化内容 ===\n" + structuredContent;
            }

            // 使用专门的方法提取附件
            List<Map<String, Object>> attachments = new ArrayList<>();
            
            // 智能提取附件
            List<Map<String, Object>> extractedAttachments = extractAttachmentsIntelligently(pageId, settings, detailUrl);
            attachments.addAll(extractedAttachments);
            log.info("智能附件提取完成，共找到{}个附件", extractedAttachments.size());
            
            // 智能检测页面上的所有附件
            log.info("开始智能检测页面上的所有附件...");
            BrowserResult attachmentResult = browserService.detectAllAttachments(pageId, detailUrl);
            if (attachmentResult.isSuccess()) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> detectedAttachments = (List<Map<String, Object>>) attachmentResult.get("attachments");
                if (detectedAttachments != null && !detectedAttachments.isEmpty()) {
                    log.info("智能检测到{}个附件", detectedAttachments.size());
                    attachments.addAll(detectedAttachments);
                } else {
                    log.info("未检测到任何附件");
                }
            } else {
                log.error("附件检测失败: " + attachmentResult.get("error"));
            }

            // 尝试通过下载事件检测动态附件（作为补充）
            log.info("尝试通过下载事件检测动态附件...");
            BrowserResult downloadResult = browserService.detectAttachmentsByDownload(pageId, ".download, .file, [href*='download'], [onclick*='download']");
            if (downloadResult.isSuccess()) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> downloadAttachments = (List<Map<String, Object>>) downloadResult.get("attachments");
                if (downloadAttachments != null && !downloadAttachments.isEmpty()) {
                    log.info("通过下载事件检测到{}个附件", downloadAttachments.size());
                    // 去重后添加
                    for (Map<String, Object> downloadAttachment : downloadAttachments) {
                        boolean exists = attachments.stream()
                            .anyMatch(existing -> java.util.Objects.equals(existing.get("url"), downloadAttachment.get("url")));
                        if (!exists) {
                            attachments.add(downloadAttachment);
                        }
                    }
                }
            }

            return CrawlerResult.success()
                    .put("title", title)
                    .put("publishTime", publishTime)
                    .put("textContent", content)
                    .put("attachments", attachments);

        } catch (Exception e) {
            log.error("处理详情页失败: {}", detailUrl, e);
            return CrawlerResult.error("处理详情页失败: " + e.getMessage());
        }
    }
    
    /**
     * 自动检测附件 - 使用 Playwright 原生功能优化版
     * 直接使用 Playwright API 查找页面上所有可能的附件链接
     */
    private List<Map<String, Object>> autoDetectAttachments(String pageId, String baseUrl) {
        List<Map<String, Object>> attachments = new ArrayList<>();

        try {
            Page page = browserService.getPage(pageId);
            if (page == null) {
                log.error("页面不存在: {}", pageId);
                return attachments;
            }

            log.info("开始使用 Playwright 原生功能检测附件（支持增量更新）...");

            // 策略1: 直接查找明显的文件链接
            String[] fileExtensions = {
                "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx",
                "zip", "rar", "7z", "tar", "gz", "csv", "txt", "xml", "json"
            };

            Set<String> processedUrls = new HashSet<>();

            for (String ext : fileExtensions) {
                try {
                    // 使用 Playwright 的 locator 查找包含特定扩展名的链接
                    String selector = String.format("a[href*='.%s']", ext);
                    var locator = page.locator(selector);
                    int count = locator.count();

                    log.info("查找 {} 文件，选择器: {}, 找到: {} 个", ext, selector, count);

                    for (int i = 0; i < count; i++) {
                        try {
                            var linkLocator = locator.nth(i);
                            String href = linkLocator.getAttribute("href");
                            String text = linkLocator.textContent();
                            String title = linkLocator.getAttribute("title");

                            if (StrUtil.isNotEmpty(href) && !processedUrls.contains(href)) {
                                processedUrls.add(href);
                                String resolvedUrl = resolveUrl(href, baseUrl);

                                Map<String, Object> attachment = new HashMap<>();
                                attachment.put("url", resolvedUrl);
                                attachment.put("text", StrUtil.isNotEmpty(text) ? text.trim() : "");
                                attachment.put("title", StrUtil.isNotEmpty(title) ? title.trim() : "");
                                attachment.put("type", ext);
                                attachment.put("selector", selector); // 添加选择器信息，用于文件名判断
                                attachment.put("detectionMethod", "file_extension");
                                attachment.put("auto_detected", true);
                                attachments.add(attachment);

                                log.info("发现 {} 文件: {} -> {}", ext.toUpperCase(), text, resolvedUrl);
                            }
                        } catch (Exception e) {
                            log.info("处理第 {} 个 {} 链接时出错: {}", i, ext, e.getMessage());
                        }
                    }
                } catch (Exception e) {
                    log.info("查找 {} 文件时出错: {}", ext, e.getMessage());
                }
            }

            log.info("Playwright 原生附件检测完成，共找到 {} 个附件（支持增量更新去重）", attachments.size());

        } catch (Exception e) {
            log.error("自动检测附件失败", e);
        }

        return attachments;
    }



    /**
     * 提取内容 - 增强版，支持多选择器和更健壮的错误处理
     */
    private String extractContent(String pageId, String selector) {
        if (StrUtil.isEmpty(selector)) {
            return "";
        }
        
        // 使用浏览器服务提取文本
        BrowserResult result = browserService.extractText(pageId, selector);
        
        if (result.isSuccess()) {
            String text = result.getString("text");
            return StrUtil.isNotEmpty(text) ? text.trim() : "";
        }
        
        return "";
    }

    /**
     * 提取可见文本内容，保留结构，参考旧版本的实现
     * @param pageId 页面ID
     * @param selector 内容选择器
     * @return 提取并格式化后的文本内容
     */
    private String extractVisibleText(String pageId, String selector) {
        try {
            Page page = browserService.getPage(pageId);
            if (page == null) {
                log.info("页面不存在: {}", pageId);
                return "";
            }

            log.info("开始提取可见文本，页面ID: {}, 选择器: {}", pageId, selector);

            // 获取页面HTML内容，使用JSoup进行智能文本提取
            String htmlContent = page.content();
            if (StrUtil.isEmpty(htmlContent)) {
                log.info("页面HTML内容为空");
                return "";
            }

            // 使用JSoup解析HTML，参考老版本的实现
            org.jsoup.nodes.Document document = org.jsoup.Jsoup.parse(htmlContent);

            // 如果有指定选择器，先尝试使用选择器提取
            if (StrUtil.isNotEmpty(selector)) {
                // 适配jQuery选择器到CSS选择器
                String adaptedSelector = adaptJQuerySelector(selector);
                log.info("原始选择器: {}, 适配后选择器: {}", selector, adaptedSelector);

                try {
                    org.jsoup.select.Elements elements = document.select(adaptedSelector);
                    if (!elements.isEmpty()) {
                        String text = extractVisibleTextFromElements(elements);
                        if (StrUtil.isNotEmpty(text)) {
                            log.info("使用配置选择器成功提取文本，长度: {}", text.length());
                            return text;
                        }
                    }
                } catch (Exception e) {
                    log.info("使用选择器提取失败: {}, 错误: {}", adaptedSelector, e.getMessage());
                }

                // 如果选择器失败，尝试直接使用Playwright提取
                BrowserResult selectorResult = browserService.extractText(pageId, selector);
                if (selectorResult.isSuccess()) {
                    String text = selectorResult.getString("text");
                    if (StrUtil.isNotEmpty(text)) {
                        log.info("使用Playwright选择器成功提取文本，长度: {}", text.length());
                        return formatExtractedText(text);
                    }
                }
            }

            // 使用JSoup进行智能文本提取（参考老版本实现）
            String extractedText = extractVisibleTextFromDocument(document);

            if (StrUtil.isNotEmpty(extractedText)) {
                log.info("使用JSoup智能提取成功，长度: {}", extractedText.length());
                return extractedText;
            }

            // 最后的降级方案：使用Playwright直接提取
            log.info("JSoup提取失败，使用Playwright降级方案");
            String fallbackText = page.textContent("body");
            return StrUtil.isNotEmpty(fallbackText) ? formatExtractedText(fallbackText) : "";

        } catch (Exception e) {
            log.info("提取可见文本失败: {}", e.getMessage());
            // 最终降级使用原始提取方法
            String content = extractContent(pageId, selector);
            log.info("使用原始文本提取方法，提取到内容长度: {}", content != null ? content.length() : 0);
            return content;
        }
    }

    /**
     * 适配jQuery选择器到CSS选择器
     */
    private String adaptJQuerySelector(String jquerySelector) {
        if (StrUtil.isEmpty(jquerySelector)) {
            return jquerySelector;
        }

        String adapted = jquerySelector;

        // 处理常见的jQuery选择器模式
        // :contains() -> 转换为属性选择器或类选择器
        if (adapted.contains(":contains(")) {
            // 简单处理，移除:contains部分，保留基础选择器
            adapted = adapted.replaceAll(":contains\\([^)]*\\)", "");
        }

        // :first -> :first-child
        adapted = adapted.replace(":first", ":first-child");

        // :last -> :last-child
        adapted = adapted.replace(":last", ":last-child");

        // :eq(n) -> :nth-child(n+1)
        adapted = adapted.replaceAll(":eq\\((\\d+)\\)", ":nth-child($1)");

        // 清理多余的空格
        adapted = adapted.trim();

        return adapted;
    }

    /**
     * 从JSoup Elements中提取可见文本
     */
    private String extractVisibleTextFromElements(org.jsoup.select.Elements elements) {
        StringBuilder text = new StringBuilder();

        for (org.jsoup.nodes.Element element : elements) {
            String elementText = element.text();
            if (StrUtil.isNotEmpty(elementText)) {
                if (text.length() > 0) {
                    text.append("\n");
                }
                text.append(elementText);
            }
        }

        return formatExtractedText(text.toString());
    }

    /**
     * 从JSoup Document中智能提取可见文本（参考老版本实现）
     */
    private String extractVisibleTextFromDocument(org.jsoup.nodes.Document document) {
        // 创建文档副本以避免修改原始文档
        org.jsoup.nodes.Document docCopy = document.clone();

        try {
            // 移除所有脚本、样式和隐藏元素
            docCopy.select("script, style, noscript, iframe, meta").remove();
            docCopy.select("[style*=display:none], [style*=visibility:hidden], [hidden]").remove();

            // 移除一些常见的无用元素
            docCopy.select("header, footer, nav, .header, .footer, .nav, .menu, .sidebar, .navigation, .pagination").remove();

            // 提取所有文本，优先使用主要内容区域
            String text = "";

            // 尝试找到主要内容区域
            org.jsoup.select.Elements mainContent = docCopy.select("main, article, .content, .main, .article, #content, #main");
            if (!mainContent.isEmpty()) {
                // 从主要内容区域提取文本
                text = mainContent.text();
            } else {
                // 没有明确的主要内容区域，使用body
                text = docCopy.body() != null ? docCopy.body().text() : docCopy.text();
            }

            return formatExtractedText(text);
        } catch (Exception e) {
            log.info("JSoup提取可见文本出错: {}", e.getMessage());
            // 降级处理 - 直接获取所有文本
            return formatExtractedText(docCopy.text());
        }
    }

    /**
     * 格式化提取的文本
     */
    private String formatExtractedText(String text) {
        if (StrUtil.isEmpty(text)) {
            return "";
        }

        // 处理提取的文本 - 清理无用空白字符
        text = text.replaceAll("\\s+", " ")  // 替换多个空白为单个空格
                   .replaceAll("\\n+", "\n") // 替换多个换行为单个换行
                   .trim();                 // 移除首尾空白

        return text;
    }

    /**
     * 提取附件 - 增强版，同时支持getFile选择器和自动检测
     */
    private List<Map<String, Object>> extractAttachments(String pageId, String selector, String baseUrl) {
        List<Map<String, Object>> attachments = new ArrayList<>();

        log.info("提取附件 - 页面ID: {}, 选择器: {}, 基础URL: {}", pageId, selector, baseUrl);

        // 如果有选择器，使用extractFiles方法
        if (StrUtil.isNotEmpty(selector)) {
            log.info("使用配置的附件选择器: {}", selector);
            BrowserResult result = browserService.extractFiles(pageId, selector);
            if (result.isSuccess()) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> files = (List<Map<String, Object>>) result.get("files");
                if (CollUtil.isNotEmpty(files)) {
                    log.info("通过配置选择器找到 {} 个附件", files.size());
                    attachments.addAll(files);
                } else {
                    log.info("配置选择器未找到附件");
                }
            } else {
                log.error("使用配置选择器提取附件失败: {}", result.getMessage());
            }
        } else {
            log.info("未配置附件选择器，将使用自动检测");
        }
        
        // 自动检测其他可能的附件
        log.info("开始自动检测附件");
        List<Map<String, Object>> autoAttachments = autoDetectAttachments(pageId, baseUrl);
        log.info("自动检测找到 {} 个附件", autoAttachments.size());

        // 移除重复项
        Set<String> existingUrls = new HashSet<>();
        for (Map<String, Object> att : attachments) {
            existingUrls.add((String) att.get("url"));
        }

        int addedCount = 0;
        for (Map<String, Object> auto : autoAttachments) {
            if (!existingUrls.contains(auto.get("url"))) {
                attachments.add(auto);
                addedCount++;
            }
        }

        log.info("附件提取完成 - 配置选择器: {}, 自动检测: {}, 去重后新增: {}, 总计: {}",
                attachments.size() - autoAttachments.size() + (autoAttachments.size() - addedCount),
                autoAttachments.size(), addedCount, attachments.size());

        return attachments;
    }

    /**
     * 查找下一页URL - 增强版，支持多种翻页模式
     */
    private String findNextPageUrl(String pageId, String nextPageSelector, String currentUrl) {
        // 如果没有指定下一页选择器，无法翻页
        if (StrUtil.isEmpty(nextPageSelector)) {
            return null;
        }
        
        log.info("查找下一页，选择器: {}", nextPageSelector);
        log.info("选择器详细信息 - 长度: {}, 字符数组: {}", nextPageSelector.length(), nextPageSelector.toCharArray());

        // 尝试方式1：使用clickNextPage方法
        BrowserResult clickResult = browserService.clickNextPage(pageId, nextPageSelector);
        if (clickResult.isSuccess()) {
            String newUrl = clickResult.getString("url");
            log.info("通过点击找到下一页: {}", newUrl);
            return newUrl;
        }

        // 尝试方式2：使用findElements查找下一页链接
        BrowserResult result = browserService.findElements(pageId, nextPageSelector);
        if (result.isSuccess()) {
            @SuppressWarnings("unchecked")
            List<Map<String, String>> elements = (List<Map<String, String>>) result.get("elements");
            
            if (!elements.isEmpty()) {
                String nextUrl = elements.get(0).get("href");
                if (StrUtil.isNotEmpty(nextUrl)) {
                    String absoluteUrl = resolveUrl(nextUrl, currentUrl);
                    log.info("通过元素找到下一页: {}", absoluteUrl);
                    return absoluteUrl;
                }
            }
        }
        
        // 尝试方式3：检测页码规律
        try {
            String pagePattern = detectPagePattern(currentUrl);
            if (StrUtil.isNotEmpty(pagePattern)) {
                String nextUrl = pagePattern;
                log.info("通过规律推断下一页: {}", nextUrl);
                return nextUrl;
            }
        } catch (Exception e) {
            log.info("检测页码规律失败: {}", e.getMessage());
        }
        
        // 尝试方式4：智能查找常用翻页元素
        String[] commonNextSelectors = {
            ".next", ".next-page", ".nextPage", 
            "a[text*='下一页']", "a[text*='Next']", // 替换jQuery风格为标准CSS选择器
            "[aria-label='Next']", ".pagination .active + li a"
        };
        
        for (String selector : commonNextSelectors) {
            try {
                BrowserResult commonResult = browserService.findElements(pageId, selector);
                if (commonResult.isSuccess()) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, String>> elements = (List<Map<String, String>>) commonResult.get("elements");
                    
                    if (!elements.isEmpty() && StrUtil.isNotEmpty(elements.get(0).get("href"))) {
                        String nextUrl = resolveUrl(elements.get(0).get("href"), currentUrl);
                        log.info("通过常用选择器找到下一页: {} -> {}", selector, nextUrl);
                        return nextUrl;
                    }
                }
            } catch (Exception e) {
                log.info("使用选择器{}查找下一页失败: {}", selector, e.getMessage());
            }
        }
        
        // 尝试方式5：处理XPath选择器或使用JavaScript查找包含"下一页"文本的链接
        try {
            Page page = browserService.getPage(pageId);
            if (page != null) {
                // 如果原始选择器是XPath格式，直接使用JavaScript处理
                if (nextPageSelector.startsWith("//")) {
                    Object result5 = page.evaluate(
                        "(xpath) => {\n" +
                        "  try {\n" +
                        "    const element = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;\n" +
                        "    if (element && element.href) {\n" +
                        "      return element.href;\n" +
                        "    }\n" +
                        "  } catch (e) {\n" +
                        "    console.warn('XPath执行失败:', e);\n" +
                        "  }\n" +
                        "  return null;\n" +
                        "}",
                        nextPageSelector
                    );

                    if (result5 != null && !result5.toString().equals("null")) {
                        String nextUrl = result5.toString();
                        log.info("通过XPath找到下一页: {}", nextUrl);
                        return nextUrl;
                    }
                } else {
                    // 通用的文本查找
                    Object result5 = page.evaluate(
                        "() => {\n" +
                        "  const links = document.querySelectorAll('a');\n" +
                        "  for (const link of links) {\n" +
                        "    const text = link.textContent.trim();\n" +
                        "    if (text.includes('下一页') || text.includes('Next') || text.includes('next')) {\n" +
                        "      return link.href;\n" +
                        "    }\n" +
                        "  }\n" +
                        "  return null;\n" +
                        "}"
                    );

                    if (result5 != null && !result5.toString().equals("null")) {
                        String nextUrl = result5.toString();
                        log.info("通过文本查找找到下一页: {}", nextUrl);
                        return nextUrl;
                    }
                }
            }
        } catch (Exception e) {
            log.info("使用JavaScript查找下一页失败: {}", e.getMessage());
        }

        log.info("未找到下一页");
        return null;
    }
    
    /**
     * 检测URL中的页码规律
     */
    private String detectPagePattern(String url) {
        // 查找常见页码参数格式 page=X, p=X, pageNum=X
        Pattern pagePattern = Pattern.compile("([&?])(page|p|pageNum|pageNumber|pageIndex|pn|pg)=([0-9]+)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pagePattern.matcher(url);
        
        if (matcher.find()) {
            String prefix = matcher.group(1); // & or ?
            String paramName = matcher.group(2); // page, p, etc.
            int currentPage = Integer.parseInt(matcher.group(3));
            int nextPage = currentPage + 1;
            
            // 替换URL中的页码参数
            String nextUrl = url.substring(0, matcher.start()) + 
                             prefix + paramName + "=" + nextPage + 
                             url.substring(matcher.end());
            
            return nextUrl;
        }
        
        // 查找URL路径中的页码，如 /page/2/, /2/, /p/2/
        Pattern pathPattern = Pattern.compile("(/|\\b)(page|p|pg)(/|=)([0-9]+)(/|$)", Pattern.CASE_INSENSITIVE);
        matcher = pathPattern.matcher(url);
        
        if (matcher.find()) {
            String prefix = matcher.group(1); // /
            String paramName = matcher.group(2); // page, p
            String separator = matcher.group(3); // / or =
            int currentPage = Integer.parseInt(matcher.group(4));
            String suffix = matcher.group(5); // / or ""
            int nextPage = currentPage + 1;
            
            // 替换URL中的页码
            String nextUrl = url.substring(0, matcher.start()) + 
                             prefix + paramName + separator + nextPage + suffix + 
                             url.substring(matcher.end());
            
            return nextUrl;
        }
        
        return null;
    }

    /**
     * 解析相对URL为绝对URL - 增强版，处理更多URL格式
     */
    private String resolveUrl(String url, String baseUrl) {
        if (StrUtil.isEmpty(url)) {
            return baseUrl;
        }
        
        // 已是绝对URL
        if (url.startsWith("http://") || url.startsWith("https://")) {
            return url;
        }
        
        // 处理协议相对URL
        if (url.startsWith("//")) {
            try {
                String protocol = new URL(baseUrl).getProtocol();
                return protocol + ":" + url;
            } catch (Exception e) {
                return "https:" + url; // 默认使用HTTPS
            }
        }
        
        try {
            // 处理根相对路径
            if (url.startsWith("/")) {
                URL base = new URL(baseUrl);
                return base.getProtocol() + "://" + base.getHost() + (base.getPort() != -1 ? ":" + base.getPort() : "") + url;
            }
            
            // 处理相对路径 
            URL base = new URL(baseUrl);
            String path = base.getPath();
            
            // 如果是文件URL，去掉文件部分
            if (path.lastIndexOf('/') != -1) {
                path = path.substring(0, path.lastIndexOf('/') + 1);
            }
            
            if (!path.endsWith("/")) {
                path += "/";
            }
            
            // 处理 ../ 和 ./ 
            if (url.startsWith("../") || url.startsWith("./")) {
                List<String> pathSegments = new ArrayList<>(Arrays.asList(path.split("/")));
                // 移除空段
                pathSegments.removeIf(String::isEmpty);
                
                String[] urlSegments = url.split("/");
                for (String segment : urlSegments) {
                    if ("..".equals(segment)) {
                        if (!pathSegments.isEmpty()) {
                            pathSegments.remove(pathSegments.size() - 1);
                        }
                    } else if (!".".equals(segment) && !segment.isEmpty()) {
                        pathSegments.add(segment);
                    }
                }
                
                StringBuilder newPath = new StringBuilder();
                for (String segment : pathSegments) {
                    newPath.append("/").append(segment);
                }
                
                return base.getProtocol() + "://" + base.getHost() + 
                       (base.getPort() != -1 ? ":" + base.getPort() : "") + newPath;
            }
            
            // 普通相对路径
            return base.getProtocol() + "://" + base.getHost() + 
                   (base.getPort() != -1 ? ":" + base.getPort() : "") + path + url;
            
        } catch (Exception e) {
            log.info("解析URL失败: {} + {}, {}", baseUrl, url, e.getMessage());
            return url;
        }
    }

    /**
     * 提取列表页文本内容，避免与详情页内容混淆
     */
    private String extractListPageText(String pageId, CrawlerSettings settings) {
        try {
            // 策略1：如果配置了专门的列表页内容选择器，优先使用
            // 注意：这里不使用getGetContent()，因为那通常是详情页选择器

            // 策略2：尝试提取列表页的概要信息，但排除详情页内容区域
            String[] listPageSelectors = {
                ".list-summary", ".page-summary", ".intro",
                ".description", ".list-intro", ".page-intro",
                ".page-header", ".list-header", ".category-desc"
            };

            for (String selector : listPageSelectors) {
                String text = extractVisibleText(pageId, selector);
                if (StrUtil.isNotEmpty(text) && text.length() > 50) {
                    log.info("使用列表页选择器 {} 提取到内容，长度: {}", selector, text.length());
                    return text;
                }
            }

            // 策略3：提取页面标题和描述性文本，但排除列表项和详情页内容
            String listPageText = extractListPageSummary(pageId, settings);
            if (StrUtil.isNotEmpty(listPageText)) {
                return listPageText;
            }

            // 策略4：如果以上都没有内容，返回空字符串（避免提取详情页内容）
            log.info("列表页未找到合适的概要内容，跳过列表页文本提取");
            return "";

        } catch (Exception e) {
            log.info("提取列表页文本失败: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 提取列表页概要信息
     */
    private String extractListPageSummary(String pageId, CrawlerSettings settings) {
        try {
            Page page = browserService.getPage(pageId);
            if (page == null) {
                return "";
            }

            // 使用JavaScript提取列表页概要，排除列表项和详情内容
            Object result = page.evaluate(
                "([listSelector, contentSelector]) => {\n" +
                "  try {\n" +
                "    // 克隆body元素\n" +
                "    const bodyClone = document.body.cloneNode(true);\n" +
                "    \n" +
                "    // 移除列表项区域\n" +
                "    if (listSelector) {\n" +
                "      const listElements = bodyClone.querySelectorAll(listSelector);\n" +
                "      listElements.forEach(el => el.remove());\n" +
                "    }\n" +
                "    \n" +
                "    // 移除详情页内容区域（如果存在）\n" +
                "    if (contentSelector) {\n" +
                "      const contentElements = bodyClone.querySelectorAll(contentSelector);\n" +
                "      contentElements.forEach(el => el.remove());\n" +
                "    }\n" +
                "    \n" +
                "    // 移除其他不相关元素\n" +
                "    const excludeSelectors = [\n" +
                "      'script', 'style', 'nav', '.nav', '.navigation',\n" +
                "      '.menu', '.sidebar', '.pagination', '.breadcrumb',\n" +
                "      'footer', '.footer', '.ad', '.advertisement'\n" +
                "    ];\n" +
                "    \n" +
                "    excludeSelectors.forEach(sel => {\n" +
                "      const elements = bodyClone.querySelectorAll(sel);\n" +
                "      elements.forEach(el => el.remove());\n" +
                "    });\n" +
                "    \n" +
                "    // 提取剩余的文本内容\n" +
                "    let text = bodyClone.innerText || bodyClone.textContent || '';\n" +
                "    \n" +
                "    // 如果文本太长，只取前500字符作为概要\n" +
                "    if (text.length > 500) {\n" +
                "      text = text.substring(0, 500) + '...';\n" +
                "    }\n" +
                "    \n" +
                "    return text;\n" +
                "  } catch (e) {\n" +
                "    return '';\n" +
                "  }\n" +
                "}",
                new String[] { settings.getClickListPath(), settings.getGetContent() }
            );

            String extractedText = result != null ? result.toString().trim() : "";

            // 如果提取的文本太短，可能没有有效内容
            if (extractedText.length() < 20) {
                return "";
            }

            return extractedText;

        } catch (Exception e) {
            log.info("提取列表页概要失败: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 智能点击列表项 - 支持多种策略
     */
    private BrowserResult smartClickListItem(String pageId, CrawlerSettings settings, int index, String detailUrl) {
        log.info("开始智能点击列表项，索引: {}", index);

        // 策略1: 使用配置的点击路径
        if (StrUtil.isNotEmpty(settings.getClickListPath())) {
            log.info("策略1: 使用配置的点击路径: {}", settings.getClickListPath());

            // 首先尝试原始配置
            BrowserResult result = browserService.clickListItem(pageId, settings.getClickListPath(), index);
            if (result.isSuccess()) {
                // 验证是否真的跳转了
                if (verifyPageNavigation(pageId, detailUrl)) {
                    log.info("配置的点击路径成功并已跳转");
                    return result;
                } else {
                    log.error("配置的点击路径点击成功但未跳转，尝试优化选择器");
                }
            } else {
                log.error("配置的点击路径失败: {}", result.getMessage());
            }

            // 如果原始配置失败或未跳转，尝试优化选择器
            String optimizedSelector = optimizeClickSelector(settings.getClickListPath());
            if (!StrUtil.equals(optimizedSelector, settings.getClickListPath())) {
                log.info("尝试优化后的选择器: {}", optimizedSelector);
                result = browserService.clickListItem(pageId, optimizedSelector, index);
                if (result.isSuccess() && verifyPageNavigation(pageId, detailUrl)) {
                    log.info("优化选择器成功并已跳转");
                    return result;
                }
            }
        }

        // 策略2: 智能检测可点击的链接
        log.info("策略2: 智能检测可点击的链接");
        String[] smartSelectors = {
            ".sse_list_1>dl>dd>a",        // 针对当前网站的优化选择器
            ".sse_list_1 a[href]",        // 该区域内的所有链接
            "dd a[href]",                 // dd中的链接
            "li a[href]",                 // li中的链接
            "tr a[href]",                 // 表格行中的链接
            ".list-item a[href]",         // 列表项中的链接
            ".title a[href]",             // 标题链接
            "a[href]",                    // 所有有href的链接
            "[onclick]",                  // 有onclick事件的元素
            ".clickable"                  // 可点击的元素
        };

        for (String selector : smartSelectors) {
            try {
                log.info("尝试智能选择器: {}", selector);
                BrowserResult result = browserService.clickListItem(pageId, selector, index);
                if (result.isSuccess()) {
                    // 验证是否真的跳转了
                    if (verifyPageNavigation(pageId, detailUrl)) {
                        log.info("智能选择器成功并已跳转: {}", selector);
                        return result;
                    } else {
                        log.error("智能选择器点击成功但未跳转: {}", selector);
                    }
                } else {
                    log.info("智能选择器点击失败: {} - {}", selector, result.getMessage());
                }
            } catch (Exception e) {
                log.info("智能选择器异常: {} - {}", selector, e.getMessage());
            }
        }

        // 策略3: 如果有详情URL，尝试直接导航
        if (StrUtil.isNotEmpty(detailUrl)) {
            log.info("策略3: 直接导航到详情URL: {}", detailUrl);
            try {
                // 构建完整的URL
                String fullUrl = detailUrl;
                if (!detailUrl.startsWith("http")) {
                    // 如果是相对路径，构建完整URL
                    String baseUrl = getCurrentPageBaseUrl(pageId);
                    if (StrUtil.isNotEmpty(baseUrl)) {
                        if (detailUrl.startsWith("/")) {
                            // 绝对路径
                            fullUrl = baseUrl + detailUrl;
                        } else {
                            // 相对路径
                            fullUrl = baseUrl + "/" + detailUrl;
                        }
                    }
                }

                log.info("导航到完整URL: {}", fullUrl);
                BrowserResult navResult = browserService.navigate(fullUrl, null, 30000);
                if (navResult.isSuccess()) {
                    // 验证导航是否成功
                    if (verifyPageNavigation(pageId, fullUrl)) {
                        log.info("直接导航成功并已跳转");
                        return BrowserResult.success();
                    } else {
                        log.error("直接导航成功但页面未跳转");
                    }
                } else {
                    log.error("直接导航失败: {}", navResult.getMessage());
                }
            } catch (Exception e) {
                log.info("直接导航异常: {}", e.getMessage());
            }
        }

        return BrowserResult.error("所有点击策略都失败了");
    }

    /**
     * 在新标签页处理详情页
     */
    private CrawlerResult processDetailPageInNewTab(String pageId, String detailUrl, CrawlerSettings settings, Map<String, String> headers) {
        String newPageId = pageId + "_detail_" + System.currentTimeMillis();

        try {
            log.info("在新标签页打开详情页: {}", detailUrl);

            // 在新标签页打开详情页
            BrowserResult navResult = browserService.navigate(detailUrl, headers, settings.getTimeout());
            if (!navResult.isSuccess()) {
                return CrawlerResult.error("无法打开详情页: " + navResult.getMessage());
            }

            // 等待页面加载
            Thread.sleep(2000);

            // 处理详情页内容 - 复用现有的处理逻辑
            CrawlerResult result = processDetailPageInSamePage(newPageId, settings, headers);

            return result;

        } catch (Exception e) {
            log.error("在新标签页处理详情页失败: {}", detailUrl, e);
            return CrawlerResult.error("处理详情页失败: " + e.getMessage());
        } finally {
            // 关闭新标签页 - 这里暂时不关闭，因为使用的是同一个页面
            // 在实际实现中，如果真的需要新标签页，需要在 PlaywrightBrowserService 中添加相应方法
            log.info("详情页处理完成");
        }
    }

    /**
     * 智能提取附件 - 支持多种策略
     */
    private List<Map<String, Object>> extractAttachmentsIntelligently(String pageId, CrawlerSettings settings, String baseUrl) {
        List<Map<String, Object>> allAttachments = new ArrayList<>();

        // 策略1: 使用配置的附件选择器
        if (StrUtil.isNotEmpty(settings.getGetFile())) {
            log.info("策略1: 使用配置的附件选择器: {}", settings.getGetFile());
            BrowserResult filesResult = browserService.extractFiles(pageId, settings.getGetFile());
            if (filesResult.isSuccess()) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> configAttachments = (List<Map<String, Object>>) filesResult.get("files");
                log.info("配置选择器找到{}个附件", configAttachments.size());
                allAttachments.addAll(configAttachments);
            } else {
                log.error("配置选择器提取附件失败: " + filesResult.get("error"));
            }
        }

        // 策略2: 智能检测所有附件
        log.info("策略2: 智能检测所有附件");
        BrowserResult smartResult = browserService.detectAllAttachments(pageId, baseUrl);
        if (smartResult.isSuccess()) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> smartAttachments = (List<Map<String, Object>>) smartResult.get("attachments");
            log.info("智能检测找到{}个附件", smartAttachments.size());

            // 去重合并
            for (Map<String, Object> smartAttachment : smartAttachments) {
                boolean isDuplicate = false;
                String smartUrl = (String) smartAttachment.get("url");

                for (Map<String, Object> existing : allAttachments) {
                    String existingUrl = (String) existing.get("url");
                    if (StrUtil.equals(smartUrl, existingUrl)) {
                        isDuplicate = true;
                        break;
                    }
                }

                if (!isDuplicate) {
                    allAttachments.add(smartAttachment);
                }
            }
        }

        // 策略3: 使用通用附件选择器
        if (allAttachments.isEmpty()) {
            log.info("策略3: 使用通用附件选择器");
            String[] commonAttachmentSelectors = {
                "a[href*='.pdf']",                    // PDF文件
                "a[href*='.doc']",                    // Word文档
                "a[href*='.docx']",                   // Word文档
                "a[href*='.xls']",                    // Excel文件
                "a[href*='.xlsx']",                   // Excel文件
                "a[href*='.ppt']",                    // PowerPoint文件
                "a[href*='.pptx']",                   // PowerPoint文件
                "a[href*='.zip']",                    // 压缩文件
                "a[href*='.rar']",                    // 压缩文件
                "a[href*='download']",                // 下载链接
                "a[href*='attachment']",              // 附件链接
                "a[href*='file']",                    // 文件链接
                ".attachment a",                      // 附件区域的链接
                ".download a",                        // 下载区域的链接
                ".file-list a",                       // 文件列表的链接
                "[download]"                          // 有download属性的元素
            };

            for (String selector : commonAttachmentSelectors) {
                try {
                    BrowserResult result = browserService.extractFiles(pageId, selector);
                    if (result.isSuccess()) {
                        @SuppressWarnings("unchecked")
                        List<Map<String, Object>> files = (List<Map<String, Object>>) result.get("files");
                        if (!files.isEmpty()) {
                            log.info("通用选择器 '{}' 找到{}个附件", selector, files.size());

                            // 去重合并
                            for (Map<String, Object> file : files) {
                                boolean isDuplicate = false;
                                String fileUrl = (String) file.get("url");

                                for (Map<String, Object> existing : allAttachments) {
                                    String existingUrl = (String) existing.get("url");
                                    if (StrUtil.equals(fileUrl, existingUrl)) {
                                        isDuplicate = true;
                                        break;
                                    }
                                }

                                if (!isDuplicate) {
                                    file.put("detectionMethod", "通用选择器: " + selector);
                                    allAttachments.add(file);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    log.info("通用选择器 '{}' 失败: {}", selector, e.getMessage());
                }
            }
        }

        // 策略4: 如果仍然没有找到附件，尝试分析页面中的所有链接
        if (allAttachments.isEmpty()) {
            log.info("策略4: 分析页面中的所有链接");
            try {
                BrowserResult allLinksResult = browserService.extractFiles(pageId, "a[href]");
                if (allLinksResult.isSuccess()) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> allLinks = (List<Map<String, Object>>) allLinksResult.get("files");

                    // 过滤出可能的附件链接
                    for (Map<String, Object> link : allLinks) {
                        String url = (String) link.get("url");
                        String text = (String) link.get("text");

                        if (isPossibleAttachment(url, text)) {
                            link.put("detectionMethod", "链接分析");
                            allAttachments.add(link);
                        }
                    }

                    if (!allAttachments.isEmpty()) {
                        log.info("链接分析找到{}个可能的附件", allAttachments.size());
                    }
                }
            } catch (Exception e) {
                log.info("链接分析失败: {}", e.getMessage());
            }
        }

        log.info("智能附件提取完成，总共找到{}个附件", allAttachments.size());
        return allAttachments;
    }

    /**
     * 判断链接是否可能是附件
     */
    private boolean isPossibleAttachment(String url, String text) {
        if (StrUtil.isEmpty(url)) {
            return false;
        }

        // 检查URL中的文件扩展名
        String[] fileExtensions = {".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
                                  ".zip", ".rar", ".7z", ".tar", ".gz", ".txt", ".csv"};
        for (String ext : fileExtensions) {
            if (url.toLowerCase().contains(ext.toLowerCase())) {
                return true;
            }
        }

        // 检查URL中的关键词
        String[] urlKeywords = {"download", "attachment", "file", "doc", "document"};
        for (String keyword : urlKeywords) {
            if (url.toLowerCase().contains(keyword.toLowerCase())) {
                return true;
            }
        }

        // 检查链接文本中的关键词
        if (StrUtil.isNotEmpty(text)) {
            String[] textKeywords = {"下载", "附件", "文件", "文档", "报告", "通知", "公告",
                                   "download", "attachment", "file", "document", "report"};
            for (String keyword : textKeywords) {
                if (text.toLowerCase().contains(keyword.toLowerCase())) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 验证页面是否真的跳转了
     */
    private boolean verifyPageNavigation(String pageId, String expectedUrl) {
        try {
            Thread.sleep(1000); // 等待页面跳转

            Page page = browserService.getPage(pageId);
            if (page == null) {
                return false;
            }

            String currentUrl = page.url();
            log.info("验证页面跳转 - 当前URL: {}, 期望URL: {}", currentUrl, expectedUrl);

            // 如果期望URL为空，只要URL发生变化就认为跳转成功
            if (StrUtil.isEmpty(expectedUrl)) {
                return true; // 无法验证，假设成功
            }

            // 检查URL是否包含期望的路径
            if (expectedUrl.startsWith("http")) {
                return StrUtil.equals(currentUrl, expectedUrl) || currentUrl.contains(extractPath(expectedUrl));
            } else {
                return currentUrl.contains(expectedUrl);
            }

        } catch (Exception e) {
            log.info("验证页面跳转失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 从URL中提取路径部分
     */
    private String extractPath(String url) {
        try {
            if (url.startsWith("http")) {
                int pathStart = url.indexOf('/', 8); // 跳过 http:// 或 https://
                if (pathStart > 0) {
                    return url.substring(pathStart);
                }
            }
            return url;
        } catch (Exception e) {
            return url;
        }
    }

    /**
     * 优化点击选择器 - 确保选择到可点击的链接
     */
    private String optimizeClickSelector(String originalSelector) {
        if (StrUtil.isEmpty(originalSelector)) {
            return "a[href]";
        }

        // 如果选择器没有选择链接，尝试添加 a 标签
        if (!originalSelector.contains(" a") && !originalSelector.endsWith("a") && !originalSelector.contains("[href")) {
            // 常见的优化策略
            String[] optimizations = {
                originalSelector + " a",           // 在选择器后添加 a
                originalSelector + " a[href]",     // 在选择器后添加 a[href]
                originalSelector + ">a",           // 直接子元素 a
                originalSelector + " [href]"       // 任何有href的元素
            };

            // 返回第一个优化选择器
            for (String opt : optimizations) {
                log.info("生成优化选择器: {} -> {}", originalSelector, opt);
                return opt;
            }
        }

        return originalSelector;
    }

    /**
     * 获取当前页面的基础URL
     */
    private String getCurrentPageBaseUrl(String pageId) {
        try {
            Page page = browserService.getPage(pageId);
            if (page != null) {
                String currentUrl = page.url();
                if (StrUtil.isNotEmpty(currentUrl)) {
                    // 提取协议和域名部分
                    if (currentUrl.startsWith("http")) {
                        int pathStart = currentUrl.indexOf('/', 8); // 跳过 http:// 或 https://
                        if (pathStart > 0) {
                            return currentUrl.substring(0, pathStart);
                        } else {
                            return currentUrl;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.info("获取基础URL失败: {}", e.getMessage());
        }
        return "";
    }

    /**
     * 优化版：点击指定索引的列表项，避免重复查找
     */
    private BrowserResult clickListItemByIndex(String pageId, String listSelector, int index) {
        try {
            Page page = browserService.getPage(pageId);
            if (page == null) {
                return BrowserResult.error("页面不存在");
            }

            log.info("准备点击列表项，选择器: {}, 索引: {}", listSelector, index);

            // 使用JavaScript直接点击指定索引的列表项，并等待页面跳转
            Object result = page.evaluate(
                "([selector, index]) => {\n" +
                "  try {\n" +
                "    const elements = document.querySelectorAll(selector);\n" +
                "    console.log('找到元素数量:', elements.length);\n" +
                "    \n" +
                "    if (index >= elements.length) {\n" +
                "      return { success: false, message: '索引超出范围: ' + index + ', 总数: ' + elements.length };\n" +
                "    }\n" +
                "    \n" +
                "    const item = elements[index];\n" +
                "    console.log('目标元素:', item.tagName, item.textContent.substring(0, 50));\n" +
                "    \n" +
                "    // 尝试找到列表项中的链接\n" +
                "    const link = item.querySelector('a[href]');\n" +
                "    if (link) {\n" +
                "      console.log('找到链接:', link.href);\n" +
                "      \n" +
                "      // 记录当前URL\n" +
                "      const currentUrl = window.location.href;\n" +
                "      \n" +
                "      // 获取完整的目标URL\n" +
                "      let targetUrl = link.href;\n" +
                "      if (targetUrl.startsWith('/')) {\n" +
                "        // 相对路径，转换为绝对路径\n" +
                "        targetUrl = window.location.origin + targetUrl;\n" +
                "      }\n" +
                "      \n" +
                "      console.log('目标URL:', targetUrl);\n" +
                "      \n" +
                "      // 使用window.location.href进行跳转，而不是click\n" +
                "      window.location.href = targetUrl;\n" +
                "      \n" +
                "      return { \n" +
                "        success: true, \n" +
                "        message: '页面跳转成功', \n" +
                "        href: targetUrl,\n" +
                "        currentUrl: currentUrl\n" +
                "      };\n" +
                "    } else {\n" +
                "      console.log('未找到链接，直接点击列表项');\n" +
                "      // 如果没有链接，直接点击列表项\n" +
                "      item.click();\n" +
                "      return { success: true, message: '点击列表项成功' };\n" +
                "    }\n" +
                "  } catch (e) {\n" +
                "    console.error('点击失败:', e);\n" +
                "    return { success: false, message: e.message };\n" +
                "  }\n" +
                "}",
                new String[] { listSelector, String.valueOf(index) }
            );

            if (result != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> resultMap = (Map<String, Object>) result;

                if (Boolean.TRUE.equals(resultMap.get("success"))) {
                    log.info("点击成功: {}", resultMap.get("message"));

                    // 等待页面跳转完成
                    try {
                        String expectedUrl = (String) resultMap.get("href");
                        String originalUrl = (String) resultMap.get("currentUrl");

                        log.info("等待页面跳转，原始URL: {}, 目标URL: {}", originalUrl, expectedUrl);

                        // 等待页面跳转
                        Thread.sleep(3000);

                        // 等待页面加载完成
                        page.waitForLoadState(com.microsoft.playwright.options.LoadState.DOMCONTENTLOADED);

                        // 检查URL是否发生变化
                        String currentUrl = page.url();

                        if (expectedUrl != null) {
                            // 检查是否跳转到了目标URL
                            if (currentUrl.equals(expectedUrl) ||
                                (expectedUrl.contains("/") && currentUrl.contains(expectedUrl.substring(expectedUrl.lastIndexOf("/"))))) {
                                log.info("页面跳转成功，当前URL: {}", currentUrl);
                            } else {
                                log.error("页面可能未跳转，当前URL: {}, 期望URL: {}", currentUrl, expectedUrl);
                                // 尝试直接导航到目标URL
                                log.info("尝试直接导航到目标URL: {}", expectedUrl);
                                page.navigate(expectedUrl);
                                page.waitForLoadState(com.microsoft.playwright.options.LoadState.DOMCONTENTLOADED);
                                Thread.sleep(2000);
                                log.info("直接导航后的URL: {}", page.url());
                            }
                        } else {
                            log.info("页面跳转完成，当前URL: {}", currentUrl);
                        }

                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    } catch (Exception e) {
                        log.error("页面跳转检测失败: {}", e.getMessage());
                    }

                    return BrowserResult.success()
                            .put("message", resultMap.get("message"))
                            .put("href", resultMap.get("href"));
                } else {
                    log.error("点击失败: {}", resultMap.get("message"));
                    return BrowserResult.error((String) resultMap.get("message"));
                }
            }

            return BrowserResult.error("JavaScript执行失败");
        } catch (Exception e) {
            log.error("点击列表项失败: {}", e.getMessage());
            return BrowserResult.error("点击列表项失败: " + e.getMessage());
        }
    }

    /**
     * 等待详情页内容加载完成
     */
    private void waitForDetailPageLoad(String pageId) {
        try {
            Page page = browserService.getPage(pageId);
            if (page != null) {
                // 等待DOM内容加载完成
                page.waitForLoadState(com.microsoft.playwright.options.LoadState.DOMCONTENTLOADED);

                // 等待网络空闲
                try {
                    page.waitForLoadState(com.microsoft.playwright.options.LoadState.NETWORKIDLE);
                } catch (Exception e) {
                    log.info("等待网络空闲超时，继续处理: {}", e.getMessage());
                }

                // 额外等待一段时间确保动态内容加载
                Thread.sleep(2000);

                log.info("详情页内容加载完成，当前URL: {}", page.url());
            }
        } catch (Exception e) {
            log.info("等待详情页加载失败: {}", e.getMessage());
        }
    }

    /**
     * 等待列表页内容加载完成
     */
    private void waitForListPageLoad(String pageId) {
        try {
            Page page = browserService.getPage(pageId);
            if (page != null) {
                // 等待DOM内容加载完成
                page.waitForLoadState(com.microsoft.playwright.options.LoadState.DOMCONTENTLOADED);

                // 额外等待一段时间确保列表内容重新加载
                Thread.sleep(1500);

                log.info("列表页内容加载完成");
            }
        } catch (Exception e) {
            log.info("等待列表页加载失败: {}", e.getMessage());
        }
    }

}
