package com.icarus.common.cotnode.ab.cmtt.vectorNode.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.common.cotnode.ab.cmtt.vectorNode.entity.AivecChunk;
import com.icarus.common.cotnode.ab.cmtt.vectorNode.mapper.AivecChunkMapper;
import com.icarus.common.cotnode.ab.cmtt.vectorNode.service.IAivecChunkService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 支持QA格式的文件切片表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Service
@RequiredArgsConstructor
public class AivecChunkServiceImpl extends ServiceImpl<AivecChunkMapper, AivecChunk> implements IAivecChunkService {

    private final AivecChunkMapper chunkMapper;

    @Override
    public List<AivecChunk> searchSimilarVectors(String queryVector, Integer limit, Float threshold,String baseId,String taskId) {
        return chunkMapper.searchSimilarVectors(queryVector, limit, threshold,baseId,taskId);
    }

    @Override
    public List<AivecChunk> getChunksBySystemId(String systemId) {
        return chunkMapper.getChunksBySystemId(systemId);
    }
}
