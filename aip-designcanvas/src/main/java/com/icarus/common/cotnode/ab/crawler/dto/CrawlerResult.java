package com.icarus.common.cotnode.ab.crawler.dto;

import java.util.HashMap;
import java.util.Map;

/**
 * 爬虫操作结果封装类
 */
public class CrawlerResult {
    private boolean success;
    private String message;
    private Map<String, Object> data;

    private CrawlerResult(boolean success, String message) {
        this.success = success;
        this.message = message;
        this.data = new HashMap<>();
    }

    public static CrawlerResult success() {
        return new CrawlerResult(true, "爬取成功");
    }

    public static CrawlerResult success(String message) {
        return new CrawlerResult(true, message);
    }

    public static CrawlerResult error(String message) {
        return new CrawlerResult(false, message);
    }

    public CrawlerResult put(String key, Object value) {
        this.data.put(key, value);
        return this;
    }

    public boolean isSuccess() {
        return success;
    }

    public String getMessage() {
        return message;
    }

    public Map<String, Object> getData() {
        return data;
    }

    @SuppressWarnings("unchecked")
    public <T> T get(String key) {
        return (T) data.get(key);
    }

    public String getString(String key) {
        Object value = data.get(key);
        return value != null ? value.toString() : null;
    }

    public Integer getInteger(String key) {
        Object value = data.get(key);
        if (value instanceof Integer) {
            return (Integer) value;
        } else if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    public Boolean getBoolean(String key) {
        Object value = data.get(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        } else if (value instanceof String) {
            return Boolean.parseBoolean((String) value);
        }
        return false;
    }
}
