package com.icarus.common.cotnode;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.icarus.common.runtime.NodeContext;
import com.icarus.enums.SliceDirection;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class SubArrayNode extends BaseNode{

    @Getter
    @Setter
    private Object[] result;

    private static final String INPUT_MAP_KEY = "array";


    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【SubArrayNode】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
        try{
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            nodeContext.setInputs(new JSONObject(resolvedInputs));
            checkInputs(resolvedInputs);

            // 解析属性配置
            SubArrayNode.Properties resolvedProperties = resolveProperties(context, SubArrayNode.Properties.class);
            //校验参数
            JSONObject nodePro = new JSONObject();
            nodePro.set("sliceDirection", resolvedProperties.getSliceDirection());
            nodePro.set("count", resolvedProperties.getCount());
            nodeContext.setProperties(nodePro);
            // 设置结果并更新状态
            JSONArray arrays = (JSONArray) resolvedInputs.get(INPUT_MAP_KEY);
            result = takeElements(arrays, resolvedProperties.getCount(), resolvedProperties.getSliceDirection());
            nodeContext.setStatus("completed");
            // 处理输出参数
            processOutputs(context);
        }catch (Exception e) {
            log.error("数组截取节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }
        log.info("数组截取节点执行结束，节点ID：{}", nodeId);
        return context;
    }

    private void checkInputs(Map<String, Object> resolvedInputs) {
        // 1. 检查整体输入是否为空
        if (CollectionUtils.isEmpty(resolvedInputs)) {
            throw new IllegalArgumentException("输入参数不能为空");
        }

        // 2. 检查关键字段是否存在且是JSONArray类型
        if (!resolvedInputs.containsKey(INPUT_MAP_KEY)) {
            throw new IllegalArgumentException("缺少必要参数: " + INPUT_MAP_KEY);
        }

        Object inputValue = resolvedInputs.get(INPUT_MAP_KEY);
        if (!(inputValue instanceof JSONArray)) {
            throw new IllegalArgumentException(INPUT_MAP_KEY + "必须是Array类型");
        }

        // 3. 检查数组是否为空
        JSONArray inputArray = (JSONArray) inputValue;
        if (inputArray.isEmpty()) {
            throw new IllegalArgumentException(INPUT_MAP_KEY + "数组不能为空");
        }
    }

    /**
     * 截取数组的子元素
     * @param array 原始数组
     * @param count 要截取的元素数量
     * @param sliceDirection FROM_START表示从头部截取，FROM_END表示从尾部截取
     * @return 截取后的数组，count不合法则返回异常
     */
    public Object[] takeElements(JSONArray array, int count, String sliceDirection) {
        if (count <= 0) {
            throw new IllegalArgumentException("count不能小于等于0");
        }
        Object[] objects = array.toArray();

        // 处理count超过数组长度的情况
        int actualCount = Math.min(count, objects.length);

        if (SliceDirection.FROM_START.name().equals(sliceDirection)) {
            // 从头部截取
            return Arrays.copyOfRange(objects, 0, actualCount);
        } else {
            // 从尾部截取
            return Arrays.copyOfRange(objects, objects.length - actualCount, objects.length);
        }
    }

    @Data
    @NoArgsConstructor
    public static class Properties {
        //截取方向
        private String sliceDirection;
        //截取数量
        private Integer count;

    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    @Override
    public String desc() {
        return "数组截取节点";
    }
}
