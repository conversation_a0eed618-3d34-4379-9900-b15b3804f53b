package com.icarus.common.cotnode;

import cn.hutool.json.JSONObject;
import com.icarus.common.runtime.NodeContext;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.tmatesoft.svn.core.*;
import org.tmatesoft.svn.core.auth.ISVNAuthenticationManager;
import org.tmatesoft.svn.core.internal.io.dav.DAVRepositoryFactory;
import org.tmatesoft.svn.core.internal.io.fs.FSRepositoryFactory;
import org.tmatesoft.svn.core.internal.io.svn.SVNRepositoryFactoryImpl;
import org.tmatesoft.svn.core.internal.wc.DefaultSVNOptions;
import org.tmatesoft.svn.core.wc.*;

import java.io.File;
import java.nio.file.Paths;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class SVNOperateNode extends BaseNode {

    String svnPath = "/aip-home/home/<USER>/";
    @Getter
    @Setter
    private String result;

    static {
        // 初始化SVN库
        setupLibrary();
    }

    private static void setupLibrary() {
        DAVRepositoryFactory.setup();
        SVNRepositoryFactoryImpl.setup();
        FSRepositoryFactory.setup();
    }

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【SVN操作】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);

        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            if (resolvedInputs == null) {
                throw new IllegalArgumentException("输入参数不能为空");
            }

            // 解析属性配置
            SVNOperateNode.Properties resolvedProperties = resolveProperties(context, SVNOperateNode.Properties.class);
            JSONObject propertiesJson = new JSONObject();
            propertiesJson.set("svnUrl", resolvedProperties.getSvnUrl());
            propertiesJson.set("localPath", resolvedProperties.getLocalPath());
            propertiesJson.set("operation", resolvedProperties.getOperation());
            propertiesJson.set("username", resolvedProperties.getUsername());
            propertiesJson.set("password", resolvedProperties.getPassword());
            propertiesJson.set("commitMessage", resolvedProperties.getCommitMessage());
            propertiesJson.set("revision", resolvedProperties.getRevision());
            nodeContext.setProperties(propertiesJson);

            // 验证必要的输入参数
            String operation = resolvedProperties.getOperation();
            String localPath  = Paths.get(svnPath, resolvedProperties.getLocalPath()).toString();
            if (operation == null || operation.trim().isEmpty()) {
                throw new IllegalArgumentException("operation 是必需的输入参数");
            }
            if (localPath == null || localPath.trim().isEmpty()) {
                throw new IllegalArgumentException("localPath 是必需的输入参数");
            }

            // 记录输入参数到上下文
            nodeContext.setInputs(new JSONObject(resolvedInputs));

            // 根据操作类型执行相应的SVN命令
            result = executeSVNOperation(resolvedProperties);

            // 设置成功状态和输出
            nodeContext.setStatus("completed");

            // 处理输出映射
            processOutputs(context);

        } catch (Exception e) {
            log.error("SVN操作失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        log.info("SVN操作节点执行结束，节点ID：{}", getId());
        return context;
    }

    private String executeSVNOperation(Properties properties) {
        String operation = properties.getOperation().toLowerCase().trim();

        switch (operation) {
            case "checkout":
                return checkoutRepository(properties);
            case "update":
                return updateRepository(properties);
            case "commit":
                return commitChanges(properties);
            case "status":
                return getStatus(properties);
            case "add":
                return addFiles(properties);
            case "info":
                return getInfo(properties);
            case "log":
                return getLog(properties);
            case "revert":
                return revertChanges(properties);
            case "cleanup":
                return cleanup(properties);
            default:
                throw new IllegalArgumentException("不支持的SVN操作: " + operation);
        }
    }

    private String checkoutRepository(Properties properties) {
        try {
            String svnUrl = properties.getSvnUrl();
            String localPath = Paths.get(svnPath, properties.getLocalPath()).toString();

            if (svnUrl == null || svnUrl.trim().isEmpty()) {
                throw new IllegalArgumentException("checkout操作需要svnUrl参数");
            }

            File localDir = new File(localPath);
            SVNURL repositoryURL = SVNURL.parseURIEncoded(svnUrl);

            SVNClientManager clientManager = createClientManager(properties);
            SVNUpdateClient updateClient = clientManager.getUpdateClient();

            // 设置忽略外部引用
            updateClient.setIgnoreExternals(false);

            long revision = updateClient.doCheckout(repositoryURL, localDir,
                    SVNRevision.HEAD, SVNRevision.HEAD, SVNDepth.INFINITY, false);

            String successMsg = "SVN仓库检出成功，本地路径: " + localPath + "，版本: " + revision;
            log.info(successMsg);
            return successMsg;

        } catch (SVNException e) {
            throw new RuntimeException("检出仓库失败: " + e.getMessage(), e);
        }
    }

    private String updateRepository(Properties properties) {
        try {
            String localPath = Paths.get(svnPath, properties.getLocalPath()).toString();
            File localDir = new File(localPath);

            if (!localDir.exists()) {
                throw new IllegalArgumentException("指定路径不存在: " + localPath);
            }

            SVNClientManager clientManager = createClientManager(properties);
            SVNUpdateClient updateClient = clientManager.getUpdateClient();

            long[] revisions = updateClient.doUpdate(new File[]{localDir},
                    SVNRevision.HEAD, SVNDepth.INFINITY, false, false);

            String successMsg = "SVN更新成功，版本: " + revisions[0];
            log.info(successMsg);
            return successMsg;

        } catch (SVNException e) {
            throw new RuntimeException("更新失败: " + e.getMessage(), e);
        }
    }

    private String commitChanges(Properties properties) {
        try {
            String localPath = Paths.get(svnPath, properties.getLocalPath()).toString();
            String commitMessage = properties.getCommitMessage();
            File localDir = new File(localPath);

            if (!localDir.exists()) {
                throw new IllegalArgumentException("指定路径不存在: " + localPath);
            }

            if (commitMessage == null || commitMessage.trim().isEmpty()) {
                throw new IllegalArgumentException("commit操作需要commitMessage参数");
            }

            SVNClientManager clientManager = createClientManager(properties);
            SVNCommitClient commitClient = clientManager.getCommitClient();

            SVNCommitInfo commitInfo = commitClient.doCommit(new File[]{localDir},
                    false, commitMessage, null, null, false, false, SVNDepth.INFINITY);

            String successMsg = "SVN提交成功，版本: " + commitInfo.getNewRevision() +
                    "，提交信息: " + commitMessage;
            log.info(successMsg);
            return successMsg;

        } catch (SVNException e) {
            throw new RuntimeException("提交失败: " + e.getMessage(), e);
        }
    }

    private String getStatus(Properties properties) {
        try {
            String localPath = Paths.get(svnPath, properties.getLocalPath()).toString();
            File localDir = new File(localPath);

            if (!localDir.exists()) {
                throw new IllegalArgumentException("指定路径不存在: " + localPath);
            }

            SVNClientManager clientManager = createClientManager(properties);
            SVNStatusClient statusClient = clientManager.getStatusClient();

            StringBuilder statusInfo = new StringBuilder();
            statusInfo.append("SVN状态信息:\n");

            statusClient.doStatus(localDir, SVNRevision.WORKING, SVNDepth.INFINITY,
                    false, false, false, false, new ISVNStatusHandler() {
                        @Override
                        public void handleStatus(SVNStatus status) throws SVNException {
                            SVNStatusType contentsStatus = status.getContentsStatus();
                            if (contentsStatus != SVNStatusType.STATUS_NORMAL &&
                                    contentsStatus != SVNStatusType.STATUS_IGNORED) {
                                statusInfo.append(contentsStatus.toString())
                                        .append(" ")
                                        .append(status.getFile().getPath())
                                        .append("\n");
                            }
                        }
                    }, null);

            if (statusInfo.length() == "SVN状态信息:\n".length()) {
                statusInfo.append("工作目录是干净的\n");
            }

            String result = statusInfo.toString();
            log.info(result);
            return result;

        } catch (SVNException e) {
            throw new RuntimeException("获取状态失败: " + e.getMessage(), e);
        }
    }

    private String addFiles(Properties properties) {
        try {
            String localPath = Paths.get(svnPath, properties.getLocalPath()).toString();
            File localDir = new File(localPath);

            if (!localDir.exists()) {
                throw new IllegalArgumentException("指定路径不存在: " + localPath);
            }

            SVNClientManager clientManager = createClientManager(properties);
            SVNWCClient wcClient = clientManager.getWCClient();

            wcClient.doAdd(localDir, false, false, false, SVNDepth.INFINITY,
                    false, false, true);

            String successMsg = "SVN添加文件成功";
            log.info(successMsg);
            return successMsg;

        } catch (SVNException e) {
            throw new RuntimeException("添加文件失败: " + e.getMessage(), e);
        }
    }

    private String getInfo(Properties properties) {
        try {
            String localPath = Paths.get(svnPath, properties.getLocalPath()).toString();
            File localDir = new File(localPath);

            if (!localDir.exists()) {
                throw new IllegalArgumentException("指定路径不存在: " + localPath);
            }

            SVNClientManager clientManager = createClientManager(properties);
            SVNWCClient wcClient = clientManager.getWCClient();

            SVNInfo info = wcClient.doInfo(localDir, SVNRevision.WORKING);

            StringBuilder infoStr = new StringBuilder();
            infoStr.append("SVN信息:\n");
            infoStr.append("URL: ").append(info.getURL()).append("\n");
            infoStr.append("版本: ").append(info.getRevision().getNumber()).append("\n");
            infoStr.append("最后修改作者: ").append(info.getAuthor()).append("\n");
            infoStr.append("最后修改时间: ").append(info.getCommittedDate()).append("\n");

            String result = infoStr.toString();
            log.info(result);
            return result;

        } catch (SVNException e) {
            throw new RuntimeException("获取信息失败: " + e.getMessage(), e);
        }
    }

    private String getLog(Properties properties) {
        try {
            String localPath = Paths.get(svnPath, properties.getLocalPath()).toString();
            File localDir = new File(localPath);

            if (!localDir.exists()) {
                throw new IllegalArgumentException("指定路径不存在: " + localPath);
            }

            SVNClientManager clientManager = createClientManager(properties);
            SVNLogClient logClient = clientManager.getLogClient();

            StringBuilder logInfo = new StringBuilder();
            logInfo.append("SVN日志信息:\n");

            logClient.doLog(new File[]{localDir}, SVNRevision.HEAD, SVNRevision.create(1),
                    SVNRevision.HEAD, false, true, 10, new ISVNLogEntryHandler() {
                        @Override
                        public void handleLogEntry(SVNLogEntry logEntry) throws SVNException {
                            logInfo.append("版本: ").append(logEntry.getRevision()).append("\n");
                            logInfo.append("作者: ").append(logEntry.getAuthor()).append("\n");
                            logInfo.append("时间: ").append(logEntry.getDate()).append("\n");
                            logInfo.append("信息: ").append(logEntry.getMessage()).append("\n");
                            logInfo.append("---\n");
                        }
                    });

            String result = logInfo.toString();
            log.info(result);
            return result;

        } catch (SVNException e) {
            throw new RuntimeException("获取日志失败: " + e.getMessage(), e);
        }
    }

    private String revertChanges(Properties properties) {
        try {
            String localPath = Paths.get(svnPath, properties.getLocalPath()).toString();
            File localDir = new File(localPath);

            if (!localDir.exists()) {
                throw new IllegalArgumentException("指定路径不存在: " + localPath);
            }

            SVNClientManager clientManager = createClientManager(properties);
            SVNWCClient wcClient = clientManager.getWCClient();

            wcClient.doRevert(new File[]{localDir}, SVNDepth.INFINITY, null);

            String successMsg = "SVN还原成功";
            log.info(successMsg);
            return successMsg;

        } catch (SVNException e) {
            throw new RuntimeException("还原失败: " + e.getMessage(), e);
        }
    }

    private String cleanup(Properties properties) {
        try {
            String localPath = Paths.get(svnPath, properties.getLocalPath()).toString();
            File localDir = new File(localPath);

            if (!localDir.exists()) {
                throw new IllegalArgumentException("指定路径不存在: " + localPath);
            }

            SVNClientManager clientManager = createClientManager(properties);
            SVNWCClient wcClient = clientManager.getWCClient();

            wcClient.doCleanup(localDir);

            String successMsg = "SVN清理成功";
            log.info(successMsg);
            return successMsg;

        } catch (SVNException e) {
            throw new RuntimeException("清理失败: " + e.getMessage(), e);
        }
    }

    private SVNClientManager createClientManager(Properties properties) {
        DefaultSVNOptions options = SVNWCUtil.createDefaultOptions(true);
        SVNClientManager clientManager;

        String username = properties.getUsername();
        String password = properties.getPassword();

        if (username != null && !username.trim().isEmpty() &&
                password != null && !password.trim().isEmpty()) {
            ISVNAuthenticationManager authManager = SVNWCUtil.createDefaultAuthenticationManager(username, password.toCharArray());
            clientManager = SVNClientManager.newInstance(options, authManager);
        } else {
            clientManager = SVNClientManager.newInstance(options);
        }

        return clientManager;
    }

    @Data
    @NoArgsConstructor
    public static class Properties {
        private String svnUrl;          // SVN仓库地址
        private String localPath;       // 本地路径
        private String operation;       // 操作方式：checkout、update、commit、status、add、info、log、revert、cleanup
        private String username;        // 用户名
        private String password;        // 密码
        private String commitMessage;   // 提交信息
        private String revision;        // 版本号
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    @Override
    public String desc() {
        return "SVN操作节点";
    }
}