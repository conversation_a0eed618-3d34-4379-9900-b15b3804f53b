package com.icarus.common.cotnode;

import cn.hutool.json.JSONObject;
import com.icarus.common.runtime.NodeContext;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.ArrayList;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class ReadDirectoryNode extends BaseNode {
    
    @Getter
    @Setter
    private String[] result;

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【读取目录】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
        
        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            if (resolvedInputs == null) {
                throw new IllegalArgumentException("输入参数不能为空");
            }

            // 验证必要的输入参数
            String directoryPath = (String) resolvedInputs.get("directoryPath");
            if (directoryPath == null) {
                throw new IllegalArgumentException("directoryPath 是必需的输入参数");
            }

            // 规范化路径，处理不同操作系统的路径分隔符
            directoryPath = new File(directoryPath).getCanonicalPath();
            nodeContext.setInputs(new JSONObject(resolvedInputs));
            
            // 检查目录是否存在和权限
            File directory = new File(directoryPath);
            if (!directory.exists()) {
                throw new IllegalArgumentException("目录不存在: " + directoryPath);
            }
            if (!directory.isDirectory()) {
                throw new IllegalArgumentException("指定路径不是目录: " + directoryPath);
            }
            if (!directory.canRead()) {
                throw new IllegalArgumentException("没有目录的读取权限: " + directoryPath + "，请检查文件系统权限");
            }

            // 获取目录下的所有文件和文件夹
            File[] files = directory.listFiles();
            if (files != null) {
                ArrayList<String> validPaths = new ArrayList<>();
                for (File file : files) {
                    try {
                        // 使用规范化的路径
                        validPaths.add(file.getCanonicalPath());
                    } catch (Exception e) {
                        log.warn("无法获取文件的规范路径: " + file.getPath(), e);
                    }
                }
                result = validPaths.toArray(new String[0]);
            } else {
                result = new String[0];
                log.warn("目录为空或无法访问: {}", directoryPath);
            }

            // 设置处理结果
            nodeContext.setStatus("completed");
            
            // 处理输出
            processOutputs(context);

        } catch (Exception e) {
            log.error("读取目录节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        log.info("读取目录节点执行结束，节点ID：{}", getId());
        return context;
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    /**
     * 节点属性定义
     */
    @Data
    @NoArgsConstructor
    public static class Properties {
        /**
         * 目录名称
         */
        private String directoryPath;
    }

    @Override
    public String desc() {
        return "读取目录节点";
    }

}