package com.icarus.common.cotnode.ab.md;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import com.icarus.common.cotnode.BaseNode;
import com.icarus.common.minio.MinioUtil;
import com.icarus.common.runtime.NodeContext;
import com.icarus.sdk.springboot.base.utils.SpringContextUtils;
import com.icarus.service.FileStorageService;
import com.icarus.utils.FileUtil;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.sl.usermodel.Placeholder;
import org.apache.poi.xslf.usermodel.*;
import org.springframework.http.MediaType;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * @ClassName PptToMarkdownNode
 * @Description PPT转Markdown节点
 * <AUTHOR>
 * @Date 2025/3/8
 * @Version 1.0
 **/
@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class Ppt2MarkdownNode extends BaseNode {

    @Getter
    @Setter
    private String result;

    // 临时变量，用于在方法间传递状态
    private String downloadDir;
    private String originalBucketName;
    private boolean isOriginalFileUrl;

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【PPT转Markdown】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
        File sourceFile = null;
        String originalFilePath = null;

        try {
            // 解析属性配置
            Properties resolvedProperties = resolveProperties(context, Properties.class);
            JSONObject propertiesJson = new JSONObject();
            propertiesJson.set("filePath", resolvedProperties.getFilePath());
            nodeContext.setProperties(propertiesJson);

            // 获取MinioUtil实例
            MinioUtil minioUtil = SpringUtil.getBean(MinioUtil.class);

            // 验证必要的输入参数
            String pptFilePath = resolvedProperties.getFilePath();
            if (pptFilePath == null) {
                throw new IllegalArgumentException("filePath 是必需的参数");
            }

            // 保存原始文件路径信息，用于后续删除
            originalFilePath = pptFilePath;
            originalBucketName = resolvedProperties.getBucketName();
            if (StrUtil.isEmpty(originalBucketName)) {
                originalBucketName = minioUtil.getMinioConfig().getBucketName();
                resolvedProperties.setBucketName(originalBucketName);
            }

            // 获取PPT文件对象，支持MinIO下载
            sourceFile = getFileFromPathWithMinioSupport(pptFilePath, minioUtil);
            if (sourceFile == null) {
                throw new IllegalArgumentException("无法获取文件: " + pptFilePath);
            }

            // 验证文件类型
            String fileExtension = getFileExtension(sourceFile.getName()).toLowerCase();
            if (!"ppt".equals(fileExtension) && !"pptx".equals(fileExtension)) {
                throw new RuntimeException("不支持的文件类型：[" + fileExtension + "] ，仅支持PPT和PPTX格式。");
            }

            // 生成时间戳
            long timestamp = System.currentTimeMillis();

            // 生成Markdown文件路径
            String mdFilePath = convertFileExtension(sourceFile.getAbsolutePath(), "md", timestamp);

            // 生成图片保存目录
            String picturePath = getTmpDirectory(sourceFile.getAbsolutePath(), timestamp);
            File pictureDir = new File(picturePath);
            if (!pictureDir.exists()) {
                pictureDir.mkdirs();
            }
            // 执行PPT转Markdown
            convertPPTToMarkdown2(sourceFile.getAbsolutePath(), mdFilePath, picturePath);
            // 将生成的文件打压缩包
            String zipFilePath = zipFile(mdFilePath, picturePath);
            log.info("压缩包路径: {}", zipFilePath);

            // 生成MinIO对象名称
            String executeId = nodeContext.getExecuteId();
            String zipFileName = new File(zipFilePath).getName();
            String objectName = String.format("files/%s/%s/%s",
                    LocalDate.now(), executeId, zipFileName);

            // 上传文件到MinIO
            String uploadedPath = minioUtil.uploadFileWithOptions(
                    convertFileToMultipartFile(new File(zipFilePath)),
                    resolvedProperties.getBucketName(),
                    objectName,
                    null,
                    true,
                    null);

            if (uploadedPath == null) {
                throw new IllegalArgumentException("上传文件到MinIO失败");
            }

            log.info("文件上传成功，MinIO路径: {}", uploadedPath);

            // 删除本地ZIP文件
            File zipFile = new File(zipFilePath);
            if (zipFile.exists()) {
                boolean zipDeleted = zipFile.delete();
                if (zipDeleted) {
                    log.info("本地ZIP文件删除成功: {}", zipFilePath);
                } else {
                    log.warn("无法删除本地ZIP文件: {}", zipFilePath);
                }
            }

            // 删除原始PPT文件（如果是从MinIO下载的）
            if (originalFilePath != null && originalBucketName != null) {
                boolean deleteSuccess = false;
                if (isOriginalFileUrl) {
                    log.info("删除原始PPT文件(URL): {}", originalFilePath);
                    deleteSuccess = minioUtil.deleteFileFromUrl(originalFilePath);
                } else {
                    log.info("删除原始PPT文件: 存储桶={}, 对象={}", originalBucketName, originalFilePath);
                    deleteSuccess = minioUtil.deleteFile(originalBucketName, originalFilePath);
                }

                if (!deleteSuccess) {
                    log.warn("删除原始PPT文件失败: {}", originalFilePath);
                } else {
                    log.info("原始PPT文件删除成功");
                }
            }

            // 返回MinIO路径而不是本地路径
            result = uploadedPath;
            nodeContext.setStatus("completed");
            processOutputs(context);

        } catch (Exception e) {
            log.error("PPT转Markdown节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            // 清理临时文件
            cleanupTempFiles(sourceFile, downloadDir);
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        log.info("PPT转Markdown节点执行结束，节点ID：{}", getId());
        return context;
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filePath) {
        int lastDotIndex = filePath.lastIndexOf(".");
        if (lastDotIndex > 0) {
            return filePath.substring(lastDotIndex + 1);
        }
        return "";
    }

    /**
     * 获取文件对象，优先使用 FileStorageService，失败则尝试直接文件路径
     * 
     * @param filePath 文件路径
     * @return File对象
     */
    private File getFileFromPath(String filePath) {
        try {
            // 首先尝试通过 FileStorageService 获取
            FileStorageService service = SpringContextUtils.getBean(FileStorageService.class);
            InputStream inputStream = service.getFile(filePath);
            if (inputStream != null) {
                String fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
                return FileUtil.inputStreamToFile(inputStream, fileName);
            }
        } catch (Exception e) {
            log.debug("通过 FileStorageService 获取文件失败，尝试本地路径");
        }
        // 直接返回本地文件对象
        return new File(filePath);
    }

    /**
     * 获取文件对象，支持MinIO下载
     * 
     * @param filePath  文件路径
     * @param minioUtil MinIO工具类
     * @return File对象
     */
    private File getFileFromPathWithMinioSupport(String filePath, MinioUtil minioUtil) {
        try {
            // 创建临时下载目录
            String executeId = System.currentTimeMillis() + "";
            downloadDir = System.getProperty("java.io.tmpdir") + File.separator + "ppt2md-downloads" + File.separator
                    + executeId;
            File downloadDirFile = new File(downloadDir);
            if (!downloadDirFile.exists()) {
                downloadDirFile.mkdirs();
            }

            String downloadedFilePath;
            if (minioUtil.isHttpUrl(filePath)) {
                // 从URL中提取文件名和bucket信息
                Map<String, String> bucketAndObjectMap = minioUtil.extractBucketAndObjectFromUrl(filePath);
                String fileName = bucketAndObjectMap.getOrDefault("objectName", null);
                // 如果是URL，保存bucket信息用于后续删除
                if (originalBucketName == null) {
                    originalBucketName = bucketAndObjectMap.getOrDefault("bucketName", null);
                }

                if (fileName.contains("/")) {
                    fileName = fileName.substring(fileName.lastIndexOf("/") + 1);
                }

                String localFilePath = downloadDir + File.separator + fileName;

                // 从HTTP URL下载文件
                log.info("从HTTP URL下载文件: {}", filePath);
                downloadedFilePath = minioUtil.downloadFromUrl(filePath, localFilePath);
                isOriginalFileUrl = true;
            } else {
                // 获取MinIO中的对象名称
                String objectName = filePath;

                // 从对象名称中提取文件名
                String fileName = objectName;
                if (objectName.contains("/")) {
                    fileName = objectName.substring(objectName.lastIndexOf("/") + 1);
                }

                String localFilePath = downloadDir + File.separator + fileName;

                // 从MinIO下载文件
                log.info("从MinIO下载文件: {}", objectName);
                downloadedFilePath = minioUtil.downloadFileToLocal(
                        originalBucketName,
                        objectName,
                        localFilePath);
            }

            if (downloadedFilePath == null) {
                throw new IllegalArgumentException("下载文件失败: " + filePath);
            }

            return new File(downloadedFilePath);
        } catch (Exception e) {
            // 如果MinIO下载失败，尝试使用FileStorageService
            try {
                FileStorageService service = SpringContextUtils.getBean(FileStorageService.class);
                InputStream inputStream = service.getFile(filePath);
                if (inputStream != null) {
                    String fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
                    return FileUtil.inputStreamToFile(inputStream, fileName);
                }
            } catch (Exception ex) {
                log.debug("通过 FileStorageService 获取文件失败，尝试本地路径");
            }
            // 最后尝试本地文件
            return new File(filePath);
        }
    }

    /**
     * 清理临时文件
     * 
     * @param sourceFile  源文件
     * @param downloadDir 下载目录
     */
    private void cleanupTempFiles(File sourceFile, String downloadDir) {
        try {
            // 如果有下载目录，删除整个下载目录
            if (downloadDir != null) {
                File downloadDirFile = new File(downloadDir);
                if (downloadDirFile.exists()) {
                    boolean deleted = deleteDirectory(downloadDirFile);
                    if (deleted) {
                        log.info("临时下载目录清理成功: {}", downloadDir);
                    } else {
                        log.warn("无法完全删除临时下载目录: {}", downloadDir);
                    }
                }
            }

            // 如果源文件在临时目录中，也尝试删除
            if (sourceFile != null && sourceFile.exists() &&
                    sourceFile.getAbsolutePath().contains("tmp") &&
                    downloadDir == null) {
                boolean deleted = sourceFile.delete();
                if (!deleted) {
                    log.warn("无法删除临时源文件: {}", sourceFile.getAbsolutePath());
                }
            }
        } catch (Exception e) {
            log.warn("清理临时文件时发生异常", e);
        }
    }

    /**
     * 将PPT转换为Markdown（每张幻灯片转换成图片，图片也可能有问题）
     * 
     * @param pptPath     待转换的PPT文件路径
     * @param mdPath      转换后的Markdown文件路径
     * @param picturePath 转换过程中生成的图片保存的目录
     * @throws Exception
     */
    public static void convertPPTToMarkdown(String pptPath, String mdPath, String picturePath) throws Exception {
        // 创建PPT幻灯片对象
        XMLSlideShow ppt = new XMLSlideShow(new FileInputStream(pptPath));

        // 获取所有幻灯片
        List<XSLFSlide> slides = ppt.getSlides();

        // 获取幻灯片尺寸
        java.awt.Dimension pgsize = ppt.getPageSize();

        // 创建Markdown文件写入器
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(mdPath))) {
            // 遍历所有幻灯片
            for (int i = 0; i < slides.size(); i++) {
                XSLFSlide slide = slides.get(i);

                // 写入幻灯片标题（作为Markdown的一级标题）
                writer.write("# 幻灯片 " + (i + 1) + "\n\n");

                // 将整个幻灯片渲染为图片
                BufferedImage img = new BufferedImage(pgsize.width, pgsize.height, BufferedImage.TYPE_INT_RGB);
                java.awt.Graphics2D graphics = img.createGraphics();

                // 设置渲染质量
                graphics.setRenderingHint(java.awt.RenderingHints.KEY_ANTIALIASING,
                        java.awt.RenderingHints.VALUE_ANTIALIAS_ON);
                graphics.setRenderingHint(java.awt.RenderingHints.KEY_RENDERING,
                        java.awt.RenderingHints.VALUE_RENDER_QUALITY);
                graphics.setRenderingHint(java.awt.RenderingHints.KEY_INTERPOLATION,
                        java.awt.RenderingHints.VALUE_INTERPOLATION_BICUBIC);
                graphics.setRenderingHint(java.awt.RenderingHints.KEY_FRACTIONALMETRICS,
                        java.awt.RenderingHints.VALUE_FRACTIONALMETRICS_ON);

                // 填充白色背景
                graphics.setColor(java.awt.Color.WHITE);
                graphics.fill(new java.awt.Rectangle(0, 0, pgsize.width, pgsize.height));

                // 绘制幻灯片
                slide.draw(graphics);

                // 生成幻灯片图片文件名
                String slideImageName = "slide_" + (i + 1) + "_" + UUID.randomUUID().toString().substring(0, 8)
                        + ".png";
                String slideImagePath = picturePath + "/" + slideImageName;

                // 保存幻灯片图片
                ImageIO.write(img, "png", new File(slideImagePath));

                // 释放资源
                graphics.dispose();

                // 在Markdown中引用幻灯片图片
                String relativePath = new File(picturePath).getName() + "/" + slideImageName;
                writer.write("![幻灯片" + (i + 1) + "](" + relativePath + ")\n\n");

                // 提取幻灯片中的文本内容
                StringBuilder slideText = new StringBuilder();
                for (XSLFShape shape : slide.getShapes()) {
                    if (shape instanceof XSLFTextShape) {
                        XSLFTextShape textShape = (XSLFTextShape) shape;
                        String text = textShape.getText();
                        if (text != null && !text.trim().isEmpty()) {
                            if (textShape.getPlaceholder() != null) {
                                switch (textShape.getPlaceholder()) {
                                    case TITLE:
                                    case CENTERED_TITLE:
                                        slideText.append("## ").append(text).append("\n\n");
                                        break;
                                    case SUBTITLE:
                                        slideText.append("### ").append(text).append("\n\n");
                                        break;
                                    default:
                                        slideText.append(text).append("\n\n");
                                        break;
                                }
                            } else {
                                slideText.append(text).append("\n\n");
                            }
                        }
                    }
                }

                if (slideText.length() > 0) {
                    writer.write("### 幻灯片文本内容\n\n");
                    writer.write(slideText.toString());
                }

                // 在幻灯片之间添加分隔符
                if (i < slides.size() - 1) {
                    writer.write("---\n\n");
                }
            }
        }

        // 关闭PPT文件
        ppt.close();
    }

    /**
     * 将PPT转换为Markdown的替代方法（解决乱码问题的增强版）
     * 
     * @param pptPath     待转换的PPT文件路径
     * @param mdPath      转换后的Markdown文件路径
     * @param picturePath 转换过程中生成的图片保存的目录
     */
    public static void convertPPTToMarkdown3(String pptPath, String mdPath, String picturePath) {
        StringBuilder mdContent = new StringBuilder();
        FileInputStream fis = null;
        XMLSlideShow ppt = null;
        BufferedWriter writer = null;

        try {
            fis = new FileInputStream(pptPath);
            ppt = new XMLSlideShow(fis);

            // 获取所有幻灯片
            List<XSLFSlide> slides = ppt.getSlides();

            // 获取幻灯片尺寸
            java.awt.Dimension pgsize = ppt.getPageSize();

            // 处理每张幻灯片
            int slideNumber = 1;
            for (XSLFSlide slide : slides) {
                // 添加幻灯片标题
                mdContent.append("## 幻灯片 ").append(slideNumber).append("\n\n");

                // 尝试获取幻灯片标题
                String title = getSlideTitle(slide);
                if (title != null && !title.isEmpty()) {
                    mdContent.append("# ").append(title).append("\n\n");
                }

                // 创建图片
                BufferedImage img = new BufferedImage(pgsize.width, pgsize.height, BufferedImage.TYPE_INT_RGB);
                java.awt.Graphics2D graphics = null;

                try {
                    graphics = img.createGraphics();

                    // 设置渲染质量
                    graphics.setRenderingHint(java.awt.RenderingHints.KEY_ANTIALIASING,
                            java.awt.RenderingHints.VALUE_ANTIALIAS_ON);
                    graphics.setRenderingHint(java.awt.RenderingHints.KEY_RENDERING,
                            java.awt.RenderingHints.VALUE_RENDER_QUALITY);
                    graphics.setRenderingHint(java.awt.RenderingHints.KEY_INTERPOLATION,
                            java.awt.RenderingHints.VALUE_INTERPOLATION_BICUBIC);
                    graphics.setRenderingHint(java.awt.RenderingHints.KEY_FRACTIONALMETRICS,
                            java.awt.RenderingHints.VALUE_FRACTIONALMETRICS_ON);
                    graphics.setRenderingHint(java.awt.RenderingHints.KEY_TEXT_ANTIALIASING,
                            java.awt.RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

                    // 设置字体 - 解决乱码问题
                    graphics.setFont(new java.awt.Font("Microsoft YaHei", java.awt.Font.PLAIN, 12));

                    // 填充白色背景
                    graphics.setColor(java.awt.Color.WHITE);
                    graphics.fill(new java.awt.Rectangle(0, 0, pgsize.width, pgsize.height));

                    // 绘制幻灯片
                    slide.draw(graphics);

                    // 生成幻灯片图片文件名
                    String slideImageName = "slide_" + slideNumber + "_" + UUID.randomUUID().toString().substring(0, 8)
                            + ".png";
                    String slideImagePath = picturePath + "/" + slideImageName;

                    // 保存幻灯片图片
                    File outputFile = new File(slideImagePath);
                    // 确保父目录存在
                    File parent = outputFile.getParentFile();
                    if (!parent.exists()) {
                        parent.mkdirs();
                    }
                    ImageIO.write(img, "png", outputFile);

                    // 在Markdown中引用幻灯片图片
                    String relativePath = new File(picturePath).getName() + "/" + slideImageName;
                    mdContent.append("![幻灯片").append(slideNumber).append("](").append(relativePath).append(")\n\n");
                } finally {
                    // 确保Graphics2D资源被释放
                    if (graphics != null) {
                        graphics.dispose();
                    }
                }

                // 获取幻灯片中的文本内容
                StringBuilder slideText = new StringBuilder();
                for (XSLFShape shape : slide.getShapes()) {
                    if (shape instanceof XSLFTextShape) {
                        XSLFTextShape textShape = (XSLFTextShape) shape;
                        String text = textShape.getText();
                        if (text != null && !text.trim().isEmpty()) {
                            if (textShape.getPlaceholder() != null) {
                                switch (textShape.getPlaceholder()) {
                                    case TITLE:
                                    case CENTERED_TITLE:
                                        slideText.append("## ").append(text).append("\n\n");
                                        break;
                                    case SUBTITLE:
                                        slideText.append("### ").append(text).append("\n\n");
                                        break;
                                    default:
                                        slideText.append(text).append("\n\n");
                                        break;
                                }
                            } else {
                                slideText.append(text).append("\n\n");
                            }
                        }
                    }
                }

                // 如果提取到了文本，添加到Markdown中
                if (slideText.length() > 0) {
                    mdContent.append("### 幻灯片文本内容\n\n");
                    mdContent.append(slideText.toString());
                }

                // 添加分隔符
                if (slideNumber < slides.size()) {
                    mdContent.append("---\n\n");
                }

                slideNumber++;
            }

            // 写入Markdown文件
            writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(mdPath), "UTF-8"));
            writer.write(mdContent.toString());

        } catch (Exception e) {
            log.error("PPT转Markdown失败", e);
            throw new RuntimeException(e);
        } finally {
            // 关闭资源
            closeQuietly(writer);
            closeQuietly(ppt);
            closeQuietly(fis);
        }
    }

    /**
     * 将Markdown文件和图片目录打包成ZIP文件，并清理临时文件
     * 
     * @param mdFilePath  Markdown文件路径
     * @param picturePath 图片目录路径
     * @return 生成的ZIP文件路径
     * @throws IOException 如果打包过程中发生IO错误
     */
    private String zipFile(String mdFilePath, String picturePath) throws IOException {
        // 生成ZIP文件名（基于Markdown文件名）
        File mdFile = new File(mdFilePath);
        String zipFileName = mdFile.getName().substring(0, mdFile.getName().lastIndexOf('.')) + ".zip";
        String zipFilePath = mdFile.getParent() + File.separator + zipFileName;

        // 创建ZIP文件输出流
        try (FileOutputStream fos = new FileOutputStream(zipFilePath);
                java.util.zip.ZipOutputStream zos = new java.util.zip.ZipOutputStream(fos)) {

            // 添加Markdown文件到ZIP
            addFileToZip(zos, mdFile, "");

            // 添加图片目录到ZIP
            File pictureDir = new File(picturePath);
            if (pictureDir.exists() && pictureDir.isDirectory()) {
                addDirectoryToZip(zos, pictureDir, pictureDir.getName() + "/");
            } else {
                log.warn("图片目录不存在或不是目录: {}", picturePath);
            }
            // 完成ZIP文件创建
            zos.flush();
        }

        log.info("成功创建ZIP文件: {}", zipFilePath);

        // 清理临时文件
        boolean mdDeleted = mdFile.delete();
        if (!mdDeleted) {
            log.warn("无法删除临时Markdown文件: {}", mdFilePath);
        }

        // 清理图片目录
        File pictureDir = new File(picturePath);
        if (pictureDir.exists()) {
            boolean dirDeleted = deleteDirectory(pictureDir);
            if (!dirDeleted) {
                log.warn("无法完全删除临时图片目录: {}", picturePath);
            }
        }

        return zipFilePath;
    }

    /**
     * 递归删除目录及其内容
     * 
     * @param directory 要删除的目录
     * @return 是否成功删除
     */
    private boolean deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        if (!file.delete()) {
                            log.warn("无法删除文件: {}", file.getAbsolutePath());
                        }
                    }
                }
            }
        }
        return directory.delete();
    }

    /**
     * 将单个文件添加到ZIP输出流
     * 
     * @param zos        ZIP输出流
     * @param file       要添加的文件
     * @param parentPath ZIP内的父路径
     * @throws IOException 如果添加过程中发生IO错误
     */
    private void addFileToZip(java.util.zip.ZipOutputStream zos, File file, String parentPath) throws IOException {
        if (!file.exists()) {
            log.warn("文件不存在，无法添加到ZIP: {}", file.getAbsolutePath());
            return;
        }

        // 创建ZIP条目
        String entryPath = parentPath + file.getName();
        java.util.zip.ZipEntry zipEntry = new java.util.zip.ZipEntry(entryPath);
        zos.putNextEntry(zipEntry);

        // 写入文件内容
        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] buffer = new byte[8192];
            int length;
            while ((length = fis.read(buffer)) > 0) {
                zos.write(buffer, 0, length);
            }
        }

        // 完成条目
        zos.closeEntry();
        log.debug("已添加文件到ZIP: {}", entryPath);
    }

    /**
     * 将目录及其内容添加到ZIP输出流
     * 
     * @param zos        ZIP输出流
     * @param directory  要添加的目录
     * @param parentPath ZIP内的父路径
     * @throws IOException 如果添加过程中发生IO错误
     */
    private void addDirectoryToZip(java.util.zip.ZipOutputStream zos, File directory, String parentPath)
            throws IOException {
        if (!directory.exists() || !directory.isDirectory()) {
            log.warn("目录不存在或不是目录，无法添加到ZIP: {}", directory.getAbsolutePath());
            return;
        }

        // 获取目录中的所有文件和子目录
        File[] files = directory.listFiles();
        if (files == null) {
            log.warn("无法列出目录内容: {}", directory.getAbsolutePath());
            return;
        }

        // 遍历并添加所有文件和子目录
        for (File file : files) {
            if (file.isDirectory()) {
                // 递归添加子目录
                addDirectoryToZip(zos, file, parentPath + file.getName() + "/");
            } else {
                // 添加文件
                addFileToZip(zos, file, parentPath);
            }
        }
    }

    private static void saveImage(byte[] imageData, String outputPath) throws IOException {
        BufferedImage img = ImageIO.read(new ByteArrayInputStream(imageData));
        if (img != null) {
            ImageIO.write(img, "png", new File(outputPath));
        }
    }

    /**
     * 生成图片目录
     * 
     * @param filePath
     * @param timestamp
     * @return
     */
    public static String getTmpDirectory(String filePath, long timestamp) {
        // 规范化路径，去除可能的多余斜杠
        filePath = filePath.replaceAll("\\\\", "/").replaceAll("/+", "/");

        // 获取父目录路径
        int lastSlashIndex = filePath.lastIndexOf("/");
        if (lastSlashIndex == -1) {
            throw new IllegalArgumentException("文件路径无效");
        }
        String parentDir = lastSlashIndex == 0 ? "/" : filePath.substring(0, lastSlashIndex);
        // 文件在根目录下，返回当前目录的 "tmp" 目录
        return "/".equals(parentDir) ? parentDir + timestamp : parentDir + "/" + timestamp;
    }

    /**
     * 生成文件绝对路径
     * 
     * @param filePath
     * @param newExtension
     * @return
     */
    public static String convertFileExtension(String filePath, String newExtension, long timestamp) {
        // 确保路径合法
        if (filePath == null || !filePath.contains(".")) {
            throw new IllegalArgumentException("输入路径无效，缺少文件扩展名");
        }

        if (!filePath.contains(".ppt") && !filePath.contains(".pptx")) {
            throw new IllegalArgumentException("只支持ppt和pptx类型的文件转换");
        }

        int lastDotIndex = filePath.lastIndexOf(".");
        if (lastDotIndex == -1) {
            throw new IllegalArgumentException("文件路径无效");
        }

        // 构造新文件路径（替换扩展名）
        return filePath.substring(0, lastDotIndex) + "_" + timestamp + "." + newExtension;
    }

    /**
     * ------------------------------------------------------------------------------------------------------
     */
    /**
     * 将PPT转换为Markdown的替代方法 (提取文字、加图片，不是背景图)
     * 
     * @param pptPath     待转换的PPT文件路径
     * @param mdPath      转换后的Markdown文件路径
     * @param picturePath 转换过程中生成的图片保存的目录
     */
    public static void convertPPTToMarkdown2(String pptPath, String mdPath, String picturePath) {
        StringBuilder mdContent = new StringBuilder();
        FileInputStream fis = null;
        XMLSlideShow ppt = null;
        BufferedWriter writer = null;

        try {
            fis = new FileInputStream(pptPath);
            ppt = new XMLSlideShow(fis);

            // 获取所有幻灯片
            List<XSLFSlide> slides = ppt.getSlides();

            // 先处理所有图片，避免在每张幻灯片中重复处理
            Map<String, String> pictureMap = new HashMap<>();
            int pictureIndex = 0;

            // 获取PPT中的所有图片并保存
            for (XSLFPictureData picture : ppt.getPictureData()) {
                String picName = "picture_" + pictureIndex + ".png";
                String imagePath = picturePath + "/" + picName;

                // 保存图片
                try {
                    saveImage1(picture.getData(), imagePath);
                    // 存储图片ID和路径的映射关系
                    pictureMap.put(picture.getPackagePart().getPartName().getName(), imagePath);
                    pictureIndex++;
                } catch (IOException e) {
                    log.error("保存图片失败: " + imagePath, e);
                }
            }

            // 处理每张幻灯片
            int slideNumber = 1;
            for (XSLFSlide slide : slides) {
                // 添加幻灯片标题
                mdContent.append("## 幻灯片 ").append(slideNumber).append("\n\n");

                // 尝试获取幻灯片标题
                String title = getSlideTitle(slide);
                if (title != null && !title.isEmpty()) {
                    mdContent.append("# ").append(title).append("\n\n");
                }

                // 获取幻灯片中的文本和图片
                for (XSLFShape shape : slide.getShapes()) {
                    if (shape instanceof XSLFTextShape) {
                        // 处理文本
                        XSLFTextShape textShape = (XSLFTextShape) shape;
                        String text = textShape.getText();
                        if (text != null && !text.trim().isEmpty()) {
                            // 根据占位符类型决定标题级别
                            if (textShape.getPlaceholder() != null) {
                                switch (textShape.getPlaceholder()) {
                                    case TITLE:
                                    case CENTERED_TITLE:
                                        mdContent.append("## ").append(text).append("\n\n");
                                        break;
                                    case SUBTITLE:
                                        mdContent.append("### ").append(text).append("\n\n");
                                        break;
                                    default:
                                        mdContent.append(text).append("\n\n");
                                        break;
                                }
                            } else {
                                mdContent.append(text).append("\n\n");
                            }
                        }
                    } else if (shape instanceof XSLFPictureShape) {
                        // 处理图片
                        XSLFPictureShape pictureShape = (XSLFPictureShape) shape;
                        XSLFPictureData pictureData = pictureShape.getPictureData();

                        // 获取图片的唯一标识
                        String pictureId = pictureData.getPackagePart().getPartName().getName();

                        // 查找此图片的保存路径
                        if (pictureMap.containsKey(pictureId)) {
                            String imagePath = pictureMap.get(pictureId);
                            String fileName = new File(imagePath).getName();
                            String relativePath = new File(picturePath).getName() + "/" + fileName;

                            // 在Markdown中引用图片
                            mdContent.append("![幻灯片").append(slideNumber)
                                    .append("_图片").append("](")
                                    .append(relativePath).append(")\n\n");
                        }
                    }
                }

                // 添加分隔符
                if (slideNumber < slides.size()) {
                    mdContent.append("---\n\n");
                }

                slideNumber++;
            }

            // 写入Markdown文件
            writer = new BufferedWriter(new FileWriter(mdPath));
            writer.write(mdContent.toString());

        } catch (Exception e) {
            log.error("PPT转Markdown失败", e);
            throw new RuntimeException(e);
        } finally {
            // 关闭资源
            closeQuietly(writer);
            closeQuietly(ppt);
            closeQuietly(fis);
        }
    }

    /**
     * 获取幻灯片的标题（从标题占位符中提取）
     * 
     * @param slide 幻灯片
     * @return 标题文本，如果没有标题则返回null
     */
    private static String getSlideTitle(XSLFSlide slide) {
        for (XSLFShape shape : slide.getShapes()) {
            if (shape instanceof XSLFTextShape) {
                XSLFTextShape textShape = (XSLFTextShape) shape;
                if (textShape.getPlaceholder() != null &&
                        (textShape.getPlaceholder() == Placeholder.TITLE ||
                                textShape.getPlaceholder() == Placeholder.CENTERED_TITLE)) {
                    return textShape.getText();
                }
            }
        }
        return null;
    }

    /**
     * 保存图片数据到文件
     * 
     * @param imageData  图片二进制数据
     * @param outputPath 输出路径
     * @throws IOException 如果保存过程中发生IO错误
     */
    private static void saveImage1(byte[] imageData, String outputPath) throws IOException {
        BufferedImage img = ImageIO.read(new ByteArrayInputStream(imageData));
        if (img != null) {
            File outputFile = new File(outputPath);
            // 确保父目录存在
            File parent = outputFile.getParentFile();
            if (!parent.exists()) {
                parent.mkdirs();
            }
            ImageIO.write(img, "png", outputFile);
        }
    }

    /**
     * 安全关闭资源，忽略可能的异常
     * 
     * @param closeable 要关闭的资源
     */
    private static void closeQuietly(Closeable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (IOException e) {
                log.warn("关闭资源失败", e);
            }
        }
    }

    /**
     * 将File转换为MultipartFile
     *
     * @param file 文件
     * @return MultipartFile对象
     */
    private MultipartFile convertFileToMultipartFile(File file) {
        return new MultipartFile() {
            @Override
            public String getName() {
                return file.getName();
            }

            @Override
            public String getOriginalFilename() {
                return file.getName();
            }

            @Override
            public String getContentType() {
                return MediaType.TEXT_MARKDOWN_VALUE;
            }

            @Override
            public boolean isEmpty() {
                return file.length() == 0;
            }

            @Override
            public long getSize() {
                return file.length();
            }

            @Override
            public byte[] getBytes() throws IOException {
                return cn.hutool.core.io.FileUtil.readBytes(file);
            }

            @Override
            public InputStream getInputStream() throws IOException {
                return new FileInputStream(file);
            }

            @Override
            public void transferTo(File dest) throws IOException, IllegalStateException {
                cn.hutool.core.io.FileUtil.copy(file, dest, true);
            }
        };
    }

    /**
     * 节点属性类
     */
    @Data
    @NoArgsConstructor
    public static class Properties {
        /**
         * 文件路径（MinIO对象路径或URL）
         */
        private String filePath;

        /**
         * 存储桶名称
         */
        private String bucketName;
    }

    @Override
    public String desc() {
        return "PPT转MD";
    }

}