package com.icarus.common.cotnode;

import cn.hutool.json.JSONObject;
import com.icarus.common.runtime.NodeContext;
import groovy.lang.Binding;
import groovy.lang.GroovyShell;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.python.core.PyObject;
import org.python.util.PythonInterpreter;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.util.Map;
import java.util.concurrent.*;

@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class CodeNode extends BaseNode {
    
    @Getter
    @Setter
    private String result;

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【代码】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
        
        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            nodeContext.setInputs(new JSONObject(resolvedInputs));  // 直接使用 JSONObject 构造函数
            
            // 解析属性配置
            Properties resolvedProperties = resolveProperties(context, Properties.class);
            JSONObject propertiesJson = new JSONObject();
            propertiesJson.set("language", resolvedProperties.getLanguage());
            propertiesJson.set("code", resolvedProperties.getCode());
            nodeContext.setProperties(propertiesJson);

            // 根据语言类型处理代码
            String code = resolvedProperties.getCode();
            String language = resolvedProperties.getLanguage().toLowerCase();
            
            // 执行脚本并获取结果
            Object scriptResult = executeScript(code, language, resolvedInputs);
            
            // 将执行结果转换为字符串
            result = scriptResult != null ? scriptResult.toString() : null;
            nodeContext.setStatus("completed");
            
            // 处理输出
            processOutputs(context);

        } catch (Exception e) {
            log.error("代码节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        log.info("代码节点执行结束，节点ID：{}", getId());
        return context;
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    /**
     * 执行脚本并返回结果
     */
    private Object executeScript(String code, String language, Map<String, Object> variables) throws Exception {
        switch (language) {
            case "python":
                return executePythonScript(code, variables);
            case "groovy":
                return executeGroovyScript(code, variables);
            case "javascript":
            case "js":
                return executeJavaScriptScript(code, variables);
            default:
                throw new RuntimeException("不支持的脚本语言: " + language);
        }
    }

    /**
     * 执行Python脚本
     */
    private Object executePythonScript(String code, Map<String, Object> variables) throws Exception {
        // 验证和预处理代码
        if (code == null || code.trim().isEmpty()) {
            throw new RuntimeException("Python代码不能为空");
        }
        final String finalCode = code;
        final long TIMEOUT = 30000L;
        final ExecutorService executor = Executors.newSingleThreadExecutor();

        Future<Object> future = executor.submit(() -> {
            try (PythonInterpreter interpreter = new PythonInterpreter()) {
                // 设置Python解释器的基本环境
                interpreter.exec(
                        "import sys\n" +
                                "import codecs\n" +
                                "def utf8print(s):\n" +
                                "    print(s.encode('utf-8').decode('utf-8') if isinstance(s, str) else str(s))\n"
                );

                // 先设置variables变量
                interpreter.set("variables", variables);

                // 设置其他变量到Python环境
                if (variables != null) {
                    for (Map.Entry<String, Object> entry : variables.entrySet()) {
                        interpreter.set(entry.getKey(), entry.getValue());
                    }
                }

                // 设置Console类
                interpreter.exec(
                        "class Console:\n" +
                                "    @staticmethod\n" +
                                "    def log(*args):\n" +
                                "        utf8print(' '.join(str(arg) for arg in args))\n" +
                                "console = Console()\n"
                );

                // 执行脚本代码
                log.info("执行Python代码: {}", finalCode);
                interpreter.exec(finalCode);

                // 尝试获取result变量
                try {
                    PyObject resultVar = interpreter.get("result");
                    if (resultVar != null) {
                        Object javaResult = resultVar.__tojava__(Object.class);
                        log.info("执行结果: {}", javaResult);
                        return javaResult;
                    }
                } catch (Exception ignored) {
                    // 如果没有result变量，返回None
                    log.debug("No 'result' variable found in Python script");
                }

                return null;
            }
        });

        try {
            return future.get(TIMEOUT, TimeUnit.MILLISECONDS);
        } catch (TimeoutException e) {
            future.cancel(true);
            throw new RuntimeException("Python脚本执行超时（" + TIMEOUT/1000 + "秒）");
        } catch (Exception e) {
            log.error("Python脚本执行失败: {}", e.getMessage(), e);
            throw new RuntimeException("Python脚本执行失败: " + e.getMessage(), e);
        } finally {
            executor.shutdownNow();
        }
    }


    /**
     * 执行Groovy脚本
     */
    private Object executeGroovyScript(String code, Map<String, Object> variables) throws Exception {
        // 创建Groovy上下文并设置变量
        Binding binding = new Binding();
        
        // 设置基础变量
        binding.setVariable("variables", variables);  // 添加variables作为顶层变量
        
        // 设置所有输入变量到binding中
        for (Map.Entry<String, Object> entry : variables.entrySet()) {
            binding.setVariable(entry.getKey(), entry.getValue());
        }
        
        // 设置输出和日志对象
        binding.setVariable("log", log);
        binding.setVariable("out", System.out);
        
        try {
            // 使用 GroovyShell 执行 Groovy 代码
            GroovyShell shell = new GroovyShell(binding);
            Object scriptResult = shell.evaluate(code);
            
            // 尝试获取result变量（如果脚本中定义了的话）
            try {
                Object resultFromBinding = binding.getVariable("result");
                if (resultFromBinding != null) {
                    return resultFromBinding;
                }
            } catch (Exception ignored) {
                // 如果没有result变量，则返回脚本的最后一个表达式的值
                return scriptResult;
            }
            
            return scriptResult;
        } catch (Exception e) {
            log.error("Groovy脚本执行失败: {}", e.getMessage(), e);
            throw new RuntimeException("Groovy脚本执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行JavaScript脚本
     */
    private Object executeJavaScriptScript(String code, Map<String, Object> variables) throws Exception {
        // 使用GraalJS引擎替代已废弃的Nashorn
        ScriptEngine engine = new ScriptEngineManager().getEngineByName("graal.js");
        if (engine == null) {
            // 如果找不到GraalJS，尝试使用Nashorn
            engine = new ScriptEngineManager().getEngineByName("nashorn");
        }
        
        if (engine == null) {
            throw new RuntimeException("无法初始化JavaScript引擎，请确保已添加GraalJS或Nashorn依赖");
        }
        
        try {
            // 添加基础工具函数
            engine.eval("var console = { log: print, error: print, warn: print };");
    
            // 设置变量到JavaScript环境
            if (variables != null) {
                for (Map.Entry<String, Object> entry : variables.entrySet()) {
                    engine.put(entry.getKey(), entry.getValue());
                }
                // 将整个variables对象也放入作用域
                engine.put("variables", variables);
            }
            
            // 执行脚本代码
            log.info("执行JavaScript代码: {}", code);
            Object scriptResult = engine.eval(code);
            
            // 尝试获取result变量
            try {
                Object resultVar = engine.get("result");
                if (resultVar != null) {
                    log.info("执行结果: {}", resultVar);
                    return resultVar;
                }
            } catch (Exception ignored) {
                // 如果没有result变量，返回脚本的最后一个表达式的值
                log.info("使用脚本返回值: {}", scriptResult);
                return scriptResult;
            }
            
            return scriptResult;
        } catch (Exception e) {
            log.error("JavaScript脚本执行失败: {}", e.getMessage(), e);
            throw new RuntimeException("JavaScript脚本执行失败: " + e.getMessage(), e);
        }
    }

    @Data
    @NoArgsConstructor
    public static class Properties {
        private String language;  // 脚本语言类型：python/groovy/javascript/js
        private String code;      // 脚本代码
    }

    @Override
    public String desc() {
        return "代码节点";
    }

} 