package com.icarus.common.cotnode.ab.cmtt.vectorNode.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.icarus.constant.ValidGroups;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 文件表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Getter
@Setter
@TableName("aivec_file")
@ApiModel(value = "AivecFile对象", description = "文件表")
public class AivecFile implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("文件ID")
    @TableId(value = "file_id", type = IdType.AUTO)
    @NotNull(message = "目录ID不能为空")
    private String fileId;

    @ApiModelProperty("所属目录ID（关联aivec_directory.dir_id）")
    @NotNull(message = "所属目录ID不能为空")
    private String dirId;

    @ApiModelProperty("目录名（冗余存储）")
    @NotBlank(message = "目录名不能为空")
    private String dirName;

    @ApiModelProperty("目录全路径（冗余存储）")
    @NotBlank(message = "目录全路径不能为空")
    private String dirPath;

    @ApiModelProperty("文件名")
    @NotBlank(message = "文件名不能为空")
    private String fileName;

    @ApiModelProperty("文件minioKey")
    @JsonIgnore
    private String fileMinioKey;

    @ApiModelProperty("文件类型（如：pdf/docx/txt）")
    @NotBlank(message = "文件类型不能为空")
    private String fileType;

    @ApiModelProperty("文件大小（字节）")
    @NotNull(message = "文件大小不能为空")
    private Long fileSize;

    @ApiModelProperty("文件标签（默认带目录tags, JSON格式，如：[\"技术文档\",\"会议记录\"]）")
    @NotNull(message = "文件标签不能为空")
    private String tags;


    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    //20250716新增字段
    @ApiModelProperty("文件标签Id（默认带目录tags, JSON格式，如：[\"111\",\"2222\"]）")
    @NotNull(message = "文件标签Id不能为空")
    private String tagIds;

    @ApiModelProperty("知识库体系")
    private String knowledgeBasePath;

    @ApiModelProperty("知识库ID")
    private String knowledgeBaseIds;

    @ApiModelProperty("应用名称")
    private String appName;


}
