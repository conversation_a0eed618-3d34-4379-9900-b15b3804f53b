package com.icarus.common.cotnode.flow;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import com.icarus.common.cotnode.BaseNode;
import com.icarus.common.runtime.NodeContext;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 条件判断节点
 */
@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class IFELSENode extends BaseNode {

    // 添加结果字段
    private String result;
    
    private static final String NUMBER_PATTERN = "^-?\\d+(\\.\\d+)?$";
    private static final Pattern pattern = Pattern.compile(NUMBER_PATTERN);
    public static final String code = "ifElseNode";
    
    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【条件判断】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
        
        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            nodeContext.setInputs(new JSONObject(resolvedInputs));
            // 解析属性配置
            Properties resolvedProperties = resolveProperties(context, Properties.class);
            JSONObject propertiesJson = new JSONObject();
            propertiesJson.set("branches", resolvedProperties.getBranches());
            nodeContext.setProperties(propertiesJson);

            // 评估所有分支
            String nextNodeId = null;
            
            if (resolvedProperties.getBranches() != null) {
                // 按优先级排序
                resolvedProperties.getBranches().sort((b1, b2) -> {
                    Integer p1 = b1.getPriority() != null ? b1.getPriority() : Integer.MAX_VALUE;
                    Integer p2 = b2.getPriority() != null ? b2.getPriority() : Integer.MAX_VALUE;
                    return p1.compareTo(p2);
                });

                // 遍历所有分支
                Branch defaultBranch = null;
                for (Branch branch : resolvedProperties.getBranches()) {
                    // 保存默认分支（否则节点）
                    if (branch.isDefaultNode()) {
                        defaultBranch = branch;
                        continue;
                    }
                    
                    // 评估条件分支
                    if (evaluateCondition(branch.getConditions(), context)) {
                        nextNodeId = branch.getNextNode();
                        result = String.format("选择分支，优先级：%d，选择节点：%s", 
                            branch.getPriority() != null ? branch.getPriority() : Integer.MAX_VALUE, 
                            nextNodeId);
                        log.info(result);
                        break;
                    }
                }

                // 如果没有找到匹配的条件分支，使用默认分支
                if (nextNodeId == null && defaultBranch != null) {
                    nextNodeId = defaultBranch.getNextNode();
                    result = String.format("使用默认分支，优先级：%d，选择节点：%s (如果这里为空，说明没连下一节点)",
                        defaultBranch.getPriority() != null ? defaultBranch.getPriority() : Integer.MAX_VALUE,
                        nextNodeId);
                    log.info(result);
                }
            }

            if (nextNodeId == null) {
                result = "未找到匹配的分支";
                log.warn(result);
            }

            // 设置下一个节点
            setNextNode(context, nextNodeId);
            nodeContext.setStatus("completed");

            // add by ksw 2025-05-12 为选择器节点增加默认outputs
            if(CollUtil.isEmpty(outputs)){
                addOutput("result", result, "String");
            }
            // 处理输出参数
            processOutputs(context);

        } catch (Exception e) {
            log.error("条件判断节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
            result = "执行失败：" + e.getMessage();
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        log.info("条件判断节点执行结束，节点ID：{}", getId());
        return context;
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    /**
     * 评估条件表达式
     */
    private boolean evaluateCondition(List<Condition> conditions, Map<String, Object> context) {
        if (conditions == null || conditions.isEmpty()) {
            return false;
        }
        boolean result = evaluateSingleCondition(conditions.get(0), context);
        // 处理多个条件
        for (int i = 1; i < conditions.size(); i++) {
            Condition condition = conditions.get(i);
            boolean currentResult = evaluateSingleCondition(condition, context);
            // 根据逻辑运算符组合结果
            if ("OR".equalsIgnoreCase(condition.getLogic())) {
                result = result || currentResult;
            } else { // AND
                result = result && currentResult;
            }
        }
        
        return result;
    }

    /**
     * 评估单个条件
     */
    private boolean evaluateSingleCondition(Condition condition, Map<String, Object> context) {
        String leftValuePath = condition.getLeftValue();
        if (leftValuePath == null) {
            log.warn("左值路径为空");
            return false;
        }
        /**
         * modify by ksw 20250402 解析引用参数
         */
        Object leftObj = resolveValueByPath(context, leftValuePath);
        if (leftObj == null) {
            log.warn("未找到变量: {}", leftValuePath);
            return false;
        }

        String leftValue = String.valueOf(leftObj);
        String rightValue = condition.getRightValue();
        String operator = condition.getOperator();
        
        try {
            // 使用正则表达式判断是否为数值
            boolean isLeftNumber = pattern.matcher(leftValue).matches();
            boolean isRightNumber = pattern.matcher(rightValue).matches();

            // 如果两个值都是数值，进行数值比较
            if (isLeftNumber && isRightNumber) {
                double leftNum = Double.parseDouble(leftValue);
                double rightNum = Double.parseDouble(rightValue);
                log.debug("使用数值比较: {} {} {}", leftNum, operator, rightNum);
                return compareAsNumber(leftNum, operator, rightNum);
            } else {
                // 否则使用字符串长度比较
                log.debug("使用字符串长度比较: {} {} {}", leftValue, operator, rightValue);
                return compareAsString(leftValue, operator, rightValue);
            }
        } catch (Exception e) {
            log.error("条件评估失败: {} {} {}", leftValue, operator, rightValue, e);
            return false;
        }
    }

    /**
     * 数值比较方法
     */
    private boolean compareAsNumber(double leftNum, String operator, double rightNum) {
        switch (operator) {
            case ">":
                return leftNum > rightNum;
            case "<":
                return leftNum < rightNum;
            case "≥":
            case ">=":
                return leftNum >= rightNum;
            case "≤":
            case "<=":
                return leftNum <= rightNum;
            case "=":
                return leftNum == rightNum;
            case "!=":
                return leftNum != rightNum;
            default:
                throw new IllegalArgumentException("不支持的操作符: " + operator);
        }
    }

    /**
     * 字符串比较方法 - 使用字符串长度进行比较
     */
    private boolean compareAsString(String leftValue, String operator, String rightValue) {
        int leftLength = leftValue.length();
        int rightLength = rightValue.length();
        
        switch (operator) {
            case "=":
                return leftValue.equals(rightValue);
            case "!=":
                return !leftValue.equals(rightValue);
            case ">":
                return leftLength > rightLength;
            case "≥":
            case ">=":
                return leftLength >= rightLength;
            case "<":
                return leftLength < rightLength;
            case "≤":
            case "<=":
                return leftLength <= rightLength;
            case "⊃":
                return leftValue.contains(rightValue);
            case "⊅":
                return !leftValue.contains(rightValue);
            default:
                throw new IllegalArgumentException("不支持的操作符: " + operator);
        }
    }

    @Data
    @NoArgsConstructor
    public static class Condition {
        private String leftValue;    // 左值（变量名）
        private String operator;     // 操作符
        private String rightValue;   // 右值
        private String logic;        // 逻辑运算符（AND/OR）
    }

    @Data
    @NoArgsConstructor
    public static class Branch {
        private List<Condition> conditions; // 条件列表
        private String nextNode;            // 条件满足时的下一个节点
        private Integer priority;           // 优先级
        private boolean defaultNode;        // 是否为默认分支（否则节点）
    }

    @Data
    @NoArgsConstructor
    public static class Properties {
        private List<Branch> branches;    // 条件分支列表
    }

    @Override
    public String desc() {
        return "ifelse节点";
    }
}
