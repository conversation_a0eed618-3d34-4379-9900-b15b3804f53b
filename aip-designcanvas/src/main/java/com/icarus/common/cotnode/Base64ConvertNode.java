package com.icarus.common.cotnode;

import cn.hutool.json.JSONObject;
import com.icarus.common.runtime.NodeContext;
import com.icarus.sdk.springboot.base.utils.SpringContextUtils;
import com.icarus.service.FileStorageService;
import com.icarus.utils.FileUtil;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.Base64;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
@Component
public class Base64ConvertNode extends BaseNode {

    @Getter
    @Setter
    private String result;

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【Base64转换】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
        File file = null;

        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            if (resolvedInputs == null) {
                throw new IllegalArgumentException("输入参数不能为空");
            }

            // 验证必要的输入参数
            String filePath = (String) resolvedInputs.get("filePath");
            if (filePath == null) {
                throw new IllegalArgumentException("filePath 是必需的输入参数");
            }

            // 记录输入参数到上下文
            nodeContext.setInputs(new JSONObject(resolvedInputs));

            // 解析属性配置
            Properties resolvedProperties = resolveProperties(context, Properties.class);
            nodeContext.setProperties(new JSONObject(resolvedProperties));

            // 获取文件
            file = getFileFromPath(filePath);
            if (file == null) {
                throw new IllegalArgumentException("无法获取文件: " + filePath);
            }

            // 转换文件为Base64
            result = convertToBase64(file, resolvedProperties);

            // 设置成功状态和输出
            nodeContext.setStatus("completed");
            // 处理输出映射
            processOutputs(context);

        } catch (Exception e) {
            log.error("Base64转换失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            if (file != null) {
                file.delete();
            }
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        log.info("Base64转换节点执行结束，节点ID：{}", getId());
        return context;
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    /**
     * 将文件转换为Base64字符串
     */
    private String convertToBase64(File file, Properties properties) throws Exception {
        try (FileInputStream fileInputStream = new FileInputStream(file)) {
            byte[] bytes = new byte[(int) file.length()];
            fileInputStream.read(bytes);
            
            String base64String = Base64.getEncoder().encodeToString(bytes);
            
            // 如果需要添加前缀
            if (properties.isAddPrefix()) {
                String mimeType = getMimeType(file.getName());
                return "data:" + mimeType + ";base64," + base64String;
            }
            
            return base64String;
        }
    }

    /**
     * 获取文件的MIME类型
     */
    private String getMimeType(String fileName) {
        String extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        switch (extension) {
            case "png":
                return "image/png";
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "gif":
                return "image/gif";
            case "pdf":
                return "application/pdf";
            case "doc":
                return "application/msword";
            case "docx":
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "xls":
                return "application/vnd.ms-excel";
            case "xlsx":
                return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            default:
                return "application/octet-stream";
        }
    }

    /**
     * 获取文件对象
     */
    private File getFileFromPath(String filePath) {
        try {
            // 首先尝试通过 FileStorageService 获取
            FileStorageService service = SpringContextUtils.getBean(FileStorageService.class);
            InputStream inputStream = service.getFile(filePath);
            if (inputStream != null) {
                String fileName = "temp_" + filePath.substring(filePath.lastIndexOf("/") + 1);
                return FileUtil.inputStreamToFile(inputStream, fileName);
            }
        } catch (Exception e) {
            log.warn("通过 FileStorageService 获取文件失败: {}", e.getMessage());
        }

        // FileStorageService 获取失败，尝试直接文件路径
        try {
            File directFile = new File(filePath);
            if (directFile.exists() && directFile.isFile()) {
                return directFile;
            }
        } catch (Exception e) {
            log.warn("通过直接文件路径获取文件失败: {}", e.getMessage());
        }

        return null;
    }

    @Override
    public String desc() {
        return "base64转换";
    }

    @Data
    @NoArgsConstructor
    public static class Properties {
        /**
         * 是否添加data:mime;base64,前缀
         */
        private boolean addPrefix = false;
    }
} 