package com.icarus.common.cotnode.ab.ex;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONException;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.icarus.common.cotnode.BaseNode;
import com.icarus.common.runtime.NodeContext;
import com.icarus.entity.Scene;
import com.icarus.entity.SceneElement;
import com.icarus.sdk.client.utils.LLMClientUtil;
import com.icarus.sdk.prompt.PromptTemplate;
import com.icarus.service.SceneElementService;
import com.icarus.service.SceneService;
import com.icarus.utils.SpringContextHolder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 对账单要素提取节点
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class ExExecuteNode extends BaseNode {

	private List<Map<String, Object>> result = new ArrayList<>();

	/**
	 * 基于EasyEX格式进行要素抽取
	 *
	 * @param context 流程上下文，包含：
	 *                1. 请求参数
	 *                2. 每个节点的执行结果
	 *                3. 流程状态信息
	 */
	@Override
	public Map<String, Object> execute(Map<String, Object> context) {
		log.info("开始执行【ExExecuteNode】节点, 当前节点id:{}", getId());
		String nodeId = getId();
		NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);

		try {
			// 解析属性配置
			Properties resolvedProperties = resolveProperties(context, Properties.class);
			nodeContext.setProperties(new JSONObject(resolvedProperties));

			// 验证必要的属性参数
			String contents = resolvedProperties.getContents();
			if (contents == null) {
				throw new IllegalArgumentException("contents 是必需的属性参数");
			}

			// 记录输入参数到上下文
			Map<String, Object> resolvedInputs = resolveAllInputs(context);
			if (resolvedInputs != null) {
				nodeContext.setInputs(new JSONObject(resolvedInputs));
			}

			// 获取要素提取配置
			List<SceneElement> sceneElements;
			if (resolvedProperties.getExConfig().size() > 0) {
				log.info("使用上下文中的场景要素提取配置");
				sceneElements = resolvedProperties.getExConfig();
			} else {
				Scene scene = SpringContextHolder.getBean(SceneService.class).getOne(new QueryWrapper<Scene>().eq("name", "基金账户对账单"));
				if (scene != null) {
					log.info("使用数据库中的场景要素提取配置");
					sceneElements = SpringContextHolder.getBean(SceneElementService.class).findScentElementBySceneId(scene.getId()); //e3fbec29392a464fb1304cd59636eb3e
				} else {
					sceneElements = null;
					throw new RuntimeException("没有找到指定的场景名称");
				}
			}

			// 判断是否多份单据
			log.info("判断文档是否可切分，是否包含多份票据");
			Map<String, Object> tools_is_multi_doc = tools_is_multi_doc(contents, resolvedProperties);

			// 每份单据返回一条数据进result
			if (tools_is_multi_doc.get("is_multi_doc").equals(true)) {
				String split_by = tools_is_multi_doc.get("split_by").toString();
				if (split_by.equals("page")) {
					log.info("文档中每页均为票据");
					// 根据split_by进行切分，如果是page，则用json解析然后每条是一个page
					JSONArray pages = JSONUtil.parseArray(contents);

					// 建线程池，起并发
					ExecutorService executor = Executors.newFixedThreadPool(10);
					List<Future<?>> futures = new ArrayList<>();

					// 进度计数
					AtomicInteger completedCount = new AtomicInteger(0);

					// 预分配结果数组，保持页面顺序
					int total = pages.size();
					Map<String, Object>[] resultsArray = new Map[total];
					for (int i = 0; i < pages.size(); i++) {
						final int index = i; // 保存当前页面索引
						final String pageStr = pages.get(i).toString();
						Future<?> future = executor.submit(() -> {
							Map<String, Object> pageRecords = tools_ex(pageStr, sceneElements, resolvedProperties);
							resultsArray[index] = pageRecords; // 直接按索引存储结果

							// 记录进度更新
							int current = completedCount.incrementAndGet();
							log.info("抽取进度:-> {} / {}", current, total);
						});
						futures.add(future);
					}

					// 等待所有任务完成
					for (Future<?> future : futures) {
						try {
							future.get();
						} catch (InterruptedException | ExecutionException e) {
							log.error("线程执行异常", e);
						}
					}

					// 按顺序将结果添加到result列表
					for (Map<String, Object> pageResult : resultsArray) {
						if (pageResult != null) {
							result.add(pageResult);
						}
					}

					executor.shutdown();
					log.info("所有页面处理完成，共处理了 {} 页", total);
				} else {
					// 根据split_by进行切分，不是page就是字符串，用来对contents进行切分，每块调用tools_ex
					String spliter = tools_is_multi_doc.get("split_by").toString();
					if (spliter.contains("") || spliter.equals(" ")) {
						log.info("文档可被切分为多份票据，但是分隔符不合理，因此跳过切分，直接处理");
						Map<String, Object> pageRecords = tools_ex(contents, sceneElements, resolvedProperties);
						result.add(pageRecords);
					} else {
						log.info("文档可被切分为多份票据，分隔为：{}", spliter);
						String[] split = contents.split(spliter);
						for (String splitStr : split) {
							Map<String, Object> pageRecords = tools_ex(splitStr, sceneElements, resolvedProperties);
							result.add(pageRecords);
						}
					}
				}
			} else {
				log.info("文档不可切分，整体处理");
				Map<String, Object> pageRecords = tools_ex(contents, sceneElements, resolvedProperties);
				result.add(pageRecords);
			}

			// 将结果设置到nodeContext
			nodeContext.setOutputs(new JSONObject(result));

			// 设置成功状态，处理输出映射
			nodeContext.setStatus("completed");
			processOutputs(context);

		} catch (Exception e) {
			log.error("ElementEx节点执行失败", e);
			nodeContext.setStatus("failed");
			nodeContext.setErrorMessage(e.getMessage());
		} finally {
			nodeContext.setEndTime(System.currentTimeMillis());
			updateNodeContext(context, nodeId, nodeContext);
		}

		log.info("ExExecuteNode节点执行结束，节点ID：{}", getId());
		return context;
	}

	/**
	 * 支持多份单据拆分的识别入口
	 *
	 * @param contents
	 * @param sceneElements
	 * @param resolvedProperties
	 * @return
	 */
	private Map<String, Object> tools_ex(String contents, List<SceneElement> sceneElements, Properties resolvedProperties) {
		log.info("开始执行【tools_ex】方法，共需要抽取 {} 个要素", sceneElements.size());
		Map<String, Object> ret = new HashMap<>();

		// 根据scenElement的scope进行分组
		Map<String, List<SceneElement>> scopeGroups = sceneElements.stream()
				.collect(Collectors.groupingBy(SceneElement::getScope));

		// 建线程池，起并发
		ExecutorService executor = Executors.newFixedThreadPool(scopeGroups.size());
		List<Future<Map<String, Object>>> futures = new ArrayList<>();

		for (Map.Entry<String, List<SceneElement>> iScopeGroup : scopeGroups.entrySet()) {
			// 将分组处理包装为Callable任务
			Callable<Map<String, Object>> task = () -> {
				String scopeKey = iScopeGroup.getKey();
				log.info("【tools_ex】para-execute->'{}'", scopeKey);
				if (scopeKey.startsWith("table(\"") && scopeKey.endsWith("\")")) {
					scopeKey = "table_wildcard";
				} else if (scopeKey.startsWith("page(\"") && scopeKey.endsWith("\")")) {
					scopeKey = "page_wildcard";
				} else if (scopeKey.startsWith("outline(\"") && scopeKey.endsWith("\")")) {
					scopeKey = "outline_wildcard";
				}

				Map<String, Object> result = new HashMap<>();
				switch (scopeKey) {
					case "content_without_table":
						String content_without_table = contents.replaceAll("<table[^>]*>[\\s\\S]*?</table>", "");
						result = tools_ex_llm(content_without_table, iScopeGroup, resolvedProperties);
						break;
					case "table_wildcard":
						result = tools_ex_table_prog(contents, iScopeGroup, resolvedProperties);
						break;
					default:
						result = tools_ex_llm(contents, iScopeGroup, resolvedProperties);
				}
				return result;
			};

			futures.add(executor.submit(task));
		}

// 等待所有任务完成并合并结果
		for (Future<Map<String, Object>> future : futures) {
			try {
				ret.putAll(future.get());
			} catch (InterruptedException | ExecutionException e) {
				log.error("分组处理失败", e);
			}
		}

		executor.shutdown();


		log.info("【tools_ex】方法执行完成");
		return ret;
	}

	/**
	 * 通过llm判断是否为多份单据，如果是多份单据，要拆分处理。也要考虑到有长文档的可能，所以要支持配置。
	 *
	 * @param contents
	 * @param resolvedProperties
	 * @return
	 */
	private Map<String, Object> tools_is_multi_doc(String contents, Properties resolvedProperties) {
		Map<String, Object> ret = new HashMap<>();

		// 将表格全去掉，不影响分页判断，减少token
		String isMultiDoc = contents.replaceAll("<table[^>]*>[\\s\\S]*?</table>", "");
		// todo:加上异常处理，防止json解析报错
		JSONArray parseMultiDoc = JSONUtil.parseArray(isMultiDoc);

		// 如果只有一页，就不用调大模型猜是否包含多个文档了（docx不一样，会只有一段）
		if (parseMultiDoc.size() == 1) {
			log.info("对于只有一页的文档，暂不考虑包含多个单据的情况");
			ret.put("is_multi_doc", false);
			ret.put("split_by", "------------------");
			return ret;
		}

		// 取前3页（如果不够3页有几页取几页）
		String slimContent = "";
		for (int i = 0; i < Math.min(parseMultiDoc.size(), 3); i++) {
			slimContent += parseMultiDoc.get(i).toString();
		}

		// 通过大模型判断是否为多份相同文档合并
		PromptTemplate ptIsMultiDoc = new PromptTemplate(resolvedProperties.getP1_is_multi_doc());
		ptIsMultiDoc.format("contents", slimContent);

		// 调用LLM(参考要素提取服务不传递系统提示词)
		String answer = LLMClientUtil.builder()
				.modelName(resolvedProperties.getModel())
				.chat(ptIsMultiDoc.toString())
				.removeThinkTAG()
				.removeScriptQuote()
				.runChatCompletion();

		// 用try包起来解析answer的过程，如果解析失败，则返回false
		try {
			JSONObject parse = JSONUtil.parseObj(answer);
			ret.put("is_multi_doc", parse.getBool("is_multi_doc"));
			ret.put("split_by", parse.getStr("split_by"));
			return ret;
		} catch (Exception e) {
			ret.put("is_multi_doc", false);
			ret.put("split_by", "------------------");
			return ret;
		}

	}

	/**
	 * 通过LLM直接抽取
	 *
	 * @param contents           用于提供待抽取的EasyEX格式内容
	 * @param iScopeGroup        提供这个分组的场景元素
	 * @param resolvedProperties 用于提供大模型
	 * @return
	 */
	private Map<String, Object> tools_ex_llm(String contents, Map.Entry<String, List<SceneElement>> iScopeGroup, Properties resolvedProperties) {
		Map<String, Object> ret = new HashMap<>();

		log.info("开始执行【tools_ex_llm】方法，抽取 {} 个要素", iScopeGroup.getValue().size());

		// 用llm抽取，可以脱掉json外壳，将每个contents都拼接起来
		contents = withoutJsonShell(contents);

		// 构建prompt，抽取要素
		PromptTemplate ptElementEX = new PromptTemplate(resolvedProperties.getP4_scene_by_llm());
		ptElementEX.format("contents", contents);

		// 构建ElementConfigs。iScopeGroup中所有SceneElement的extraction_config
		List<String> ElementConfigs = new ArrayList<>();
		for (SceneElement sceneElement : iScopeGroup.getValue()) {
			ElementConfigs.add(sceneElement.getExtractionConfig());
		}
		ptElementEX.format("ElementConfigs", ElementConfigs);

		// 构建SynonymConfigs。iScopeGroup中所有SceneElement的element_name和synonymous
		List<String> SynonymConfigs = new ArrayList<>();
		for (SceneElement sceneElement : iScopeGroup.getValue()) {
			SynonymConfigs.add(sceneElement.getElementName() + " 可能的同义词包括：" + sceneElement.getSynonymous());
		}
		ptElementEX.format("SynonymConfigs", SynonymConfigs);


		// 调用LLM(参考要素提取服务不传递系统提示词)
		String answer = LLMClientUtil.builder()
				.modelName(resolvedProperties.getModel())
				.chat(ptElementEX.toString())
				.removeThinkTAG()
				.removeScriptQuote()
				.runChatCompletion();

		if (StrUtil.isEmpty(answer)) return null;

		// 将解析结果添加到result中
		try {
			JSONObject parse = JSONUtil.parseObj(answer);
			for (Map.Entry<String, Object> entry : parse.entrySet()) {
				String key = entry.getKey();
				Object value = entry.getValue();
				ret.put(key, String.valueOf(value));
			}
		} catch (JSONException e) {
			log.error("LLM返回的JSON数组解析失败，answer内容：{}", answer, e);
		} catch (NullPointerException e) {
			log.error("LLM返回结果为空", e);
		}

		log.info("执行完成【tools_ex_llm】，抽取到 {} 个要素\n{}", ret.size(), ret);

		return ret;

	}

	/**
	 * 脱掉json外壳，将内容拼接起来
	 *
	 * @param contents
	 * @return
	 */
	private String withoutJsonShell(String contents) {
		if (isJsonArrString(contents)) {
			String ret = "";
			JSONArray parse = JSONUtil.parseArray(contents);
			//从parse中取每个json的content，拼接起来
			for (Object o : parse) {
				JSONObject jsonObject = JSONUtil.parseObj(o);
				ret += jsonObject.getStr("content") + "\n";
			}

			return ret;
		} else {
			return contents;
		}
	}

	/**
	 * scope为table("账户的持仓余额表")时，通过LLM判断字段序列-解析table-抽取表格中数据
	 *
	 * @param contents           用于提供待抽取的EasyEX格式内容
	 * @param iScopeGroup        提供这个分组的场景元素
	 * @param resolvedProperties 用于提供大模型
	 * @return
	 */
	private Map<String, Object> tools_ex_table_prog(String contents, Map.Entry<String, List<SceneElement>> iScopeGroup, Properties resolvedProperties) {
		log.info("开始执行【tools_ex_table_prog】方法，抽取 {} 中的要素", iScopeGroup.getKey());

		Map<String, Object> ret = new HashMap<>();

		contents = withoutJsonShell(contents);

		//处理content内容，所有<table>中的<tr>只保留最多2行
		Document doc = Jsoup.parse(contents);
		Document tmp = doc.clone();
		Elements tabs = tmp.select("table");
		for (Element itab : tabs) {
			Elements trs = itab.select("tr");
			if (trs.size() > 2) {
				Elements newTrs = new Elements();
				newTrs.add(trs.get(0));
				newTrs.add(trs.get(1));
				itab.select("tr").remove();
				itab.appendChild(newTrs.get(0));
				itab.appendChild(newTrs.get(1));
			}
		}
		String slimContent = tmp.body().html();

		// 从iScopeGroup.getValue()提取所有要素名称，用逗号分隔，组成exHeadsNames
		StringBuilder exHeadsNames = new StringBuilder();
		for (SceneElement iSceneElement : iScopeGroup.getValue()) {
			exHeadsNames.append(iSceneElement.getElementName()).append(",");
		}

		// 组装prompt，定位要抽取的表并分析字段序列
		log.info("【tools_ex_table_prog】开始定位表格位置");
		PromptTemplate ptLocateTable = new PromptTemplate(resolvedProperties.getP2_locate_table());
		ptLocateTable.format("contents", slimContent);
		ptLocateTable.format("extraction_configs", iScopeGroup.getValue()
				.stream()
				.map(SceneElement::getExtractionConfig)
				.collect(Collectors.joining("\n")));
		ptLocateTable.format("exHeadsNames", exHeadsNames.toString());
		String answer = LLMClientUtil.builder()
				.modelName(resolvedProperties.getModel())
				.chat(ptLocateTable.toString())
				.removeThinkTAG()
				.removeScriptQuote()
				.runChatCompletion();
		log.info("【tools_ex_table_prog】定位表格结果\n{}", answer);

		// 解析大模型返回的json，找到要抽取的表 以及 抽取字段序列
		List<Map<String, String>> tableRows = new ArrayList<>();
		int selectedTableIndex = -1;
		int headRowNum = 0;
		int[] exHeadsSequence = null;
		try {
			JSONObject parseLLMResponseFindTab = JSONUtil.parseObj(answer);
			String pos_tab_idx = parseLLMResponseFindTab.get("pos_tab_idx").toString();
			String cols_seq_in_pos_tab = parseLLMResponseFindTab.get("cols_seq_in_pos_tab").toString();
			String rows_used_by_table_head = parseLLMResponseFindTab.get("rows_used_by_table_head").toString();

			//找到要抽取的表才需要解析后面参数
			selectedTableIndex = Integer.parseInt(pos_tab_idx) - 1;
			if (selectedTableIndex >= 0) {
				exHeadsSequence = Arrays.stream(cols_seq_in_pos_tab.split(","))
						.mapToInt(Integer::parseInt)
						.toArray();

				headRowNum = Integer.parseInt(rows_used_by_table_head) - 1;
				if (headRowNum > 2) headRowNum = 2; //不允许超过3行的表头，容易是识别错误，宁可把一些表头当数据抓进来，也不要丢掉数据
			}

		} catch (Exception e) {
			log.error("LLM返回的JSON数组解析失败，answer内容：{}", answer, e);
		}

		// 抽取表（策略1：用LLM直接问。策略2：分析<table>进行提取
		// 定位失败标志（selectedTableIndex=-1或超出表格个数 exHeadsSequence为空）
		if (exHeadsSequence == null || selectedTableIndex < 0 || selectedTableIndex + 1 > doc.select("table").size()) {
			log.info("【tools_ex_table_prog】表格定位失败，尝试通过LLM重建表格后再执行结构化抽取");

			// 兜底1：用llm尝试重构建表格，再进行抽取，因为前面没找到表格，因此重建时不需要再看table了，全去掉

			// 组装prompt，通过大模型重建无线表格
			PromptTemplate ptTableRebuild = new PromptTemplate(resolvedProperties.getP3_rebuild_table_by_llm());
			ptTableRebuild.format("contents", contents);
			ptTableRebuild.format("extraction_configs", iScopeGroup.getValue()
					.stream()
					.map(SceneElement::getExtractionConfig)
					.collect(Collectors.joining("\n")));
			ptTableRebuild.format("exHeadsNames", exHeadsNames.toString());

			answer = LLMClientUtil.builder()
					.modelName(resolvedProperties.getModel())
					.chat(ptTableRebuild.toString())
					.removeThinkTAG()
					.removeScriptQuote()
					.runChatCompletion();

			// 解析返回，应该是一个<table>的内容，用Jsoup解析，注意异常处理，有了这个设置，就会跳过“兜底LLM抽取”
			try {
				doc = Jsoup.parse(answer);
				selectedTableIndex = 0;
				int exHeadsSequenceSize = exHeadsNames.toString().split(",").length;
				exHeadsSequence = new int[exHeadsSequenceSize];
				for (int i = 0; i < exHeadsSequenceSize; i++) {
					exHeadsSequence[i] = i + 1;
				}
				headRowNum = 0;
				log.info("【tools_ex_table_prog】重建表格完成\n{}", answer);
			} catch (Exception e) {
				log.error("【tools_ex_table_prog】重建表格失败，answer内容：{}", answer, e);
			}

			// 根据重建的表格再次定位
			if (exHeadsSequence == null || selectedTableIndex < 0 || selectedTableIndex + 1 > doc.select("table").size()) {
				// 重建表格失败，进兜底逻辑：用llm直接抽取表格

				//用doc.select("table")判断一下selectedTableIndex是否存在。如果没有找到表，用llm作一轮兜底
				log.info("【tools_ex_table_prog】兜底抽取流程->{}", iScopeGroup.getKey());

				// 兜底：构建prompt，抽取要素
				PromptTemplate ptElementEXList = new PromptTemplate(resolvedProperties.getP5_scene_table_by_llm());
				ptElementEXList.format("contents", contents);

				// 构建ElementConfigs。iScopeGroup中所有SceneElement的extraction_config
				List<String> ElementConfigs = new ArrayList<>();
				for (SceneElement sceneElement : iScopeGroup.getValue()) {
					ElementConfigs.add("提取文本中的【" + sceneElement.getElementName() + "】，只返回提取到的信息，不要添加任何评论与符号，没有相关信息则返回空字符串");
				}
				ptElementEXList.format("ElementConfigs", ElementConfigs);

				// 调用LLM(参考要素提取服务不传递系统提示词)
				answer = LLMClientUtil.builder()
						.modelName(resolvedProperties.getModel())
						.chat(ptElementEXList.toString())
						.removeThinkTAG()
						.removeScriptQuote()
						.runChatCompletion();

				// 解析结果
				try {
					JSONArray jsonArray = JSONUtil.parseArray(answer);
					if (jsonArray != null) {
						for (Object obj : jsonArray) {
							JSONObject item = (JSONObject) obj;
							Map<String, String> rowMap = new HashMap<>();
							for (Map.Entry<String, Object> entry : item.entrySet()) {
								String key = entry.getKey();
								String value = String.valueOf(entry.getValue());
								rowMap.put(key, value.trim());
							}
							tableRows.add(rowMap);
						}
					}
				} catch (JSONException e) {
					log.error("LLM返回的JSON数组解析失败，answer内容：{}", answer, e);
					tableRows.add(Collections.emptyMap());
				} catch (NullPointerException e) {
					// 处理answer为空的情况
					log.error("LLM返回结果为空", e);
					tableRows.add(Collections.emptyMap());
				}

				// 将tableRows添加到ret中，key是类似这样的table("账户的持仓余额表")值，要将它转换成"tab-账户的持仓余额表"
				String name = iScopeGroup.getValue().stream().findFirst().get().getScope();
				name = name.replaceAll("\\(\"", "-");
				name = name.replaceAll("\"\\)", "");
				ret.put(name, tableRows);

				log.info("执行完成【tools_ex_table_prog】，抽取对象->{} ", iScopeGroup.getKey());

				return ret;
			}
		} else {

		}

		// 常规：依次抽取，形成结果
		log.info("【tools_ex_table_prog】执行结构化抽取，定位表格->{}", selectedTableIndex);
		Element tableForEX = doc.select("table").get(selectedTableIndex);
		Elements rows = tableForEX.select("tr:gt(" + headRowNum + ")"); // 跳过表头行
		log.info("【tools_ex_table_prog】找到表行->{} 个", rows.size());
		String[] headerNames = exHeadsNames.toString().split(",");
		log.info("【tools_ex_table_prog】确认表头->{}", exHeadsNames);
		for (Element row : rows) {
			Elements cells = row.select("td");

			// 检查当前行是否是“合计、小计、总计、sum、total”等关键字，如果是则跳过
			if (row.text().toLowerCase().contains("合计") || row.text().toLowerCase().contains("小计") || row.text().toLowerCase().contains("总计") || row.text().toLowerCase().contains("sum") || row.text().toLowerCase().contains("total")) {
				continue;
			}

			// 使用exHeadsSequence中指定的列提取数据出数据，用exHeadsNames中对应的名字作为key
			Map<String, String> rowMap = new HashMap<>();
			for (int i = 0; i < exHeadsSequence.length; i++) {
				int columnIndex = exHeadsSequence[i];
				if (columnIndex > 0 && columnIndex <= cells.size() && i < headerNames.length) {
					String headerName = headerNames[i];
					String cellValue = cells.get(columnIndex - 1).text().trim();
					rowMap.put(headerName, cellValue);
				} else {
					rowMap.put(headerNames[i], "");
				}
			}
			tableRows.add(rowMap);
		}

		// 将tableRows添加到ret中，key是类似这样的table("账户的持仓余额表")值，要将它转换成"tab-账户的持仓余额表"
		String name = iScopeGroup.getValue().stream().findFirst().get().getScope();
		name = name.replaceAll("\\(\"", "-");
		name = name.replaceAll("\"\\)", "");
		ret.put(name, tableRows);

		log.info("执行完成【tools_ex_table_prog】->{} ", iScopeGroup.getKey());

		return ret;
	}


	@Override
	protected Object getCurrentNodeProperty(String propertyName) {
		if ("result".equals(propertyName)) {
			return result;
		}
		return super.getCurrentNodeProperty(propertyName);
	}

	@Data
	@NoArgsConstructor
	public static class Properties {
		private String model;    // 大模型
		private String contents; // EasyEX的内容
		private List<SceneElement> exConfig; // 抽取场景配置（最优先）
		private String p1_is_multi_doc;
		private String p2_locate_table;
		private String p3_rebuild_table_by_llm;
		private String p4_scene_by_llm;
		private String p5_scene_table_by_llm;

	}

	@Override
	public String desc() {
		return "要素提取-执行";
	}
} 