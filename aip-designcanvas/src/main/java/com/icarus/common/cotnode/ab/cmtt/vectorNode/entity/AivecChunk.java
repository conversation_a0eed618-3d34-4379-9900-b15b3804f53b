package com.icarus.common.cotnode.ab.cmtt.vectorNode.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.icarus.common.cotnode.ab.cmtt.vectorNode.typehandler.VectorTypeHandler;
import com.icarus.constant.ValidGroups;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 支持QA格式的文件切片表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Getter
@Setter
@TableName("aivec_chunk")
@ApiModel(value = "AivecChunk对象", description = "支持QA格式的文件切片表")
public class AivecChunk implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("切片ID")
    @TableId(value = "chunk_id", type = IdType.AUTO)
    @NotNull(message = "切片  ID不能为空")
    private String chunkId;

    @ApiModelProperty("所属文件ID（冗余存储，避免关联file表）")
//    @NotNull(message = "所属文件ID不能为空")
    private String fileId;

    @ApiModelProperty("所属目录ID（冗余存储，避免关联directory表）")
//    @NotNull(message = "所属目录ID不能为空")
    private String dirId;

    @ApiModelProperty("文件名（冗余存储）")
//    @NotBlank(message = "文件名不能为空")
    private String fileName;

    @ApiModelProperty("文件minioKey（冗余存储）")
    private String fileMinioKey;

    @ApiModelProperty("目录名（冗余存储）")
//    @NotBlank(message = "目录名不能为空")
    private String dirName;

    @ApiModelProperty("目录全路径（冗余存储）")
//    @NotBlank(message = "目录全路径不能为空")
    private String dirPath;

    @ApiModelProperty("文件标签（冗余存储file.tags）")
    private String fileTags;


    @ApiModelProperty("切片内容（纯文本或问题部分）")
//    @NotBlank(message = "切片内容不能为空")
    private String chunkContent;

    @ApiModelProperty("显式问题（如：\"如何配置数据库？\"）")
    private String qaQuestion;

    @ApiModelProperty("问题对应的答案内容")
    private String qaAnswer;

    @ApiModelProperty("切片序号（用于重组原文）")
//    @NotNull(message = "切片序号不能为空")
    private Integer chunkNumber;

    @ApiModelProperty("向量 embedding（用于RAG检索）")
    @TableField(typeHandler = VectorTypeHandler.class)
    private String embeddingVector;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    //新增字段
    @ApiModelProperty("文件标签Id（默认带目录tags, JSON格式，如：[\"111\",\"2222\"]）")
//    @NotNull(message = "文件标签Id不能为空")
    private String tagIds;

    @ApiModelProperty("发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    @NotNull(message = "发布时间不能为空")
    private LocalDateTime publishTime;

    @ApiModelProperty("来源主体")
//    @NotNull(message = "来源主体不能为空")
    private String sourceEntity;

    @ApiModelProperty("知识库体系")
    private String knowledgeBasePath;

    @ApiModelProperty("知识库ID")
    private String knowledgeBaseIds;

    @ApiModelProperty("应用名称")
    private String appName;

    @TableField(exist = false)
    private Float similarity;

    @TableField(exist = false)
    private Integer number;

    private String taskId;



}
