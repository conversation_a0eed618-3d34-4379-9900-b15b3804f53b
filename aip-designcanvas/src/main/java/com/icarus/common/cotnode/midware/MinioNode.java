package com.icarus.common.cotnode.midware;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import com.icarus.common.cotnode.BaseNode;
import com.icarus.common.minio.MinioUtil;
import com.icarus.common.runtime.NodeContext;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class MinioNode extends BaseNode {

    @Getter
    @Setter
    private String result;

    private static final String ACTION_UPLOAD = "upload";
    private static final String ACTION_DELETE = "delete";
    private static final String ACTION_DOWNLOAD = "download";
//    private static final String ACTION_UPLOAD_MULTIPART = "uploadMultipart";


    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【minio】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
        String executeId = nodeContext.getExecuteId();
        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            nodeContext.setInputs(new JSONObject(resolvedInputs));

            // 解析属性配置
            Properties resolvedProperties = resolveProperties(context, Properties.class);
            JSONObject propertiesJson = new JSONObject();
//            propertiesJson.set("action", resolvedProperties.getAction());
//            propertiesJson.set("endpoint", resolvedProperties.getEndpoint());
//            propertiesJson.set("bucketName", resolvedProperties.getBucketName());
            nodeContext.setProperties(propertiesJson);

            String action = resolvedProperties.getMinioOperationType();

            String filePath = resolvedProperties.getFilePath();


//            String filePath = (String) resolvedInputs.get("filePath");
//            String file = (String) resolvedInputs.get("file");
//            MultipartFile[] multipartFiles = ((RuntimeRequest) context.get("request")).getFiles();


            if (action == null) {
                throw new IllegalArgumentException("操作类型参数不能为空");
            }
            MinioUtil minioUtil = SpringUtil.getBean(MinioUtil.class);

            String bucketName = resolvedProperties.getBucketName();
            if (StrUtil.isEmpty(bucketName)) {
                bucketName = minioUtil.getMinioConfig().getBucketName();
                resolvedProperties.setBucketName(bucketName);
            }

            // 确保存储桶存在
            ensureBucketExists(minioUtil, bucketName);

            // 根据action执行相应操作
            switch (action) {
                case ACTION_UPLOAD: {
                    // 验证必要的输入参数
                    if (filePath == null) {
                        throw new IllegalArgumentException("filePath 是必需的参数");
                    }

                    // 下载文件到本地临时目录
                    String downloadDir = System.getProperty("java.io.tmpdir") + File.separator + "minio-downloads" + File.separator + executeId;
                    File downloadDirFile = new File(downloadDir);
                    if (!downloadDirFile.exists()) {
                        downloadDirFile.mkdirs();
                    }

                    // 从MinIO中提取文件名
                    String fileName = filePath;
                    if (fileName.contains("/")) {
                        fileName = fileName.substring(fileName.lastIndexOf("/") + 1);
                    }
                    
                    String localFilePath = downloadDir + File.separator + fileName;
                    
                    // 从MinIO下载文件
                    log.info("从MinIO下载文件: {}/{}", bucketName, filePath);
                    String downloadedFilePath = minioUtil.downloadFileToLocal(
                            bucketName,
                            filePath, 
                            localFilePath
                    );
                    
                    if (downloadedFilePath == null) {
                        throw new IllegalArgumentException("下载文件失败: " + filePath);
                    }

                    File downloadedFile = new File(downloadedFilePath);
                    List<String> uploadedPaths = new ArrayList<>();

                    try {
                        String objectName = generateObjectName(downloadedFile.getName(), executeId);

                        // 解析过期时间（如果有）
                        Integer expirationDays = null;
                        if (StrUtil.isNotEmpty(resolvedProperties.getExpirationDate())) {
                            try {
                                expirationDays = Integer.parseInt(resolvedProperties.getExpirationDate());
                            } catch (NumberFormatException e) {
                                log.warn("无效的过期天数: {}", resolvedProperties.getExpirationDate());
                            }
                        }

                        // 解析访问权限
                        Boolean isPublic = StrUtil.isNotEmpty(resolvedProperties.getMinioOperationAuth()) &&
                                "public".equalsIgnoreCase(resolvedProperties.getMinioOperationAuth());

                        // 使用MinioUtil上传文件
                        String uploadedPath = minioUtil.uploadFileWithOptions(
                                convertFileToMultipartFile(downloadedFile),
                                bucketName,
                                objectName,
                                expirationDays,
                                isPublic,
                                resolvedProperties.getFileRestrictions()
                        );
                        if (uploadedPath != null) {
                            uploadedPaths.add(uploadedPath);
                        }
                    } finally {
                        // 清理临时文件
                        if (downloadedFile != null) {
                            downloadedFile.delete();
                        }
                    }
                    result = String.join(",", uploadedPaths);
                    break;
                }
                case ACTION_DOWNLOAD: {
                    if (filePath == null) {
                        throw new IllegalArgumentException("filePath参数不能为空");
                    }
                    
                    // 生成本地下载路径
                    String downloadDir = System.getProperty("java.io.tmpdir") + File.separator + "minio-downloads" + File.separator + executeId;
                    File downloadDirFile = new File(downloadDir);
                    if (!downloadDirFile.exists()) {
                        downloadDirFile.mkdirs();
                    }
                    
                    // 判断是HTTP URL还是MinIO对象路径
                    String downloadedFilePath;
                    if (minioUtil.isHttpUrl(filePath)) {
                        // 从Minio URL中提取bucket和object信息
                        Map<String, String> bucketAndObjectMap = minioUtil.extractBucketAndObjectFromUrl(filePath);
                        String fileName = bucketAndObjectMap.getOrDefault("objectName", null);
                        if (fileName.contains("/")) {
                            fileName = fileName.substring(fileName.lastIndexOf("/") + 1);
                        }
                        
                        String localFilePath = downloadDir + File.separator + fileName;
                        
                        // 从HTTP URL下载文件
                        log.info("从HTTP URL下载文件: {}", filePath);
                        downloadedFilePath = minioUtil.downloadFromUrl(filePath, localFilePath);
                    } else {
                        // 获取MinIO中的对象名称
                        String objectName = filePath;
                        
                        // 从对象名称中提取文件名
                        String fileName = objectName;
                        if (objectName.contains("/")) {
                            fileName = objectName.substring(objectName.lastIndexOf("/") + 1);
                        }
                        
                        String localFilePath = downloadDir + File.separator + fileName;
                        
                        // 从MinIO下载文件
                        log.info("从MinIO下载文件: {}/{}", bucketName, objectName);
                        downloadedFilePath = minioUtil.downloadFileToLocal(
                                bucketName,
                                objectName, 
                                localFilePath
                        );
                    }
                    
                    if (downloadedFilePath == null) {
                        throw new IllegalArgumentException("下载文件失败: " + filePath);
                    }
                    
                    result = downloadedFilePath;
                    break;
                }
                case ACTION_DELETE: {
                    if (filePath == null) {
                        throw new IllegalArgumentException("filePath参数不能为空");
                    }
                    
                    log.info("执行删除操作，文件路径: {}", filePath);
                    boolean deleted;
                    
                    // 判断是HTTP URL还是MinIO对象路径
                    if (minioUtil.isHttpUrl(filePath)) {
                        // 从URL中删除文件
                        log.info("检测到HTTP URL，尝试从URL中删除文件: {}", filePath);
                        
                        // 用于调试的URL解码信息
                        String urlDebugInfo = minioUtil.testUrlDecode(filePath);
                        log.info("URL解码信息:\n{}", urlDebugInfo);
                        
                        deleted = minioUtil.deleteFileFromUrl(filePath);
                        
                        if (!deleted) {
                            log.error("从URL中删除文件失败: {}", filePath);
                            throw new IllegalArgumentException("从URL中删除文件失败: " + filePath);
                        }
                        
                        log.info("从URL中删除文件成功: {}", filePath);
                        result = "从URL中删除文件成功: " + filePath;
                    } else {
                        // 从MinIO中删除文件
                        log.info("检测到MinIO对象路径，尝试从MinIO中删除文件: {}/{}", bucketName, filePath);
                        deleted = minioUtil.deleteFile(bucketName, filePath);
                        
                        if (!deleted) {
                            log.error("从MinIO中删除文件失败: {}/{}", bucketName, filePath);
                            throw new IllegalArgumentException("删除文件失败: " + filePath);
                        }
                        
                        log.info("从MinIO中删除文件成功: {}/{}", bucketName, filePath);
                        result = "文件删除成功: " + filePath;
                    }
                    
                    break;
                }
                default:
                    throw new IllegalArgumentException("不支持的操作类型: " + action);
            }

            // 设置处理结果
            nodeContext.setStatus("completed");

            // 处理输出
            processOutputs(context);

        } catch (Exception e) {
            log.error("Minio节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        log.info("Minio节点执行结束，节点ID：{}", getId());
        return context;
    }

    private void ensureBucketExists(MinioUtil minioUtil, String bucketName) throws Exception {
        // Use MinioUtil instead of direct MinioClient
        minioUtil.createBucket(bucketName);
    }

    private String generateObjectName(String originalFilename, String executeId) {
        return String.format("files/%s/%s/%s",
                LocalDate.now(),
                executeId,
                originalFilename);
    }

    /**
     * 将File转换为MultipartFile
     *
     * @param file 文件
     * @return MultipartFile对象
     */
    private MultipartFile convertFileToMultipartFile(File file) {
        return new MultipartFile() {
            @Override
            public String getName() {
                return file.getName();
            }

            @Override
            public String getOriginalFilename() {
                return file.getName();
            }

            @Override
            public String getContentType() {
                return MediaType.APPLICATION_OCTET_STREAM_VALUE;
            }

            @Override
            public boolean isEmpty() {
                return file.length() == 0;
            }

            @Override
            public long getSize() {
                return file.length();
            }

            @Override
            public byte[] getBytes() throws IOException {
                return FileUtil.readBytes(file);
            }

            @Override
            public InputStream getInputStream() throws IOException {
                return new FileInputStream(file);
            }

            @Override
            public void transferTo(File dest) throws IOException, IllegalStateException {
                FileUtil.copy(file, dest, true);
            }
        };
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    @Data
    @NoArgsConstructor
    public static class Properties {
        /**
         * 操作类型
         */
        private String minioOperationType;

        /**
         * 存储桶
         */
        private String bucketName;

        /**
         * 文件路径
         * 对于上传操作：本地文件或目录路径
         * 对于下载操作：MinIO对象路径或HTTP URL
         * 对于删除操作：MinIO对象路径或MinIO生成的HTTP URL
         */
        private String filePath;

        /**
         * 过期时间(天)
         */
        private String expirationDate;

        /**
         * 访问权限
         */
        private String minioOperationAuth;

        /**
         * 文件限制（MB）
         */
        private String fileRestrictions;
    }

    @Override
    public String desc() {
        return "Minio节点";
    }

}