package com.icarus.common.cotnode;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import com.icarus.common.minio.MinioUtil;
import com.icarus.common.runtime.NodeContext;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.http.MediaType;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class PdfToPngConvertNode extends BaseNode {

    @Getter
    @Setter
    private String result;

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【PDF转PNG】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
        File downloadedFile = null;
        List<File> pngFiles = new ArrayList<>();
        String originalFilePath;
        String originalBucketName;
        boolean isOriginalFileUrl = false;
        
        try {
            // 解析属性配置
            Properties resolvedProperties = resolveProperties(context, Properties.class);
            JSONObject propertiesJson = new JSONObject();
            propertiesJson.set("filePath", resolvedProperties.getFilePath());
            propertiesJson.set("dpi", resolvedProperties.getDpi());
            propertiesJson.set("outputFormat", resolvedProperties.getOutputFormat());
            propertiesJson.set("deleteOriginal", resolvedProperties.isDeleteOriginal());
            nodeContext.setProperties(propertiesJson);

            // 获取MinioUtil实例
            MinioUtil minioUtil = SpringUtil.getBean(MinioUtil.class);

            // 验证必要的输入参数
            String filePath = resolvedProperties.getFilePath();
            if (filePath == null) {
                throw new IllegalArgumentException("filePath 是必需的参数");
            }
            
            // 保存原始文件路径，用于后续删除
            originalFilePath = filePath;
            originalBucketName = resolvedProperties.getBucketName();
            if (StrUtil.isEmpty(originalBucketName)) {
                originalBucketName = minioUtil.getMinioConfig().getBucketName();
                resolvedProperties.setBucketName(originalBucketName);
            }

            // 下载文件到本地临时目录
            String executeId = nodeContext.getExecuteId();
            String downloadDir = System.getProperty("java.io.tmpdir") + File.separator + "pdf2png-downloads" + File.separator + executeId;
            File downloadDirFile = new File(downloadDir);
            if (!downloadDirFile.exists()) {
                downloadDirFile.mkdirs();
            }

            // 下载文件
            String downloadedFilePath;
            if (minioUtil.isHttpUrl(filePath)) {
                // 从URL中提取文件名和bucket信息
                Map<String, String> bucketAndObjectMap = minioUtil.extractBucketAndObjectFromUrl(filePath);
                String fileName = bucketAndObjectMap.getOrDefault("objectName", null);
                // 如果是URL，保存bucket信息用于后续删除
                if (originalBucketName == null) {
                    originalBucketName = bucketAndObjectMap.getOrDefault("bucketName", null);
                }
                
                if (fileName.contains("/")) {
                    fileName = fileName.substring(fileName.lastIndexOf("/") + 1);
                }
                
                String localFilePath = downloadDir + File.separator + fileName;
                
                // 从HTTP URL下载文件
                log.info("从HTTP URL下载文件: {}", filePath);
                downloadedFilePath = minioUtil.downloadFromUrl(filePath, localFilePath);
                isOriginalFileUrl = true;
            } else {
                // 获取MinIO中的对象名称
                String objectName = filePath;
                
                // 从对象名称中提取文件名
                String fileName = objectName;
                if (objectName.contains("/")) {
                    fileName = objectName.substring(objectName.lastIndexOf("/") + 1);
                }
                
                String localFilePath = downloadDir + File.separator + fileName;
                
                // 从MinIO下载文件
                log.info("从MinIO下载文件: {}", objectName);
                downloadedFilePath = minioUtil.downloadFileToLocal(
                        resolvedProperties.getBucketName(), 
                        objectName, 
                        localFilePath
                );
            }
            
            if (downloadedFilePath == null) {
                throw new IllegalArgumentException("下载文件失败: " + filePath);
            }
            
            downloadedFile = new File(downloadedFilePath);
            
            // 检查文件类型
            if (!downloadedFile.getName().toLowerCase().endsWith(".pdf")) {
                throw new IllegalArgumentException("不支持的文件类型，仅支持PDF格式");
            }
            
            // 转换PDF为PNG
            pngFiles = convertPdfToPng(downloadedFile, resolvedProperties);
            
            if (pngFiles.isEmpty()) {
                throw new IllegalArgumentException("PDF转换失败，没有生成PNG文件");
            }
            
            // 上传所有PNG文件到MinIO
             String pdfBaseName = downloadedFile.getName().substring(0, downloadedFile.getName().lastIndexOf('.'));
             List<String> uploadedPaths = new ArrayList<>();
             
             for (int i = 0; i < pngFiles.size(); i++) {
                 File pngFile = pngFiles.get(i);
                 // 生成新的文件名：PDF文件名+页码后缀
                 String newFileName = String.format("%s_%03d.%s", pdfBaseName, i + 1, resolvedProperties.getOutputFormat());
                 String objectName = generateObjectName(pdfBaseName, newFileName);
                 
                 String uploadedPath = minioUtil.uploadFileWithOptions(
                         convertFileToMultipartFile(pngFile),
                         resolvedProperties.getBucketName(),
                         objectName,
                         null,  // 不设置过期时间
                         true,  // 公开访问
                         null   // 不设置文件限制
                 );
                 
                 if (uploadedPath == null) {
                     throw new IllegalArgumentException("上传PNG文件失败: " + newFileName);
                 }
                 
                 uploadedPaths.add(uploadedPath);
                 log.info("已上传图片: {}", uploadedPath);
             }
             
             if (uploadedPaths.isEmpty()) {
                 throw new IllegalArgumentException("没有成功上传任何PNG文件");
             }
            
            // 删除原始PDF文件（如果配置了删除）
            if (resolvedProperties.isDeleteOriginal()) {
                boolean deleteSuccess = false;
                if (isOriginalFileUrl) {
                    log.info("删除原始PDF文件(URL): {}", originalFilePath);
                    deleteSuccess = minioUtil.deleteFileFromUrl(originalFilePath);
                } else if (originalFilePath != null && originalBucketName != null) {
                    log.info("删除原始PDF文件: 存储桶={}, 对象={}", originalBucketName, originalFilePath);
                    deleteSuccess = minioUtil.deleteFile(originalBucketName, originalFilePath);
                }
                
                if (!deleteSuccess) {
                    log.warn("删除原始PDF文件失败: {}", originalFilePath);
                } else {
                    log.info("原始PDF文件删除成功");
                }
            }
            LocalDate today = LocalDate.now();
            result =  String.format("pdf2png/%d/%02d/%02d/%s",
                    today.getYear(),
                    today.getMonthValue(),
                    today.getDayOfMonth(),
                    pdfBaseName);

            
            // 设置成功状态和输出
            nodeContext.setStatus("completed");
            
            // 处理输出映射
            processOutputs(context);
            
        } catch (Exception e) {
            log.error("PDF转PNG失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            // 清理临时文件
            if (downloadedFile != null) {
                downloadedFile.delete();
            }
            for (File pngFile : pngFiles) {
                if (pngFile != null) {
                    pngFile.delete();
                }
            }
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }
        
        log.info("PDF转PNG节点执行结束，节点ID：{}", getId());
        return context;
    }

    /**
     * 将PDF文件转换为PNG图片
     * 
     * @param pdfFile PDF文件
     * @param properties 配置属性
     * @return PNG文件列表
     */
    private List<File> convertPdfToPng(File pdfFile, Properties properties) throws IOException {
        List<File> pngFiles = new ArrayList<>();
        
        try (PDDocument document = Loader.loadPDF(pdfFile)) {
            PDFRenderer pdfRenderer = new PDFRenderer(document);
            int pageCount = document.getNumberOfPages();
            
            log.info("PDF文件共{}页，开始转换", pageCount);
            
            for (int page = 0; page < pageCount; page++) {
                // 渲染页面为图片
                BufferedImage image = pdfRenderer.renderImageWithDPI(page, properties.getDpi(), ImageType.RGB);
                
                // 生成PNG文件名（临时文件名）
                 String baseName = pdfFile.getName().substring(0, pdfFile.getName().lastIndexOf('.'));
                 String pngFileName = String.format("%s_temp_%03d.%s", baseName, page + 1, properties.getOutputFormat());
                 File pngFile = new File(pdfFile.getParent(), pngFileName);
                
                // 保存图片
                ImageIO.write(image, properties.getOutputFormat(), pngFile);
                pngFiles.add(pngFile);
                
                log.info("已转换第{}页: {}", page + 1, pngFile.getName());
            }
            
            log.info("PDF转换完成，共生成{}个图片文件", pngFiles.size());
        }
        
        return pngFiles;
    }
    

    
    /**
     * 将File转换为MultipartFile
     * 
     * @param file 文件
     * @return MultipartFile
     */
    private MultipartFile convertFileToMultipartFile(File file) throws IOException {
        return new MultipartFile() {
            @Override
            public String getName() {
                return "file";
            }

            @Override
            public String getOriginalFilename() {
                return file.getName();
            }

            @Override
            public String getContentType() {
                return MediaType.APPLICATION_OCTET_STREAM_VALUE;
            }

            @Override
            public boolean isEmpty() {
                return file.length() == 0;
            }

            @Override
            public long getSize() {
                return file.length();
            }

            @Override
            public byte[] getBytes() throws IOException {
                return FileUtil.readBytes(file);
            }

            @Override
            public InputStream getInputStream() throws IOException {
                return new FileInputStream(file);
            }

            @Override
            public void transferTo(File dest) throws IOException, IllegalStateException {
                FileUtil.copy(file, dest, true);
            }
        };
    }
    
    /**
     * 生成对象名称
     * 
     * @param pdfBaseName PDF文件基础名称（不含扩展名）
     * @param fileName 图片文件名
     * @return 对象名称
     */
    private String generateObjectName(String pdfBaseName, String fileName) {
        LocalDate today = LocalDate.now();
        return String.format("pdf2png/%d/%02d/%02d/%s/%s",
                today.getYear(), 
                today.getMonthValue(), 
                today.getDayOfMonth(),
                pdfBaseName,  // 使用PDF文件名作为文件夹
                fileName);
    }

    @Data
    @NoArgsConstructor
    public static class Properties {
        /**
         * PDF文件路径
         */
        private String filePath;
        
        /**
         * 存储桶名称
         */
        private String bucketName;
        
        /**
         * DPI设置，默认150
         */
        private float dpi = 150f;
        
        /**
         * 输出格式，默认PNG
         */
        private String outputFormat = "PNG";
        
        /**
         * 是否删除原始PDF文件，默认false
         */
        private boolean deleteOriginal = false;
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    @Override
    public String desc() {
        return "PDF转PNG节点";
    }
}