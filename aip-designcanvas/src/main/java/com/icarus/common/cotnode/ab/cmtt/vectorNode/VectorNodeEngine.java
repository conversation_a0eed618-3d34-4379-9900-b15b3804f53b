package com.icarus.common.cotnode.ab.cmtt.vectorNode;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.common.cotnode.ab.cmtt.vectorNode.entity.AivecChunk;
import com.icarus.common.cotnode.ab.cmtt.vectorNode.entity.AivecDirectory;
import com.icarus.common.cotnode.ab.cmtt.vectorNode.entity.AivecFile;
import com.icarus.common.cotnode.ab.cmtt.vectorNode.service.IAivecChunkService;
import com.icarus.common.cotnode.ab.cmtt.vectorNode.service.IAivecDirectoryService;
import com.icarus.common.cotnode.ab.cmtt.vectorNode.service.IAivecFileService;
import com.icarus.common.rest.BaseResponse;
import com.icarus.constant.ValidGroups;
import com.icarus.sdk.client.utils.LLMClientUtil;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import java.lang.reflect.Array;
import java.time.LocalDateTime;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

/**
 * 向量节点引擎
 * <p>
 * 负责处理向量相关操作，包括目录、文件和文件切片的增删改查及分页查询功能。
 * 该引擎支持通过统一的入口方法 processCRUD 来处理不同类型的操作请求，
 * 以及通过 processVectorRetrieve 方法处理向量检索请求。
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class VectorNodeEngine {

    /**
     * 分页查询请求基类
     * <p>
     * 定义了分页查询的基本参数，包括当前页码和每页大小
     * </p>
     */
    @Data
    public static class PageQueryReq {
        /** 当前页码，默认为第1页 */
        private Integer current = 1;
        /** 每页大小，默认为10条记录 */
        private Integer size = 10;
    }
    
    /**
     * 处理向量检索请求
     * <p>
     * 将文本查询转换为向量，并在向量数据库中查找最相似的文档切片
     * </p>
     *
     * @param bodyJson 包含查询文本、结果数量限制和相似度阈值的JSON字符串
     * @return 相似文档切片列表
     */
    public BaseResponse processVectorRetrieve(String bodyJson) {
        // 获取切片处理器并调用向量检索方法
        return SpringUtil.getBean(AivecChunkHandlerVector.class)
                .searchSimilar(JSONUtil.toBean(bodyJson, AivecChunkHandlerVector.ChunkVectorRetrieveReq.class, true));
    }

    /**
     * 处理向量操作请求
     *
     * @param vectorOperationType 操作类型，如创建、删除、更新等
     * @param vectorOperationObject 操作对象，如目录、文件、切片等
     * @param bodyJson 请求体JSON字符串，包含操作所需的数据
     * @return 操作结果响应
     */
    public BaseResponse processCRUD(@NotNull(message = "操作类型不能为空") VectorNode.VectorOperationType vectorOperationType,
                                @NotNull(message = "操作对象不能为空") VectorNode.VectorOperationObject vectorOperationObject,
                                String bodyJson) {
        // 根据操作对象类型获取对应的处理器Bean，并调用handle方法处理请求
        return ((VectorBaseHandler) SpringUtil.getBean(vectorOperationObject.getHandlerClass()))
                .handle(vectorOperationType, vectorOperationObject, bodyJson);
    }

    /**
     * 基础处理器抽象类
     * <p>
     * 定义了处理各种操作类型的通用方法和流程
     * </p>
     *
     * @param <T> 处理的实体类型
     */
    public static abstract class VectorBaseHandler<T> {

        /**
         * 处理请求的统一入口
         *
         * @param vectorOperationType 操作类型
         * @param vectorOperationObject 操作对象
         * @param bodyJson 请求体JSON
         * @return 操作结果响应
         */
        public BaseResponse handle(VectorNode.VectorOperationType vectorOperationType, VectorNode.VectorOperationObject vectorOperationObject, String bodyJson) {
            // 根据操作类型调用不同的处理方法
            switch (vectorOperationType) {
                case CREATE:
                    // 创建操作：将JSON转换为实体对象，并调用create方法
                    return this.create(JSONUtil.toBean(bodyJson, vectorOperationObject.getEntityClass(), true));
                case DELETE:
                    // 删除操作：将JSON转换为ID，并调用delete方法
                    return this.delete(StrUtil.isBlank(bodyJson) ? null : bodyJson);
                case UPDATE:
                    // 更新操作：将JSON转换为实体对象，并调用update方法
                    return this.update(JSONUtil.toBean(bodyJson, vectorOperationObject.getEntityClass(), true));
                case GET:
                    // 获取操作：将JSON转换为ID，并调用get方法
                    return this.get(StrUtil.isBlank(bodyJson) ? null : bodyJson);
                case LIST:
                    // 列表操作：调用list方法获取所有记录
                    return this.list();
                case QUERY:
                    // 查询操作：将JSON转换为查询请求对象，并调用page方法
                    return this.page(JSONUtil.toBean(bodyJson, vectorOperationObject.getPageReqClass(), true));
                    // 将知识库和切片绑定
                case COMBINE:
                    return this.combine(JSONUtil.toBean(bodyJson,CombineDTO.class, true));
                default:
                    // 不支持的操作类型，抛出异常
                    throw new IllegalArgumentException("不支持的操作类型: " + vectorOperationType.name());
            }
        }


        @Data
        public static class CombineDTO{
            //知识库id
            private String systemId;

            private List<String> chunkIds;

        }

        /**
         * 创建实体记录
         * @param bean 实体对象
         * @return 创建结果
         */
        public abstract BaseResponse create(T bean);
        
        /**
         * 删除实体记录
         * @param bean 实体ID
         * @return 删除结果
         */
        public abstract BaseResponse delete(String bean);
        
        /**
         * 更新实体记录
         * @param bean 实体对象
         * @return 更新结果
         */
        public abstract BaseResponse update(T bean);
        
        /**
         * 获取实体记录详情
         * @param bean 实体ID
         * @return 实体详情
         */
        public abstract BaseResponse get(String bean);
        
        /**
         * 获取实体记录列表
         * @return 实体列表
         */
        public abstract BaseResponse list();
        
        /**
         * 分页查询实体记录
         * @param req 分页查询请求
         * @return 分页查询结果
         */
        public abstract BaseResponse page(PageQueryReq req);

        public abstract BaseResponse combine(CombineDTO combineDTO);
    }

    /**
     * 文件切片处理器
     * <p>
     * 处理文件切片相关的增删改查及分页查询操作
     * </p>
     */
    @Component
    @RequiredArgsConstructor
    @Validated
    public static class AivecChunkHandlerVector extends VectorBaseHandler<AivecChunk> {

        /** 文件切片服务 */
        private final IAivecChunkService chunkService;
        /** 数据校验器 */
        private final Validator validator;
        /** 文件服务 */
        private final IAivecFileService fileService;

        /**
         * 文件切片分页查询请求
         */
        @Data
        public static class ChunkPageQueryReq extends PageQueryReq {
            /** 关键词，用于模糊匹配 */
            private String keyword;
            /** 文件ID，用于精确匹配 */
            private Integer fileId;
            /** 目录ID，用于精确匹配 */
            private Integer dirId;
            /** 文件名, 用于精确匹配 */
            private String fileName;
        }

        /**
         * 文件切片向量化检索请求体
         * <p>
         * 用于语义搜索，将查询文本转换为向量后进行相似度检索
         * </p>
         */
        @Data
        public static class ChunkVectorRetrieveReq {
            /** 查询文本，将被转换为向量 */
            private String query;
            /** 返回结果数量限制，默认为10 */
            private Integer limit = 10;
            /** 相似度阈值，低于此值的结果将被过滤，默认为0.7 */
            private Float threshold = 0.7f;
            /**
             * 知识库id
             */
            private String baseId;
            /**
             * 任务id
             */
            private String taskId;
        }

        /**
         * 创建文件切片
         *
         * @param chunk 文件切片对象
         * @return 创建结果
         */
        public BaseResponse<AivecChunk> create(AivecChunk chunk) {
            // 手动验证参数
            Set<ConstraintViolation<AivecChunk>> violations = validator.validate(chunk, ValidGroups.Create.class);
            if (!violations.isEmpty()) {
                // 构建错误信息
                StringBuilder sb = new StringBuilder();
                Iterator<ConstraintViolation<AivecChunk>> iterator = violations.iterator();
                while (iterator.hasNext()) {
                    ConstraintViolation<AivecChunk> violation = iterator.next();
                    sb.append(violation.getMessage()).append("; ");
                }
                throw new RuntimeException(sb.toString());
            }

            // 校验文件记录数据一致性
            checkFileConsistency(chunk);

            // 生成向量嵌入
            String embeddingVector = LLMClientUtil.embedding(chunk.getChunkContent());
            // 确保向量数据格式正确，必须以 [ 开头，以 ] 结尾
            if (embeddingVector != null && !embeddingVector.startsWith("[")) {
                embeddingVector = "[" + embeddingVector + "]";
            }
            chunk.setEmbeddingVector(embeddingVector);

            // 设置创建时间和更新时间
            chunk.setCreateTime(LocalDateTime.now());
            chunk.setUpdateTime(LocalDateTime.now());
            // 保存到数据库
            chunkService.save(chunk);
            return BaseResponse.ok(chunk);
        }

        /**
         * 删除文件切片
         *
         * @param chunkId 切片ID
         * @return 删除结果
         */
        public BaseResponse<Void> delete(String chunkId) {
            // 校验ID不为空
            if (chunkId == null) {
                throw new RuntimeException("切片ID不能为空");
            }
            // 从数据库中删除
            chunkService.removeById(chunkId);
            return BaseResponse.ok(null);
        }

        /**
         * 更新文件切片
         *
         * @param chunk 文件切片对象
         * @return 更新结果
         */
        public BaseResponse<AivecChunk> update(AivecChunk chunk) {
            // 手动验证参数
            Set<ConstraintViolation<AivecChunk>> violations = validator.validate(chunk, ValidGroups.Update.class);
            if (!violations.isEmpty()) {
                // 构建错误信息
                StringBuilder sb = new StringBuilder();
                Iterator<ConstraintViolation<AivecChunk>> iterator = violations.iterator();
                while (iterator.hasNext()) {
                    ConstraintViolation<AivecChunk> violation = iterator.next();
                    sb.append(violation.getMessage()).append("; ");
                }
                throw new RuntimeException(sb.toString());
            }

            // 校验文件记录数据一致性
            checkFileConsistency(chunk);

            // 获取原有记录，确保存在
            AivecChunk byId = chunkService.getById(chunk.getChunkId());
            if (byId == null){
                throw new RuntimeException("根据ID获取切片失败");
            }

            // 如果内容发生变化，重新生成向量嵌入
            if (!StrUtil.equals(byId.getChunkContent(), chunk.getChunkContent())) {
                String embeddingVector = LLMClientUtil.embedding(chunk.getChunkContent());
                // 确保向量数据格式正确，必须以 [ 开头，以 ] 结尾
                if (embeddingVector != null && !embeddingVector.startsWith("[")) {
                    embeddingVector = "[" + embeddingVector + "]";
                }
                chunk.setEmbeddingVector(embeddingVector);
            } else {
                // 内容未变，保留原向量
                chunk.setEmbeddingVector(byId.getEmbeddingVector());
            }

            // 更新时间，保留原创建时间
            chunk.setUpdateTime(LocalDateTime.now());
            chunk.setCreateTime(byId.getCreateTime());
            // 更新到数据库
            chunkService.updateById(chunk);
            return BaseResponse.ok(chunk);
        }

        /**
         * 获取文件切片详情
         *
         * @param chunkId 切片ID
         * @return 切片详情
         */
        public BaseResponse<AivecChunk> get(String chunkId) {
            // 校验ID不为空
            if (chunkId == null) {
                throw new RuntimeException("切片ID不能为空");
            }
            // 从数据库获取
            return BaseResponse.ok(chunkService.getById(chunkId));
        }

        /**
         * 获取所有文件切片
         *
         * @return 切片列表
         */
        public BaseResponse<List<AivecChunk>> list() {
            // 获取所有切片记录
            return BaseResponse.ok(chunkService.list());
        }
        
        /**
         * 分页查询文件切片
         *
         * @param req 分页查询请求
         * @return 分页查询结果
         */
        public BaseResponse<Page<AivecChunk>> page(PageQueryReq req) {
            // 类型转换
            ChunkPageQueryReq chunkReq = (ChunkPageQueryReq) req;
            // 创建分页对象
            Page<AivecChunk> page = new Page<>(chunkReq.getCurrent(), chunkReq.getSize());
            // 创建查询条件
            LambdaQueryWrapper<AivecChunk> wrapper = new LambdaQueryWrapper<>();
            
            // 关键词模糊查询
            if (StrUtil.isNotBlank(chunkReq.getKeyword())) {
                wrapper.and(qw -> qw
                        .like(AivecChunk::getFileName, chunkReq.getKeyword())
                        .or()
                        .like(AivecChunk::getDirName, chunkReq.getKeyword())
                        .or()
                        .likeRight(AivecChunk::getDirPath, chunkReq.getKeyword())
                        .or()
                        .like(AivecChunk::getFileTags, chunkReq.getKeyword())
                );
            }
            // 文件ID精确查询
            if (chunkReq.getFileId() != null) {
                wrapper.eq(AivecChunk::getFileId, chunkReq.getFileId());
            }
            // 目录ID精确查询
            if (chunkReq.getDirId() != null) {
                wrapper.eq(AivecChunk::getDirId, chunkReq.getDirId());
            }
            
            // 执行分页查询
            return BaseResponse.ok(chunkService.page(page, wrapper));
        }

        @Override
        public BaseResponse combine(CombineDTO combineDTO) {
            if (CollectionUtil.isEmpty(combineDTO.getChunkIds())){
                log.info("没有需要关联的文件片");
                return BaseResponse.ok(null);
            }

            //移除原有的
            log.info("开始释放原先文件片");
            List<AivecChunk> oldOne = chunkService.getChunksBySystemId(combineDTO.getSystemId());
            for (AivecChunk aivecChunk : oldOne) {
                JSONArray objects = JSONUtil.parseArray(aivecChunk.getKnowledgeBaseIds());
                objects.remove(combineDTO.getSystemId());
                aivecChunk.setKnowledgeBaseIds(objects.toString());
            }
            chunkService.updateBatchById(oldOne);


            log.info("开始关联文件片");
            List<AivecChunk> list = chunkService.lambdaQuery().in(AivecChunk::getChunkId, combineDTO.getChunkIds()).list();
            for (AivecChunk aivecChunk : list) {
                JSONArray objects = JSONUtil.parseArray(aivecChunk.getKnowledgeBaseIds());
                if(objects.contains(combineDTO.getSystemId())){
                    continue;
                }
                objects.add(combineDTO.getSystemId());
                aivecChunk.setKnowledgeBaseIds(objects.toString());
            }
            return BaseResponse.ok(chunkService.updateBatchById(list));
        }

        /**
         * 搜索相似向量
         * <p>
         * 将查询文本转换为向量，并在向量数据库中查找最相似的文档切片
         * </p>
         *
         * @param req 向量检索请求
         * @return 相似文档切片列表
         */
        public BaseResponse<List<AivecChunk>> searchSimilar(ChunkVectorRetrieveReq req) {
            // 获取查询文本的向量表示
            String queryVector = LLMClientUtil.embedding(req.getQuery());
            // 确保向量数据格式正确，必须以 [ 开头，以 ] 结尾
            if (queryVector != null && !queryVector.startsWith("[")) {
                queryVector = "[" + queryVector + "]";
            }
            
            // 调用服务执行相似向量搜索
            List<AivecChunk> aivecChunks = chunkService.searchSimilarVectors(queryVector, req.getLimit(), req.getThreshold(),req.getBaseId(),req.getTaskId());
            return BaseResponse.ok(aivecChunks);
        }

        /**
         * 校验文件记录数据一致性
         * 确保切片中的文件相关信息与数据库中的文件记录一致
         *
         * @param chunk 文件切片对象
         */
        private void checkFileConsistency(AivecChunk chunk) {
//            if (chunk.getFileId() == null) {
//                throw new RuntimeException("文件ID不能为空");
//            }
//
//            // 获取文件记录
            AivecFile file = fileService.getById(chunk.getFileId());
//            if (file == null) {
//                throw new RuntimeException("切片关联的文件不存在，请检查文件ID");
//            }
//
//            // 校验文件名一致性
//            if (!StrUtil.equals(file.getFileName(), chunk.getFileName())) {
//                throw new RuntimeException("切片文件名与文件记录不一致，切片文件名: " + chunk.getFileName() + "，文件记录名: " + file.getFileName());
//            }
//
//            // 校验目录ID一致性
//            if (!file.getDirId().equals(chunk.getDirId())) {
//                throw new RuntimeException("切片目录ID与文件记录不一致，切片目录ID: " + chunk.getDirId() + "，文件记录目录ID: " + file.getDirId());
//            }
//
//            // 校验目录名一致性
//            if (!StrUtil.equals(file.getDirName(), chunk.getDirName())) {
//                throw new RuntimeException("切片目录名与文件记录不一致，切片目录名: " + chunk.getDirName() + "，文件记录目录名: " + file.getDirName());
//            }
//
//            // 校验目录路径一致性
//            if (!StrUtil.equals(file.getDirPath(), chunk.getDirPath())) {
//                throw new RuntimeException("切片目录路径与文件记录不一致，切片目录路径: " + chunk.getDirPath() + "，文件记录目录路径: " + file.getDirPath());
//            }
//            // 校验应用名称一致性
//            if (!StrUtil.equals(file.getAppName(), chunk.getAppName())) {
//                throw new RuntimeException("切片应用名称与文件记录不一致，切片应用名称: " + chunk.getAppName() + "，文件记录应用名称: " + file.getAppName());
//            }

            
            // 校验文件标签一致性 (如果需要)
            if (file!=null&&StrUtil.isNotBlank(file.getTags())) {
                // 文件的标签是JSON字符串，切片的标签是List<String>，需要转换后比较
                List<String> fileTagsList = JSONUtil.toList(file.getTags(), String.class);
                List<String> chunkTagsList = JSONUtil.toList(chunk.getFileTags(), String.class);
                // 更新切片的文件标签以匹配文件记录
                chunk.setFileTags(JSONUtil.toJsonStr(CollUtil.union(fileTagsList, chunkTagsList)));
            }

            if (file!=null&&StrUtil.isNotBlank(file.getTagIds())) {
                // 文件的标签是JSON字符串，切片的标签是List<String>，需要转换后比较
                List<String> fileTagsList = JSONUtil.toList(file.getTagIds(), String.class);
                List<String> chunkTagsList = JSONUtil.toList(chunk.getTagIds(), String.class);
                // 更新切片的文件标签以匹配文件记录
                chunk.setTagIds(JSONUtil.toJsonStr(CollUtil.union(fileTagsList, chunkTagsList)));
            }

            if (file!=null&&StrUtil.isNotBlank(file.getKnowledgeBasePath())) {
                // 文件的标签是JSON字符串，切片的标签是List<String>，需要转换后比较
                List<String> fileTagsList = JSONUtil.toList(file.getKnowledgeBasePath(), String.class);
                List<String> chunkTagsList = JSONUtil.toList(chunk.getKnowledgeBasePath(), String.class);
                // 更新切片的文件标签以匹配文件记录
                chunk.setKnowledgeBasePath(JSONUtil.toJsonStr(CollUtil.union(fileTagsList, chunkTagsList)));
            }

            if (file!=null&&StrUtil.isNotBlank(file.getKnowledgeBaseIds())) {
                // 文件的标签是JSON字符串，切片的标签是List<String>，需要转换后比较
                List<String> fileTagsList = JSONUtil.toList(file.getKnowledgeBaseIds(), String.class);
                List<String> chunkTagsList = JSONUtil.toList(chunk.getKnowledgeBaseIds(), String.class);
                // 更新切片的文件标签以匹配文件记录
                chunk.setKnowledgeBaseIds(JSONUtil.toJsonStr(CollUtil.union(fileTagsList, chunkTagsList)));
            }
        }
    }

    /**
     * 目录处理器
     * <p>
     * 处理目录相关的增删改查及分页查询操作
     * </p>
     */
    @Component
    @RequiredArgsConstructor
    @Validated
    public static class AivecDirectoryHandlerVector extends VectorBaseHandler<AivecDirectory> {

        /** 目录服务 */
        private final IAivecDirectoryService directoryService;
        /** 数据校验器 */
        private final Validator validator;

        /**
         * 目录分页查询请求
         */
        @Data
        public static class DirPageQueryReq extends PageQueryReq {
            /** 关键词，用于模糊匹配 */
            private String keyword;
            /** 父目录ID，用于精确匹配 */
            private Integer parentDirId;
        }

        /**
         * 创建目录
         *
         * @param directory 目录对象
         * @return 创建结果
         */
        public BaseResponse<AivecDirectory> create(AivecDirectory directory) {
            // 手动验证参数
            Set<ConstraintViolation<AivecDirectory>> violations = validator.validate(directory, ValidGroups.Create.class);
            if (!violations.isEmpty()) {
                // 构建错误信息
                StringBuilder sb = new StringBuilder();
                Iterator<ConstraintViolation<AivecDirectory>> iterator = violations.iterator();
                while (iterator.hasNext()) {
                    ConstraintViolation<AivecDirectory> violation = iterator.next();
                    sb.append(violation.getMessage()).append("; ");
                }
                throw new RuntimeException(sb.toString());
            }

            //校验目录全路径不可重复
            LambdaQueryWrapper<AivecDirectory> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AivecDirectory::getDirPath, directory.getDirPath());
            if (directoryService.count(wrapper) > 0) {
                throw new RuntimeException("保存失败, 目录全路径已存在");
            }

            //校验标签格式
            if (StrUtil.isNotBlank(directory.getTags())) {
                try {
                    JSONUtil.toList(directory.getTags(), String.class);
                } catch (Exception e) {
                    throw new RuntimeException("目录标签格式不正确, 请检查数据");
                }
            }
            
            // 设置创建时间和更新时间
            directory.setCreateTime(LocalDateTime.now());
            directory.setUpdateTime(LocalDateTime.now());
            // 保存到数据库
            directoryService.save(directory);
            return BaseResponse.ok(directory);
        }

        /**
         * 删除目录
         *
         * @param dirId 目录ID
         * @return 删除结果
         */
        public BaseResponse<Void> delete(String dirId) {
            if (dirId == null) {
                throw new RuntimeException("目录ID不能为空");
            }

            // 从数据库中删除
            directoryService.removeById(dirId);
            return BaseResponse.ok(null);
        }

        /**
         * 更新目录
         *
         * @param directory 目录对象
         * @return 更新结果
         */
        public BaseResponse<AivecDirectory> update(AivecDirectory directory) {
            // 手动验证参数
            Set<ConstraintViolation<AivecDirectory>> violations = validator.validate(directory, ValidGroups.Update.class);
            if (!violations.isEmpty()) {
                // 构建错误信息
                StringBuilder sb = new StringBuilder();
                Iterator<ConstraintViolation<AivecDirectory>> iterator = violations.iterator();
                while (iterator.hasNext()) {
                    ConstraintViolation<AivecDirectory> violation = iterator.next();
                    sb.append(violation.getMessage()).append("; ");
                }
                throw new RuntimeException(sb.toString());
            }

            //校验标签格式
            if (StrUtil.isNotBlank(directory.getTags())) {
                try {
                    JSONUtil.toList(directory.getTags(), String.class);
                } catch (Exception e) {
                    throw new RuntimeException("目录标签格式不正确, 请检查数据");
                }
            }

            // 获取原有记录，确保存在
            AivecDirectory byId = directoryService.getById(directory.getDirId());
            if (byId == null){
                throw new RuntimeException("根据ID获取目录失败");
            }

            //校验目录全路径不可重复
            LambdaQueryWrapper<AivecDirectory> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AivecDirectory::getDirPath, directory.getDirPath())
                    .ne(AivecDirectory::getDirId, directory.getDirId());
            if (directoryService.count(wrapper) > 0) {
                throw new RuntimeException("保存失败, 目录全路径已存在");
            }

            // 更新时间，保留原创建时间
            directory.setUpdateTime(LocalDateTime.now());
            directory.setCreateTime(byId.getCreateTime());
            // 更新到数据库
            directoryService.updateById(directory);
            return BaseResponse.ok(directory);
        }

        /**
         * 获取目录详情
         *
         * @param dirId 目录ID
         * @return 目录详情
         */
        public BaseResponse<AivecDirectory> get(String dirId) {
            if (dirId == null) {
                throw new RuntimeException("目录ID不能为空");
            }
            // 从数据库获取
            return BaseResponse.ok(directoryService.getById(dirId));
        }

        /**
         * 获取所有目录
         *
         * @return 目录列表
         */
        public BaseResponse<List<AivecDirectory>> list() {
            // 获取所有目录记录
            return BaseResponse.ok(directoryService.list());
        }

        /**
         * 分页查询目录
         *
         * @param req 分页查询请求
         * @return 分页查询结果
         */
        public BaseResponse<Page<AivecDirectory>> page(PageQueryReq req) {
            // 类型转换
            DirPageQueryReq dirReq = (DirPageQueryReq) req;
            // 创建分页对象
            Page<AivecDirectory> page = new Page<>(dirReq.getCurrent(), dirReq.getSize());
            // 创建查询条件
            LambdaQueryWrapper<AivecDirectory> wrapper = new LambdaQueryWrapper<>();

            // 关键词模糊查询
            if (StrUtil.isNotBlank(dirReq.getKeyword())) {
                wrapper.and(qw ->
                        qw.like(AivecDirectory::getDirName, dirReq.getKeyword())
                                .or()
                                .likeRight(AivecDirectory::getDirPath, dirReq.getKeyword())
                                .or()
                                .like(AivecDirectory::getTags, dirReq.getKeyword()));
            }
            // 父目录ID精确查询
            if (dirReq.getParentDirId() != null) {
                wrapper.eq(AivecDirectory::getParentDirId, dirReq.getParentDirId());
            }

            // 执行分页查询
            return BaseResponse.ok(directoryService.page(page, wrapper));
        }

        @Override
        public BaseResponse combine(CombineDTO combineDTO) {
            return null;
        }
    }

    /**
     * 文件处理器
     * <p>
     * 处理文件相关的增删改查及分页查询操作
     * </p>
     */
    @Component
    @RequiredArgsConstructor
    @Validated
    public static class AivecFileHandlerVector extends VectorBaseHandler<AivecFile> {
        /** 目录服务 */
        private final IAivecDirectoryService directoryService;
        /** 文件服务 */
        private final IAivecFileService fileService;
        /** 数据校验器 */
        private final Validator validator;

        /**
         * 文件分页查询请求
         */
        @Data
        public static class FilePageQueryReq extends PageQueryReq {
            /** 用于模糊匹配 */
            private String keyword;
            /** 文件名, 用于精确匹配 */
            private String fileName;
            /** 目录ID，用于精确匹配 */
            private Integer dirId;
            /** 文件类型，用于精确匹配 */
            private String fileType;
        }

        /**
         * 创建文件
         *
         * @param file 文件对象
         * @return 创建结果
         */
        public BaseResponse<AivecFile> create(AivecFile file) {
            // 手动验证参数
            Set<ConstraintViolation<AivecFile>> violations = validator.validate(file, ValidGroups.Create.class);
            if (!violations.isEmpty()) {
                // 构建错误信息
                StringBuilder sb = new StringBuilder();
                Iterator<ConstraintViolation<AivecFile>> iterator = violations.iterator();
                while (iterator.hasNext()) {
                    ConstraintViolation<AivecFile> violation = iterator.next();
                    sb.append(violation.getMessage()).append("; ");
                }
                throw new RuntimeException(sb.toString());
            }

            checkDirInfos(file);

            // 校验同目录下文件名不可重复
            if (fileService.exists(new LambdaQueryWrapper<AivecFile>()
                    .eq(AivecFile::getDirId, file.getDirId())
                    .eq(AivecFile::getFileName, file.getFileName()))) {
                throw new RuntimeException("保存失败, 同目录下文件名不可重复, 请修改文件名称或对应目录");
            }

            AivecDirectory directory = directoryService.getById(file.getDirId());

            // 校验应用名称是否一致
            if (!StrUtil.equals(file.getAppName(), directory.getAppName())) {
                throw new RuntimeException("切片应用名称与文件记录不一致，目录应用名称: " + directory.getAppName() + "，文件记录应用名称: " + file.getAppName());
            }
            
            // 设置创建时间和更新时间
            file.setCreateTime(LocalDateTime.now());
            file.setUpdateTime(LocalDateTime.now());
            
            // 保存到数据库
            fileService.save(file);
            return BaseResponse.ok(file);
        }

        private void checkDirInfos(AivecFile file) {
            //校验目录
            AivecDirectory byId = directoryService.getById(file.getDirId());
            if (byId == null){
                throw new RuntimeException("保存失败, 要保存的文件对应的目录不存在, 请检查数据");
            }
            if (!StrUtil.equals(byId.getDirName(), file.getDirName())) {
                throw new RuntimeException("保存失败, 要保存的文件对应目录名数据不一致, 请检查数据");
            }
            if (!StrUtil.equals(byId.getDirPath(), file.getDirPath())) {
                throw new RuntimeException("保存失败, 要保存的文件对应目录全路径数据不一致, 请检查数据");
            }
            //默认补充目录tags
            if (StrUtil.isNotBlank(byId.getTags())) {
                List<String> dirTags = JSONUtil.toList(byId.getTags(), String.class);
                List<String> fileTags = JSONUtil.toList(file.getTags(), String.class);
                file.setTags(JSONUtil.toJsonStr(CollUtil.union(fileTags, dirTags)));
            }
        }

        /**
         * 删除文件
         *
         * @param fileId 文件ID
         * @return 删除结果
         */
        public BaseResponse<Void> delete(String fileId) {
            // 校验ID不为空
            if (fileId == null) {
                throw new RuntimeException("文件ID不能为空");
            }
            // 从数据库中删除
            fileService.removeById(fileId);
            return BaseResponse.ok(null);
        }

        /**
         * 更新文件
         *
         * @param file 文件对象
         * @return 更新结果
         */
        public BaseResponse<AivecFile> update(AivecFile file) {
            // 手动验证参数
            Set<ConstraintViolation<AivecFile>> violations = validator.validate(file, ValidGroups.Update.class);
            if (!violations.isEmpty()) {
                // 构建错误信息
                StringBuilder sb = new StringBuilder();
                Iterator<ConstraintViolation<AivecFile>> iterator = violations.iterator();
                while (iterator.hasNext()) {
                    ConstraintViolation<AivecFile> violation = iterator.next();
                    sb.append(violation.getMessage()).append("; ");
                }
                throw new RuntimeException(sb.toString());
            }
            
            // 获取原有记录，确保存在
            AivecFile byId = fileService.getById(file.getFileId());
            if (byId == null) {
                throw new RuntimeException("根据ID获取文件失败");
            }

            checkDirInfos(file);

            // 更新时间，保留原创建时间
            file.setUpdateTime(LocalDateTime.now());
            file.setCreateTime(byId.getCreateTime());
            // 更新到数据库
            fileService.updateById(file);
            return BaseResponse.ok(file);
        }

        /**
         * 获取文件详情
         *
         * @param fileId 文件ID
         * @return 文件详情
         */
        public BaseResponse<AivecFile> get(String fileId) {
            // 校验ID不为空
            if (fileId == null) {
                throw new RuntimeException("文件ID不能为空");
            }
            // 从数据库获取
            return BaseResponse.ok(fileService.getById(fileId));
        }

        /**
         * 获取所有文件
         *
         * @return 文件列表
         */
        public BaseResponse<List<AivecFile>> list() {
            // 获取所有文件记录
            return BaseResponse.ok(fileService.list());
        }

        /**
         * 分页查询文件
         *
         * @param req 分页查询请求
         * @return 分页查询结果
         */
        public BaseResponse<Page<AivecFile>> page(PageQueryReq req) {
            // 类型转换
            FilePageQueryReq fileReq = (FilePageQueryReq) req;
            // 创建分页对象
            Page<AivecFile> page = new Page<>(fileReq.getCurrent(), fileReq.getSize());
            // 创建查询条件
            LambdaQueryWrapper<AivecFile> wrapper = new LambdaQueryWrapper<>();

            // 关键字模糊查询
            if (StrUtil.isNotBlank(fileReq.getKeyword())) {
                wrapper.and(qw -> qw
                        .like(AivecFile::getFileName, fileReq.getKeyword())
                        .or()
                        .likeRight(AivecFile::getDirPath, fileReq.getKeyword())
                        .or()
                        .like(AivecFile::getDirName, fileReq.getKeyword())
                        .or()
                        .like(AivecFile::getTags, fileReq.getKeyword())
                );
            }
            // 文件名精确查询
            if (StrUtil.isNotBlank(fileReq.getFileName())) {
                wrapper.eq(AivecFile::getFileName, fileReq.getFileName());
            }
            // 目录ID精确查询
            if (fileReq.getDirId() != null) {
                wrapper.eq(AivecFile::getDirId, fileReq.getDirId());
            }
            // 文件类型精确查询
            if (StrUtil.isNotBlank(fileReq.getFileType())) {
                wrapper.eq(AivecFile::getFileType, fileReq.getFileType());
            }

            // 执行分页查询
            return BaseResponse.ok(fileService.page(page, wrapper));
        }

        @Override
        public BaseResponse combine(CombineDTO combineDTO) {
            return null;
        }
    }
}
