package com.icarus.common.cotnode.flow;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.icarus.common.cotnode.BaseNode;
import com.icarus.common.runtime.NodeContext;
import com.icarus.dto.RuntimeRequest;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@NoArgsConstructor
public class StartNode extends BaseNode {

    public static final String code = "startNode";
    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【开始】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
        nodeContext.setNodeId(nodeId);
        nodeContext.setNodeType("StartNode");
        
        try {
            // 获取请求参数并解析为JSONObject
            RuntimeRequest request = (RuntimeRequest) context.get("request");
            // 设置flowData - 用于流程处理
            setFlowData(context, request.getHeaders(), request.getCommons(), request.getData());
            JSONObject flowData = getFlowData(context);
            if(ObjUtil.isNotNull(flowData)){
                // 设置输入 - 使用第一层key作为输入key
                Map<String, Object> inputs = new HashMap<>();
                for (Map.Entry<String, Object> entry : flowData.entrySet())
                    inputs.put(entry.getKey(), JSONUtil.parse(JSONUtil.toJsonStr(entry.getValue())));  // 深拷贝每个值
                nodeContext.setInputs(new JSONObject(inputs));

                // 设置输出 - 使用第一层key作为输出key
                Map<String, Object> outputs = new HashMap<>();
                for (Map.Entry<String, Object> entry : flowData.entrySet())
                    outputs.put(entry.getKey(), JSONUtil.parse(JSONUtil.toJsonStr(entry.getValue())));  // 深拷贝每个值
                nodeContext.setOutputs(new JSONObject(outputs));
            }
            // 设置节点执行状态
            nodeContext.setStatus("completed");
            // 设置节点执行开始时间时间
            nodeContext.setStartTime(System.currentTimeMillis());
        } catch (Exception e) {
            log.error("开始节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }
        
        log.info("开始节点执行结束，节点ID：{}", getId());
        return context;
    }

    /**
     * 获取值的类型
     */
    private String getValueType(Object value) {
        if (value == null) return "string";
        if (value instanceof JSONObject) return "object";
        if (value instanceof Number) return "number";
        if (value instanceof Boolean) return "boolean";
        return "string";
    }

    @Override
    public String desc() {
        return "开始节点";
    }
}