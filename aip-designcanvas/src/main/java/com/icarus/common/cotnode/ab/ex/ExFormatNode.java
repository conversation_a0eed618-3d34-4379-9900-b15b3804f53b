package com.icarus.common.cotnode.ab.ex;

import cn.hutool.json.JSONObject;
import com.icarus.common.cotnode.BaseNode;
import com.icarus.common.runtime.FileConverterFactory;
import com.icarus.common.runtime.NodeContext;
import com.icarus.entity.SceneElement;
import com.icarus.sdk.springboot.base.utils.SpringContextUtils;
import com.icarus.service.FileStorageService;
import com.icarus.service.SpireService;
import com.icarus.utils.FileUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class ExFormatNode extends BaseNode {

    private List<Map<String, Object>> result;

    /**
     * 将文件处理成easyEX格式（文字无格式、公式md、表格用table标签、图片依据同样规则识别，目录处理成ul标签）
     *
     * @param context 流程上下文，包含：
     *                1. 请求参数
     *                2. 每个节点的执行结果
     *                3. 流程状态信息
     * @return
     */
    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【ElementHandleNode处理】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
        
        // 创建一个临时的HashMap来存储数据
        Map<String, Object> tempOutputs = new HashMap<>();
        
        // 定义页数阈值
        final int PAGE_THRESHOLD = 99;
        
        File pdfFile = null;

        try {
            // 解析属性配置
            Properties resolvedProperties = resolveProperties(context, Properties.class);
            nodeContext.setProperties(new JSONObject(resolvedProperties));

            // 验证PDF文件路径
            String pdfPath = resolvedProperties.getFilePath();
            //获取文件的后缀名
            String suffixName = pdfPath.substring(pdfPath.lastIndexOf(".") + 1).toLowerCase();
            if (StringUtils.isBlank(pdfPath)) {
                throw new IllegalArgumentException(suffixName+"文件路径不能为空");
            }
//            if (!pdfPath.toLowerCase().endsWith(".pdf")) {
//                throw new IllegalArgumentException("只支持PDF文件格式");
//            }

            // 获取文件
            pdfFile = getFileFromPath(pdfPath);
            if (pdfFile == null) {
                throw new IllegalArgumentException("无法获取文件: " + pdfPath);
            }

            // 记录输入参数到上下文
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            if (resolvedInputs != null) {
                nodeContext.setInputs(new JSONObject(resolvedInputs));
            }
            //获取实现类
            SpireService converter = FileConverterFactory.getConverter(suffixName);
            // 判断大小，决定要不要分块处理，以防OOM（PDF用页数）
            int pageCount = converter.getPageCount(pdfFile.getAbsolutePath());
            log.info("总页数: {}", pageCount);
            if (pageCount < PAGE_THRESHOLD) {
                // 如果页数小于阈值，处理整个PDF内容
                result = new ArrayList<>();
                List<Map<String, String>> pageResult = converter.convert2EasyEX(pdfFile.getAbsolutePath(), 1, pageCount,resolvedProperties);
                for (Map<String, String> page : pageResult) {
                    result.add(new HashMap<>(page));
                }
                tempOutputs.put("singlePagePdfPath", pdfFile.getAbsolutePath());
                tempOutputs.put("pdfContent", result);
                log.info("文件少于{}页，已处理完整内容", PAGE_THRESHOLD);
            } else {
                // 多页情况下，分块处理，以免OOM
                // 先改成99页，以后再调
            }
            
            // 将结果设置到nodeContext
            nodeContext.setOutputs(new JSONObject(tempOutputs));
            
            // 设置成功状态
            nodeContext.setStatus("completed");
            // 处理输出映射
            processOutputs(context);

        } catch (Exception e) {
            log.error("文件处理失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            // 清理临时文件
            if (pdfFile != null) {
                pdfFile.delete();
            }
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        log.info("文件处理节点执行结束，节点ID：{}", getId());
        return context;
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    /**
     * 从文件路径获取文件对象
     */
    private File getFileFromPath(String filePath) {
        try {
            // 首先尝试通过 FileStorageService 获取
            FileStorageService service = SpringContextUtils.getBean(FileStorageService.class);
            InputStream inputStream = service.getFile(filePath);
            if (inputStream != null) {
                String fileName = "temp_" + filePath.substring(filePath.lastIndexOf("/") + 1);
                return FileUtil.inputStreamToFile(inputStream, fileName);
            }
        } catch (Exception e) {
            log.warn("通过 FileStorageService 获取文件失败: {}", e.getMessage());
        }

        // FileStorageService 获取失败，尝试直接文件路径
        try {
            File directFile = new File(filePath);
            if (directFile.exists() && directFile.isFile()) {
                return directFile;
            }
        } catch (Exception e) {
            log.warn("通过直接文件路径获取文件失败: {}", e.getMessage());
        }

        return null;
    }

    @Data
    @NoArgsConstructor
    public static class Properties {
        private String filePath = "";
        private List<SceneElement> exConfig = new ArrayList<>();
    }

    @Override
    public String desc() {
        return "要素提取-预处理";
    }

}