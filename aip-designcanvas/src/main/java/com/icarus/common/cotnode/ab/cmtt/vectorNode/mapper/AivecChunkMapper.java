package com.icarus.common.cotnode.ab.cmtt.vectorNode.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.icarus.common.cotnode.ab.cmtt.vectorNode.entity.AivecChunk;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 支持QA格式的文件切片表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Mapper
public interface AivecChunkMapper extends BaseMapper<AivecChunk> {

    /**
     * 使用PostgreSQL的向量相似度搜索
     * 使用vector_cosine_ops操作符计算余弦相似度，数值越大表示越相似
     * 使用IVFFLAT索引加速查询
     *
     * @param queryVector 查询向量
     * @param limit 返回结果数量
     * @param threshold 相似度阈值（可选），范围0-1，越大表示越相似
     * @return 相似度排序后的切片列表
     */
    List<AivecChunk> searchSimilarVectors(
        @Param("queryVector") String queryVector,
        @Param("limit") Integer limit,
        @Param("threshold") Float threshold,
        @Param("baseId") String baseId,
        @Param("taskId") String taskId
    );

    /**
     * 使用HNSW索引的向量相似度搜索
     * HNSW索引适合高召回率场景，但索引构建较慢
     *
     * @param queryVector 查询向量
     * @param limit 返回结果数量
     * @param threshold 相似度阈值（可选），范围0-1，越大表示越相似
     * @return 相似度排序后的切片列表
     */
    List<AivecChunk> searchSimilarVectorsWithHNSW(
        @Param("queryVector") float[] queryVector,
        @Param("limit") Integer limit,
        @Param("threshold") Float threshold
    );

    /**
     * 使用IVFFlat索引的向量相似度搜索
     * IVFFlat索引在查询速度和召回率之间取得平衡
     *
     * @param queryVector 查询向量
     * @param limit 返回结果数量
     * @param threshold 相似度阈值（可选），范围0-1，越大表示越相似
     * @return 相似度排序后的切片列表
     */
    List<AivecChunk> searchSimilarVectorsWithIVFFlat(
        @Param("queryVector") float[] queryVector,
        @Param("limit") Integer limit,
        @Param("threshold") Float threshold
    );

    List<AivecChunk> getChunksBySystemId(@Param("systemId") String systemId);
}
