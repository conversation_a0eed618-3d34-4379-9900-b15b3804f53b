package com.icarus.common.cotnode.ab.cmtt.vectorNode.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.icarus.constant.ValidGroups;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 目录树表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Getter
@Setter
@TableName("aivec_directory")
@ApiModel(value = "AivecDirectory对象", description = "目录树表")
public class AivecDirectory implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("目录ID")
    @TableId(value = "dir_id", type = IdType.AUTO)
    @NotNull(message = "目录ID不能为空")
    private String dirId;

    @ApiModelProperty("父目录ID（NULL表示根目录）")
    private String parentDirId;

    @ApiModelProperty("目录名称")
    @NotBlank(message = "目录名称不能为空")
    private String dirName;

    @ApiModelProperty("目录全路径（如：/data/report/2023）")
    @NotBlank(message = "目录全路径不能为空")
    private String dirPath;

    @ApiModelProperty("目录标签（JSON格式，如：[\"项目资料\",\"公开文档\"]）")
    private String tags;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;


    //新增字段
    @ApiModelProperty("文件标签Id（默认带目录tags, JSON格式，如：[\"111\",\"2222\"]）")
    private String tagIds;

    @ApiModelProperty("应用名称")
    private String appName;

}
