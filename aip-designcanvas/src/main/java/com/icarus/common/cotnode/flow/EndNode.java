package com.icarus.common.cotnode.flow;

import cn.hutool.json.JSONObject;
import com.icarus.common.cotnode.BaseNode;
import com.icarus.common.runtime.NodeContext;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

import java.util.List;
import java.util.Map;

@Slf4j
@NoArgsConstructor
public class EndNode extends BaseNode {

    public static final String code = "endNode";

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【结束】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);

        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            nodeContext.setInputs(new JSONObject(resolvedInputs));

            // 处理输出
            if (outputs != null && !outputs.isEmpty()) {
                JSONObject flowData = (JSONObject) context.getOrDefault("flowData", new JSONObject());
                JSONObject processedOutputs = processOutputs(context, outputs);
                context.put("flowData", flowData);
                nodeContext.setOutputs(processedOutputs);
            }

            nodeContext.setStatus("completed");
            context.put("flowStatus", "completed");

        } catch (Exception e) {
            log.error("结束节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        log.info("结束节点执行结束，节点ID：{}", getId());
        return context;
    }

    /**
     * 处理输出参数，包括递归处理子参数
     *
     * @param context    上下文
     * @param parameters 参数列表
     * @return 处理后的输出结果（层级结构的JSON）
     */
    private JSONObject processOutputs(Map<String, Object> context, List<Parameter> parameters) {
        JSONObject result = new JSONObject();
        JSONObject flowData = (JSONObject) context.getOrDefault("flowData", new JSONObject());

        for (Parameter parameter : parameters) {
            // 处理当前参数
            String sourcePath = parameter.getValue().toString();
            String targetPath = parameter.getName();

            Object value = resolveValueByPath(context, sourcePath);
            // 递归处理子参数
            if (ObjectUtils.isNotEmpty(parameter.getChildren())) {
                JSONObject childOutputs = processOutputs(context, parameter.getChildren());
                for (String key : childOutputs.keySet()) {
                    String childTargetPath = targetPath + "." + key;
                    setValueByPath(result, childTargetPath, childOutputs.get(key));
                    setValueByPath(flowData, childTargetPath, childOutputs.get(key));
                }
            } else {
                setValueByPath(result, targetPath, value);
                setValueByPath(flowData, targetPath, value);
            }
        }
        context.put("flowData", flowData);
        return result;
    }

    @Override
    public String desc() {
        return "结束节点";
    }
}