package com.icarus.common.cotnode;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.icarus.common.runtime.NodeContext;
import com.icarus.utils.JsonUtils;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 生成树
 */
@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class TreeNode extends DatabaseNode {

    @Getter
    @Setter
    private List<TreeValue> treeResult;

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【生成树】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);


        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            nodeContext.setInputs(new JSONObject(resolvedInputs));
            // 解析属性配置
            Properties resolvedProperties = resolveProperties(context,Properties.class);
            nodeContext.setProperties(new JSONObject(resolvedProperties));
            if(StrUtil.isBlank(resolvedProperties.getTableName())){
                throw new RuntimeException("请配置表名");
            }
            if (StrUtil.isBlank(resolvedProperties.getIdField())){
                throw new RuntimeException("请配置id字段");
            }
            if (StrUtil.isBlank(resolvedProperties.getParentField())){
                throw new RuntimeException("请配置parent字段");
            }
            if (StrUtil.isBlank(resolvedProperties.getNeedFields())){
                throw new RuntimeException("请配置需要字段");
            }
            //链接数据库获
            DatabaseNode.Properties dbInfo = getDbInfo(context);
            // 获取数据库连接
            Connection connection = getConnection(dbInfo);
            log.info("数据库连接成功: {}", getDatabaseInfo(connection));
            //获取要执行的sql
            String sql = getSql(resolvedProperties);
            log.info("准备执行SQL: {}", sql);
            String sqlResult  = executeSql(connection, sql, "SELECT");
            if (sqlResult.startsWith("[")) {
                List<JSONObject> arr = JsonUtils.jsonToList(sqlResult,JSONObject.class);
                resolvedProperties.setArrays(arr);
                treeResult = buildTree(resolvedProperties);
            } else if (sqlResult.startsWith("{")) {
                //如果只有一条直接返回 说明不是树形数据
                JSONObject obj = JSONUtil.parseObj(sqlResult);
                TreeValue treeValue = new TreeValue();
                treeValue.setValues(obj);
                treeResult.add(treeValue);
            }
            HashMap<String, Object> map = new HashMap<>();
            map.put("result",treeResult);
            JSONObject entries = new JSONObject(map);
            nodeContext.setOutputs(entries);
            nodeContext.setStatus("completed");
            // 处理输出
            processOutputs(context);
        } catch (Exception e){
            log.error("生成树节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        }finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }
        log.info("生成树节点执行结束，节点ID：{}", getId());
        return context;

    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        // 根据属性名返回对应的值
        if ("result".equals(propertyName)) {
            return treeResult;
        }
        return super.getCurrentNodeProperty(propertyName);
    }


    @Override
    public String desc() {
        return "生成树节点";
    }

    @Data
    @NoArgsConstructor
    public static class Properties {
        private String tableName;
        private String needFields;
        private String idField;
        private String parentField;
        private String type;
        private String typeIndex;
        private String parentId;
        private String sortField;
        private String sortDirection;
        private int maxLevel;
        private int sort;
        private List<JSONObject>  arrays;

    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TreeValue {
        private JSONObject values;
        private List<TreeValue> children = new ArrayList<>();
    }

    /**
     * 构建树形结构
     */
    public List<TreeValue> buildTree(Properties properties) {
        List<JSONObject> items = properties.getArrays();

        // 找出所有根节点（parentId为空）
        List<TreeValue> rootNodes = items.stream()
                .filter(item -> isRootItem(item,properties))
                .map(item -> new TreeValue(item, new ArrayList<>()))
                .collect(Collectors.toList());
        // 构建子树
        rootNodes.forEach(root -> buildChildren(
                root, items, properties.getIdField(),
               properties.getParentField(),properties.getMaxLevel(),properties.getSort(),1));
        //截取树
        if (1==properties.getSort()){
            return pruneTrees(rootNodes, properties.getMaxLevel());
        }
        return rootNodes;
    }

    /**
     * 判断是否为根节点
     */
    private boolean isRootItem(Object item, Properties properties) {
        JSONObject entries = JSONUtil.parseObj(item);
        if(!StrUtil.isBlankIfStr(properties.getParentId())){
            return StrUtil.equals(entries.getStr(properties.getParentField()),properties.getParentId());
        }else {
            Object parentId = entries.get(properties.getParentField());
            return parentId == null || StrUtil.isBlankIfStr(parentId);
        }
    }

    /**
     * 递归构建子树
     */
    private void buildChildren(TreeValue parent, List<JSONObject>  allItems,
                               String idFiled, String parentField,Integer maxLevel,Integer sort,Integer currentLevel) {

        // 获取所有parentId等于当前节点ID的项
        List<TreeValue> children = allItems.stream()
                .filter(item -> parent.getValues().getStr(idFiled).equals(item.getStr(parentField)))
                .map(item -> new TreeValue(item, new ArrayList<>()))
                .collect(Collectors.toList());

        parent.setChildren(children);
        // 递归构建每个子节点的子树
        children.forEach(child ->
                buildChildren(child, allItems, idFiled,
                        parentField,maxLevel,sort,currentLevel + 1));
        //最大层数和方向
        if (sort == null || sort == 0) {
            // sort=0时，从根节点开始向下限制层级
            parent.setChildren(children);  // 总是先设置子节点
            if (maxLevel != null && maxLevel>0 && currentLevel >= maxLevel) {
                parent.setChildren(new ArrayList<>()); // 关键修改：超过层级时清空子节点
            } else {
                parent.setChildren(children);
                children.forEach(child -> buildChildren(
                        child, allItems, idFiled, parentField, maxLevel, sort, currentLevel + 1));
            }
        }
    }

    /**
     * 裁剪树
     */
    public List<TreeValue> pruneTrees(List<TreeValue> trees, int maxLevel) {
        ArrayList<TreeValue> result = new ArrayList<>();
        if (trees == null || maxLevel <= 0) {
            return result;
        }

        // 计算所有树中的最大深度
        int maxDepth = 0;
        for (TreeValue tree : trees) {
            maxDepth = Math.max(maxDepth, calculateMaxDepth(tree));
        }

        // 计算需要保留的最小深度
        int retainDepth = maxDepth - maxLevel + 1;

        // 修剪每棵树
        for (TreeValue tree : trees) {
            List<TreeValue> prunedTree = pruneTree(result,tree, 1,retainDepth);
            if (prunedTree!=null) {
                result.addAll(prunedTree);
            }
        }

        return result;
    }

    private int calculateMaxDepth(TreeValue node) {
        if (node == null) {
            return 0;
        }

        int maxChildDepth = 0;
        for (TreeValue child : node.getChildren()) {
            maxChildDepth = Math.max(maxChildDepth, calculateMaxDepth(child));
        }

        return maxChildDepth + 1;
    }

    /**
     * 裁剪树
     */
    private List<TreeValue> pruneTree(ArrayList<TreeValue> result, TreeValue node, Integer currentLevel, int retainDepth) {
        if (node == null) {
            return null;
        }
        if (retainDepth < 2) {
            // 当保留深度小于2时，直接返回当前节点
            return List.of(node);
        }
        currentLevel++;
        if (node.getChildren().isEmpty()) {
            if (currentLevel < retainDepth) {
                return null;
            }
            if (currentLevel == retainDepth) {
                result.add(node);
            }
        } else {
            if (currentLevel < retainDepth) {
                for (TreeValue child : node.getChildren()) {
                    // 递归寻找需要保存的节点，并处理返回值
                    List<TreeValue> prunedChildren = pruneTree(result, child, currentLevel, retainDepth);
                    if (prunedChildren != null) {
                        result.addAll(prunedChildren);
                    }
                }
            }
            if (currentLevel == retainDepth) {
                // 获取它的所有子节点
                result.addAll(node.getChildren());
            }
            if (currentLevel > retainDepth) {
                return result;
            }
        }
        return null;
    }

    /**
     * 获取数据库信息
     */
    private DatabaseNode.Properties getDbInfo(Map<String, Object> context) {
        List<String> ids = getPrevNodeIds();
        if (ids == null || ids.isEmpty()) {
            throw new RuntimeException("找不到数据库节点");
        }
        NodeContext dbNodeContext = getNodeContext(context, ids.get(0));
        if (dbNodeContext == null) {
            throw new RuntimeException("找不到数据库节点");
        }
        JSONObject dbInfo = new JSONObject(dbNodeContext.getProperties());
        return dbInfo.toBean(DatabaseNode.Properties.class);

    }

    /**
     * 获取sql
     */
    private String getSql(Properties properties){
        StringBuffer allSql = new StringBuffer();
        allSql.append("SELECT ").
                append(properties.getNeedFields()).append(" FROM ").
                append(properties.getTableName());

        if(StrUtil.isNotBlank(properties.getType())&&StrUtil.isNotBlank(properties.getTypeIndex())){
            allSql.append(" WHERE ").append(properties.getType()).append(" = ").
                    append("'").
                    append(properties.getTypeIndex()).append("'");
        }
        if (StrUtil.isNotBlank(properties.getSortField())&&StrUtil.isNotBlank(properties.getSortDirection())){
            allSql.append(" ORDER BY ").append(properties.getSortField()).append(" ").append(properties.getSortDirection());
        }
        return allSql.toString();
    }


}