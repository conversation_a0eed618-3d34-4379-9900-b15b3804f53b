package com.icarus.common.cotnode.ab.ex;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.icarus.common.cotnode.BaseNode;
import com.icarus.common.runtime.NodeContext;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * 要素提取配置
 */
@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class ExConfigNode extends BaseNode {

	private JSONArray result;

	@Override
	public Map<String, Object> execute(Map<String, Object> context) {
		log.info("开始执行【ExConfig】节点, 当前节点id:{}", getId());
		String nodeId = getId();
		NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);

		try {
			// 解析属性配置
			Properties resolvedProperties = resolveProperties(context, Properties.class);
			nodeContext.setProperties(new JSONObject(resolvedProperties));

			// 记录输入参数到上下文
			Map<String, Object> resolvedInputs = resolveAllInputs(context);
			if (resolvedInputs != null) {
				nodeContext.setInputs(new JSONObject(resolvedInputs));
			}

			// 将结果设置到nodeContext
			result = new JSONObject(resolvedProperties.getExconfig()).getJSONArray("tableData");

			// 设置成功状态
			nodeContext.setStatus("completed");

			// 处理输出映射
			processOutputs(context);

		} catch (Exception e) {
			log.error("ExConfig节点执行失败", e);
			nodeContext.setStatus("failed");
			nodeContext.setErrorMessage(e.getMessage());
		} finally {
			nodeContext.setEndTime(System.currentTimeMillis());
			updateNodeContext(context, nodeId, nodeContext);
		}

		log.info("ExConfig节点执行结束，节点ID：{}", getId());
		return context;
	}


	@Override
	protected Object getCurrentNodeProperty(String propertyName) {
		if ("result".equals(propertyName)) {
			return result;
		}
		return super.getCurrentNodeProperty(propertyName);
	}

	@Data
	@NoArgsConstructor
	public static class Properties {
		private String exconfig;    // 要素抽取配置
	}

	@Override
	public String desc() {
		return "要素提取-配置";
	}
} 