package com.icarus.common.cotnode.ab.md;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.icarus.common.cotnode.BaseNode;
import com.icarus.common.minio.MinioUtil;
import com.icarus.common.runtime.NodeContext;
import com.icarus.utils.FileContentReader;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.util.*;

/**
 * Excel转Markdown节点
 * 将Excel文件（如xls、xlsx）转换为Markdown格式，并删除原始Excel文件
 */
@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class Xls2MdNode extends BaseNode {

    @Getter
    @Setter
    private String result;

    // 支持的Excel文件类型
    private static final Set<String> SUPPORTED_EXCEL_TYPES = new HashSet<>(Arrays.asList("XLS", "XLSX"));

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【Excel转Markdown】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
        File downloadedFile = null;
        File mdFile = null;
        String originalFilePath = null;
        String originalBucketName = null;
        boolean isOriginalFileUrl = false;
        
        try {
            // 解析属性配置
            Properties resolvedProperties = resolveProperties(context, Properties.class);
            JSONObject propertiesJson = new JSONObject();
            propertiesJson.set("filePath", resolvedProperties.getFilePath());
            nodeContext.setProperties(propertiesJson);

            // 获取MinioUtil实例
            MinioUtil minioUtil = SpringUtil.getBean(MinioUtil.class);

            // 验证必要的输入参数
            String filePath = resolvedProperties.getFilePath();
            if (filePath == null) {
                throw new IllegalArgumentException("filePath 是必需的参数");
            }
            
            // 保存原始文件路径，用于后续删除
            originalFilePath = filePath;
            originalBucketName = resolvedProperties.getBucketName();
            if (StrUtil.isEmpty(originalBucketName)) {
                originalBucketName = minioUtil.getMinioConfig().getBucketName();
                resolvedProperties.setBucketName(originalBucketName);
            }

            // 下载文件到本地临时目录
            String executeId = nodeContext.getExecuteId();
            String downloadDir = System.getProperty("java.io.tmpdir") + File.separator + "xls2md-downloads" + File.separator + executeId;
            File downloadDirFile = new File(downloadDir);
            if (!downloadDirFile.exists()) {
                downloadDirFile.mkdirs();
            }

            // 下载文件
            String downloadedFilePath;
            if (minioUtil.isHttpUrl(filePath)) {
                // 从URL中提取文件名和bucket信息
                Map<String, String> bucketAndObjectMap = minioUtil.extractBucketAndObjectFromUrl(filePath);
                String fileName = bucketAndObjectMap.getOrDefault("objectName", null);
                // 如果是URL，保存bucket信息用于后续删除
                if (originalBucketName == null) {
                    originalBucketName = bucketAndObjectMap.getOrDefault("bucketName", null);
                }
                
                if (fileName.contains("/")) {
                    fileName = fileName.substring(fileName.lastIndexOf("/") + 1);
                }
                
                String localFilePath = downloadDir + File.separator + fileName;
                
                // 从HTTP URL下载文件
                log.info("从HTTP URL下载文件: {}", filePath);
                downloadedFilePath = minioUtil.downloadFromUrl(filePath, localFilePath);
                isOriginalFileUrl = true;
            } else {
                // 获取MinIO中的对象名称
                String objectName = filePath;
                
                // 从对象名称中提取文件名
                String fileName = objectName;
                if (objectName.contains("/")) {
                    fileName = objectName.substring(objectName.lastIndexOf("/") + 1);
                }
                
                String localFilePath = downloadDir + File.separator + fileName;
                
                // 从MinIO下载文件
                log.info("从MinIO下载文件: {}", objectName);
                downloadedFilePath = minioUtil.downloadFileToLocal(
                        resolvedProperties.getBucketName(), 
                        objectName, 
                        localFilePath
                );
            }
            
            if (downloadedFilePath == null) {
                throw new IllegalArgumentException("下载文件失败: " + filePath);
            }
            
            downloadedFile = new File(downloadedFilePath);
            
            // 检查文件类型
            String fileType = getFileTypeFromPath(downloadedFile.getName());
            if (!SUPPORTED_EXCEL_TYPES.contains(fileType)) {
                throw new IllegalArgumentException("不支持的文件类型: " + fileType + "，仅支持XLS、XLSX格式");
            }
            
            // 读取Excel内容
            String excelContent = FileContentReader.readContent(downloadedFile, fileType, "UTF-8");
            
            // 转换为Markdown格式
            String markdownContent = convertToMarkdown(excelContent);
            
            // 创建Markdown文件
            String mdFileName = downloadedFile.getName().substring(0, downloadedFile.getName().lastIndexOf('.')) + ".md";
            String mdFilePath = downloadDir + File.separator + mdFileName;
            mdFile = new File(mdFilePath);
            FileUtil.writeString(markdownContent, mdFile, "UTF-8");
            
            // 上传Markdown文件到MinIO
            String objectName = generateObjectName(mdFileName, executeId);
            
            // 上传文件
            String uploadedPath = minioUtil.uploadFileWithOptions(
                    convertFileToMultipartFile(mdFile),
                    resolvedProperties.getBucketName(),
                    objectName,
                    null,  // 不设置过期时间
                    true,  // 公开访问
                    null   // 不设置文件限制
            );
            
            if (uploadedPath == null) {
                throw new IllegalArgumentException("上传Markdown文件失败");
            }
            
            // 删除原始Excel文件
            boolean deleteSuccess = false;
            if (isOriginalFileUrl) {
                log.info("删除原始Excel文件(URL): {}", originalFilePath);
                deleteSuccess = minioUtil.deleteFileFromUrl(originalFilePath);
            } else if (originalFilePath != null && originalBucketName != null) {
                log.info("删除原始Excel文件: 存储桶={}, 对象={}", originalBucketName, originalFilePath);
                deleteSuccess = minioUtil.deleteFile(originalBucketName, originalFilePath);
            }
            
            if (!deleteSuccess) {
                log.warn("删除原始Excel文件失败: {}", originalFilePath);
            } else {
                log.info("原始Excel文件删除成功");
            }
            
            // 设置结果
            result = uploadedPath;
            
            // 设置成功状态和输出
            nodeContext.setStatus("completed");
            
            // 处理输出映射
            processOutputs(context);
            
        } catch (Exception e) {
            log.error("Excel转Markdown失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            // 清理临时文件
            if (downloadedFile != null) {
                downloadedFile.delete();
            }
            if (mdFile != null) {
                mdFile.delete();
            }
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }
        
        log.info("Excel转Markdown节点执行结束，节点ID：{}", getId());
        return context;
    }
    
    /**
     * 将Excel内容转换为Markdown格式
     * 
     * @param excelContent Excel内容（JSON格式的字符串）
     * @return Markdown格式的内容
     */
    private String convertToMarkdown(String excelContent) {
        if (StringUtils.isBlank(excelContent)) {
            return "";
        }
        
        try {
            // 步骤1: 转换Excel内容为更丰富的HTML格式
            String htmlContent = convertExcelToHtml(excelContent);
            
            // 步骤2: 转换HTML为更准确的Markdown
            String markdownContent = convertHtmlToMarkdown(htmlContent);
            
            // 步骤3: 优化Markdown表格格式
            markdownContent = optimizeMarkdownTable(markdownContent);
            
            return markdownContent;
        } catch (Exception e) {
            log.error("转换Excel到Markdown失败", e);
            throw new RuntimeException("转换Excel到Markdown失败: " + e.getMessage());
        }
    }
    
    /**
     * 优化Markdown表格格式
     * 
     * @param markdown 原始Markdown内容
     * @return 优化后的Markdown内容
     */
    private String optimizeMarkdownTable(String markdown) {
        if (markdown == null || markdown.isEmpty()) {
            return markdown;
        }
        
        String[] lines = markdown.split("\n");
        if (lines.length < 3) {
            return markdown; // 表格至少需要3行（标题行、分隔行和至少一行数据）
        }
        
        // 计算每列的最大宽度
        List<List<String>> table = new ArrayList<>();
        List<Integer> columnWidths = new ArrayList<>();
        
        // 解析表格内容
        for (String line : lines) {
            if (line.trim().isEmpty()) continue;
            
            // 删除首尾的|并分割单元格
            String[] cells = line.trim().replaceAll("^\\|\\s*|\\s*\\|$", "").split("\\s*\\|\\s*");
            List<String> row = new ArrayList<>();
            
            for (int i = 0; i < cells.length; i++) {
                String cell = cells[i].trim();
                
                // 更新列宽度
                if (columnWidths.size() <= i) {
                    columnWidths.add(cell.length());
                } else if (cell.length() > columnWidths.get(i)) {
                    columnWidths.set(i, cell.length());
                }
                
                row.add(cell);
            }
            
            table.add(row);
        }
        
        // 重建表格，确保对齐
        StringBuilder result = new StringBuilder();
        
        // 添加标题行
        if (!table.isEmpty()) {
            result.append("| ");
            List<String> headerRow = table.get(0);
            
            for (int i = 0; i < headerRow.size(); i++) {
                String cell = headerRow.get(i);
                result.append(padRight(cell, columnWidths.get(i))).append(" | ");
            }
            result.append("\n");
            
            // 添加分隔行
            result.append("| ");
            for (int width : columnWidths) {
                result.append(padRight("", width, '-')).append(" | ");
            }
            result.append("\n");
            
            // 添加数据行
            for (int rowIdx = 1; rowIdx < table.size(); rowIdx++) {
                result.append("| ");
                List<String> dataRow = table.get(rowIdx);
                
                for (int i = 0; i < dataRow.size(); i++) {
                    String cell = dataRow.get(i);
                    int width = i < columnWidths.size() ? columnWidths.get(i) : cell.length();
                    result.append(padRight(cell, width)).append(" | ");
                }
                result.append("\n");
            }
        }
        
        return result.toString();
    }
    
    /**
     * 右填充字符串到指定宽度
     */
    private String padRight(String s, int width) {
        return padRight(s, width, ' ');
    }
    
    /**
     * 右填充字符串到指定宽度，使用指定的填充字符
     */
    private String padRight(String s, int width, char padChar) {
        if (s.length() >= width) {
            return s;
        }
        StringBuilder sb = new StringBuilder(s);
        while (sb.length() < width) {
            sb.append(padChar);
        }
        return sb.toString();
    }
    
    /**
     * 将Excel内容转换为HTML格式
     * 
     * @param excelContent Excel内容（JSON格式的字符串）
     * @return HTML格式的内容
     */
    private String convertExcelToHtml(String excelContent) throws Exception {
        StringBuilder html = new StringBuilder();
        html.append("<!DOCTYPE html><html><head><meta charset='UTF-8'></head><body>");
        
        // 解析JSON格式的Excel内容
        final List<List<String>> rowsList = new ArrayList<>();
        JSONUtil.parseArray(excelContent).forEach(row -> {
            List<String> rowData = new ArrayList<>();
            if (row instanceof List<?>) {
                ((List<?>) row).forEach(cell -> rowData.add(cell != null ? cell.toString() : ""));
            }
            rowsList.add(rowData);
        });
        
        if (!rowsList.isEmpty()) {
            // 确定表格中空字符串的处理方式
            List<List<String>> processedRows = preprocessTableData(rowsList);
            
            // 创建表格
            html.append("<table border='1' cellspacing='0' cellpadding='5'>\n");
            
            // 第一行作为表头
            html.append("<thead>\n<tr>\n");
            List<String> headerRow = processedRows.get(0);
            for (String cell : headerRow) {
                String cellValue = StringUtils.isBlank(cell) ? "&nbsp;" : escapeHtml(cell);
                html.append("<th>").append(cellValue).append("</th>\n");
            }
            html.append("</tr>\n</thead>\n");
            
            // 剩余行作为表格内容
            html.append("<tbody>\n");
            for (int i = 1; i < processedRows.size(); i++) {
                html.append("<tr>\n");
                List<String> dataRow = processedRows.get(i);
                
                // 确保行数据长度与表头一致
                while (dataRow.size() < headerRow.size()) {
                    dataRow.add("");
                }
                
                for (int j = 0; j < headerRow.size(); j++) {
                    String cell = j < dataRow.size() ? dataRow.get(j) : "";
                    String cellValue = StringUtils.isBlank(cell) ? "&nbsp;" : escapeHtml(cell);
                    
                    // 尝试检测数字内容并右对齐
                    if (isNumeric(cell)) {
                        html.append("<td align='right'>").append(cellValue).append("</td>\n");
                    } else {
                        html.append("<td>").append(cellValue).append("</td>\n");
                    }
                }
                html.append("</tr>\n");
            }
            html.append("</tbody>\n");
            
            html.append("</table>");
        }
        
        html.append("</body></html>");
        return html.toString();
    }
    
    /**
     * 预处理表格数据，处理空单元格和合并重复的表头
     */
    private List<List<String>> preprocessTableData(List<List<String>> rows) {
        if (rows.isEmpty()) {
            return rows;
        }
        
        // 处理表头中的空单元格
        List<String> header = rows.get(0);
        for (int i = 0; i < header.size(); i++) {
            if (StringUtils.isBlank(header.get(i))) {
                header.set(i, "Column " + (i + 1));
            }
        }
        
        // 确保所有数据行的长度一致
        int maxColumns = header.size();
        for (List<String> row : rows) {
            maxColumns = Math.max(maxColumns, row.size());
        }
        
        // 如果有行超出表头长度，扩展表头
        while (header.size() < maxColumns) {
            header.add("Column " + (header.size() + 1));
        }
        
        return rows;
    }
    
    /**
     * 检查字符串是否为数字
     */
    private boolean isNumeric(String str) {
        if (StringUtils.isBlank(str)) {
            return false;
        }
        
        try {
            Double.parseDouble(str.trim());
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * 将HTML内容转换为Markdown格式
     * 
     * @param htmlContent HTML内容
     * @return Markdown格式的内容
     */
    private String convertHtmlToMarkdown(String htmlContent) {
        StringBuilder markdown = new StringBuilder();
        
        // 使用更健壮的方式处理HTML表格
        String content = htmlContent;
        
        // 移除DOCTYPE和html/head/body标签
        content = content.replaceAll("<!DOCTYPE[^>]*>|<html>|</html>|<head>.*?</head>|<body>|</body>", "");
        
        // 处理表格，提取表头和数据行
        try {
            // 提取表格完整内容
            int tableStart = content.indexOf("<table");
            int tableEnd = content.indexOf("</table>", tableStart);
            
            if (tableStart != -1 && tableEnd != -1) {
                String tableContent = content.substring(tableStart, tableEnd + 8);
                
                // 提取表头
                int theadStart = tableContent.indexOf("<thead>");
                int theadEnd = tableContent.indexOf("</thead>");
                
                if (theadStart != -1 && theadEnd != -1) {
                    // 提取表头行
                    String theadContent = tableContent.substring(theadStart, theadEnd + 8);
                    String headerRowContent = theadContent.substring(theadContent.indexOf("<tr>"), theadContent.indexOf("</tr>") + 5);
                    
                    // 提取表头单元格
                    StringBuilder headerRow = new StringBuilder("|");
                    String[] headerCells = headerRowContent.split("<th[^>]*>");
                    
                    for (int i = 1; i < headerCells.length; i++) {
                        String cellContent = headerCells[i].replaceAll("</th>.*", "").trim();
                        // 解码HTML实体
                        cellContent = decodeHtmlEntities(cellContent);
                        headerRow.append(" ").append(cellContent).append(" |");
                    }
                    
                    // 添加表头行
                    markdown.append(headerRow).append("\n");
                    
                    // 添加分隔行
                    StringBuilder separatorRow = new StringBuilder("|");
                    for (int i = 1; i < headerCells.length; i++) {
                        separatorRow.append(" --- |");
                    }
                    markdown.append(separatorRow).append("\n");
                    
                    // 提取表体
                    int tbodyStart = tableContent.indexOf("<tbody>");
                    int tbodyEnd = tableContent.indexOf("</tbody>");
                    
                    if (tbodyStart != -1 && tbodyEnd != -1) {
                        String tbodyContent = tableContent.substring(tbodyStart, tbodyEnd + 8);
                        String[] rows = tbodyContent.split("<tr[^>]*>");
                        
                        // 处理每一行
                        for (int i = 1; i < rows.length; i++) {
                            String row = rows[i];
                            StringBuilder dataRow = new StringBuilder("|");
                            
                            // 提取单元格
                            String[] cells = row.split("<td[^>]*>");
                            
                            for (int j = 1; j < cells.length; j++) {
                                String cellContent = cells[j].replaceAll("</td>.*", "").trim();
                                // 解码HTML实体
                                cellContent = decodeHtmlEntities(cellContent);
                                dataRow.append(" ").append(cellContent).append(" |");
                            }
                            
                            markdown.append(dataRow).append("\n");
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理HTML表格时出错", e);
            // 使用备用方法进行简单转换
            return fallbackHtmlToMarkdown(content);
        }
        
        return markdown.toString();
    }
    
    /**
     * 备用的HTML到Markdown转换方法
     */
    private String fallbackHtmlToMarkdown(String content) {
        // 提取表格行
        String[] tableRows = content.split("<tr[^>]*>");
        StringBuilder markdown = new StringBuilder();
        
        boolean headerProcessed = false;
        
        for (int i = 1; i < tableRows.length; i++) {
            String row = tableRows[i];
            
            // 检查是否为表头行
            boolean isHeader = row.contains("<th");
            String[] cells;
            
            if (isHeader) {
                cells = row.split("<th[^>]*>");
            } else {
                cells = row.split("<td[^>]*>");
            }
            
            if (cells.length <= 1) continue;
            
            StringBuilder markdownRow = new StringBuilder("|");
            
            for (int j = 1; j < cells.length; j++) {
                String cellContent;
                
                if (isHeader) {
                    cellContent = cells[j].replaceAll("</th>.*", "").trim();
                } else {
                    cellContent = cells[j].replaceAll("</td>.*", "").trim();
                }
                
                // 解码HTML实体
                cellContent = decodeHtmlEntities(cellContent);
                markdownRow.append(" ").append(cellContent).append(" |");
            }
            
            markdown.append(markdownRow).append("\n");
            
            // 在表头后添加分隔行
            if (isHeader && !headerProcessed) {
                headerProcessed = true;
                StringBuilder separatorRow = new StringBuilder("|");
                
                for (int j = 1; j < cells.length; j++) {
                    separatorRow.append(" --- |");
                }
                
                markdown.append(separatorRow).append("\n");
            }
        }
        
        return markdown.toString();
    }
    
    /**
     * 解码HTML实体
     */
    private String decodeHtmlEntities(String content) {
        return content.replace("&amp;", "&")
                     .replace("&lt;", "<")
                     .replace("&gt;", ">")
                     .replace("&quot;", "\"")
                     .replace("&#39;", "'")
                     .replace("&nbsp;", " ");
    }
    
    /**
     * 转义HTML特殊字符
     * 
     * @param text 原始文本
     * @return 转义后的文本
     */
    private String escapeHtml(String text) {
        return text.replace("&", "&amp;")
                   .replace("<", "&lt;")
                   .replace(">", "&gt;")
                   .replace("\"", "&quot;")
                   .replace("'", "&#39;");
    }
    
    /**
     * 从文件路径获取文件类型
     * 
     * @param fileName 文件名
     * @return 文件类型（大写）
     */
    private String getFileTypeFromPath(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return fileName.substring(lastDotIndex + 1).toUpperCase();
        }
        return "";
    }
    
    /**
     * 生成对象名称
     * 
     * @param originalFilename 原始文件名
     * @param executeId 执行ID
     * @return 对象名称
     */
    private String generateObjectName(String originalFilename, String executeId) {
        return String.format("files/%s/%s/%s",
                LocalDate.now(),
                executeId,
                originalFilename);
    }
    
    /**
     * 将File转换为MultipartFile
     * 
     * @param file 文件
     * @return MultipartFile对象
     */
    private MultipartFile convertFileToMultipartFile(File file) {
        return new MultipartFile() {
            @Override
            public String getName() {
                return file.getName();
            }

            @Override
            public String getOriginalFilename() {
                return file.getName();
            }

            @Override
            public String getContentType() {
                return MediaType.TEXT_MARKDOWN_VALUE;
            }

            @Override
            public boolean isEmpty() {
                return file.length() == 0;
            }

            @Override
            public long getSize() {
                return file.length();
            }

            @Override
            public byte[] getBytes() throws IOException {
                return FileUtil.readBytes(file);
            }

            @Override
            public InputStream getInputStream() throws IOException {
                return new FileInputStream(file);
            }

            @Override
            public void transferTo(File dest) throws IOException, IllegalStateException {
                FileUtil.copy(file, dest, true);
            }
        };
    }
    
    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }
    
    @Data
    @NoArgsConstructor
    public static class Properties {
        /**
         * 文件路径（MinIO对象路径或URL）
         */
        private String filePath;
        
        /**
         * 存储桶名称
         */
        private String bucketName;
    }

    @Override
    public String desc() {
        return "xls转MD";
    }

} 