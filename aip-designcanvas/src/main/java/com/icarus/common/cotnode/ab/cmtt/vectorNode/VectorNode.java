package com.icarus.common.cotnode.ab.cmtt.vectorNode;

import cn.hutool.core.util.EnumUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.icarus.common.cotnode.BaseNode;
import com.icarus.common.cotnode.ab.cmtt.vectorNode.entity.AivecChunk;
import com.icarus.common.cotnode.ab.cmtt.vectorNode.entity.AivecDirectory;
import com.icarus.common.cotnode.ab.cmtt.vectorNode.entity.AivecFile;
import com.icarus.common.runtime.NodeContext;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * 向量节点
 * <p>
 * 该节点用于处理向量相关操作，包括目录、文件和文件切片的增删改查及分页查询功能，
 * 以及向量检索功能。
 * </p>
 * <p>
 * 支持的操作类型：
 * - CREATE：创建记录
 * - DELETE：删除记录
 * - UPDATE：更新记录
 * - GET：获取单条记录
 * - LIST：获取所有记录
 * - QUERY：分页查询记录
 * - VECTOR_RETRIEVE：向量检索
 * </p>
 * <p>
 * 支持的操作对象：
 * - DIR：目录
 * - FILE：文件
 * - CHUNK：文件切片
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class VectorNode extends BaseNode {

    /**
     * 操作结果，JSON格式字符串
     */
    @Getter
    @Setter
    private String result;

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【Vector】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
        String executeId = nodeContext.getExecuteId();
        try {
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            nodeContext.setInputs(new JSONObject(resolvedInputs));

            // 参数转换
            VectorOperationType vectorOperationType = null;
            VectorOperationObject vectorOperationObject = null;
            String bodyJson = null;
            try {
                // 解析属性配置
                Properties resolvedProperties = resolveProperties(context, Properties.class);
                nodeContext.setProperties(new JSONObject(resolvedProperties));

                vectorOperationType = EnumUtil.fromString(VectorOperationType.class, resolvedProperties.getVectorOperationType());
                vectorOperationObject = EnumUtil.fromString(VectorOperationObject.class, resolvedProperties.getVectorOperationObject());
                bodyJson = resolvedProperties.getBodyJson();
            } catch (Exception e) {
                throw new RuntimeException("参数转换失败", e);
            }

            // 获取向量节点引擎
            VectorNodeEngine engine = SpringUtil.getBean(VectorNodeEngine.class);

            // 根据操作类型执行不同的处理逻辑
            if (vectorOperationType == VectorOperationType.VECTOR_RETRIEVE) {
                // 执行向量检索操作
                result = JSONUtil.toJsonStr(engine.processVectorRetrieve(bodyJson));
            } else {
                // 执行CRUD操作
                result = JSONUtil.toJsonStr(engine.processCRUD(vectorOperationType, vectorOperationObject, bodyJson));
            }

            // 设置处理结果
            nodeContext.setStatus("completed");
            // 将结果赋值到输出
            nodeContext.setOutputs(new JSONObject(result));

            // 处理输出
            processOutputs(context);

        } catch (Exception e) {
            log.error("Vector节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }

        log.info("Vector节点执行结束，节点ID：{}", getId());
        return context;
    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    /**
     * 向量节点属性配置
     */
    @Data
    @NoArgsConstructor
    public static class Properties {
        /**
         * 操作类型
         * <p>
         * 包括：CREATE、DELETE、UPDATE、GET、LIST、QUERY、VECTOR_RETRIEVE
         * </p>
         *
         * @see VectorOperationType
         */
        private String vectorOperationType;

        /**
         * 操作对象类型
         * <p>
         * DIR：目录
         * FILE：文件
         * CHUNK：文件片
         * </p>
         *
         * @see VectorOperationObject
         */
        private String vectorOperationObject;

        /**
         * 参数体
         * <p>
         * JSON格式的请求参数，根据不同的操作类型和对象类型有不同的结构
         * </p>
         */
        private String bodyJson;
    }

    /**
     * 向量操作类型枚举
     */
    @Getter
    public enum VectorOperationType {
        /** 创建记录 */
        CREATE,
        /** 删除记录 */
        DELETE,
        /** 更新记录 */
        UPDATE,
        /** 获取单条记录 */
        GET,
        /** 获取所有记录 */
        LIST,
        /** 分页查询记录 */
        QUERY,
        /** 向量检索 */
        VECTOR_RETRIEVE,
        /**
         * 结合
         */
        COMBINE
        ;
    }

    /**
     * 向量操作对象枚举
     */
    @Getter
    public enum VectorOperationObject {
        /** 目录 */
        DIR("目录", VectorNodeEngine.AivecDirectoryHandlerVector.class, AivecDirectory.class, VectorNodeEngine.AivecDirectoryHandlerVector.DirPageQueryReq.class),
        /** 文件 */
        FILE("文件", VectorNodeEngine.AivecFileHandlerVector.class, AivecFile.class, VectorNodeEngine.AivecFileHandlerVector.FilePageQueryReq.class),
        /** 文件切片 */
        CHUNK("文件片", VectorNodeEngine.AivecChunkHandlerVector.class, AivecChunk.class, VectorNodeEngine.AivecChunkHandlerVector.ChunkPageQueryReq.class),
        ;
        /** 描述 */
        private final String desc;
        /** 处理器类 */
        private final Class<?> handlerClass;
        /** 实体类 */
        private final Class<?> entityClass;
        /** 分页请求类 */
        private final Class<?> pageReqClass;

        /**
         * 构造方法
         *
         * @param desc 描述
         * @param handlerClass 处理器类
         * @param entityClass 实体类
         * @param pageReqClass 分页请求类
         */
        VectorOperationObject(String desc, Class<?> handlerClass, Class<?> entityClass, Class<?> pageReqClass) {
            this.desc = desc;
            this.handlerClass = handlerClass;
            this.entityClass = entityClass;
            this.pageReqClass = pageReqClass;
        }
    }

    @Override
    public String desc() {
        return "Vector节点";
    }
}