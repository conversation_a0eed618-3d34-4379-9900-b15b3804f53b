package com.icarus.common.cotnode;

import cn.hutool.json.JSONObject;
import com.icarus.common.runtime.NodeContext;
import lombok.*;

import java.io.File;
import java.util.*;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import javax.mail.*;
import javax.mail.internet.*;
import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;

@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor
public class EmailSendNode extends BaseNode{
    @Getter
    @Setter
    private String result;

    // 新增 SMTP 相关常量
    private static final String SMTP_AUTH = "mail.smtp.auth";
    private static final String SMTP_STARTTLS_ENABLE = "mail.smtp.starttls.enable";
    private static final String SMTP_HOST = "mail.smtp.host";
    private static final String SMTP_PORT = "mail.smtp.port";
    private static final String SEPARATOR = ";";//分隔符
    private static final String SMTP_TIMEOUT = "mail.smtp.timeout";
    private static final String SMTP_TIMEOUT_VALUE = "30000";

    @Override
    public Map<String, Object> execute(Map<String, Object> context) {
        log.info("开始执行【sendEmail】节点, 当前节点id:{}", getId());
        String nodeId = getId();
        NodeContext nodeContext = getOrCreateNodeContext(context, nodeId);
        try{
            // 解析输入参数
            Map<String, Object> resolvedInputs = resolveAllInputs(context);
            nodeContext.setInputs(new JSONObject(resolvedInputs));

            // 解析属性配置
            EmailSendNode.Properties resolvedProperties = resolveProperties(context, EmailSendNode.Properties.class);
            //校验参数
            checkParam(resolvedProperties);

            JSONObject nodePro = new JSONObject();
            nodePro.set("smtp_host", resolvedProperties.getSmtp_host());
            nodePro.set("smtp_port", resolvedProperties.getSmtp_port());
            nodePro.set("username", resolvedProperties.getUsername());
            nodePro.set("password", resolvedProperties.getPassword());
            nodeContext.setProperties(nodePro);

            sendEmailWithAttachments(resolvedProperties);
            // 设置结果并更新状态
            result = "邮件发送成功";
            nodeContext.setStatus("completed");
            // 处理输出参数
            processOutputs(context);
        }catch (Exception e) {
            log.error("邮件发送节点执行失败", e);
            nodeContext.setStatus("failed");
            nodeContext.setErrorMessage(e.getMessage());
        } finally {
            nodeContext.setEndTime(System.currentTimeMillis());
            updateNodeContext(context, nodeId, nodeContext);
        }
        log.info("邮件发送节点执行结束，节点ID：{}", nodeId);
        return context;
    }


    /**
     * 发送邮件
     * @param properties
     * @throws Exception
     */
    public void sendEmailWithAttachments(EmailSendNode.Properties properties) throws Exception {

        // 设置邮件服务器属性
        java.util.Properties props = new java.util.Properties();
        //启用SMTP服务器身份验证
        props.put(SMTP_AUTH, "true");
        //启用STARTTLS加密传输
        props.put(SMTP_STARTTLS_ENABLE, "true");
        props.put(SMTP_HOST, properties.getSmtp_host());
        props.put(SMTP_PORT, properties.getSmtp_port());
        props.put(SMTP_TIMEOUT, SMTP_TIMEOUT_VALUE);

        // 创建会话
        Session session = Session.getInstance(props, new javax.mail.Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(properties.getUsername(), properties.getPassword());
            }
        });

        try {
            // 创建邮件消息
            MimeMessage message = new MimeMessage(session);

            // 设置发件人
            message.setFrom(new InternetAddress(properties.getUsername()));


            // 设置收件人

            for (String to : splitStringToList(properties.getReceive_to(), SEPARATOR)) {
                message.addRecipient(Message.RecipientType.TO, new InternetAddress(to));
            }

            // 设置抄送
            for (String cc : splitStringToList(properties.getReceive_cc(), SEPARATOR)) {
                message.addRecipient(Message.RecipientType.CC, new InternetAddress(cc));
            }

            // 设置密送
            for (String bcc : splitStringToList(properties.getReceive_bcc(), SEPARATOR)) {
                message.addRecipient(Message.RecipientType.BCC, new InternetAddress(bcc));
            }

            // 设置主题
            message.setSubject(properties.getSubject());

            // 创建多部分消息
            Multipart multipart = new MimeMultipart();

            String content = StringUtils.isNotEmpty(properties.getContent())?properties.getContent():StringUtils.EMPTY;
            // 创建邮件正文部分
            MimeBodyPart messageBodyPart = new MimeBodyPart();
            messageBodyPart.setContent(content, "text/html; charset=utf-8");
            multipart.addBodyPart(messageBodyPart);

            // 添加所有附件，文件路径用英文;分隔
            List<String> attachments = splitStringToList(properties.getAttachments(), SEPARATOR);
            if (attachments != null && !attachments.isEmpty()) {
                for (String filePath : attachments) {
                    MimeBodyPart attachmentPart = new MimeBodyPart();
                    try {
                        File file = new File(filePath);
                        if (!file.exists()) {
                            throw new Exception("Attachment file not found: " + filePath);
                        }

                        // 创建数据源并将其添加到消息部分
                        DataSource source = new FileDataSource(file);
                        attachmentPart.setDataHandler(new DataHandler(source));
                        attachmentPart.setFileName(file.getName());
                        multipart.addBodyPart(attachmentPart);
                    } catch (Exception e) {
                        throw new Exception("Error adding attachment: " + filePath, e);
                    }
                }
            }

            // 设置邮件内容
            message.setContent(multipart);

            // 发送邮件
            Transport.send(message);

        } catch (MessagingException e) {
            throw new Exception("发送邮件失败" , e);
        }
    }

    /**
     * 校验参数
     * 邮箱地址是否合法
     * 必填项是否都填写
     * @param pro
     */
    private void checkParam(Properties pro) {
        List<String> errors = new ArrayList<>();

        // 校验发送者邮箱格式
        validateEmailFormat(pro.getUsername(), "发送者邮箱", errors);

        // 校验收件人邮箱格式
        if(StringUtils.isNotEmpty(pro.getReceive_to())) {
            validateEmailListFormat(pro.getReceive_to(), "收件人邮箱", errors);
        }

        // 校验抄送邮箱格式
        if(StringUtils.isNotEmpty(pro.getReceive_cc())) {
            validateEmailListFormat(pro.getReceive_cc(), "抄送邮箱", errors);
        }

        // 校验密送邮箱格式
        if(StringUtils.isNotEmpty(pro.getReceive_bcc())) {
            validateEmailListFormat(pro.getReceive_bcc(), "密送邮箱", errors);
        }

        // 判断收件人、抄送、密送是否为空
        if (StringUtils.isEmpty(pro.getReceive_to()) &&
                StringUtils.isEmpty(pro.getReceive_cc()) &&
                StringUtils.isEmpty(pro.getReceive_bcc())) {
            errors.add("收件人、抄送、密送不能同时为空");
        }

        // 检查必填字段
        if (StringUtils.isEmpty(pro.getSmtp_host())) {
            errors.add("SMTP服务器地址不能为空");
        }
        if (StringUtils.isEmpty(pro.getSmtp_port())) {
            errors.add("SMTP端口不能为空");
        }
        if (StringUtils.isEmpty(pro.getUsername())) {
            errors.add("发送人邮箱不能为空");
        }
        if (StringUtils.isEmpty(pro.getPassword())) {
            errors.add("发送人密码不能为空");
        }

        // 如果有错误，统一抛出
        if(!errors.isEmpty()) {
            throw new IllegalArgumentException(String.join(SEPARATOR, errors));
        }
    }

    // 校验单个邮箱格式
    private void validateEmailFormat(String email, String fieldName, List<String> errors) {
        try {
            InternetAddress.parse(email, true);
        } catch (AddressException e) {
            errors.add(fieldName + "格式不正确: " + email);
        }
    }

    // 校验邮箱列表格式
    private void validateEmailListFormat(String emailList, String fieldName, List<String> errors) {
        for(String email : splitStringToList(emailList, SEPARATOR)) {
            validateEmailFormat(email, fieldName, errors);
        }
    }


    /**
     * 根据分隔符将字符串转成list
     */
    private List<String> splitStringToList(String str, String separator) {
        return (null == str || str.isEmpty())
                ? Collections.emptyList()
                : Arrays.asList(str.split(separator));
    }

    @Data
    @NoArgsConstructor
    public static class Properties {
        //SMTP服务器
        private String smtp_host;
        //SMTP端口
        private String smtp_port;
        //发送者邮箱
        private String username;
        //发送者邮箱密码
        private String password;
        //收件人邮箱
        private String receive_to;
        //抄送人邮箱
        private String receive_cc;
        //密送人邮箱
        private String receive_bcc;
        //邮件主题
        private String subject;
        //邮件内容
        private String content;
        //邮件附件，文件路径用分号分隔
        private String attachments;

    }

    @Override
    protected Object getCurrentNodeProperty(String propertyName) {
        if ("result".equals(propertyName)) {
            return result;
        }
        return super.getCurrentNodeProperty(propertyName);
    }

    @Override
    public String desc() {
        return "邮件发送节点";
    }
}
