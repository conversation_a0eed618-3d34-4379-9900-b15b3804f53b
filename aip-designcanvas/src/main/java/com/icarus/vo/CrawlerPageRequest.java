package com.icarus.vo;

import lombok.Data;

/**
 * 爬虫分页查询请求
 */
@Data
public class CrawlerPageRequest {
    
    /**
     * 当前页码
     */
    private Integer current = 1;
    
    /**
     * 每页大小
     */
    private Integer pageSize = 10;
    
    /**
     * 关键词搜索
     */
    private String keyword;
    
    /**
     * 状态筛选
     */
    private String status;
    
    /**
     * 开始时间
     */
    private String startTime;
    
    /**
     * 结束时间
     */
    private String endTime;
    
    /**
     * 配置ID
     */
    private Long configId;
    
    /**
     * 任务ID
     */
    private Long taskId;
    
    /**
     * 批次ID
     */
    private String batchId;
}