package com.icarus.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;

/**
 * 场景要素实体（来自要素提取服务中的场景要素配置表）
 * <AUTHOR>
 * @date 2025-03-24
 */
@Data
@TableName("aidoc_ew_sys_scene_element")
public class SceneElement {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 场景ID
     */
    private String sceneId;

    private Integer batch;

    private Integer sequence;
    /**
     * 要素名称
     */
    private String elementName;

    /**
     * 要素类型
     */
    private String elementType;

    /**
     * 范围描述
     * @description  章节列表（从文章的某处到文章的另一个地方） 通过新接口传入
     */
    private String scope;

    private String extractionType;
    /**
     * 要素提取方法
     * @enum  大模型提取、正则提取（正则表达式）
     */
    private String extractionConfig;

    /**
     * 备注
     */
    private String memo;

    /**
     * 要素提取方法
     * @enum  大模型提取、正则提取（正则表达式）
     */
    private String extractionRuleSelect;

    /**
     * 要素提取方法
     * @enum  大模型提取、正则提取（正则表达式）
     */
    private String extractionRule;

    private String scopeType;

    private String scopeName;

    private String patternContent;

    private String businessRules;
    //同义词
    private String synonymous;

    private String elementDescribe;

    private String refSectionid;

    private String jsonName;

    private String sqlName;

    /**
     * 是否去除收尾空格（Y/N）
     */
    private String ifTrim;


    /**
     * 是否去除所有空格（Y/N）
     */
    private String ifReplayEmpty;


    /**
     * 是否去除乱码（Y/N）
     */
    private String ifReplayErrorCode;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private  Date updateTime;

}
