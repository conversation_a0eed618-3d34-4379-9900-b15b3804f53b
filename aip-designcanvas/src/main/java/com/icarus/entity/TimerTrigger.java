package com.icarus.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 定时触发器实体类
 */
@Data
@TableName("design_timer_trigger")
public class TimerTrigger {

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 触发器名称
     */
    private String triggerName;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 工作流ID
     */
    private String workflowId;

    /**
     * 路径
     */
    private String path;

    /**
     * 触发时间类型
     * preset: 预设时间
     * cron: Cron表达式
     */
    private String triggerTimeType;

    /**
     * 触发时间 (HH:mm:ss)
     */
    private String triggerTime;

    /**
     * 频率类型
     * daily: 每日
     * weekly: 每周
     * monthly: 每月
     * interval: 间隔
     */
    private String frequencyType;

    /**
     * 频率日期
     * 对于weekly: 1-7表示周几
     * 对于monthly: 1-31表示几号
     * 对于interval: 表示间隔天数
     */
    private Integer frequencyDay;

    /**
     * Cron表达式
     */
    private String cronExpression;

    /**
     * 时区
     */
    private String timezone;

    /**
     * 状态
     * 1: 启用
     * 0: 禁用
     */
    private Integer status;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 描述
     */
    private String description;
}