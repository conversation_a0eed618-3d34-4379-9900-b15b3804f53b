package com.icarus.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 爬虫任务实体类
 */
@Data
@TableName("crawler_task")
public class CrawlerTask {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 爬虫配置ID
     */
    private Long settingsId;
    
    /**
     * 批次ID
     */
    private String batchId;
    
    /**
     * 任务状态：running-运行中，completed-已完成，failed-失败
     */
    private String status;
    
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    
    /**
     * 处理页面数
     */
    private Integer pageCount;
    
    /**
     * 处理文件数
     */
    private Integer fileCount;
    
    /**
     * 失败文件数
     */
    private Integer failedCount;

    /**
     * 文本内容
     */
    @TableField(value = "text_content")
    private String textContent;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    /**
     * 最后处理的URL，用于断点续爬
     */
    private String lastProcessedUrl;
    
    /**
     * 当前处理的页码，用于断点续爬
     */
    private Integer currentPage;
    
    /**
     * 已处理的URL列表，JSON格式，用于断点续爬
     */
    private String processedUrls;
    
    /**
     * 失败文件列表，JSON格式，用于断点续爬
     */
    private String failedFiles;
}