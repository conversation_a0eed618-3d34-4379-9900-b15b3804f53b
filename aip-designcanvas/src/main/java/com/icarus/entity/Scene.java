package com.icarus.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;

/**
 * 场景实体类
 * <AUTHOR>
 * @date 2024-03-24
 */
@Data
@TableName("aidoc_ew_sys_scene")
public class Scene {

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 场景名称
     */
    private String sceneName;

    /**
     * 场景描述
     */
    private String description;

    /**
     * 模板ID
     */
    private String templateId;

    /**
     * 公司名称
     */
    private String company;

    /**
     * 状态：0禁用 1启用
     */
    private String status;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
} 