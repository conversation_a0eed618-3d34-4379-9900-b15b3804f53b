package com.icarus.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("design_model")
public class Model {
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String name;

    private String code;

    private String usageType;// 对话，生图，视频，语音，嵌入，重排序

    private String groupType;// 分组 QVQ QWQ Qwen2.5 Qwen2

    private String provider;// DeepSeek， Qwen2.5  腾讯混元 质朴AI

    private Float contentLength; // 8k 16k 32k 128k

    private Float size; // 7 14 32 70 xxx

    private String architectureType; // MoE、Dense、Sparse、Hybrid

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

} 