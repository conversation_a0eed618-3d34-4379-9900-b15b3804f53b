package com.icarus.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@TableName("design_node")
public class Node {
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    
    private String name;
    
    private String type;
    
    private String content;

    private String directoryId;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    private Integer extension;

    @TableField(exist = false)
    private List<Node> children;
} 