package com.icarus.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 爬虫配置实体类
 */
@Data
@TableName("crawler_settings")
public class CrawlerSettings {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 爬虫配置名称
     */
    private String name;
    
    /**
     * 爬取起始URL
     */
    private String crawlUrl;
    
    /**
     * 列表项选择器
     */
    private String clickListPath;
    
    /**
     * 下一页选择器
     */
    private String nextPagePath;
    
    /**
     * 标题选择器
     */
    private String getTitle;
    
    /**
     * 发布时间选择器
     */
    private String getPubTime;
    
    /**
     * 内容选择器
     */
    private String getContent;
    
    /**
     * 附件选择器
     */
    private String getFile;
    
    /**
     * iframe选择器
     */
    private String iframe;
    
    /**
     * HTTP请求头，JSON格式Map<String, String>字符串
     */
    private String headers;

    /**
     * 代理服务器地址，格式为 host:port
     */
    private String proxy;

    /**
     * HTTP请求超时时间（毫秒）
     */
    private Integer timeout;

    /**
     * HTTP请求最大重试次数
     */
    private Integer retryCount;
    
    /**
     * 最大爬取页数
     */
    private Integer maxPages;
    
    /**
     * 额外信息配置，JSON格式
     */
    private String mainExtraInfo;
    
    /**
     * 状态：active-启用，inactive-禁用
     */
    private String status;
    
    /**
     * 配置描述
     */
    private String description;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}