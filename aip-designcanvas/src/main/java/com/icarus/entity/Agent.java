package com.icarus.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("design_agent")
public class Agent {
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String name;

    private String code;

    private String agentType;

    private String agentTags;

    private String prompt;

    private String description;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}