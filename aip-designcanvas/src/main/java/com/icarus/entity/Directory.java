package com.icarus.entity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
@TableName("design_directory")
public class Directory implements TreeNode<Directory> {
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    
    private String name;

    private String userId;
    
    private String parentId;
    
    private String path;

    private String type;
    
    private Integer level;
    
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @TableField(exist = false)
    private List<Directory> children;
    
    @TableField(exist = false)
    private List<?> contents;
} 