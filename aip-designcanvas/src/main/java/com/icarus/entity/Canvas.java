package com.icarus.entity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;
@Data
@TableName("design_canvas")
public class Canvas {
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    
    private String name;
    
    private String content;
    
    private String directoryId;

    private String description;

    private String userId;
    
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}