package com.icarus.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("design_agent_work_flow")
public class AgentWorkFlow {
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String agentId;

    private String workFlowId;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}