package com.icarus.entity;

import lombok.Data;

@Data
public class DesignEmbeddingDetail {
    /**
     * 主键ID
     */
    private String id;
    
    /**
     * 文件ID
     */
    private String fileId;
    
    /**
     * 文件拆分标题
     */
    private String splitTitle;
    
    /**
     * 文件顺序
     */
    private Integer index;
    
    /**
     * 文件内容
     */
    private String context;
    
    /**
     * 向量值
     */
    private String embedding;
    
    /**
     * 向量生成模型
     */
    private String model;
} 