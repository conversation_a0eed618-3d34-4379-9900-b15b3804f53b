package com.icarus.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("design_endpoint")
public class Endpoint {
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String project;

    private String subsystem;

    private String module;

    private String description;

    private String type;

    /**
     * 唯一标识，避免path重复时错误调用
     */
    private String alias;

    /**
     * 画布ID
     */
    @TableField("template_id")
    private String templateId;

    @TableField("req_param")
    private String reqParam;

    @TableField("path")
    private String path;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}