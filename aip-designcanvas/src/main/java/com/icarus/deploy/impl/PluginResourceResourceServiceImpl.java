package com.icarus.deploy.impl;

import com.icarus.deploy.PluginResourceService;
import com.icarus.plugin.info.PluginInfo;
import com.icarus.plugin.properties.PluginProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.pf4j.PluginState;
import org.pf4j.PluginWrapper;
import org.pf4j.spring.SpringPluginManager;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class PluginResourceResourceServiceImpl implements PluginResourceService {

    private final SpringPluginManager pluginManager;

    private final PluginProperties properties;

    @Override
    public List<PluginInfo> getPlugins() {
        List<PluginWrapper> startedPlugins = pluginManager.getPlugins();
        List<PluginInfo> pluginInfos = new ArrayList<>();
        if (startedPlugins == null) {
            return pluginInfos;
        }
        return startedPlugins.stream()
                .filter(Objects::nonNull)
                .map(this::getPluginInfo)
                .collect(Collectors.toList());
    }

    @Override
    public boolean stopPlugin(String pluginId) {
        boolean b = checkPluginState(pluginId);
        if(!b){
            return false;
        }
        PluginState pluginState = pluginManager.stopPlugin(pluginId);
        if(pluginState.equals(PluginState.STOPPED)){
            return true;
        }
        return false;
    }

    @Override
    public boolean startPlugin(String pluginId) {
        boolean b = checkPluginState(pluginId);
        if(b){
            PluginState pluginState = pluginManager.startPlugin(pluginId);
            if(pluginState.equals(PluginState.STARTED)){
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean uninstall(String pluginId) {
        boolean b = checkPluginState(pluginId);
        if(!b){
            return true;
        }
       return pluginManager.deletePlugin(pluginId);
    }

    @Override
    public String loadPlugin(String path) {
        Path file = Paths.get(path);
        if(file.toFile().exists()){
            throw new RuntimeException("文件不存在");
        }
        return pluginManager.loadPlugin(file);
    }

    @Override
    public String installAndStart(MultipartFile pluginFile) throws Exception{
        if (pluginFile == null) {
            throw new IllegalArgumentException("Method:uploadPluginAndStart param 'pluginFile' can not be null");
        }
        Path path = uploadPlugin(pluginFile);
        String pluginId = pluginManager.loadPlugin(path);
        PluginState pluginState = pluginManager.startPlugin(pluginId);
        return pluginState.equals(PluginState.STARTED) ? pluginId : "";
    }

    /**
     * 判断插件是否安装
     * @param pluginId
     * @return
     */
    private boolean checkPluginState(String pluginId){
        PluginWrapper plugin = pluginManager.getPlugin(pluginId);
        if(null == plugin){
            log.info("插件未安装");
            return false;
        }
        return true;
    }


    /**
     * 上传插件
     *
     * @param pluginFile 插件文件
     * @return 返回上传的插件路径
     * @throws Exception 异常信息
     */
    protected Path uploadPlugin(MultipartFile pluginFile) throws Exception {
        if (pluginFile == null) {
            throw new IllegalArgumentException("Method:uploadPlugin param 'pluginFile' can not be null");
        }
        // 获取文件的后缀名
        String fileName = pluginFile.getOriginalFilename();
        String suffixName = fileName.substring(fileName.lastIndexOf(".") + 1);
        //检查文件格式是否合法
        if (StringUtils.isEmpty(suffixName)) {
            throw new IllegalArgumentException("Invalid file type, please select .jar or .zip file");
        }
        if (!"jar".equalsIgnoreCase(suffixName) && !"zip".equalsIgnoreCase(suffixName)) {
            throw new IllegalArgumentException("Invalid file type, please select .jar or .zip file");
        }
        String tempPathString = properties.getUploadTempPath() + File.separator + fileName;
        File tempPathFile = new File(properties.getUploadTempPath());
        if (!tempPathFile.exists()) {
            tempPathFile.mkdirs();
        }
        File file = new File(tempPathString);
        file.delete();
        file.createNewFile();
        Files.write(file.toPath(), pluginFile.getBytes());
        return file.toPath();
    }

    /**
     * 通过PluginWrapper得到插件信息
     *
     * @param pluginWrapper pluginWrapper
     * @return PluginInfo
     */
    private PluginInfo getPluginInfo(PluginWrapper pluginWrapper) {
        return new PluginInfo(pluginWrapper.getDescriptor(), pluginWrapper.getPluginState(),
                pluginWrapper.getPluginPath().toAbsolutePath().toString());
    }
}
