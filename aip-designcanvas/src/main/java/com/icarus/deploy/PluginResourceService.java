package com.icarus.deploy;

import com.icarus.plugin.info.PluginInfo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @description 插件资源操作类
 * @date 2025/2/25 11:31
 **/
public interface PluginResourceService {

    /**
     * 获取插件信息
     * @return 返回插件信息
     */
    List<PluginInfo> getPlugins();

    /**
     * 根据插件id停止插件
     * @param id 插件id
     * @return 返回操作结果
     */
    boolean stopPlugin(String id);

    /**
     * 根据插件id启动插件
     * @param id 插件id
     * @return 返回操作结果
     */
    boolean startPlugin(String id);

    /**
     * 根据插件id卸载插件
     * @param id 插件id
     * @return 返回操作结果
     */
    boolean uninstall(String id);

    /**
     * 根据插件路径安装插件。该插件jar必须在服务器上存在。注意: 该操作只适用于生产环境
     * @param path 插件路径名称
     * @return 操作结果
     */
    String loadPlugin(String path);

    /**
     * 安装和启动插件
     * @param file 插件文件
     * @return 返回操作结果
     */
    String installAndStart(MultipartFile file) throws Exception;
}
