package com.icarus.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 爬虫任务数据传输对象
 */
@Data
public class CrawlerTaskDTO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 爬虫配置ID
     */
    private Long settingsId;
    
    /**
     * 批次ID
     */
    private String batchId;
    
    /**
     * 任务状态：running-运行中，completed-已完成，failed-失败
     */
    private String status;
    
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    
    /**
     * 已爬取页数
     */
    private Integer pageCount;
    
    /**
     * 已下载文件数
     */
    private Integer fileCount;
    
    /**
     * 失败文件数
     */
    private Integer failedCount;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 配置名称（非数据库字段，用于展示）
     */
    private String configName;
    /**
     * 网页正文内容
     */
    private String textContent;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    /**
     * 当前页码(分页查询参数)
     */
    private Integer current;

    /**
     * 每页大小(分页查询参数)
     */
    private Integer pageSize;

    /**
     * 搜索关键词(搜索参数)
     */
    private String keywords;
}