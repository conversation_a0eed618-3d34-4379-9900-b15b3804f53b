package com.icarus.dto;

import com.icarus.entity.Model;
import com.icarus.sdk.client.api.completion.chat.ChatMessage;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@Data
public class ChatContextDTO {

    private String tabId;        // 标签页ID
    private String sessionId;    // 会话ID
    private Long timestamp;      // 请求时间戳
    private String requestId;    // 请求唯一标识

    private Model model;
    private List<ChatMessage> messages;

    private Integer maxTokens;
    private Float temperature;
    private Float topP;
    private Float topK;
    private Float frequencyPenalty;
    private Float presencePenalty;

}
