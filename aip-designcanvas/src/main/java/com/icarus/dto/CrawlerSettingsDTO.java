package com.icarus.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 爬虫配置数据传输对象
 */
@Data
public class CrawlerSettingsDTO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 爬虫配置名称
     */
    private String name;
    
    /**
     * 爬取起始URL
     */
    private String crawlUrl;
    
    /**
     * 列表项选择器
     */
    private String clickListPath;
    
    /**
     * 下一页选择器
     */
    private String nextPagePath;
    
    /**
     * 标题选择器
     */
    private String getTitle;
    
    /**
     * 发布时间选择器
     */
    private String getPubTime;
    
    /**
     * 内容选择器
     */
    private String getContent;
    
    /**
     * 附件选择器
     */
    private String getFile;
    
    /**
     * iframe选择器
     */
    private String iframe;
    
    /**
     * HTTP请求头，JSON格式
     */
    private String headers;

    /**
     * 代理设置，格式为host:port
     */
    private String proxy;

    /**
     * 超时时间，单位毫秒
     */
    private Integer timeout;

    /**
     * 重试次数
     */
    private Integer retryCount;
    
    /**
     * 最大爬取页数
     */
    private Integer maxPages;
    
    /**
     * 额外信息配置，JSON格式
     */
    private String mainExtraInfo;
    
    /**
     * 状态：active-启用，inactive-禁用
     */
    private String status;
    
    /**
     * 配置描述
     */
    private String description;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    /**
     * 当前页码(分页查询参数)
     */
    private Integer current;

    /**
     * 每页大小(分页查询参数)
     */
    private Integer pageSize;

    /**
     * 关键词(查询参数)
     */
    private String keyword;

    /**
     * 开始时间(查询范围参数)
     */
    private String startTime;

    /**
     * 结束时间(查询范围参数)
     */
    private String endTime;
}