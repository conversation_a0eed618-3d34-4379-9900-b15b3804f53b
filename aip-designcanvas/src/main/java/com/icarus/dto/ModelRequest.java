package com.icarus.dto;

import lombok.Data;
import java.util.Map;
import java.util.HashMap;

/**
 * 模型请求实体类
 * 用于封装发送给AI模型的请求参数
 */
@Data
public class ModelRequest {
    /** 请求ID */
    private String requestId;
    
    /** 模型名称 */
    private String modelName;
    
    /** 提示词 */
    private String prompt;
    
    /** 温度参数 0-2 之间，越大越有创造性，默认1 */
    private Double temperature = 1.0;
    
    /** 最大生成token数 */
    private Integer maxTokens;
    
    /**
     * 模型参数
     */
    private Map<String, Object> parameters;
    
    // 添加默认参数
    public ModelRequest() {
        this.parameters = new HashMap<>();
        this.parameters.put("temperature", 0.7);
        this.parameters.put("max_tokens", 2000);
    }
} 