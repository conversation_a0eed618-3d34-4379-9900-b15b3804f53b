package com.icarus.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 爬虫文件数据传输对象
 */
@Data
public class CrawlerFileDTO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 爬虫任务ID
     */
    private Long taskId;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 文件类型
     */
    private String fileType;
    
    /**
     * 文件大小（字节）
     */
    private Long fileSize;
    
    /**
     * MinIO对象存储URL
     */
    private String minioUrl;
    
    /**
     * 原始下载URL
     */
    private String originalUrl;
    
    /**
     * 网页标题
     */
    private String title;
    
    /**
     * 发布时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime publishTime;
    
    /**
     * 网页来源URL
     */
    private String sourceUrl;
    
    /**
     * 额外信息，JSON格式
     */
    private String extraInfo;
    
    /**
     * 内容哈希值，用于去重
     */
    private String contentHash;
    
    /**
     * 状态：success-成功，failed-失败
     */
    private String status;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    /**
     * 当前页码(分页查询参数)
     */
    private Integer current;

    /**
     * 每页大小(分页查询参数)
     */
    private Integer pageSize;
}