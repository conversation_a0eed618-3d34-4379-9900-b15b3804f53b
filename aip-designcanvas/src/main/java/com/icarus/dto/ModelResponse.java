package com.icarus.dto;

import lombok.Data;

/**
 * 模型响应实体类
 * 用于封装AI模型返回的结果
 */
@Data
public class ModelResponse {
    /** 请求ID */
    private String requestId;
    
    /** 模型名称 */
    private String modelName;
    
    /** 模型返回的结果 */
    private String result;
    
    /** 状态：success/failed */
    private String status;
    
    /** 错误信息 */
    private String error;
    
    /** 使用的token数 */
    private Integer totalTokens;
} 