package com.icarus.utils;

import com.icarus.common.cotnode.ab.ex.ExFormatNode;
import com.icarus.service.SpireService;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @description: txt转化工具类
 */
@Slf4j
public class TxtUtil_SpireTxt implements SpireService {


    @Override
    public List<Map<String, String>> convert2EasyEX(String filePath, int startPage, int endPage, ExFormatNode.Properties properties) throws IOException {
        log.info("开始处理TXT文件：{}", filePath);
        List<Map<String, String>> chunks = new ArrayList<>();

        List<String> textContent = paginateToList(filePath);
        for (int i=startPage;i<=endPage;i++){
            String content = textContent.get(i-1);
            Map<String, String> chunk = new HashMap<>();
            chunk.put("content",convertTxtTablesToHtml(content));
            chunk.put("pageRange", String.valueOf(i));
            chunks.add(chunk);
        }
        return chunks;
    }

    /**
     * 将包含表格的TXT文本转换为HTML表格
     */
    public static String convertTxtTablesToHtml(String txtContent) {
        StringBuilder result = new StringBuilder();
        String[] lines = txtContent.split("\n");
        List<String> tableLines = new ArrayList<>();
        boolean inTable = false;

        for (String line : lines) {
            if (isTableRow(line)) {
                if (!inTable) {
                    inTable = true;
                    tableLines.clear();
                }
                tableLines.add(line);
            } else {
                if (inTable) {
                    result.append(convertToHtmlTable(tableLines));
                    inTable = false;
                    tableLines.clear();
                }
                result.append(line).append("\n");
            }
        }

        // 处理末尾残留的表格
        if (!tableLines.isEmpty()) {
            result.append(convertToHtmlTable(tableLines));
        }

        return result.toString();
    }

    /**
     * 判断一行是否是表格行
     */
    private static boolean isTableRow(String line) {
        if (line == null || line.trim().isEmpty()) return false;

        if (line.chars().filter(ch -> ch == '|').count() >= 2) return true;
        if (line.contains("\t") && line.chars().filter(ch -> ch == '\t').count() >= 2) return true;
        if (line.matches(".*\\S\\s{2,}\\S.*")) return true;
        return line.matches("^([\\-+=]*\\s*)+$");
    }

    /**
     * 将表格行转换为HTML表格
     */
    private static String convertToHtmlTable(List<String> tableLines) {
        if (tableLines.isEmpty()) return "";

        List<String> filteredLines = new ArrayList<>();
        for (String line : tableLines) {
            if (!line.matches("^([\\-+=]*\\s*)+$")) {
                filteredLines.add(line);
            }
        }

        if (filteredLines.isEmpty()) return "";

        List<List<String>> rows = new ArrayList<>();
        // 全角空格和普通空格
        Pattern pattern = Pattern.compile("[\\s\\u3000]+");

        for (String line : filteredLines) {
            Matcher matcher = pattern.matcher(line);
            List<String> cells = new ArrayList<>();
            int lastEnd = 0;
            while (matcher.find()) {
                String segment = line.substring(lastEnd, matcher.start());
                if (!segment.trim().isEmpty()) {
                    cells.add(segment.trim());
                }
                lastEnd = matcher.end();
            }
            String segment = line.substring(lastEnd);
            if (!segment.trim().isEmpty()) {
                cells.add(segment.trim());
            }

            if (!cells.isEmpty()) {
                rows.add(cells);
            }
        }

        if (rows.isEmpty()) return "";

        StringBuilder html = new StringBuilder("<table>");

        for (List<String> row : rows) {
            html.append("<tr>");
            for (String cell : row) {
                html.append("<td>").append(cell).append("</td>");
            }
            html.append("</tr>\n");
        }

        html.append("</table>\n");
        return html.toString();
    }

    @Override
    public int getPageCount(String filePath) throws IOException {
        List<String> list = paginateToList(filePath);
        return list.size();
    }

    /**
     * 智能分页方法 - 将分页内容存储到Map集合中
     * @param filePath TXT文件路径
     * @return Map集合，键是页码(从1开始)，值是该页的内容
     */
    public static List<String> paginateToList(String filePath) throws IOException {
        List<String> pageList = new ArrayList<>();
        StringBuilder currentPage = new StringBuilder();

        // 自动检测文件编码
        Charset charset = detectFileEncoding(filePath);

        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(filePath), charset))) {

            // 匹配页码格式：第X页 或 页X
            Pattern pagePattern = Pattern.compile(
                    "^(第[\\d一二三四五六七八九十百千万]+页|页\\s*\\d+\\s*)$");

            String line;
            String previousLine = null; // 用于跟踪上一行

            while ((line = reader.readLine()) != null) {
                boolean currentLineIsEmpty = line.trim().isEmpty();

                // 检查是否是分页条件
                boolean isPageBreak = false;

                // 条件1: 当前行是页码行
                if (pagePattern.matcher(line.trim()).matches()) {
                    isPageBreak = true;
                }
                // 条件2: 连续出现两行或以上空白行
                // (previousLine为空白 且 当前行为空白) 表示至少连续两个空白行的开始
                else if (previousLine != null && previousLine.trim().isEmpty() && currentLineIsEmpty) {
                    isPageBreak = true;
                }

                if (isPageBreak) {
                    // 只有当前页有内容时才分页
                    if (!currentPage.isEmpty()) {
                        // 移除末尾可能多余的换行符
                        String pageContent = currentPage.toString().replaceAll("\\n+$", "");
                        pageList.add(pageContent);
                        currentPage = new StringBuilder();
                    }
                    // 重置 previousLine，因为分页后连续空白行计数应重新开始
                    previousLine = null;
                    continue; // 跳过页码行或作为分隔符的空白行
                }

                // 如果当前行不是空白行，则添加到当前页
                if (!currentLineIsEmpty) {
                    currentPage.append(line).append("\n");
                }
                // 如果当前行是单个空白行（不构成连续两行），则也添加（可选，取决于是否想保留单个空白行）
                // 否则，单个空白行会被忽略

                previousLine = line; // 更新上一行
            }

            // 添加最后一页（如果有内容）
            if (!currentPage.isEmpty()) {
                String pageContent = currentPage.toString().replaceAll("\\n+$", "");
                pageList.add(pageContent);
            }
        }

        return pageList;
    }

    /**
     * 自动检测文件编码
     */
    private static Charset detectFileEncoding(String filePath) throws IOException {
        // 常见中文编码列表（按可能性排序）
        Charset[] charsetList = {
                StandardCharsets.UTF_8,
                Charset.forName("GBK"),
                Charset.forName("GB2312"),
                StandardCharsets.ISO_8859_1,
                StandardCharsets.UTF_16
        };

        // 只读取文件前100KB内容用于编码检测
        final int MAX_BYTES_TO_READ = 1024 * 100;

        for (Charset charset : charsetList) {
            try (InputStreamReader reader = new InputStreamReader(
                    new FileInputStream(filePath), charset)) {

                StringBuilder content = new StringBuilder();
                char[] buffer = new char[4096];
                int totalRead = 0;
                int read;

                // 只读取前部分内容
                while ((read = reader.read(buffer)) != -1 && totalRead < MAX_BYTES_TO_READ) {
                    content.append(buffer, 0, read);
                    totalRead += read;
                }

                // 检查常见中文关键词（更严格的验证）
                String sample = content.toString();
                if (sample.contains("价") || sample.contains("额") ||
                        sample.contains("日") || sample.contains("卖")) {
                    return charset;
                }
            }
        }

        // 默认返回UTF-8（最常见的编码）
        return StandardCharsets.UTF_8;
    }
}
