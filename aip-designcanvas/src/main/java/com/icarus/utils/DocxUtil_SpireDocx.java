package com.icarus.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.icarus.common.cotnode.ab.ex.ExFormatNode;
import com.icarus.service.SpireService;
import com.spire.doc.*;
import com.spire.doc.collections.*;
import com.spire.doc.documents.Paragraph;
import com.spire.doc.pages.FixedLayoutDocument;
import com.spire.doc.pages.FixedLayoutPage;
import com.spire.doc.pages.FixedLayoutRow;
import com.spire.doc.pages.LayoutCollection;
import com.spire.pdf.PdfDocument;
import com.spire.pdf.PdfPageBase;
import com.spire.pdf.exporting.PdfImageInfo;
import com.spire.pdf.texts.PdfTextExtractOptions;
import com.spire.pdf.texts.PdfTextExtractor;
import com.spire.pdf.utilities.PdfTable;
import com.spire.pdf.utilities.PdfTableExtractor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.list.SetUniqueList;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.IdGenerator;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: Doc,Docx抽取文本
 */
@Slf4j
public class DocxUtil_SpireDocx implements SpireService {
    static {
        String licKey = "8lS3aTV/9xA5AQDcY/pJN7VuMkpOJwTuHVC2DGGIiLKmxdgnsnYfHalR03U4yIfF3iRrxZDJcl9NwsBoG24/TGF+fblxknR5UamvOT4PkRp5RLO0u+o/RPqgC4RCp5c9psVtiSv3/I5zycQ8O3+GXYSB8z+rgMHcyLEzc9RwVdOUBNvyCVHaB8+j5hLDWtjVazChg+Wb8klpjpX9bziPtFq8JPzdd289w1e59F/USu/HjgyW9JnVoloKKpDap0xVb4KcHBkEbuZ/MQqE8GghbpxcLe1/3o5bCwLoOGdg4jf7EkJRJKDZRLI4oLgur6MLAI2OLBlyldBQuVsUxFh5yKdJ182SPdmTzWw+bVdyYAjUUOkSpSVGaKFI17QafbANTlFOulia9JYyz81HtlBGihgi5aFqaq/W4QKGU6uuYYjZ4u8JJjUrx5VLZDbZ2TM2796dV9fsRaozw4frpKB2i2jTBgGNRVbhWOb9JF11Ve+T5z9/oIv32ybtO0HYY/j0oTmYRMyNwcZICLPfwMneoSIBiXa1G+4UtpTqpVnbPNysf67iPoVEiBtg+tPNjBnxlh2o3Tb2MNs8trxd5RXKYDCxaWU09YVtmngMW6esiPCZLKdYP7P9y6ja7m/1Oaj48K9iiFZGBb2RLbvF7k19FVuvVymChwdhJY3q8319CB4ZPJ1udGvXt2g/RJNkPHwpV7LyHtHsEkgZ78/slg5yqr9w4bPFaxr/B2TmBPmmMUh1f7nDOG/cZGmkfk7KCnr3C8Jy7QaPHumHAnuSWBfrlS83vaBgf3jtQ2K2LZTZxqvWtovEk2pXM7dFb8GZJyUeRqpPoEDulzjxAipo3UJKdQx8W8+wWxqHcJP+meNmetHRGnMjSnOkyoymi8uRiGCSmrw3X2+e+0Ls365Vq1+S0FuzpLu1A2mv47KX/jjkx1IqCjdwVyI8VCJiNlElU3ootD8kOjeVfR+w+bq3/a3E8SSmQgI5vOAYi3REnTbThA5Uaqz3/YVnguV9FvESrzvycK1045txxydHeImhmjbo8lWwW/mIM0K6l3PNrpOMElnSj8QFPTNE4FUGd5ijze2q8MhspYN4fODh8UfnO8i6JAtQy3stlRbVgILY+vZ2ivPcdwmEcvKbfms5nu6em2ZmDIw7qR6du9QH+j29ifsIQKd7Hq3MkKVYAghQT8BMi+uH7pAY9K9yz42gOG3KfBE/TTkl+TTgz1S2lni3Z+uIY2vTYxl5jckduguZUyK788tTveQ+W+0I+ygykw+OfjKJ1Yi+8rSNBfV3nUlbEhftH5/1X6XsH2oSr0gIlyCNpCW79zTK6bqQHSynBdV4cGyb58+W0WF3L1OzaPyjrcr/rF6oPD5ox3TkWnsxG60kzhCWxGcB+DjhYxKzLDD850S/5BZL0uCE2mReL08c7SjGiCU7S+JdGAwGBZGRKe6Yneu5u7a/SsR94WaBqZOPRhXhHVEEvDrpLrPmCokTbHPUbCAKoOStJA4bYT46fnl4FWfwwzbu6ElsLggJL7TUgWAQI4J3kfyED1uthCkT+uaY+cqC2ViTOIhCmRkvCKpc7oYsIU7Xq9Dgm9621x835uj126X0NyEYu4aYt9hs0fiU8jUXpFOxDTN2";
        com.spire.doc.license.LicenseProvider.setLicenseKey(licKey);
        com.spire.xls.license.LicenseProvider.setLicenseKey(licKey);
        com.spire.presentation.license.LicenseProvider.setLicenseKey(licKey);
        com.spire.pdf.license.LicenseProvider.setLicenseKey(licKey);
        com.spire.ocr.license.LicenseProvider.setLicenseKey(licKey);
    }


    public  List<Map<String, String>> convert2EasyEX(String filePath, int startPage, int endPage, ExFormatNode.Properties properties) {
        log.info("开始处理Doc文件：{}", filePath);
        List<Map<String, String>> chunks = new ArrayList<>();
        // 设置字体路径
        String fontPath = "aip-designcanvas/src/main/java/com/icarus/utils/";
        try {
            Document.setGlobalCustomFontsFolders(fontPath);
            log.info("字体设置成功: {}", fontPath);
        } catch (Exception e) {
            log.error("字体设置失败: {}", fontPath, e);
        }

        Document spireDoc = new Document();
        spireDoc.loadFromFile(filePath);

        int start = Math.max(1, startPage);
        int end = Math.min(spireDoc.getPageCount(), endPage);

        // 创建一个固定布局文档对象
        FixedLayoutDocument layoutDoc = new FixedLayoutDocument(spireDoc);

        // 处理每一页
        for (int page_i = start; page_i <= end; page_i++) {
            Map<String, String> chunk = new HashMap<>();

            int iPageNum = page_i - 1;

            // 获取当前页的文本内容
            FixedLayoutPage page = layoutDoc.getPages().get(iPageNum);
            String pageContent = page.getText();
            pageContent = pageContent.replaceAll("\n¤","¶").replaceAll("¤","").
                    replaceAll("Evaluation Warning: The document was created with Spire.Doc for JAVA.", "");

            // 分割文本内容为行数组，用于定位表格的位置。定位表格时，把每行中的空格全去掉再比较，如果连续一致就是表格的位置，需要换掉
            String[] linesContent = Arrays.stream(pageContent.split("¶"))
                    .map(String::trim)
                    .toArray(String[]::new);
            String[] linesWithoutSpace = Arrays.stream(linesContent)
                    .map(s -> s.replaceAll("\\s+", "")
                    )
                    .toArray(String[]::new);

            // 获取当前页的表格
            Document document = copyPageToDoc(page);
            Section section = document.getSections().get(0);
            TableCollection tableCollection = section.getTables();


            if (CollectionUtil.isEmpty(tableCollection)) {
                chunk.put("content", pageContent.trim());
                chunk.put("pageRange", String.valueOf(page_i));
            } else {
                // 文本中表格的行定位
                List<Map<String, Object>> replaceList = new ArrayList<>();
                for (int iTableNum = tableCollection.getCount() - 1; iTableNum >= 0; iTableNum--) {
                    Table itab =tableCollection.get(iTableNum);
                    // 将表格转换成list<string>，每一行的字段拼在一起形成（注意不要空格），形成一个列表，用于比较
                    List<String> tableRows = new ArrayList<>();
                    for (int k = 0; k < itab.getRows().getCount(); k++) {
                        // 获取一行
                        TableRow row = itab.getRows().get(k);
                        // 遍历行中的单元格
                        StringBuilder rowText = new StringBuilder();
                        CellCollection cells = row.getCells();
                        for (int l = 0; l < cells.getCount(); l++) {
                            String cellText = "";
                            for (int m = 0; m < cells.get(l).getParagraphs().getCount(); m++) {
                                Paragraph paragraph = cells.get(l).getParagraphs().get(m);
                                cellText += paragraph.getText() + " ";
                            }
                            if (cellText.trim().length() > 0){
                                rowText.append(cellText.trim()
                                        .replaceAll(" ", "")
                                        .replaceAll(" ", "")
                                        .replaceAll("\n", "")
                                        .replaceAll("\r", "")
                                );
                            }
                        }
                        tableRows.add(rowText.toString().trim());
                        }

                    // 寻找表格在文本中的起始和结束行号（连续匹配），使用与表格接近长度的字符串来进行相似度匹配，会包含多行
                    int iStart = -1, iStop = -1;
                    int tableSize = tableRows.size();
                    if (tableSize == 0) continue; // 跳过空表格
                    for (int i = 0; i <= linesWithoutSpace.length - tableSize; i++) {
                        boolean match = false;
                        // 待比较的表格（将html标签去掉了）
                        ArrayList<String> lstTable2Compare = new ArrayList<>(tableRows.stream()
                                .map(s -> s.replaceAll("\\<.*?\\>", "")) // 使用正则表达式去除HTML标签
                                .collect(Collectors.toList()));
                        // 构建待比较的正文，是从i行开始取，直到字符数>=表格字符数
                        int targetChars = lstTable2Compare.stream().mapToInt(String::length).sum();
                        ArrayList<String> lstContentWithoutSpace = new ArrayList<>();
                        int currentChars = 0;
                        for (int k = i; k < linesWithoutSpace.length; k++) {
                            String line = linesWithoutSpace[k];
                            currentChars += line.length();
                            lstContentWithoutSpace.add(line);
                            if (currentChars >= targetChars) {
                                break;
                            }
                        }
                        // 比较正文i行开始的块，和表格，以0.9作为阈值，确定这块文字就是表格
                        if (tools_calc_similarity(lstContentWithoutSpace, lstTable2Compare) > 0.80) {
                            match = true;
                            iStart = i;
                            iStop = i + lstContentWithoutSpace.size() - 1;
                            // 记录下来要替换的行号以及内容
                            HashMap<String, Object> replaceMap = new HashMap<>();
                            replaceMap.put("iStart", iStart);
                            replaceMap.put("iStop", iStop);
                            String htmlTable = convertTableToHtml(itab);
                            replaceMap.put("replaceContent", htmlTable);
                            replaceList.add(replaceMap);
                            //不能break，因为表格可能重复
                        }
                    }
                }

                // 根据iStart重新排列replaceList
                // 根据iStart排序，如果iStart相同则保留iStop较大的对象
                replaceList.sort((map1, map2) -> {
                    int startCompare = Integer.compare((int) map1.get("iStart"), (int) map2.get("iStart"));
                    if (startCompare == 0) {
                        // iStart相等时，按照iStop降序排列并保留第一个
                        return Integer.compare((int) map2.get("iStop"), (int) map1.get("iStop"));
                    }
                    return startCompare;
                });

                // 创建一个新列表来存储去重后的结果
                List<Map<String, Object>> uniqueList = new ArrayList<>();
                for (Map<String, Object> current : replaceList) {
                    if (uniqueList.isEmpty()) {
                        uniqueList.add(current);
                    } else {
                        Map<String, Object> last = uniqueList.get(uniqueList.size() - 1);
                        if ((int) current.get("iStart") != (int) last.get("iStart")) {
                            uniqueList.add(current);
                        }
                        // 如果iStart相同，则不添加当前对象（保留已有的，因为已经按iStop排序）
                    }
                }

                replaceList.clear();
                replaceList.addAll(uniqueList);
                replaceList.sort(Comparator.comparingInt(map -> (int) map.get("iStart")));

                // 根据替换列表，重构pageContent
                StringBuilder newPageContent = new StringBuilder();
                int iFrom = 0;
                for (int i = 0; i < replaceList.size(); i++) {
                    Map<String, Object> replaceMap = replaceList.get(i);
                    int iStart = (int) replaceMap.get("iStart");
                    int iStop = (int) replaceMap.get("iStop");
                    String replaceContent = (String) replaceMap.get("replaceContent");

                    if (iStart == -1 || iStop == -1 || iStart > iStop) continue;

                    for (int j = iFrom; j < iStart; j++) {
                        newPageContent.append(linesContent[j]).append("\n");
                    }

                    newPageContent.append(replaceContent).append("\n");
                    iFrom = iStop + 1;
                }
                for (int i = iFrom; i < linesContent.length; i++) {
                    newPageContent.append(linesContent[i]).append("\n");
                }

                chunk.put("content", newPageContent.toString().trim());
                chunk.put("pageRange", String.valueOf(page_i));
            }

            chunks.add(chunk);
            document.dispose();
        }
        spireDoc.close();
        return chunks;
    }
    /**
     * 将表格转换为HTML格式，支持合并单元格
     */
    private static String convertTableToHtml(Table table) {
        StringBuilder html = new StringBuilder();
        html.append("<table>\n");

        // 创建一个二维数组来跟踪单元格的状态
        // 行的集合
        RowCollection rows = table.getRows();
        int max = 0;
        for (int i = 0; i < rows.getCount(); i++) {
            max = Math.max(max, rows.get(i).getCells().getCount());
        }
        boolean[][] processedCells = new boolean[rows.getCount()][max];
        // 遍历所有行
        for (int row = 0; row < rows.getCount(); row++) {
            // 检查这一行是否有内容
            boolean hasContent = false;
            //这行所有单元格
            CellCollection cells = rows.get(row).getCells();
            for (int col = 0; col < cells.getCount(); col++){
                String cellText = "";
                for (int m = 0; m < cells.get(col).getParagraphs().getCount(); m++) {
                    Paragraph paragraph = cells.get(col).getParagraphs().get(m);
                    cellText += paragraph.getText() + " ";
                }
                if (StrUtil.isNotBlank(cellText)){
                    hasContent = true;
                    break;
                }
            }
            if (!hasContent) continue; // 跳过空行

            html.append("<tr>");

            // 处理这一行的所有列
            for (int col = 0; col <  cells.getCount(); col++) {
                // 如果单元格已经被处理过（作为合并单元格的一部分），则跳过
                if (processedCells[row][col]) continue;

                String cellText = "";
                for (int m = 0; m < cells.get(col).getParagraphs().getCount(); m++) {
                    Paragraph paragraph = cells.get(col).getParagraphs().get(m);
                    cellText += paragraph.getText() + " ";
                }
                if (cellText == null) cellText = "";
                cellText = cellText.trim();

                // 检查合并单元格
                int rowSpan = 1;
                int colSpan = 1;

                // 检查向右合并
                for (int c = col + 1; c < cells.getCount(); c++) {
                    String nextText = "";
                    for (int m = 0; m < cells.get(c).getParagraphs().getCount(); m++) {
                        Paragraph paragraph = cells.get(c).getParagraphs().get(m);
                        nextText += paragraph.getText() + " ";
                    }
                    if (StrUtil.isBlank(nextText)) {
                        colSpan++;
                        processedCells[row][c] = true;
                    } else {
                        break;
                    }
                }

                // 检查向下合并
                boolean canMergeDown = true;
                for (int r = row + 1; r < rows.getCount() && canMergeDown; r++) {
                    ParagraphCollection paragraphs = table.get(r, col).getParagraphs();
                    String currentCell = "";
                    for (int m = 0; m < paragraphs.getCount(); m++) {
                        Paragraph paragraph =paragraphs.get(m);
                        currentCell += paragraph.getText() + " ";
                    }
                    if (StrUtil.isBlank(currentCell)){
                        boolean allEmpty = true;
                        // 检查合并列范围内的所有单元格是否都为空
                        for (int c = col; c < col + colSpan && allEmpty; c++) {
                            String nextCell = "";
                            ParagraphCollection paragraphCollection = table.get(r, c).getParagraphs();
                            for (int m = 0; m < paragraphCollection.getCount(); m++) {
                                Paragraph paragraph =paragraphs.get(m);
                                nextCell += paragraph.getText() + " ";
                            }
                            if (StrUtil.isNotBlank(nextCell)) {
                                allEmpty = false;
                            }
                        }
                        if (allEmpty) {
                            rowSpan++;
                            // 标记这些单元格为已处理
                            for (int c = col; c < col + colSpan; c++) {
                                processedCells[r][c] = true;
                            }
                        } else {
                            canMergeDown = false;
                        }
                    } else {
                        canMergeDown = false;
                    }
                }

                // 生成单元格HTML
                String tag = (row == 0) ? "th" : "td";
                html.append("<").append(tag);

                // 添加合并属性
                if (rowSpan > 1) html.append(" rowspan='").append(rowSpan).append("'");
                if (colSpan > 1) html.append(" colspan='").append(colSpan).append("'");


                html.append(">");
                html.append(cellText);
                html.append("</").append(tag).append(">");
            }

            html.append("</tr>\n");
        }

        html.append("</table>");
        return html.toString();
    }

    /**
     * 用来辅助表格定位用的相似度
     * 比较两个字符串的相似度，可能会有少字、多字的情况，不要比较不可见字符，最后输出一个0-1之间的double数字代表相似度
     *
     * @param lstOrg
     * @param lstMod
     * @return
     */
    public static double tools_calc_similarity(List<String> lstOrg, List<String> lstMod) {
        // 1. 合并所有行并去除空白字符，但保留换行符，因为行数对表格替换很重要
        String original = lstOrg.stream()
                .map(line -> line.replaceAll("\\s+", "")) // 去除每行内的空白字符
                .collect(Collectors.joining("\n")); // 用换行符连接，保留空行（空字符串
        String modified = lstMod.stream()
                .map(line -> line.replaceAll("\\s+", "")) // 去除每行内的空白字符
                .collect(Collectors.joining("\n"));

        if (original.isEmpty()) {
            return modified.isEmpty() ? 1.0 : 0.0;
        }

        // 检查前3个字符是否匹配（考虑空行）
        int minLength = Math.min(original.length(), modified.length());
        if (minLength < 3) {
            if (!original.equals(modified)) return 0.0;
        } else {
            if (!original.substring(0, 3).equals(modified.substring(0, 3))) {
                return 0.0;
            }
        }

        // 2. 统计字符频率
        Map<Character, Integer> originalCounts = new HashMap<>();
        for (char c : original.toCharArray()) {
            originalCounts.put(c, originalCounts.getOrDefault(c, 0) + 1);
        }

        Map<Character, Integer> modifiedCounts = new HashMap<>();
        for (char c : modified.toCharArray()) {
            modifiedCounts.put(c, modifiedCounts.getOrDefault(c, 0) + 1);
        }

        // 3. 计算交集的最小总次数
        int total = 0;
        for (Map.Entry<Character, Integer> entry : originalCounts.entrySet()) {
            char key = entry.getKey();
            int countInOriginal = entry.getValue();
            int countInModified = modifiedCounts.getOrDefault(key, 0);
            total += Math.min(countInOriginal, countInModified);
        }

        return (double) total / original.length();
    }



    @Override
    public int getPageCount(String filePath)  {
        //创建Document对象
        Document doc = new Document();
        //加载Word文档
        doc.loadFromFile(filePath);
        return doc.getPageCount();
    }

    private Document copyPageToDoc(FixedLayoutPage page) {
        // 获取页面所在的节
        Section section = page.getSection();

        // 获取页面的第一个段落
        Paragraph paragraphStart = page.getColumns().get(0).getLines().getFirst().getParagraph();
        int startIndex = 0;
        if (paragraphStart != null)
        {
            // 获取段落在节中的索引
            startIndex = section.getBody().getChildObjects().indexOf(paragraphStart);
        }

        // 获取页面的最后一个段落
        Paragraph paragraphEnd = page.getColumns().get(0).getLines().getLast().getParagraph();

        int endIndex = 0;
        if (paragraphEnd != null)
        {
            // 获取段落在节中的索引
            endIndex = section.getBody().getChildObjects().indexOf(paragraphEnd);
        }

        // 创建一个新的文档对象
        Document newdoc = new Document();

        // 添加一个新的节
        Section newSection = newdoc.addSection();

        // 克隆原节的属性到新节
        section.cloneSectionPropertiesTo(newSection);

        // 复制原文档中页面内容到新文档
        for (int i = startIndex; i <=endIndex; i++)
        {
            newSection.getBody().getChildObjects().add(section.getBody().getChildObjects().get(i).deepClone());
        }
        return newdoc;
    }
}