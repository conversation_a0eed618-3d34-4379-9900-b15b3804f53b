package com.icarus.utils;

import cn.hutool.core.util.IdUtil;
import com.icarus.common.cotnode.ab.ex.ExFormatNode;
import com.icarus.common.minio.MinioUtil;
import com.icarus.config.UrlConfigHolder;
import com.icarus.sdk.client.api.completion.chat.ChatMessage;
import com.icarus.sdk.client.utils.LLMClientUtil;
import com.icarus.sdk.prompt.PromptTemplate;
import com.icarus.sdk.springboot.base.utils.SpringContextUtils;
import com.icarus.service.SpireService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.icarus.utils.PdfUtil_SpirePDF.*;


/**
 * @description: 图片抽取要素
 */
@Slf4j
public class PicUtil_SpirePic implements SpireService {


    @Override
    public List<Map<String, String>> convert2EasyEX(String filePath, int startPage, int endPage, ExFormatNode.Properties properties) throws IOException {
        log.info("开始处理图片格式：{}", filePath);
        List<Map<String, String>> chunks = new ArrayList<>();
        Map<String, String> chunk = new HashMap<>();
        chunk.put("content",convertTxtTablesToHtml(filePath));
        chunk.put("pageRange", "1");
        chunks.add(chunk);

        return chunks;
    }

    private String convertTxtTablesToHtml(String filePath) throws IOException {
        MinioUtil minioUtil = SpringContextUtils.getBean(MinioUtil.class);
        //从filePath截取文件名
        File file = null;
        String type = filePath.substring(filePath.lastIndexOf(".") + 1);
        if ("tiff".equalsIgnoreCase(type)||"tif".equalsIgnoreCase(type)||"bmp".equalsIgnoreCase(type)){
            file= convertTiffToJpeg(filePath);
            filePath = file.getAbsolutePath();
            type = "jpg";
        }

        String fileName = IdUtil.fastSimpleUUID() + "." + type;
        //下载文件到本地
        log.info("开始下载文件: {}", filePath);

        //将filePath转化成MultipartFile
        log.info("将filePath转化成MultipartFile");
        CustomMultipartFile multipartFile = new CustomMultipartFile(filePath);
        minioUtil.uploadFile(multipartFile, "temp-bucket", fileName);
        if (file != null){
            file.delete();
        }
        log.info("已上传文件: {}", fileName);
        //删除文件
        //拼接vl模型需要的url
        //获取提示词
        String pdfEasyEx = PromptTemplate.builder("prompts/EX/vl_pdf_easyEx.groovy", "pdf_easyEx").build();
        // 创建ChatMessage并添加提示词
        ChatMessage msg = new ChatMessage(pdfEasyEx);
        String url = UrlConfigHolder.getMyServerUrl() + "aip/vlm/view/" + fileName;
        msg.attachImageUrl(url);
        log.info("已添加图片URL: {}", url);
    // 调用LLMClientUtil进行分析
        String answer = LLMClientUtil.builder()
            .modelName(MODEL_NAME)
            .maxTokens(MAX_TOKEN)
            .temperature(TEMP)
            .topP(TOP)
            .chat(msg)
            .runChatCompletion();
        log.info("VLM模型分析完成，结果长度：{}", answer != null ? answer.length() : 0);
        minioUtil.deleteFile("temp-bucket", fileName);
        return answer;
    }

    public static File convertTiffToJpeg(String inputPath) throws IOException {
        // 读取TIFF文件
        File inputFile = new File(inputPath);
        BufferedImage tiffImage = ImageIO.read(inputFile);

        // 检查是否成功读取
        if (tiffImage == null) {
            throw new IOException("无法读取TIFF文件或格式不受支持");
        }

        // 创建输出文件路径（将.后面的文本替换成jpg）
        String outputPath = inputPath.substring(0, inputPath.lastIndexOf(".")) + ".jpg";
        File outputFile = new File(outputPath);

        // 如果目标文件已存在，则删除
        if (outputFile.exists()) {
            if (!outputFile.delete()) {
                throw new IOException("无法删除已存在的输出文件: " + outputPath);
            }
        }

        // 写入JPEG文件
        boolean success = ImageIO.write(tiffImage, "jpg", outputFile);

        if (!success) {
            throw new IOException("JPEG转换失败");
        }

        return outputFile;
    }

    @Override
    public int getPageCount(String filePath) {
        //暂时只支持一张图片
        return 1;
    }

    public class CustomMultipartFile implements MultipartFile {
        private final byte[] fileContent;
        private final String fileName;
        private final String contentType;

        public CustomMultipartFile(String filePath) throws IOException {
            File file = new File(filePath);
            this.fileName = file.getName();
            this.fileContent = Files.readAllBytes(file.toPath());
            this.contentType = Files.probeContentType(file.toPath());
        }

        @Override
        public String getName() {
            return "file";
        }

        @Override
        public String getOriginalFilename() {
            return fileName;
        }

        @Override
        public String getContentType() {
            return contentType;
        }

        @Override
        public boolean isEmpty() {
            return fileContent == null || fileContent.length == 0;
        }

        @Override
        public long getSize() {
            return fileContent.length;
        }

        @Override
        public byte[] getBytes() throws IOException {
            return fileContent;
        }

        @Override
        public InputStream getInputStream() throws IOException {
            return new ByteArrayInputStream(fileContent);
        }

        @Override
        public void transferTo(File dest) throws IOException, IllegalStateException {
            try (FileOutputStream fos = new FileOutputStream(dest)) {
                fos.write(fileContent);
            }
        }
    }

}