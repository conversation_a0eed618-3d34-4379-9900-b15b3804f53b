package com.icarus.utils;

import com.icarus.common.cotnode.ab.ex.ExFormatNode;
import com.icarus.service.SpireService;
import com.spire.xls.Workbook;
import com.spire.xls.Worksheet;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @description: xlsx格式抽取工具类
 */
@Slf4j
public class XlsxUtil_SpireXlsx implements SpireService {

    @Override
    public List<Map<String, String>> convert2EasyEX(String filePath, int startPage, int endPage, ExFormatNode.Properties properties) throws IOException {
        log.info("开始处理XLSX文件：{}", filePath);
        Workbook workbook = new Workbook();
        workbook.loadFromFile(filePath);
        List<Map<String, String>> chunks = new ArrayList<>();
        for (int i = startPage-1; i < endPage;i++){
            Worksheet worksheet = workbook.getWorksheets().get(i);
            File tempFile = File.createTempFile("worksheet-", ".html");
            tempFile.deleteOnExit();
            worksheet.saveToHtml(tempFile.getAbsolutePath());
            worksheet.dispose();
            String content = Files.readString(Path.of(tempFile.getAbsolutePath()));
            Map<String, String> chunk = new HashMap<>();
            chunk.put("content", extractCleanTable(content));
            chunk.put("pageRange", String.valueOf(i+1));
            chunks.add(chunk);
            boolean deleted = tempFile.delete();
            if (!deleted) {
                log.warn("临时文件删除失败: {}", tempFile.getAbsolutePath());
            }
        }
        workbook.dispose();
        return chunks;
    }

    @Override
    public int getPageCount(String filePath) {
        Workbook workbook = new Workbook();
        workbook.loadFromFile(filePath);
        return workbook.getWorksheets().getCount();
    }

    public static String extractCleanTable(String html) {
        // 1. 提取<table>...</table>部分
        Pattern tablePattern = Pattern.compile("<table[^>]*?>.*?</table>", Pattern.DOTALL);
        Matcher tableMatcher = tablePattern.matcher(html);

        if (!tableMatcher.find()) {
            return "";
        }

        String tableHtml = tableMatcher.group();

        // 2. 移除所有<col>标签
        tableHtml = tableHtml.replaceAll("<[cC][oO][lL][^>]*?>", "");

        // 3. 处理隐藏文本和特殊标签
        tableHtml = tableHtml.replaceAll("<span[^>]*?style\\s*=\\s*[\"'][^\"']*?display\\s*:\\s*none[^\"']*?[\"'][^>]*?>.*?</span>", "")
                .replaceAll("<a[^>]*?title\\s*=\\s*\"([^\"]*)\"[^>]*>.*?</a>", "$1");

        // 4. 移除样式和无关标签
        tableHtml = tableHtml.replaceAll("<style.*?>.*?</style>", "")
                .replaceAll("<div[^>]*?>|</div>", "")
                .replaceAll("<font[^>]*?>|</font>", "")
                .replaceAll("<h2[^>]*?>.*?</h2>", "")
                .replaceAll("<head.*?>.*?</head>", "")
                .replaceAll("<body.*?>|</body>", "")
                .replaceAll("<html.*?>|</html>", "")
                .replaceAll("(class|style)\\s*=\\s*[\"'][^\"']*[\"']", "");

        // 5. 特殊处理保留colspan和rowspan属性（包括大写形式）
        tableHtml = tableHtml.replaceAll( "<(td|th)([^>]*?)((colspan|rowspan)\\s*=\\s*[\"']?\\d+[\"']?)([^>]*)>",
                        "<$1 $3>");

        // 6. 清理其他标签属性
        tableHtml = tableHtml.replaceAll("<(tr|table)[^>]*?>", "<$1>");

        // 7. 处理换行和空格
        tableHtml = tableHtml.replaceAll("(?m)^\\s*$[\n\r]{1,}", "")
                .replaceAll("\\s+", " ")
                .replaceAll(">\\s+<", "><")
                .trim();

        return tableHtml;
    }
}