package com.icarus.utils;

import com.spire.doc.Document;
import com.spire.pdf.PdfDocument;
import com.spire.pdf.PdfPageBase;
import com.spire.pdf.texts.PdfTextExtractOptions;
import com.spire.pdf.texts.PdfTextExtractor;
import com.spire.pdf.utilities.PdfTable;
import com.spire.pdf.utilities.PdfTableExtractor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.pdfbox.text.TextPosition;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

@Slf4j
public class PdfHandleUtil {
    
    /**
     * PDF拆分模式枚举
     */
    public enum PDFSplitMode {
        BY_FONTSIZE,    // 按字体大小拆分（用于识别标题等）
        BY_PAGE,        // 按页码拆分
        BY_CHAPTER      // 按章节拆分（基于标题识别）
    }

    /**
     * 判断两个表格是否应该合并
     */
    private static boolean shouldMergeTables(PdfTable table1, PdfTable table2) {
        if (table1 == null || table2 == null) {
            return false;
        }

        // 检查列数是否相同或近似（允许差异1列）
        if (Math.abs(table1.getColumnCount() - table2.getColumnCount()) > 1) {
            return false;
        }

        // 检查行数是否合理
        if (table1.getRowCount() == 0 || table2.getRowCount() == 0) {
            return false;
        }

        // 优先检查是否为表格延续
        if (isTableContinuation(table1, table2)) {
            return true;
        }

        // 检查表头是否相同（对于没有续表标记的情况）
        if (hasMatchingHeaders(table1, table2)) {
            return true;
        }

        // 检查内容连续性和结构相似性
        return (checkRowsContinuity(table1, table2) && hasSimilarColumnStructure(table1, table2));
    }

    /**
     * 检查是否为表格延续
     */
    private static boolean isTableContinuation(PdfTable table1, PdfTable table2) {
        // 获取第二个表格的第一行内容
        String firstRowContent = "";
        String secondRowContent = "";
        
        // 检查第一行
        for (int col = 0; col < table2.getColumnCount(); col++) {
            String cellText = table2.getText(0, col);
            if (cellText != null) {
                firstRowContent += cellText.trim() + " ";
            }
        }
        
        // 如果有第二行，也检查第二行（有时续表标记在第二行）
        if (table2.getRowCount() > 1) {
            for (int col = 0; col < table2.getColumnCount(); col++) {
                String cellText = table2.getText(1, col);
                if (cellText != null) {
                    secondRowContent += cellText.trim() + " ";
                }
            }
        }
        
        firstRowContent = firstRowContent.trim().toLowerCase();
        secondRowContent = secondRowContent.trim().toLowerCase();
        
        // 扩展续表关键词检查
        String[] continuationKeywords = {
            "续", "（续）", "(续)", "续表", "continued", "(continued)",
            "续上表", "续前表", "（续上页）", "(续上页)", "续第", "续表", 
            "continued from", "continued from previous page"
        };
        
        for (String keyword : continuationKeywords) {
            if (firstRowContent.contains(keyword) || secondRowContent.contains(keyword)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检查两个表格是否有匹配的表头
     */
    private static boolean hasMatchingHeaders(PdfTable table1, PdfTable table2) {
        if (table1.getRowCount() == 0 || table2.getRowCount() == 0) {
            return false;
        }

        // 获取两个表格的表头
        String[] header1 = getRowContent(table1, 0);
        String[] header2 = getRowContent(table2, 0);

        // 如果表头完全相同，直接返回true
        if (Arrays.equals(header1, header2)) {
            return true;
        }

        // 计算表头的相似度
        int matchCount = 0;
        int minLength = Math.min(header1.length, header2.length);
        
        for (int i = 0; i < minLength; i++) {
            String h1 = header1[i] != null ? header1[i].trim() : "";
            String h2 = header2[i] != null ? header2[i].trim() : "";
            
            // 移除可能的续表标记后比较
            h1 = removeTableContinuationMarks(h1);
            h2 = removeTableContinuationMarks(h2);
            
            if (h1.equals(h2) || (h1.length() > 0 && h2.length() > 0 && 
                (h1.contains(h2) || h2.contains(h1)))) {
                matchCount++;
            }
        }

        // 如果有超过60%的表头列相似，认为是同一个表格
        return (double) matchCount / minLength >= 0.6;
    }

    /**
     * 移除文本中的续表标记
     */
    private static String removeTableContinuationMarks(String text) {
        if (text == null) return "";
        return text.replaceAll("\\s*[\\(（]续[表]?[）\\)]\\s*", "")
                   .replaceAll("\\s*续\\s*", "")
                   .replaceAll("\\s*continued\\s*", "")
                   .trim();
    }

    /**
     * 检查两个表格的列结构相似度
     */
    private static boolean hasSimilarColumnStructure(PdfTable table1, PdfTable table2) {
        int cols1 = table1.getColumnCount();
        int cols2 = table2.getColumnCount();
        int minCols = Math.min(cols1, cols2);
        
        // 比较每列的内容模式
        int matchCount = 0;
        for (int i = 0; i < minCols; i++) {
            if (hasMatchingColumnPattern(table1, table2, i)) {
                matchCount++;
            }
        }
        
        // 如果超过70%的列具有相似的内容模式，认为是相同结构
        return (double) matchCount / minCols >= 0.7;
    }

    /**
     * 检查两个表格的指定列是否具有相似的内容模式
     */
    private static boolean hasMatchingColumnPattern(PdfTable table1, PdfTable table2, int colIndex) {
        // 获取两个表格该列的所有非空单元格数量
        int nonEmptyCount1 = 0;
        int nonEmptyCount2 = 0;
        
        // 检查第一个表格的列
        for (int row = 0; row < table1.getRowCount(); row++) {
            String cell = table1.getText(row, colIndex);
            if (cell != null && !cell.trim().isEmpty()) {
                nonEmptyCount1++;
            }
        }
        
        // 检查第二个表格的列
        for (int row = 0; row < table2.getRowCount(); row++) {
            String cell = table2.getText(row, colIndex);
            if (cell != null && !cell.trim().isEmpty()) {
                nonEmptyCount2++;
            }
        }
        
        // 计算非空单元格比例的差异
        double ratio1 = (double) nonEmptyCount1 / table1.getRowCount();
        double ratio2 = (double) nonEmptyCount2 / table2.getRowCount();
        
        // 如果两列的非空单元格比例差异在30%以内，认为它们具有相似的模式
        return Math.abs(ratio1 - ratio2) <= 0.3;
    }

    /**
     * 检查行的连续性
     */
    private static boolean checkRowsContinuity(PdfTable table1, PdfTable table2) {
        // 获取第一个表格的最后一行
        String[] lastRow = getRowContent(table1, table1.getRowCount() - 1);
        // 获取第二个表格的第一行
        String[] firstRow = getRowContent(table2, 0);

        // 检查数字序列的连续性或内容连续性
        return checkNumberSequence(lastRow, firstRow) || 
               checkContentContinuity(lastRow, firstRow) ||
               checkTableStructure(table1, table2);
    }

    /**
     * 检查表格结构的相似性
     */
    private static boolean checkTableStructure(PdfTable table1, PdfTable table2) {
        // 检查第一行（表头）的内容是否相似
        String[] header1 = getRowContent(table1, 0);
        String[] header2 = getRowContent(table2, 0);
        
        // 如果两个表格的表头完全相同，很可能是跨页的同一个表格
        boolean headerMatch = Arrays.equals(header1, header2);
        if (headerMatch) {
            return true;
        }

        // 检查单元格的非空模式是否相似
        return checkCellPattern(table1, table2);
    }

    /**
     * 检查表格的单元格填充模式是否相似
     */
    private static boolean checkCellPattern(PdfTable table1, PdfTable table2) {
        int cols = table1.getColumnCount();
        
        // 获取最后一行和第一行的非空单元格位置
        boolean[] lastRowPattern = new boolean[cols];
        boolean[] firstRowPattern = new boolean[cols];
        
        String[] lastRow = getRowContent(table1, table1.getRowCount() - 1);
        String[] firstRow = getRowContent(table2, 0);
        
        for (int i = 0; i < cols; i++) {
            lastRowPattern[i] = StringUtils.isNotBlank(lastRow[i]);
            firstRowPattern[i] = StringUtils.isNotBlank(firstRow[i]);
        }
        
        // 检查模式的相似度
        int matchCount = 0;
        for (int i = 0; i < cols; i++) {
            if (lastRowPattern[i] == firstRowPattern[i]) {
                matchCount++;
            }
        }
        
        // 如果超过70%的列具有相同的填充模式，认为是相似的
        return (double) matchCount / cols >= 0.7;
    }

    /**
     * 获取表格指定行的内容
     */
    private static String[] getRowContent(PdfTable table, int rowIndex) {
        String[] content = new String[table.getColumnCount()];
        for (int i = 0; i < table.getColumnCount(); i++) {
            content[i] = table.getText(rowIndex, i);
        }
        return content;
    }

    /**
     * 检查数字序列的连续性
     */
    private static boolean checkNumberSequence(String[] row1, String[] row2) {
        // 尝试在两行中找到数字序列
        for (int i = 0; i < row1.length; i++) {
            Integer num1 = extractNumber(row1[i]);
            Integer num2 = extractNumber(row2[i]);
            if (num1 != null && num2 != null && num2 - num1 == 1) {
                return true;
            }
        }
        return false;
    }

    /**
     * 从字符串中提取数字
     */
    private static Integer extractNumber(String text) {
        if (text == null) return null;
        try {
            return Integer.parseInt(text.replaceAll("[^0-9]", ""));
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 检查内容的连续性
     */
    private static boolean checkContentContinuity(String[] row1, String[] row2) {
        if (row1 == null || row2 == null || row1.length == 0 || row2.length == 0) {
            return false;
        }

        for (int i = 0; i < row1.length; i++) {
            String text1 = row1[i];
            String text2 = row2[i];
            if (text1 != null && text2 != null) {
                // 检查常见的连续性标记
                if ((text1.endsWith("\"") && !text2.startsWith("\"")) ||
                    (text1.endsWith("、") && Character.isDigit(text2.charAt(0))) ||
                    (text1.endsWith("：") && !text2.contains("：")) ||
                    (text1.endsWith(",") && !text2.contains(",")) ||
                    (text1.endsWith("；") && !text2.startsWith("；")) ||
                    (text1.endsWith("。") && Character.isUpperCase(text2.charAt(0)))) {
                    return true;
                }
                
                // 检查数字序列
                if (text1.matches(".*\\d+.*") && text2.matches(".*\\d+.*")) {
                    Integer num1 = extractNumber(text1);
                    Integer num2 = extractNumber(text2);
                    if (num1 != null && num2 != null && num2 - num1 == 1) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * 合并两个表格
     */
    private static String mergeTablesIntoHtml(PdfTable table1, PdfTable table2) {
        StringBuilder html = new StringBuilder();
        html.append("<table class='pdf-table'>");
        
        // 处理第一个表格（包含合并单元格）
        String table1Html = convertTableToHtml(table1);
        // 移除表格标签，只保留内容
        table1Html = table1Html.replaceAll("<table[^>]*>|</table>", "");
        html.append(table1Html);
        
        // 处理第二个表格（跳过表头，包含合并单元格）
        // 提取第二个表格的行（跳过表头）
        for (int row = 1; row < table2.getRowCount(); row++) {
            html.append("<tr>");
            boolean hasContent = false;
            for (int col = 0; col < table2.getColumnCount(); col++) {
                String cellText = table2.getText(row, col);
                if (cellText != null && !cellText.trim().isEmpty()) {
                    hasContent = true;
                }
            }
            // 只添加非空行
            if (hasContent) {
                for (int col = 0; col < table2.getColumnCount(); col++) {
                    String cellText = table2.getText(row, col);
                    html.append("<td>");
                    html.append(cellText != null ? cellText.trim() : "");
                    html.append("</td>");
                }
            }
            html.append("</tr>");
        }
        
        html.append("</table>");
        return html.toString();
    }

    /**
     * 修改后的splitByPage方法，支持表格和文本混合格式输出
     */
    public static List<Map<String, String>> splitByPage(PDDocument document, int startPage, int endPage) throws Exception {
        List<Map<String, String>> chunks = new ArrayList<>();
        
        // 设置字体路径
        String fontPath = "aip-designcanvas/src/main/java/com/icarus/utils/simsun.ttc";
        try {
            Document.setGlobalCustomFontsFolders(fontPath);
            log.info("字体设置成功: {}", fontPath);
        } catch (Exception e) {
            log.error("字体设置失败: {}", fontPath, e);
        }

        PdfDocument spireDoc = new PdfDocument();
        spireDoc.loadFromBytes(documentToBytes(document));
        
        int start = Math.max(1, startPage);
        int end = Math.min(document.getNumberOfPages(), endPage);
        
        // 提取文本内容
        PDFTextStripper stripper = new PDFTextStripper();
        log.info("提取文本内容成功");
        
        // 处理每一页
        for (int page = start; page <= end; page++) {
            Map<String, String> chunk = new HashMap<>();
            
            // 获取当前页的表格
            PdfTableExtractor extractor = new PdfTableExtractor(spireDoc);
            PdfTable[] tables = extractor.extractTable(page - 1);
            List<PdfTable> pageTables = new ArrayList<>();
            if (tables != null && tables.length > 0) {
                pageTables.addAll(Arrays.asList(tables));
            }
            
            // 获取当前页的文本内容
            stripper.setStartPage(page);
            stripper.setEndPage(page);
            String pageContent = stripper.getText(document);
            
            // 将文本内容转换为Markdown格式
            String[] lines = pageContent.split("\n");
            StringBuilder processedContent = new StringBuilder();
            int currentLine = 0;
            
            while (currentLine < lines.length) {
                String line = lines[currentLine].trim();
                
                // 跳过空行
                if (line.isEmpty()) {
                    processedContent.append("\n");
                    currentLine++;
                    continue;
                }
                
                // 检查是否是表格的位置
                if (!pageTables.isEmpty()) {
                    PdfTable matchingTable = null;
                    int bestPosition = -1;
                    
                    // 向前查找最近的表格位置（最多查找3行）
                    for (int lookBack = 0; lookBack < 3 && currentLine - lookBack >= 0; lookBack++) {
                        String checkLine = lines[currentLine - lookBack].trim();
                        for (PdfTable table : pageTables) {
                            if (isTablePosition(checkLine, table)) {
                                matchingTable = table;
                                bestPosition = currentLine - lookBack;
                                break;
                            }
                        }
                        if (matchingTable != null) break;
                    }
                    
                    if (matchingTable != null) {
                        // 在找到的位置插入表格
                        // 如果不是当前行，需要先处理中间的文本
                        while (currentLine < bestPosition) {
                            String intermediateLine = lines[currentLine].trim();
                            if (!intermediateLine.isEmpty()) {
                                processedContent.append(convertToMarkdown(intermediateLine)).append("\n");
                            }
                            currentLine++;
                        }
                        
                        // 插入表格
                        processedContent.append("\n");
                        processedContent.append(convertTableToHtml(matchingTable)).append("\n\n");
                        pageTables.remove(matchingTable);
                        currentLine = bestPosition + 1; // 跳过表格标题行
                        continue;
                    }
                }
                
                // 处理普通文本为Markdown格式
                String markdownLine = convertToMarkdown(line);
                processedContent.append(markdownLine).append("\n");
                currentLine++;
            }
            
            // 处理剩余的表格（没有找到明确插入位置的表格）
            if (!pageTables.isEmpty()) {
                for (PdfTable table : pageTables) {
                    processedContent.append("\n").append(convertTableToHtml(table)).append("\n\n");
                }
            }
            
            chunk.put("content", processedContent.toString().trim());
            chunk.put("pageRange", String.valueOf(page));
            if (!pageTables.isEmpty()) {
                chunk.put("hasTable", "true");
            }
            
            chunks.add(chunk);
        }
        
        spireDoc.close();
        return chunks;
    }

    /**
     * 将文本行转换为Markdown格式
     */
    private static String convertToMarkdown(String line) {
        // 标题处理
        if (line.matches("^第[一二三四五六七八九十]+[章节].*")) {
            return "# " + line;
        }
        if (line.matches("^\\d+[.、]\\s+.*")) {
            return "## " + line;
        }
        
        // 列表项处理
        if (line.matches("^[•·○●]\\s+.*")) {
            return "* " + line.substring(1).trim();
        }
        if (line.matches("^\\d+[.、]\\s+.*")) {
            return "1. " + line.replaceFirst("^\\d+[.、]\\s+", "");
        }
        
        // 引用处理
        if (line.startsWith("注：") || line.startsWith("注意：")) {
            return "> " + line;
        }
        
        // 强调处理
        if (line.matches(".*[重要注意提示警告].*")) {
            return "**" + line + "**";
        }
        
        // 普通段落
        return line;
    }

    /**
     * 判断当前行是否是表格的插入位置
     */
    private static boolean isTablePosition(String line, PdfTable table) {
        if (line == null || table == null || table.getRowCount() == 0) {
            return false;
        }

        // 获取表格的前两行内容作为特征
        StringBuilder tableFirstTwoRows = new StringBuilder();
        for (int row = 0; row < Math.min(2, table.getRowCount()); row++) {
            for (int col = 0; col < table.getColumnCount(); col++) {
                String cellText = table.getText(row, col);
                if (cellText != null && !cellText.trim().isEmpty()) {
                    tableFirstTwoRows.append(cellText.trim()).append(" ");
                }
            }
        }
        String tableContent = tableFirstTwoRows.toString().trim();

        // 1. 检查是否是明确的表格标题行
        if (line.matches(".*表\\s*\\d+.*") || 
            line.matches(".*[表清单列表]\\s*[:：].*") ||
            line.matches(".*如下表所示.*") ||
            line.matches(".*下表[为是].*") ||
            line.matches(".*见下表.*") ||
            line.matches(".*详见表.*")) {
            
            // 如果是表格标题行，检查下一行是否包含表格内容
            return true;
        }

        // 2. 检查当前行是否与表格内容匹配
        String cleanLine = line.replaceAll("[（(]续[)）]|续表", "").trim();
        String cleanTableContent = tableContent.replaceAll("[（(]续[)）]|续表", "").trim();
        
        // 2.1 直接匹配（考虑部分包含关系）
        if (cleanTableContent.contains(cleanLine) || cleanLine.contains(cleanTableContent)) {
            return true;
        }

        // 2.2 关键词匹配（分词后进行匹配）
        String[] tableWords = cleanTableContent.split("\\s+");
        String[] lineWords = cleanLine.split("\\s+");
        
        int matchCount = 0;
        for (String tableWord : tableWords) {
            if (tableWord.length() > 1) {  // 忽略单字词
                for (String lineWord : lineWords) {
                    if (lineWord.length() > 1 && 
                        (lineWord.contains(tableWord) || tableWord.contains(lineWord))) {
                        matchCount++;
                        break;
                    }
                }
            }
        }
        
        // 如果匹配度超过50%，认为是表格位置
        double matchRatio = (double) matchCount / Math.max(1, tableWords.length);
        if (matchRatio >= 0.5) {
            return true;
        }

        // 3. 检查是否是续表标记
        String lowerLine = line.toLowerCase();
        if (lowerLine.contains("续表") || 
            lowerLine.contains("（续）") || 
            lowerLine.contains("(续)") || 
            lowerLine.contains("continued")) {
            return true;
        }

        // 4. 检查特殊格式（如表格编号）
        if (line.matches(".*[表]\\s*\\d+[-－]+\\d+.*") || // 表1-1 格式
            line.matches(".*[表]\\s*\\d+\\.\\d+.*")) {    // 表1.1 格式
            return true;
        }

        return false;
    }

    /**
     * 按字体大小拆分
     */
    public static List<Map<String, String>> splitByFontSize(PDDocument document, float fontSizeThreshold) throws Exception {
        List<Map<String, String>> chunks = new ArrayList<>();
        CustomPDFTextStripper stripper = new CustomPDFTextStripper(fontSizeThreshold);
        
        PdfDocument spireDoc = new PdfDocument();
        spireDoc.loadFromBytes(documentToBytes(document));
        PdfTableExtractor extractor = new PdfTableExtractor(spireDoc);
        
        for (int page = 1; page <= document.getNumberOfPages(); page++) {
            stripper.setStartPage(page);
            stripper.setEndPage(page);
            String content = stripper.getText(document);
            
            if (StringUtils.isNotBlank(content)) {
                Map<String, String> chunk = new HashMap<>();
                chunk.put("content", content.trim());
                chunk.put("index", String.valueOf(page));
                
                // 提取表格
                PdfTable[] tables = extractor.extractTable(page - 1);
                if (tables != null && tables.length > 0) {
                    StringBuilder tableHtml = new StringBuilder();
                    for (PdfTable table : tables) {
                        tableHtml.append(convertTableToHtml(table));
                    }
                    chunk.put("tables", tableHtml.toString());
                    chunk.put("hasTable", "true");
                }
                
                chunks.add(chunk);
            }
        }
        
        spireDoc.close();
        return chunks;
    }

    /**
     * 按章节拆分
     */
    public static List<Map<String, String>> splitByChapter(PDDocument document, String chapterPattern, 
                                                          int startChapter, int endChapter) throws Exception {
        List<Map<String, String>> chunks = new ArrayList<>();
        
        PdfDocument spireDoc = new PdfDocument();
        spireDoc.loadFromBytes(documentToBytes(document));
        
        try {
            // 提取完整文本
            PDFTextStripper stripper = new PDFTextStripper();
            String fullText = stripper.getText(document);
            
            // 使用正则表达式匹配章节
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(chapterPattern);
            java.util.regex.Matcher matcher = pattern.matcher(fullText);
            
            List<Integer> chapterStarts = new ArrayList<>();
            while (matcher.find()) {
                chapterStarts.add(matcher.start());
            }
            
            // 处理章节范围
            int start = Math.max(0, startChapter - 1);
            int end = Math.min(chapterStarts.size(), endChapter);
            
            for (int i = start; i < end; i++) {
                int chapterStart = chapterStarts.get(i);
                int chapterEnd = (i < chapterStarts.size() - 1) ? chapterStarts.get(i + 1) : fullText.length();
                
                // 获取章节对应的页码
                int startPage = getPageForPosition(document, chapterStart);
                int endPage = getPageForPosition(document, chapterEnd);
                
                Map<String, String> chunk = new HashMap<>();
                
                // 提取表格并按页码分组
                Map<Integer, List<PdfTable>> tablesByPage = extractTablesGroupedByPage(spireDoc, startPage, endPage);
                
                // 处理表格合并
                List<PdfTable> allTables = processMergeTables(tablesByPage, startPage, endPage, chunk);
                
                // 获取章节内容
                String chapterContent = fullText.substring(chapterStart, chapterEnd);
                
                // 处理文本内容，插入表格
                Map<String, Object> processedResult = processContentWithTables(chapterContent, allTables);
                String processedContent = (String) processedResult.get("content");
                boolean tableInserted = (Boolean) processedResult.get("tableInserted");
                
                    // 设置块内容
                chunk.put("content", processedContent);
                chunk.put("pageRange", startPage + "-" + endPage);
                chunk.put("chapterIndex", String.valueOf(i + 1));
                chunk.put("index", String.valueOf(i + 1));
                if (tableInserted) {
                    chunk.put("hasTable", "true");
                }
                
                chunks.add(chunk);
            }
        } finally {
            spireDoc.close();
        }
        
        return chunks;
    }

    /**
     * 将表格转换为HTML格式，支持合并单元格
     */
    private static String convertTableToHtml(PdfTable table) {
        StringBuilder html = new StringBuilder();
        html.append("<table class='pdf-table' border='1' cellspacing='0' cellpadding='3'>");
        
        // 创建一个二维数组来跟踪单元格的状态
        boolean[][] processedCells = new boolean[table.getRowCount()][table.getColumnCount()];
        
        // 遍历所有行
        for (int row = 0; row < table.getRowCount(); row++) {
            // 检查这一行是否有内容
            boolean hasContent = false;
            for (int col = 0; col < table.getColumnCount(); col++) {
                String cellText = table.getText(row, col);
                if (cellText != null && !cellText.trim().isEmpty()) {
                    hasContent = true;
                    break;
                }
            }
            
            if (!hasContent) continue; // 跳过空行
            
            html.append("<tr>");
            
            // 处理这一行的所有列
            for (int col = 0; col < table.getColumnCount(); col++) {
                // 如果单元格已经被处理过（作为合并单元格的一部分），则跳过
                if (processedCells[row][col]) continue;
                
                String cellText = table.getText(row, col);
                if (cellText == null) cellText = "";
                cellText = cellText.trim();
                
                // 检查合并单元格
                int rowSpan = 1;
                int colSpan = 1;
                
                // 检查向右合并
                for (int c = col + 1; c < table.getColumnCount(); c++) {
                    String nextText = table.getText(row, c);
                    if (nextText == null || nextText.trim().isEmpty()) {
                        colSpan++;
                        processedCells[row][c] = true;
                    } else {
                        break;
                    }
                }
                
                // 检查向下合并
                boolean canMergeDown = true;
                for (int r = row + 1; r < table.getRowCount() && canMergeDown; r++) {
                    String currentCell = table.getText(r, col);
                    if (currentCell == null || currentCell.trim().isEmpty()) {
                        boolean allEmpty = true;
                        // 检查合并列范围内的所有单元格是否都为空
                        for (int c = col; c < col + colSpan && allEmpty; c++) {
                            String cell = table.getText(r, c);
                            if (cell != null && !cell.trim().isEmpty()) {
                                allEmpty = false;
                            }
                        }
                        if (allEmpty) {
                            rowSpan++;
                            // 标记这些单元格为已处理
                            for (int c = col; c < col + colSpan; c++) {
                                processedCells[r][c] = true;
                            }
                        } else {
                            canMergeDown = false;
                        }
                    } else {
                        canMergeDown = false;
                    }
                }
                
                // 生成单元格HTML
                String tag = (row == 0) ? "th" : "td";
                html.append("<").append(tag);
                
                // 添加合并属性
                if (rowSpan > 1) html.append(" rowspan='").append(rowSpan).append("'");
                if (colSpan > 1) html.append(" colspan='").append(colSpan).append("'");
                
                
                html.append(">");
                html.append(cellText);
                html.append("</").append(tag).append(">");
            }
            
            html.append("</tr>");
        }
        
        html.append("</table>");
        return html.toString();
    }

    /**
     * 判断两个单元格是否为同一个合并单元格的一部分
     */
    private static boolean isSameMergedCell(String text1, String text2) {
        if (text1 == null) text1 = "";
        if (text2 == null) text2 = "";
        
        // 完全相同的文本
        if (text1.equals(text2)) return true;
        
        // 一个为空而另一个不为空，可能是合并单元格的一部分
        if (text1.trim().isEmpty() || text2.trim().isEmpty()) return true;
        
        // 检查是否为跨页合并单元格的特殊情况
        // 例如："项目 (续)" 或 "项目(续表)"这样的情况
        String pattern1 = text1.replaceAll("\\s*[\\(（]续[表]?[）\\)]\\s*", "").trim();
        String pattern2 = text2.replaceAll("\\s*[\\(（]续[表]?[）\\)]\\s*", "").trim();
        
        return pattern1.equals(pattern2);
    }

    /**
     * 清理文本内容
     */
    private static String cleanContent(String content) {
        return content.trim()
            .replaceAll("(?m)^\\s+", "")  // 移除行首空白
            .replaceAll("(?m)\\s+$", "")  // 移除行尾空白
            .replaceAll("\\n{3,}", "\n\n");  // 将多个空行减少为最多两个
    }

    /**
     * 获取文本位置对应的页码
     */
    private static int getPageForPosition(PDDocument document, int position) throws IOException {
        PDFTextStripper stripper = new PDFTextStripper();
        int currentPosition = 0;
        
        for (int page = 1; page <= document.getNumberOfPages(); page++) {
            stripper.setStartPage(page);
            stripper.setEndPage(page);
            String pageText = stripper.getText(document);
            currentPosition += pageText.length();
            
            if (currentPosition > position) {
                return page;
            }
        }
        
        return document.getNumberOfPages();
    }

    /**
     * 将PDDocument转换为字节数组
     */
    private static byte[] documentToBytes(PDDocument document) throws IOException {
        java.io.ByteArrayOutputStream baos = new java.io.ByteArrayOutputStream();
        document.save(baos);
        return baos.toByteArray();
    }

    /**
     * 抽取表格并按页码分组
     * 
     * @param spireDoc PDF文档
     * @param startPage 开始页码
     * @param endPage 结束页码
     * @return 按页码分组的表格Map
     */
    private static Map<Integer, List<PdfTable>> extractTablesGroupedByPage(PdfDocument spireDoc, int startPage, int endPage) {
        Map<Integer, List<PdfTable>> tablesByPage = new HashMap<>();
        PdfTableExtractor extractor = new PdfTableExtractor(spireDoc);
        
        for (int page = startPage; page <= endPage; page++) {
            PdfTable[] tables = extractor.extractTable(page - 1);
            if (tables != null && tables.length > 0) {
                tablesByPage.put(page, new ArrayList<>(Arrays.asList(tables)));
            }
        }
        
        return tablesByPage;
    }

    /**
     * 处理表格合并
     * 
     * @param tablesByPage 按页码分组的表格Map
     * @param startPage 开始页码
     * @param endPage 结束页码
     * @param chunk 存储合并结果的Map
     * @return 处理后的表格列表
     */
    private static List<PdfTable> processMergeTables(Map<Integer, List<PdfTable>> tablesByPage, 
                                               int startPage, int endPage, Map<String, String> chunk) {
        List<PdfTable> allTables = new ArrayList<>();
        
        // 检查是否存在表格
        boolean hasTables = false;
        for (List<PdfTable> tables : tablesByPage.values()) {
            if (tables != null && !tables.isEmpty()) {
                hasTables = true;
                break;
            }
        }
        
        // 如果没有表格，直接返回空列表
        if (!hasTables) {
            return allTables;
        }
        
        // 收集所有表格
        for (List<PdfTable> tables : tablesByPage.values()) {
            if (tables != null) {
                allTables.addAll(tables);
            }
        }
        
        // 如果只处理一页的表格，直接返回所有表格（不需要合并）
        if (startPage == endPage) {
            return allTables;
        }
        
        // 处理表格合并
        for (int page = startPage; page < endPage; page++) {
            List<PdfTable> pageTables = tablesByPage.get(page);
            if (pageTables == null || pageTables.isEmpty()) {
                continue;
            }
            
            List<PdfTable> nextPageTables = tablesByPage.get(page + 1);
            if (nextPageTables == null || nextPageTables.isEmpty()) {
                continue;
            }
            
            // 特别处理页面头部的表格
            if (page > startPage) {
                for (PdfTable nextTable : nextPageTables) {
                    if (isTableAtPageTop(nextTable)) {
                        // 检查与上一页的最后一个表格是否应该合并
                        for (PdfTable prevTable : pageTables) {
                            if (prevTable != null && shouldMergeTables(prevTable, nextTable)) {
                                String mergedHtml = mergeTablesIntoHtml(prevTable, nextTable);
                                chunk.put("mergedTable_" + page + "_top", mergedHtml);
                                // 标记这些表格为已处理
                                pageTables.set(pageTables.indexOf(prevTable), null);
                                nextPageTables.set(nextPageTables.indexOf(nextTable), null);
                                break;
                            }
                        }
                    }
                }
            }
            
            // 尝试合并表格
            for (int i = 0; i < pageTables.size(); i++) {
                if (pageTables.get(i) == null) continue;
                
                boolean merged = false;
                for (int j = 0; j < nextPageTables.size(); j++) {
                    if (nextPageTables.get(j) == null) continue;
                    
                    // 检查是否应该合并表格
                    if (shouldMergeTables(pageTables.get(i), nextPageTables.get(j))) {
                        // 合并表格并更新当前页的表格
                        String mergedHtml = mergeTablesIntoHtml(pageTables.get(i), nextPageTables.get(j));
                        // 标记这些表格为已处理
                        pageTables.set(i, null);
                        nextPageTables.set(j, null);
                        // 存储合并后的HTML
                        chunk.put("mergedTable_" + page + "_" + i, mergedHtml);
                        merged = true;
                        break;
                    }
                }
            }
        }
        
        // 过滤出非null表格
        List<PdfTable> filteredTables = new ArrayList<>();
        for (PdfTable table : allTables) {
            if (table != null) {
                filteredTables.add(table);
            }
        }
        
        return filteredTables;
    }

    /**
     * 判断表格是否位于页面顶部
     */
    private static boolean isTableAtPageTop(PdfTable table) {
        if (table == null || table.getRowCount() == 0) {
            return false;
        }

        // 获取表格第一行的内容
        String firstRowContent = "";
        for (int col = 0; col < table.getColumnCount(); col++) {
            String cellText = table.getText(0, col);
            if (cellText != null) {
                firstRowContent += cellText.trim() + " ";
            }
        }
        firstRowContent = firstRowContent.trim().toLowerCase();

        // 检查是否包含续表相关的关键词，这通常表示表格在页面顶部
        return firstRowContent.contains("续") || 
               firstRowContent.contains("（续）") ||
               firstRowContent.contains("(续)") ||
               firstRowContent.contains("续表") ||
               firstRowContent.contains("continued") ||
               firstRowContent.contains("(continued)");
    }

    /**
     * 处理文本内容，插入表格
     * 
     * @param content 原始文本内容
     * @param allTables 所有表格
     * @return 处理后的内容和是否插入表格的标志
     */
    private static Map<String, Object> processContentWithTables(String content, List<PdfTable> allTables) {
        StringBuilder processedContent = new StringBuilder();
        String[] lines = content.split("\n");
        boolean tableInserted = false;
        List<PdfTable> remainingTables = new ArrayList<>(allTables);
        
        // 遍历每一行文本
        for (int i = 0; i < lines.length; i++) {
            String line = lines[i];
            processedContent.append(line).append("\n");
            
            // 检查是否需要在此处插入表格
            if (!remainingTables.isEmpty()) {
                Iterator<PdfTable> iterator = remainingTables.iterator();
                while (iterator.hasNext()) {
                    PdfTable table = iterator.next();
                    if (table != null && isTablePosition(line, table)) {
                        // 如果找到表格位置，插入表格
                        String tableHtml = convertTableToHtml(table);
                        processedContent.append(tableHtml).append("\n\n");
                        iterator.remove();
                        tableInserted = true;
                    }
                }
            }
            
            // 特殊处理：如果当前行是空行，并且前后文本有明显的段落特征，考虑在此处插入表格
            if (!remainingTables.isEmpty() && line.trim().isEmpty() && i > 0 && i < lines.length - 1) {
                String prevLine = lines[i - 1].trim();
                String nextLine = lines[i + 1].trim();
                
                // 检查前后文本是否有段落结束和开始的特征
                if (isParagraphEnd(prevLine) && isParagraphStart(nextLine)) {
                    Iterator<PdfTable> iterator = remainingTables.iterator();
                    while (iterator.hasNext()) {
                        PdfTable table = iterator.next();
                        if (table != null) {
                            String tableHtml = convertTableToHtml(table);
                            processedContent.append(tableHtml).append("\n\n");
                            iterator.remove();
                            tableInserted = true;
                            break; // 每个段落间隔只插入一个表格
                        }
                    }
                }
            }
        }
        
        // 如果还有未插入的表格，检查是否应该放在文档末尾
        if (!remainingTables.isEmpty()) {
            String lastLine = lines[lines.length - 1].trim();
            if (isParagraphEnd(lastLine)) {
                processedContent.append("\n");
                for (PdfTable table : remainingTables) {
                    if (table != null) {
                        String tableHtml = convertTableToHtml(table);
                        processedContent.append(tableHtml).append("\n\n");
                        tableInserted = true;
                    }
                }
            }
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("content", processedContent.toString().trim());
        result.put("tableInserted", tableInserted);
        
        return result;
    }

    /**
     * 判断是否是段落结束
     */
    private static boolean isParagraphEnd(String line) {
        if (line.isEmpty()) return false;
        // 检查段落结束的特征
        return line.endsWith("。") || line.endsWith("；") || line.endsWith("：") || 
               line.endsWith(".") || line.endsWith(":") || line.endsWith(";") ||
               line.matches(".*[0-9]+.*"); // 包含数字的行可能是表格的引用
    }

    /**
     * 判断是否是段落开始
     */
    private static boolean isParagraphStart(String line) {
        if (line.isEmpty()) return false;
        // 检查段落开始的特征
        return Character.isUpperCase(line.charAt(0)) || // 大写字母开头
               Character.isDigit(line.charAt(0)) || // 数字开头
               line.startsWith("第") || // 章节标记
               line.startsWith("（") || // 括号开头
               line.startsWith("(") ||
               line.matches("^[一二三四五六七八九十]+、.*"); // 中文数字开头
    }

    /**
     * 自定义PDFTextStripper用于处理字体大小
     */
    private static class CustomPDFTextStripper extends PDFTextStripper {
        private final float fontSizeThreshold;

        public CustomPDFTextStripper(float fontSizeThreshold) throws IOException {
            this.fontSizeThreshold = fontSizeThreshold;
        }

        @Override
        protected void processTextPosition(TextPosition text) {
            if (text.getFontSizeInPt() >= fontSizeThreshold) {
                super.processTextPosition(text);
            }
        }
    }

    /**
     * 获取PDF文件的总页数
     * @param pdfPath PDF文件路径
     * @return PDF文件的总页数
     * @throws IOException 如果文件读取失败
     */
    public static int getPdfPageCount(String pdfPath) throws IOException {
        try (PDDocument document = Loader.loadPDF(new File(pdfPath))) {
            return document.getNumberOfPages();
        }
    }

    /**
     * 提取基金账户余额表
     * @param pdfPath PDF文件路径
     * @param pageThreshold 处理的页数阈值
     * @return 提取的表格内容，如果没有找到则返回多页内容
     */
    public static Map<String, Object> extractBalanceTable(String pdfPath, int pageThreshold) throws Exception {
        Map<String, Object> result = new HashMap<>();
        result.put("found", false); // 默认为未找到
        
        PdfDocument spireDoc = null;
        
        try (PDDocument document = Loader.loadPDF(new File(pdfPath))) {
            spireDoc = new PdfDocument();
            spireDoc.loadFromBytes(documentToBytes(document));
            
            // 正则表达式匹配基金账户余额表相关标题
            String[] patterns = {
                ".*基金账户余额表.*",
                ".*基金账户资产表.*",
                ".*基金资产余额表.*",
                ".*账户资产余额表.*",
                ".*余额.*",
                ".*账户资产情况.*",
                ".*账户余额.*",
                ".*持仓明细.*",
                ".*持仓情况.*",
                ".*持仓变动.*",
                ".*持仓变动情况.*",
                ".*持仓变动明细.*",
                ".*持仓变动明细表.*",
                ".*账户资产.*",
                ".*份额.*",
                ".*份额对账单.*"
            };
            
            PDFTextStripper stripper = new PDFTextStripper();
            boolean patternFound = false;
            
            // 仅扫描第一页查找匹配的模式
            stripper.setStartPage(1);
            stripper.setEndPage(1);
            String pageText = stripper.getText(document);
            String[] lines = pageText.split("\n");
            
            // 检查文本中是否包含相关标题
            for (String line : lines) {
                for (String pattern : patterns) {
                    if (line.trim().matches(pattern)) {
                        patternFound = true;
                        result.put("title", line.trim());
                        break;
                    }
                }
                if (patternFound) break;
            }
            
            if (patternFound) {
                // 如果找到匹配的模式，处理第一页内容并提取表格
                log.info("找到匹配的余额表模式，提取第一页内容");
                
                // 使用splitByPage获取第一页的完整内容
                List<Map<String, String>> firstPageContent = splitByPage(document, 1, 1);
                if (!firstPageContent.isEmpty()) {
                    // 第一页的内容
                    String content = firstPageContent.get(0).get("content");
                    result.put("pageContent", content);
                    
                    // 查找第一个表格标签
                    int tableStart = content.indexOf("<table");
                    int tableEnd = -1;
                    
                    if (tableStart != -1) {
                        tableEnd = content.indexOf("</table>", tableStart) + 8; // 8是</table>的长度
                        
                        if (tableEnd != -1) {
                            // 提取第一个表格
                            String firstTable = content.substring(tableStart, tableEnd);
                            
                            // 提取表格前的内容
                            String aboveContent = content.substring(0, tableStart).trim();
                            
                            result.put("firstTable", firstTable);
                            result.put("aboveContent", aboveContent);
                            
                            // 构建完整内容字符串
                            StringBuilder completeContent = new StringBuilder();
                            if (!aboveContent.isEmpty()) {
                                completeContent.append(aboveContent).append("\n\n");
                            }
                            completeContent.append(firstTable);
                            
                            result.put("content", completeContent.toString());
                            result.put("found", true);
                            result.put("startPage", 1);
                            result.put("pageCount", 1);
                        }
                    }
                    
                    // 即使没有找到表格，也返回页面内容
                    if (tableStart == -1 || tableEnd == -1) {
                        result.put("content", content);
                        result.put("found", true);
                        result.put("startPage", 1);
                        result.put("pageCount", 1);
                    }
                }
            } else {
                // 如果没有找到匹配的模式，返回前pageThreshold页的内容
                log.info("未找到匹配的余额表模式，返回前{}页内容", pageThreshold);
                
                // 使用splitByPage获取内容
                List<Map<String, String>> multiPageContent = splitByPage(document, 1, 
                        Math.min(pageThreshold, document.getNumberOfPages()));
                
                result.put("pdfContent", multiPageContent);
                result.put("pageCount", Math.min(pageThreshold, document.getNumberOfPages()));
                result.put("fullContent", true); // 标记这是多页完整内容
            }
        } finally {
            // 确保spireDoc被关闭
            if (spireDoc != null) {
                spireDoc.close();
            }
        }
        
        return result;
    }

}
