package com.icarus.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.icarus.auth.license.LicenseUtil;
import com.icarus.common.cotnode.ab.ex.ExFormatNode;
import com.icarus.common.minio.MinioUtil;
import com.icarus.config.UrlConfigHolder;
import com.icarus.entity.SceneElement;
import com.icarus.sdk.client.api.completion.chat.ChatMessage;
import com.icarus.sdk.client.utils.LLMClientUtil;
import com.icarus.sdk.prompt.PromptTemplate;
import com.icarus.sdk.springboot.base.utils.SpringContextUtils;
import com.icarus.service.SpireService;
import com.spire.doc.Document;
import com.spire.pdf.PdfDocument;
import com.spire.pdf.PdfPageBase;
import com.spire.pdf.exporting.PdfImageInfo;
import com.spire.pdf.texts.PdfTextExtractOptions;
import com.spire.pdf.texts.PdfTextExtractor;
import com.spire.pdf.utilities.PdfImageHelper;
import com.spire.pdf.utilities.PdfTable;
import com.spire.pdf.utilities.PdfTableExtractor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
public class PdfUtil_SpirePDF implements SpireService {


	/**
	 * VLM模型名称，默认为VL_7B
	 */
	public final static String MODEL_NAME = "VL_7B";

	/**
	 * 最大token数，默认1024
	 */
	public final static Integer MAX_TOKEN = 1024;

	/**
	 * 温度参数，控制输出的随机性，默认0.5
	 */
	public final static Double TEMP = 0.5;

	/**
	 * TopP参数，控制输出的多样性，默认0.95
	 */
	public final static Double TOP = 0.95;


	/**
	 * 将pdf的startPage到endPage转换成EasyEX 格式的json数据
	 */
	public  List<Map<String, String>> convert2EasyEX(String pdfFilePath, int startPage, int endPage, ExFormatNode.Properties properties) throws IOException {
		//设置SpireDoc的许可证
		String licKey = LicenseUtil.getAvailableLicense().getContent().getJSONObject("LICs").getStr("SpireDoc").trim();
		com.spire.doc.license.LicenseProvider.setLicenseKey(licKey);
		com.spire.xls.license.LicenseProvider.setLicenseKey(licKey);
		com.spire.presentation.license.LicenseProvider.setLicenseKey(licKey);
		com.spire.pdf.license.LicenseProvider.setLicenseKey(licKey);
		com.spire.ocr.license.LicenseProvider.setLicenseKey(licKey);

		List<Map<String, String>> chunks = new ArrayList<>();

		// 设置字体路径
		String fontPath = "aip-designcanvas/src/main/java/com/icarus/utils/";
		try {
			Document.setGlobalCustomFontsFolders(fontPath);
			log.info("字体设置成功: {}", fontPath);
		} catch (Exception e) {
			log.error("字体设置失败: {}", fontPath, e);
		}
		PdfDocument spireDoc = new PdfDocument();
		spireDoc.loadFromFile(pdfFilePath);

		int start = Math.max(1, startPage);
		int end = Math.min(spireDoc.getPages().getCount(), endPage);
		HashMap<String, PdfPageBase> useVL = new HashMap<>();


		// 处理每一页
		for (int page_i = start; page_i <= end; page_i++) {
			Map<String, String> chunk = new HashMap<>();

			int iPageNum = page_i - 1;

			// 获取当前页的文本内容
			PdfPageBase page = spireDoc.getPages().get(iPageNum);
			PdfTextExtractor textExtractor = new PdfTextExtractor(page);
			PdfTextExtractOptions extractOptions = new PdfTextExtractOptions();
			String pageContent = textExtractor.extract(extractOptions);
			//防止误读水印
			//如果文面中有个一个大于500*500的图片则vl模型
			boolean isContent = true;
			for (PdfImageInfo pdfImageInfo : page.getImagesInfo()) {
				if (pdfImageInfo.getImage().getWidth() > 500 && pdfImageInfo.getImage().getHeight() > 500) {
					isContent = false;
					break;
				}
			}

//			pageContent = pageContent.replaceAll("Evaluation Warning : The document was created with Spire.PDF for java.", "");
			if (isContent) {
				// 获取当前页面的图片
				//todo 暂时先不处理 文中中包含图片的 图片内容
//				PdfImageInfo[] images = page.getImagesInfo();
//				for (PdfImageInfo iImage : images) {
//					iImage.getIndex();
//					BufferedImage bufferdImage = iImage.getImage();
//					String image_txt = tools_ocr_pic(bufferdImage);
//				}

				// 分割文本内容为行数组，用于定位表格的位置。定位表格时，把每行中的空格全去掉再比较，如果连续一致就是表格的位置，需要换掉
				String[] linesContent = Arrays.stream(pageContent.split("\n"))
						.map(String::trim)
						.toArray(String[]::new);
				String[] linesWithoutSpace = Arrays.stream(linesContent)
						.map(s -> s.replaceAll("\\s+", "")
						)
						.toArray(String[]::new);

				// 获取当前页的表格
				PdfTableExtractor extractor = new PdfTableExtractor(spireDoc);
				PdfTable[] tables = extractor.extractTable(iPageNum);

				if (tables == null || tables.length <= 0) {
					chunk.put("content", pageContent.trim());
					chunk.put("pageRange", String.valueOf(page_i));
				} else {
					// 文本中表格的行定位
					List<Map<String, Object>> replaceList = new ArrayList<>();
					for (int iTableNum = tables.length - 1; iTableNum >= 0; iTableNum--) {
						PdfTable itab = tables[iTableNum];
						// 将表格转换成list<string>，每一行的字段拼在一起形成（注意不要空格），形成一个列表，用于比较
						int icols = itab.getColumnCount();
						int irows = itab.getRowCount();
						List<String> tableRows = new ArrayList<>();
						for (int i = 0; i < irows; i++) {
							StringBuilder row = new StringBuilder();
							for (int j = 0; j < icols; j++) {
								String cell = itab.getText(i, j).trim();
								if (!cell.isEmpty()) row.append(cell);
							}
							if (row.length() > 0) {
								tableRows.add(row.toString().trim()
										.replaceAll(" ", "")
										.replaceAll(" ", "")
										.replaceAll("\n", "")
										.replaceAll("\r", "")
								);
							}
						}

						// 寻找表格在文本中的起始和结束行号（连续匹配），使用与表格接近长度的字符串来进行相似度匹配，会包含多行
						int iStart = -1, iStop = -1;
						int tableSize = tableRows.size();
						if (tableSize == 0) continue; // 跳过空表格
						for (int i = 0; i <= linesWithoutSpace.length - tableSize; i++) {
							boolean match = false;
							// 待比较的表格（将html标签去掉了）
							ArrayList<String> lstTable2Compare = new ArrayList<>(tableRows.stream()
									.map(s -> s.replaceAll("\\<.*?\\>", "")) // 使用正则表达式去除HTML标签
									.collect(Collectors.toList()));
							// 构建待比较的正文，是从i行开始取，直到字符数>=表格字符数
							int targetChars = lstTable2Compare.stream().mapToInt(String::length).sum();
							ArrayList<String> lstContentWithoutSpace = new ArrayList<>();
							int currentChars = 0;
							for (int k = i; k < linesWithoutSpace.length; k++) {
								String line = linesWithoutSpace[k];
								currentChars += line.length();
								lstContentWithoutSpace.add(line);
								if (currentChars >= targetChars) {
									break;
								}
							}
							// 比较正文i行开始的块，和表格，以0.9作为阈值，确定这块文字就是表格
							if (tools_calc_similarity(lstContentWithoutSpace, lstTable2Compare) > 0.80) {
								match = true;
								iStart = i;
								iStop = i + lstContentWithoutSpace.size() - 1;
								break;
							}
						}

						// 记录下来要替换的行号以及内容
						HashMap<String, Object> replaceMap = new HashMap<>();
						replaceMap.put("iStart", iStart);
						replaceMap.put("iStop", iStop);
						String htmlTable = convertTableToHtml(itab);
						replaceMap.put("replaceContent", htmlTable);
						replaceList.add(replaceMap);
					}

					// 根据iStart重新排列replaceList
					replaceList.sort(Comparator.comparingInt(map -> (int) map.get("iStart")));

					// 根据替换列表，重构pageContent
					StringBuilder newPageContent = new StringBuilder();
					int iFrom = 0;
					for (int i = 0; i < replaceList.size(); i++) {
						Map<String, Object> replaceMap = replaceList.get(i);
						int iStart = (int) replaceMap.get("iStart");
						int iStop = (int) replaceMap.get("iStop");
						String replaceContent = (String) replaceMap.get("replaceContent");

						if (iStart == -1 || iStop == -1 || iStart > iStop) continue;

						for (int j = iFrom; j < iStart; j++) {
							newPageContent.append(linesContent[j]).append("\n");
						}

						newPageContent.append(replaceContent).append("\n");
						iFrom = iStop + 1;
					}
					for (int i = iFrom; i < linesContent.length; i++) {
						newPageContent.append(linesContent[i]).append("\n");
					}

					chunk.put("content", newPageContent.toString().trim());
					chunk.put("pageRange", String.valueOf(page_i));
				}
				chunks.add(chunk);
			} else {
				useVL.put(String.valueOf(page_i),page);
				//线程池调用赋空值
			}
		}
		//开启线程池对userVL中的值进行处理
		if (!useVL.isEmpty()) {
			// 创建线程池
			ExecutorService executor = Executors.newFixedThreadPool(15);
			MinioUtil minioUtil = SpringContextUtils.getBean(MinioUtil.class);
			try {
				// 存储Future任务的集合
				Map<String, Future<String>> futures = new HashMap<>(useVL.size());

				// 提交所有任务
				for (Map.Entry<String,PdfPageBase> entry : useVL.entrySet()) {
					final String key = entry.getKey();
					final PdfPageBase page = entry.getValue();
					Callable<String> task = () -> {
						// 调用VL模型处理PDF页面
						return processPageWithVLModel(key,page, minioUtil,spireDoc);
					};
					futures.put(key, executor.submit(task));
				}

				// 收集结果
				Map<String, String> resultMap = new HashMap<>(useVL.size());

				for (Map.Entry<String, Future<String>> entry : futures.entrySet()) {
					String key = entry.getKey();
					try {
						// 获取处理结果，带超时控制
						String result = entry.getValue().get(200, TimeUnit.SECONDS);
						resultMap.put(key, result);
					} catch (Exception e) {
						// 记录失败的任务key
						log.error("处理键 " + key + " 时出错: " + e.getCause());
					}
				}
				resultMap.forEach((key, value) -> {
					HashMap<String, String> chunk = new HashMap<>();
					chunk.put("content",value);
					chunk.put("pageRange", key);
					chunks.add(chunk);
				});
				//对chunks按大小排序
				chunks.sort(Comparator.comparingInt(chunk -> Integer.parseInt(chunk.get("pageRange"))));
			} finally {
				// 关闭线程池
				executor.shutdownNow();
			}
		}

		spireDoc.close();
		return chunks;
	}

	private String processPageWithVLModel(String iPageNum,PdfPageBase page,MinioUtil minioUtil,PdfDocument spireDoc ) throws IOException {
		// 创建PdfImageHelper对象
		PdfImageHelper imageHelper = new PdfImageHelper();
		// 获取当前页面的所有图像信息
		com.spire.pdf.utilities.PdfImageInfo[] imageInfos = imageHelper.getImagesInfo(page);

		//获取提示词
		String pdfEasyEx = PromptTemplate.builder("prompts/EX/vl_pdf_easyEx.groovy", "pdf_easyEx").build();
		// 创建ChatMessage并添加提示词
		ChatMessage msg = new ChatMessage(pdfEasyEx);


		//长度大于一说明有多图层，不是标准的扫描件 先转图片处理
		if (imageInfos.length>1){
			BufferedImage bufferedImage = spireDoc.saveAsImage(Integer.parseInt(iPageNum)-1);
			buildVlParam(msg,bufferedImage,minioUtil);
		}else {
			// 遍历当前页面的所有图像信息
			for (com.spire.pdf.utilities.PdfImageInfo info : imageInfos) {
				// 获取当前图像
				BufferedImage image = info.getImage();
				buildVlParam(msg,image,minioUtil);

			}
		}
		// 调用LLMClientUtil进行分析
		String answer = LLMClientUtil.builder()
				.modelName(MODEL_NAME)
				.maxTokens(MAX_TOKEN)
				.temperature(TEMP)
				.topP(TOP)
				.chat(msg)
				.runChatCompletion();
		log.info("VLM模型分析完成，结果长度：{}", answer != null ? answer.length() : 0);
		//todo 删除minio文件 影响时间
		return dealWithVLMResult(answer);
	}

	public  void buildVlParam(ChatMessage msg,BufferedImage image,MinioUtil minioUtil) throws IOException {
		// 指定文件路径和文件名
		// 获取系统临时目录
		String tempDir = System.getProperty("java.io.tmpdir");
		File outputDir = new File(tempDir, "extracted_images");
		if (!outputDir.exists()) {
			outputDir.mkdirs(); // 创建目录
		}
		String fileName = IdUtil.randomUUID()+".png";
		File output = new File(outputDir,fileName);
		// 将图片保存为PNG文件
		ImageIO.write(image, "PNG", output);
		//转化成文件类型
		CustomMultipartFile customMultipartFile = new CustomMultipartFile(output);
		minioUtil.uploadFile(customMultipartFile, "temp-bucket", fileName);
		//删除文件
		output.delete();
		//拼接vl模型需要的url
		String url = UrlConfigHolder.getMyServerUrl() + "aip/vlm/view/" + fileName;
		msg.attachImageUrl(url);
		log.info("已添加图片URL: {}", url);
	}


	public static String  dealWithVLMResult(String answer) {
		 answer = answer.replaceAll("```html", "").replaceAll("```", "");
	if (answer == null || answer.isEmpty()) {
			return "";
		}
		StringBuilder result = new StringBuilder();
		Pattern pattern = Pattern.compile("\\\\|.*\\\\|\\\\s*\\\\n?", Pattern.MULTILINE);
		Matcher matcher = pattern.matcher(answer);

		int lastEnd = 0;

		while (matcher.find()) {
			// 添加非表格部分
			result.append(answer, lastEnd, matcher.start());
			lastEnd = matcher.end();

			// 获取表格内容
			String tableText = matcher.group();
			result.append(convertToHtmlTable(tableText));
		}

		// 添加剩余部分
		result.append(answer.substring(lastEnd));

		return result.toString();
	}

	private static String convertToHtmlTable(String input) {
		StringBuilder html = new StringBuilder();
		html.append("<table>\n");

		String[] lines = input.trim().split("\\r?\\n");
		boolean headerProcessed = false;

		for (String line : lines) {
			line = line.trim();
			if (line.isEmpty()) continue;

			// 跳过分隔行
			if (line.matches("^\\|[-\\s|]+$")) {
				headerProcessed = true;
				continue;
			}

			html.append("  <tr>\n");

			String[] cells = line.split("\\|");
			boolean isHeader = !headerProcessed;

			for (int i = 1; i < cells.length - 1; i++) {
				String cell = cells[i].trim();
				if (isHeader) {
					html.append("    <th>").append(cell).append("</th>\n");
				} else {
					html.append("    <td>").append(cell).append("</td>\n");
				}
			}

			html.append("  </tr>\n");
		}
		html.append("</table>\n");
		return html.toString();
	}
	/**
	 * 将文本行转换为Markdown格式
	 */
	private static String convertToMarkdown(String line) {
		// 标题处理
		if (line.matches("^第[一二三四五六七八九十]+[章节].*")) {
			return "# " + line;
		}
		if (line.matches("^\\d+[.、]\\s+.*")) {
			return "## " + line;
		}

		// 列表项处理
		if (line.matches("^[•·○●]\\s+.*")) {
			return "* " + line.substring(1).trim();
		}
		if (line.matches("^\\d+[.、]\\s+.*")) {
			return "1. " + line.replaceFirst("^\\d+[.、]\\s+", "");
		}

		// 引用处理
		if (line.startsWith("注：") || line.startsWith("注意：")) {
			return "> " + line;
		}

		// 强调处理
		if (line.matches(".*[重要注意提示警告].*")) {
			return "**" + line + "**";
		}

		// 普通段落
		return line;
	}

	/**
	 * 将表格转换为HTML格式，支持合并单元格
	 */
	private static String convertTableToHtml(PdfTable table) {
		StringBuilder html = new StringBuilder();
		html.append("<table>\n");

		// 创建一个二维数组来跟踪单元格的状态
		boolean[][] processedCells = new boolean[table.getRowCount()][table.getColumnCount()];

		// 遍历所有行
		for (int row = 0; row < table.getRowCount(); row++) {
			// 检查这一行是否有内容
			boolean hasContent = false;
			for (int col = 0; col < table.getColumnCount(); col++) {
				String cellText = table.getText(row, col);
				if (cellText != null && !cellText.trim().isEmpty()) {
					hasContent = true;
					break;
				}
			}

			if (!hasContent) continue; // 跳过空行

			html.append("<tr>");

			// 处理这一行的所有列
			for (int col = 0; col < table.getColumnCount(); col++) {
				// 如果单元格已经被处理过（作为合并单元格的一部分），则跳过
				if (processedCells[row][col]) continue;

				String cellText = table.getText(row, col);
				if (cellText == null) cellText = "";
				cellText = cellText.trim();

				// 检查合并单元格
				int rowSpan = 1;
				int colSpan = 1;

				// 检查向右合并
				for (int c = col + 1; c < table.getColumnCount(); c++) {
					String nextText = table.getText(row, c);
					if (nextText == null || nextText.trim().isEmpty()) {
						colSpan++;
						processedCells[row][c] = true;
					} else {
						break;
					}
				}

				// 检查向下合并
				boolean canMergeDown = true;
				for (int r = row + 1; r < table.getRowCount() && canMergeDown; r++) {
					String currentCell = table.getText(r, col);
					if (currentCell == null || currentCell.trim().isEmpty()) {
						boolean allEmpty = true;
						// 检查合并列范围内的所有单元格是否都为空
						for (int c = col; c < col + colSpan && allEmpty; c++) {
							String cell = table.getText(r, c);
							if (cell != null && !cell.trim().isEmpty()) {
								allEmpty = false;
							}
						}
						if (allEmpty) {
							rowSpan++;
							// 标记这些单元格为已处理
							for (int c = col; c < col + colSpan; c++) {
								processedCells[r][c] = true;
							}
						} else {
							canMergeDown = false;
						}
					} else {
						canMergeDown = false;
					}
				}

				// 生成单元格HTML
				String tag = (row == 0) ? "th" : "td";
				html.append("<").append(tag);

				// 添加合并属性
				if (rowSpan > 1) html.append(" rowspan='").append(rowSpan).append("'");
				if (colSpan > 1) html.append(" colspan='").append(colSpan).append("'");


				html.append(">");
				html.append(cellText);
				html.append("</").append(tag).append(">");
			}

			html.append("</tr>\n");
		}

		html.append("</table>");
		return html.toString();
	}

	/**
	 * 获取PDF文件的总页数
	 *
	 * @param pdfPath PDF文件路径
	 * @return PDF文件的总页数
	 * @throws IOException 如果文件读取失败
	 */
	public  int getPageCount(String pdfPath) throws IOException {
		PdfDocument spireDoc = new PdfDocument();
		spireDoc.loadFromFile(pdfPath);

		return spireDoc.getPages().getCount();
	}

	/**
	 * 用来辅助表格定位用的相似度
	 * 比较两个字符串的相似度，可能会有少字、多字的情况，不要比较不可见字符，最后输出一个0-1之间的double数字代表相似度
	 *
	 * @param lstOrg
	 * @param lstMod
	 * @return
	 */
	public static double tools_calc_similarity(List<String> lstOrg, List<String> lstMod) {
		// 1. 合并所有行并去除空白字符，但保留换行符，因为行数对表格替换很重要
		String original = lstOrg.stream()
				.map(line -> line.replaceAll("\\s+", "")) // 去除每行内的空白字符
				.collect(Collectors.joining("\n")); // 用换行符连接，保留空行（空字符串
		String modified = lstMod.stream()
				.map(line -> line.replaceAll("\\s+", "")) // 去除每行内的空白字符
				.collect(Collectors.joining("\n"));

		if (original.isEmpty()) {
			return modified.isEmpty() ? 1.0 : 0.0;
		}

		// 检查前3个字符是否匹配（考虑空行）
		int minLength = Math.min(original.length(), modified.length());
		if (minLength < 3) {
			if (!original.equals(modified)) return 0.0;
		} else {
			if (!original.substring(0, 3).equals(modified.substring(0, 3))) {
				return 0.0;
			}
		}

		// 2. 统计字符频率
		Map<Character, Integer> originalCounts = new HashMap<>();
		for (char c : original.toCharArray()) {
			originalCounts.put(c, originalCounts.getOrDefault(c, 0) + 1);
		}

		Map<Character, Integer> modifiedCounts = new HashMap<>();
		for (char c : modified.toCharArray()) {
			modifiedCounts.put(c, modifiedCounts.getOrDefault(c, 0) + 1);
		}

		// 3. 计算交集的最小总次数
		int total = 0;
		for (Map.Entry<Character, Integer> entry : originalCounts.entrySet()) {
			char key = entry.getKey();
			int countInOriginal = entry.getValue();
			int countInModified = modifiedCounts.getOrDefault(key, 0);
			total += Math.min(countInOriginal, countInModified);
		}

		return (double) total / original.length();
	}

	/**
	 * 用spire-ocr方式，识别图片的内容
	 *
	 * @return
	 */
	private static String tools_ocr_pic(BufferedImage bufferdImage) {
		String ret = "";
//		try {
//			// 创建OcrScanner实例
//			OcrScanner scanner = new OcrScanner();
//			// 设置扫描器配置
//			ConfigureOptions configureOptions = new ConfigureOptions();
//			// 指定文本识别的语言，默认设置为English（支持语言：English，Chinese，Chinesetraditional，French，German，Japanese和Korean）
//			configureOptions.setLanguage("Chinese");
//			// 指定模型的路径
//			configureOptions.setModelPath("/Users/<USER>/mmVolume/spire-ocr-lib/mac/");
//			// 将配置应用于扫描器
//			scanner.ConfigureDependencies(configureOptions);
//
//			ByteArrayOutputStream baos = new ByteArrayOutputStream();
//			ImageIO.write(bufferdImage, "PNG", baos);
//			baos.flush();
//			InputStream isImage = new ByteArrayInputStream(baos.toByteArray());
//			scanner.Scan(isImage, OCRImageFormat.Png);
//
//			ret = scanner.getText().toString();
//
//		} catch (Exception e) {
//			log.error("tools_ocr_pic方法运行时错误 {}", e.getMessage(), e);
//		}

		return ret;
	}

	public class CustomMultipartFile implements MultipartFile {

		private final File file;

		public CustomMultipartFile(File file) {
			this.file = file;
		}

		@Override
		public String getName() {
			// 返回表单中文件字段的名称
			return file.getName();
		}

		@Override
		public String getOriginalFilename() {
			// 返回原始文件名
			return file.getName();
		}

		@Override
		public String getContentType() {
			// 你可以在这里尝试猜测文件类型，或返回 null
			return "image/png";
		}

		@Override
		public boolean isEmpty() {
			return file.length() == 0;
		}

		@Override
		public long getSize() {
			return file.length();
		}

		@Override
		public byte[] getBytes() throws IOException {
			return java.nio.file.Files.readAllBytes(file.toPath());
		}

		@Override
		public InputStream getInputStream() throws IOException {
			return new FileInputStream(file);
		}

		@Override
		public void transferTo(File dest) throws IOException, IllegalStateException {
			// 将此文件复制到目标位置
			java.nio.file.Files.copy(file.toPath(), dest.toPath(), java.nio.file.StandardCopyOption.REPLACE_EXISTING);
		}
	}

	public static String convertTablesToHtml(String text) {
		StringBuilder result = new StringBuilder(text);
		int pos = 0;

		while (pos < result.length()) {
			// 1. 查找第一个 '|' 的位置
			int pipeIndex = result.indexOf("|", pos);
			if (pipeIndex == -1) break; // 没有更多表格了

			// 2. 向前查找最近的换行符，确定表格起始行
			int lineStart = pipeIndex;
			while (lineStart > 0 && result.charAt(lineStart - 1) != 'n' && result.charAt(lineStart - 2) != '\\') {
				lineStart--;
			}

			// 3. 向后查找表格的结束位置
			int tableEnd = findTableEnd(result, pipeIndex);
			if (tableEnd == -1) {
				// 如果找不到结束位置，说明表格不完整，跳过
				pos = pipeIndex + 1;
				continue;
			}

			// 4. 提取表格区域
			String tableText = result.substring(lineStart, tableEnd + 1);
			String[] lines = tableText.split("\n");

			// 5. 转换为 HTML 表格
			String htmlTable = generateHtmlTable(lines);

			// 6. 替换原始文本中的表格区域
			result.replace(lineStart, tableEnd + 1, htmlTable);

			// 更新 pos，跳过刚处理的表格区域
			pos = lineStart + htmlTable.length();
		}

		return result.toString();
	}

	// 查找表格结束位置（某行中在 '|' 后 10 个字符内都没有另一个 '|'）
	private static int findTableEnd(StringBuilder text, int startPos) {
		int index = startPos;
		boolean inTable = true;

		while (inTable && index < text.length()) {
			int lineEnd = text.indexOf("\\n", index);
			if (lineEnd == -1) lineEnd = text.length();

			// 检查当前行是否是表格行（至少两个 |）
			int pipeCount = 0;
			for (int i = index; i < lineEnd && pipeCount < 2; i++) {
				if (text.charAt(i) == '|') {
					pipeCount++;
					if (pipeCount == 2) break;
				}
			}

			if (pipeCount < 2) {
				// 不是表格行，结束
				inTable = false;
				break;
			}

			index = lineEnd + 1;
		}

		return index - 2; // 返回上一行的结尾位置
	}

	// 将表格行转换为 HTML 表格
	private static String generateHtmlTable(String[] lines) {
		StringBuilder html = new StringBuilder();
		html.append("<table>\n");

		// 用于存储表头和数据
		List<String[]> headerRows = new ArrayList<>();
		List<String[]> dataRows = new ArrayList<>();

		for (String line : lines) {
			String trimmedLine = line.trim().replace("\\", "").replaceAll("n","|");
			if (trimmedLine.isEmpty()) continue;

			// 拆分成单元格
			List<String> cells = new ArrayList<>();
			List<String> separators = new ArrayList<>();

			// 按 | 分割，并记录哪些是 --- 的部分
			String[] parts = trimmedLine.split("\\|");
			for (String part : parts) {
				String cell = part.trim();
				if (cell.equals("---")) {
					separators.add(cell);
				} else {
					cells.add(cell);
				}
			}

			// 如果有分隔符，说明这行可能是混合表头+数据的行
			if (!separators.isEmpty()) {
				// 查找第一个 "---" 出现的位置
				int firstSepIndex = -1;
				for (int i = 0; i < parts.length; i++) {
					if (parts[i].trim().equals("---")) {
						firstSepIndex = i;
						break;
					}
				}

				// 查找最后一个 "---" 出现的位置
				int lastSepIndex = -1;
				for (int i = parts.length - 1; i >= 0; i--) {
					if (parts[i].trim().equals("---")) {
						lastSepIndex = i;
						break;
					}
				}

				if (firstSepIndex != -1 && lastSepIndex != -1) {
					// 表头部分（第一个 --- 之前）
					List<String> headerCells = new ArrayList<>();
					for (int i = 0; i < firstSepIndex && i < parts.length; i++) {
						String cell = parts[i].trim();
						if (!cell.isEmpty()) {
							headerCells.add(cell);
						}
					}

					// 数据部分（最后一个 --- 之后）
					List<String> dataCells = new ArrayList<>();
					for (int i = lastSepIndex + 1; i < parts.length; i++) {
						String cell = parts[i].trim();
						if (!cell.isEmpty()) {
							dataCells.add(cell);
						}
					}

					if (!headerCells.isEmpty()) {
						headerRows.add(headerCells.toArray(new String[0]));
					}
					if (!dataCells.isEmpty()) {
						dataRows.add(dataCells.toArray(new String[0]));
					}

					continue;
				}
			}

			// 普通行，没有分隔符，作为表头或数据（取决于是否已找到分隔行）
			String[] cellsArray = cells.toArray(new String[0]);
			if (headerRows.isEmpty() && dataRows.isEmpty()) {
				headerRows.add(cellsArray);
			} else {
				dataRows.add(cellsArray);
			}
		}

		// 输出表头
		for (String[] cells : headerRows) {
			html.append("  <tr>\n");
			for (String cell : cells) {
				html.append("    <th>").append(cell).append("</th>\n");
			}
			html.append("  </tr>\n");
		}

		// 输出数据
		for (String[] cells : dataRows) {
			html.append("  <tr>\n");
			for (String cell : cells) {
				html.append("    <td>").append(cell).append("</td>\n");
			}
			html.append("  </tr>\n");
		}

		html.append("</table>\n");
		return html.toString();
	}

}
