package com.icarus.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 数据库类型枚举
 */
public enum DatabaseType {
    mysql("mysql", "com.mysql.cj.jdbc.Driver", "SELECT 1"),
    postgresql("postgresql", "org.postgresql.Driver", "SELECT 1"),
    oracle("oracle", "oracle.jdbc.OracleDriver", "SELECT 1 FROM DUAL"),
    sqlserver("sqlserver", "com.microsoft.sqlserver.jdbc.SQLServerDriver", "SELECT 1"),
    db2("db2", "com.ibm.db2.jcc.DB2Driver", "VALUES 1"),
    unknown("unknown", "", "");

    private final String type;
    private final String driverClass;

    private final String testSql;

    DatabaseType(String type, String driverClass, String testSql) {
        this.type = type;
        this.driverClass = driverClass;
        this.testSql = testSql;
    }

    public String getType() {
        return type;
    }

    public String getDriverClass() {
        return driverClass;
    }

    public String getTestSql() {
        return testSql;
    }

    public static DatabaseType fromString(String type) {
        if (StringUtils.isBlank(type)) {
            return unknown;
        }

        String normalizedType = type.toLowerCase().trim();
        for (DatabaseType dbType : DatabaseType.values()) {
            if (dbType.getType().equals(normalizedType)) {
                return dbType;
            }
        }
        return unknown;
    }

}
