-- 爬虫配置表
CREATE TABLE IF NOT EXISTS "public"."crawler_settings" (
    "id" BIGSERIAL NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "crawl_url" VARCHAR(1000) NOT NULL,
    "click_list_path" VARCHAR(255),
    "next_page_path" VARCHAR(255),
    "get_title" VARCHAR(255),
    "get_pub_time" VARCHAR(255),
    "get_content" VARCHAR(255),
    "get_file" VARCHAR(255),
    "iframe" VARCHAR(255),
    "headers" TEXT,
    "proxy" VARCHAR(255),
    "timeout" INTEGER,
    "retry_count" INTEGER,
    "max_pages" INTEGER,
    "main_extra_info" TEXT,
    "status" VARCHAR(50) DEFAULT 'active',
    "description" TEXT,
    "create_time" TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "update_time" TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "crawler_settings_pkey" PRIMARY KEY ("id")
);

COMMENT ON TABLE "public"."crawler_settings" IS '爬虫配置表';
COMMENT ON COLUMN "public"."crawler_settings"."id" IS '主键ID';
COMMENT ON COLUMN "public"."crawler_settings"."name" IS '爬虫配置名称';
COMMENT ON COLUMN "public"."crawler_settings"."crawl_url" IS '爬取起始URL';
COMMENT ON COLUMN "public"."crawler_settings"."click_list_path" IS '列表项选择器';
COMMENT ON COLUMN "public"."crawler_settings"."next_page_path" IS '下一页选择器';
COMMENT ON COLUMN "public"."crawler_settings"."get_title" IS '标题选择器';
COMMENT ON COLUMN "public"."crawler_settings"."get_pub_time" IS '发布时间选择器';
COMMENT ON COLUMN "public"."crawler_settings"."get_content" IS '内容选择器';
COMMENT ON COLUMN "public"."crawler_settings"."get_file" IS '附件选择器';
COMMENT ON COLUMN "public"."crawler_settings"."iframe" IS 'iframe选择器';
COMMENT ON COLUMN "public"."crawler_settings"."headers" IS 'HTTP请求头，JSON格式';
COMMENT ON COLUMN "public"."crawler_settings"."proxy" IS '代理设置，格式为host:port';
COMMENT ON COLUMN "public"."crawler_settings"."timeout" IS '超时时间，单位毫秒';
COMMENT ON COLUMN "public"."crawler_settings"."retry_count" IS '重试次数';
COMMENT ON COLUMN "public"."crawler_settings"."max_pages" IS '最大爬取页数';
COMMENT ON COLUMN "public"."crawler_settings"."main_extra_info" IS '额外信息配置，JSON格式';
COMMENT ON COLUMN "public"."crawler_settings"."status" IS '状态：active-启用，inactive-禁用';
COMMENT ON COLUMN "public"."crawler_settings"."description" IS '配置描述';
COMMENT ON COLUMN "public"."crawler_settings"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."crawler_settings"."update_time" IS '更新时间';

-- 爬虫任务表
CREATE TABLE IF NOT EXISTS `crawler_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `settings_id` bigint(20) NOT NULL COMMENT '爬虫配置ID',
  `batch_id` varchar(64) NOT NULL COMMENT '批次ID',
  `status` varchar(20) NOT NULL COMMENT '任务状态：running-运行中，completed-已完成，failed-失败',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `page_count` int(11) DEFAULT '0' COMMENT '处理页面数',
  `file_count` int(11) DEFAULT '0' COMMENT '处理文件数',
  `failed_count` int(11) DEFAULT '0' COMMENT '失败文件数',
  `text_content` longtext COMMENT '文本内容',
  `error_message` varchar(1000) DEFAULT NULL COMMENT '错误信息',
  `last_processed_url` varchar(1000) DEFAULT NULL COMMENT '最后处理的URL，用于断点续爬',
  `current_page` int(11) DEFAULT NULL COMMENT '当前处理的页码，用于断点续爬',
  `processed_urls` longtext COMMENT '已处理的URL列表，JSON格式，用于断点续爬',
  `failed_files` longtext COMMENT '失败文件列表，JSON格式，用于断点续爬',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_settings_id` (`settings_id`),
  KEY `idx_batch_id` (`batch_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='爬虫任务表';

-- 爬虫文件表
CREATE TABLE IF NOT EXISTS "public"."crawler_files" (
    "id" BIGSERIAL NOT NULL,
    "task_id" BIGINT NOT NULL,
    "file_name" VARCHAR(255) NOT NULL,
    "file_type" VARCHAR(50),
    "file_size" BIGINT,
    "minio_url" VARCHAR(1000),
    "original_url" VARCHAR(1000),
    "title" VARCHAR(500),
    "publish_time" TIMESTAMP WITHOUT TIME ZONE,
    "source_url" VARCHAR(1000),
    "extra_info" TEXT,
    "content_hash" VARCHAR(64),
    "status" VARCHAR(50) DEFAULT 'success',
    "create_time" TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "update_time" TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "crawler_files_pkey" PRIMARY KEY ("id")
);

COMMENT ON TABLE "public"."crawler_files" IS '爬虫文件表';
COMMENT ON COLUMN "public"."crawler_files"."id" IS '主键ID';
COMMENT ON COLUMN "public"."crawler_files"."task_id" IS '爬虫任务ID';
COMMENT ON COLUMN "public"."crawler_files"."file_name" IS '文件名';
COMMENT ON COLUMN "public"."crawler_files"."file_type" IS '文件类型';
COMMENT ON COLUMN "public"."crawler_files"."file_size" IS '文件大小，单位字节';
COMMENT ON COLUMN "public"."crawler_files"."minio_url" IS 'MinIO对象存储URL';
COMMENT ON COLUMN "public"."crawler_files"."original_url" IS '原始下载URL';
COMMENT ON COLUMN "public"."crawler_files"."title" IS '网页标题';
COMMENT ON COLUMN "public"."crawler_files"."publish_time" IS '发布时间';
COMMENT ON COLUMN "public"."crawler_files"."source_url" IS '网页来源URL';
COMMENT ON COLUMN "public"."crawler_files"."extra_info" IS '额外信息，JSON格式';
COMMENT ON COLUMN "public"."crawler_files"."content_hash" IS '内容哈希值，用于去重';
COMMENT ON COLUMN "public"."crawler_files"."status" IS '状态：success-成功，failed-失败';
COMMENT ON COLUMN "public"."crawler_files"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."crawler_files"."update_time" IS '更新时间';

-- 添加外键约束
ALTER TABLE "public"."crawler_tasks" ADD CONSTRAINT "fk_crawler_tasks_settings"
    FOREIGN KEY ("settings_id") REFERENCES "public"."crawler_settings" ("id") ON DELETE CASCADE;

ALTER TABLE "public"."crawler_files" ADD CONSTRAINT "fk_crawler_files_tasks"
    FOREIGN KEY ("task_id") REFERENCES "public"."crawler_tasks" ("id") ON DELETE CASCADE;

-- 添加索引
CREATE INDEX "idx_crawler_settings_name" ON "public"."crawler_settings" ("name");
CREATE INDEX "idx_crawler_tasks_batch_id" ON "public"."crawler_tasks" ("batch_id");
CREATE INDEX "idx_crawler_tasks_settings_id" ON "public"."crawler_tasks" ("settings_id");
CREATE INDEX "idx_crawler_files_task_id" ON "public"."crawler_files" ("task_id");
CREATE INDEX "idx_crawler_files_content_hash" ON "public"."crawler_files" ("content_hash"); 