
-- 创建向量文件表
CREATE TABLE design_embedding_file (
   id VARCHAR(255) PRIMARY KEY,
   fileName VARCHAR(255) NOT NULL,
   fileUrl VARCHAR(1000) NOT NULL
);

-- 添加表注释
COMMENT ON TABLE design_embedding_file IS '向量文件表';
COMMENT ON COLUMN design_embedding_file.id IS '主键';
COMMENT ON COLUMN design_embedding_file.fileName IS '文件名';
COMMENT ON COLUMN design_embedding_file.fileUrl IS '文件路径';

-- 创建向量明细表 
CREATE TABLE design_embedding_detail (
   id VARCHAR(255) PRIMARY KEY,
   fileId VARCHAR(255) NOT NULL,
   splitTitle VARCHAR(500),
   "index" INT NOT NULL,
   context TEXT,
   embedding TEXT,
   model VARCHAR(50)
);

-- 添加表注释
COMMENT ON TABLE design_embedding_detail IS '向量明细表';
COMMENT ON COLUMN design_embedding_detail.id IS '主键';
COMMENT ON COLUMN design_embedding_detail.fileId IS '文件id';
COMMENT ON COLUMN design_embedding_detail.splitTitle IS '文件拆分标题';
COMMENT ON COLUMN design_embedding_detail.index IS '文件顺序';
COMMENT ON COLUMN design_embedding_detail.context IS '文件内容';
COMMENT ON COLUMN design_embedding_detail.embedding IS 'eb值';
COMMENT ON COLUMN design_embedding_detail.model IS 'eb生成模型';