-- 场景表
CREATE TABLE aidoc_ew_sys_scene (
    id varchar NOT NULL,
    scene_name varchar NULL, -- 场景名称
    description text NULL, -- 场景描述
    template_id varchar NULL, -- 模板id
    company varchar NULL, -- 公司名称
    create_time date NULL, -- 创建日期
    update_time date NULL, -- 修改日期
    status varchar NULL, -- 状态 0禁用 1启用
    business_type varchar NULL, -- 业务类型
    template_name varchar NULL, -- 模板名称
    CONSTRAINT aidoc_ew_sys_scene_id PRIMARY KEY (id)
);

-- 注释
COMMENT ON TABLE aidoc_ew_sys_scene IS '场景表';
COMMENT ON COLUMN aidoc_ew_sys_scene.scene_name IS '场景名称';
COMMENT ON COLUMN aidoc_ew_sys_scene.description IS '场景描述';
COMMENT ON COLUMN aidoc_ew_sys_scene.template_id IS '模板id';
COMMENT ON COLUMN aidoc_ew_sys_scene.company IS '公司名称';
COMMENT ON COLUMN aidoc_ew_sys_scene.create_time IS '创建日期';
COMMENT ON COLUMN aidoc_ew_sys_scene.update_time IS '修改日期';
COMMENT ON COLUMN aidoc_ew_sys_scene.status IS '状态 0禁用 1启用';
COMMENT ON COLUMN aidoc_ew_sys_scene.business_type IS '业务类型';
COMMENT ON COLUMN aidoc_ew_sys_scene.template_name IS '模板名称';

-- 场景要素配置表
CREATE TABLE aidoc_ew_sys_scene_element (
    id varchar NULL,
    scene_id varchar NULL, -- 场景id
    batch int4 NULL, -- 抽取批次
    "sequence" int4 NULL, -- 抽取序列
    element_name varchar NULL, -- 要素名称
    element_type varchar NULL, -- 要素类型（Java基本类型）
    "scope" text NULL, -- 抽取范围(章节号)
    extraction_type varchar NULL, -- 抽取类型(llm、正则)
    extraction_config varchar NULL, -- 抽取配置（prompt）
    memo varchar NULL, -- 备注
    extraction_rule varchar NULL, -- 匹配规则(合同对比)
    extraction_rule_select varchar NULL, -- 匹配规则详情
    scope_type text NULL, -- 范围描述(一级标题编号还是名称)
    pattern_content varchar NULL, -- 正则表达式内容
    ref_sectionid varchar NULL, -- 段落编号
    synonymous varchar NULL, -- 近义词(用^分割保存)
    business_rules varchar NULL, -- 业务规则
    scope_name varchar NULL, -- 章节名称
    element_describe varchar NULL, -- 要素描述
    json_name varchar NULL, -- 对象名称
    sql_name varchar NULL, -- 字段名称
    if_trim varchar NULL, -- 是否去除收尾空格 Y/N
    if_replay_empty varchar NULL, -- 是否空格 Y/N
    if_replay_error_code varchar NULL, -- 是否乱码 Y/N
    create_time timestamp NULL,
    update_time timestamp NULL
);

-- 注释
COMMENT ON TABLE aidoc_ew_sys_scene_element IS '场景要素配置表';
COMMENT ON COLUMN aidoc_ew_sys_scene_element.scene_id IS '场景id';
COMMENT ON COLUMN aidoc_ew_sys_scene_element.batch IS '抽取批次';
COMMENT ON COLUMN aidoc_ew_sys_scene_element."sequence" IS '抽取序列';
COMMENT ON COLUMN aidoc_ew_sys_scene_element.element_name IS '要素名称';
COMMENT ON COLUMN aidoc_ew_sys_scene_element.element_type IS '要素类型（Java基本类型）';
COMMENT ON COLUMN aidoc_ew_sys_scene_element."scope" IS '抽取范围(章节号)';
COMMENT ON COLUMN aidoc_ew_sys_scene_element.extraction_type IS '抽取类型(llm、正则)';
COMMENT ON COLUMN aidoc_ew_sys_scene_element.extraction_config IS '抽取配置（prompt）';
COMMENT ON COLUMN aidoc_ew_sys_scene_element.memo IS '备注 ';
COMMENT ON COLUMN aidoc_ew_sys_scene_element.extraction_rule IS '匹配规则(合同对比)';
COMMENT ON COLUMN aidoc_ew_sys_scene_element.extraction_rule_select IS '匹配规则详情';
COMMENT ON COLUMN aidoc_ew_sys_scene_element.scope_type IS '范围描述(一级标题编号还是名称)';
COMMENT ON COLUMN aidoc_ew_sys_scene_element.pattern_content IS '正则表达式内容';
COMMENT ON COLUMN aidoc_ew_sys_scene_element.ref_sectionid IS '段落编号';
COMMENT ON COLUMN aidoc_ew_sys_scene_element.synonymous IS '近义词(用^分割保存)';
COMMENT ON COLUMN aidoc_ew_sys_scene_element.business_rules IS '业务规则';
COMMENT ON COLUMN aidoc_ew_sys_scene_element.scope_name IS '章节名称';
COMMENT ON COLUMN aidoc_ew_sys_scene_element.element_describe IS '要素描述';
COMMENT ON COLUMN aidoc_ew_sys_scene_element.json_name IS '对象名称';
COMMENT ON COLUMN aidoc_ew_sys_scene_element.sql_name IS '字段名称';
COMMENT ON COLUMN aidoc_ew_sys_scene_element.if_trim IS '是否去除收尾空格 Y/N';
COMMENT ON COLUMN aidoc_ew_sys_scene_element.if_replay_empty IS '是否空格 Y/N';
COMMENT ON COLUMN aidoc_ew_sys_scene_element.if_replay_error_code IS '是否乱码 Y/N';
