create table design_agent
(
    id          varchar(32)  not null
            constraint design_agent_pkey
            primary key,
    name        varchar(100) not null,
    code        varchar(100) not null,
    agent_type       varchar(100),
    agent_Tags        varchar(100) not null,
    prompt        text,
    description        text,
    update_time       timestamp default CURRENT_TIMESTAMP not null,
    create_time       timestamp default CURRENT_TIMESTAMP not null
);

comment on column design_agent.name is 'agent名称';
comment on column design_agent.code is 'agent编码';
comment on column design_agent.agent_type is '分类';

COMMENT ON COLUMN design_agent.agent_Tags IS '标签';


alter table design_agent
    owner to pgvector;


create table design_agent_work_flow
(
    id          varchar(32)  not null
        constraint design_agent_work_flow_pkey
            primary key,
    agent_id        varchar(100) not null,
    work_flow_id        varchar(100) not null,
    update_time       timestamp default CURRENT_TIMESTAMP not null,
    create_time       timestamp default CURRENT_TIMESTAMP not null
);

alter table design_agent_work_flow
    owner to pgvector;