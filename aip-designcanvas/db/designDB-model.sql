create table design_model
(
    id          varchar(32)  not null
            constraint design_model_pkey
            primary key,
    name        varchar(100) not null,
    code        varchar(100) not null,
    group_type       varchar(100),
    provider   varchar(100) not null,
    usage_type        varchar(50)  not null,
    content_length INT NOT NULL,
    size NUMERIC(6,1) NOT NULL,
    architecture_type VARCHAR(10) NOT NULL,
    update_time       timestamp default CURRENT_TIMESTAMP not null,
    create_time       timestamp default CURRENT_TIMESTAMP not null

);

comment on column design_model.name is '模型名称';
comment on column design_model.code is '模型编码';
comment on column design_model.group_type is '分类';
comment on column design_model.provider is '供应商';

comment on column design_model.usage_type is '模型用途分类';
comment on column design_model.content_length is '模型上下文大小';
comment on column design_model.size is '模型参数大小';
COMMENT ON COLUMN design_model.architecture_type IS '模型架构';


COMMENT ON COLUMN design_model.update_time IS '更新时间';


alter table design_model
    owner to pgvector;

