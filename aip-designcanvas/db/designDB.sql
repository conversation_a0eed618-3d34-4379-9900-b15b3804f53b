-- 1. 首先创建更新时间戳的函数
CREATE OR REPLACE FUNCTION public.update_timestamp()
RET<PERSON>NS trigger AS $$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 2. 创建 t_node 表及其相关对象
CREATE TABLE "public"."t_node" (
  "id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "type" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "content" "pg_catalog"."text" COLLATE "pg_catalog"."default",
  "create_time" "pg_catalog"."timestamp" DEFAULT CURRENT_TIMESTAMP,
  "update_time" "pg_catalog"."timestamp" DEFAULT CURRENT_TIMESTAMP,
  "extension" "pg_catalog"."int" DEFAULT 0,
  CONSTRAINT "t_node_pkey" PRIMARY KEY ("id")
);

ALTER TABLE "public"."t_node" OWNER TO "pgvector";

CREATE TRIGGER "update_node_timestamp" 
BEFORE UPDATE ON "public"."t_node"
FOR EACH ROW
EXECUTE PROCEDURE "public"."update_timestamp"();

COMMENT ON COLUMN "public"."t_node"."name" IS '节点名称';
COMMENT ON COLUMN "public"."t_node"."type" IS '节点类型';
COMMENT ON COLUMN "public"."t_node"."content" IS '节点内容（JSON格式）';
COMMENT ON COLUMN "public"."t_node"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."t_node"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."t_node"."extension" IS '是否扩展节点';

-- 3. 创建 t_designer_enum 表及其相关对象
CREATE TABLE "public"."t_designer_enum" (
  "id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "type" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "content" "pg_catalog"."text" COLLATE "pg_catalog"."default" NOT NULL,
  "create_time" "pg_catalog"."timestamp" DEFAULT CURRENT_TIMESTAMP,
  "update_time" "pg_catalog"."timestamp" DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT "t_designer_enum_pkey" PRIMARY KEY ("id")
);

ALTER TABLE "public"."t_designer_enum" OWNER TO "pgvector";

CREATE TRIGGER "update_designer_enum_timestamp" 
BEFORE UPDATE ON "public"."t_designer_enum"
FOR EACH ROW
EXECUTE PROCEDURE "public"."update_timestamp"();

COMMENT ON COLUMN "public"."t_designer_enum"."type" IS '枚举类型';
COMMENT ON COLUMN "public"."t_designer_enum"."content" IS '枚举内容（JSON格式）';
COMMENT ON COLUMN "public"."t_designer_enum"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."t_designer_enum"."update_time" IS '更新时间';

-- 4. 创建 t_directory 表及其相关对象
CREATE TABLE "public"."t_directory" (
  "id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "parent_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "path" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "level" "pg_catalog"."int4" NOT NULL,
  "create_time" "pg_catalog"."timestamp" NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "update_time" "pg_catalog"."timestamp" NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT "t_directory_pkey" PRIMARY KEY ("id")
);

ALTER TABLE "public"."t_directory" OWNER TO "pgvector";

-- 添加自引用外键约束
ALTER TABLE "public"."t_directory"
ADD CONSTRAINT "fk_directory_parent" 
FOREIGN KEY ("parent_id") 
REFERENCES "public"."t_directory" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

CREATE TRIGGER update_directory_timestamp
BEFORE UPDATE ON public.t_directory
FOR EACH ROW
EXECUTE PROCEDURE public.update_timestamp();

CREATE INDEX "idx_directory_parent" ON "public"."t_directory" USING btree (
  "parent_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

CREATE INDEX "idx_directory_path" ON "public"."t_directory" USING btree (
  "path" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- 5. 创建 t_canvas 表及其相关对象
CREATE TABLE "public"."t_canvas" (
  "id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "content" "pg_catalog"."text" COLLATE "pg_catalog"."default",
  "create_time" "pg_catalog"."timestamp" NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "update_time" "pg_catalog"."timestamp" NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "directory_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  CONSTRAINT "t_canvas_pkey" PRIMARY KEY ("id"),
  CONSTRAINT "fk_canvas_directory" FOREIGN KEY ("directory_id") 
    REFERENCES "public"."t_directory" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION
);

ALTER TABLE "public"."t_canvas" OWNER TO "pgvector";

CREATE TRIGGER update_canvas_timestamp
BEFORE UPDATE ON public.t_canvas
FOR EACH ROW
EXECUTE PROCEDURE public.update_timestamp();

CREATE INDEX "idx_canvas_name" ON "public"."t_canvas" USING btree (
  "name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- 6. 最后创建 t_endpoint 表及其相关对象（因为依赖 t_canvas）
CREATE TABLE "public"."t_endpoint" (
  "id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "project" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "subsystem" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "module" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "description" "pg_catalog"."text" COLLATE "pg_catalog"."default",
  "type" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "template_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "create_time" "pg_catalog"."timestamp" NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "update_time" "pg_catalog"."timestamp" NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT "t_endpoint_pkey" PRIMARY KEY ("id"),
  CONSTRAINT "t_endpoint_template_id_fkey" FOREIGN KEY ("template_id") 
    REFERENCES "public"."t_canvas" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION
);

ALTER TABLE "public"."t_endpoint" OWNER TO "pgvector";

CREATE TRIGGER update_endpoint_timestamp
BEFORE UPDATE ON public.t_endpoint
FOR EACH ROW
EXECUTE PROCEDURE public.update_timestamp();

CREATE INDEX "idx_endpoint_project" ON "public"."t_endpoint" USING btree (
  "project" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

CREATE INDEX "idx_endpoint_template" ON "public"."t_endpoint" USING btree (
  "template_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);