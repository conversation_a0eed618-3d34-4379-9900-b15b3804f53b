-- 创建定时触发器表
CREATE TABLE "public"."design_timer_trigger" (
  "id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "trigger_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "user_id" varchar(255) COLLATE "pg_catalog"."default",
  "workflow_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "path" varchar(500) COLLATE "pg_catalog"."default" NOT NULL,
  "trigger_time_type" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "trigger_time" varchar(50) COLLATE "pg_catalog"."default",
  "frequency_type" varchar(50) COLLATE "pg_catalog"."default",
  "frequency_day" int4,
  "cron_expression" varchar(255) COLLATE "pg_catalog"."default",
  "timezone" varchar(50) COLLATE "pg_catalog"."default" DEFAULT 'Asia/Shanghai',
  "status" int4 DEFAULT 1,
  "description" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" varchar(32) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_by" varchar(32) COLLATE "pg_catalog"."default",
  "update_time" timestamp(6),
  CONSTRAINT "design_timer_trigger_pkey" PRIMARY KEY ("id")
);

-- 设置表所有者
ALTER TABLE "public"."design_timer_trigger" 
  OWNER TO "pgvector";

-- 添加表注释
COMMENT ON TABLE "public"."design_timer_trigger" IS '定时触发器表';

-- 添加列注释
COMMENT ON COLUMN "public"."design_timer_trigger"."id" IS '主键';
COMMENT ON COLUMN "public"."design_timer_trigger"."trigger_name" IS '触发器名称';
COMMENT ON COLUMN "public"."design_timer_trigger"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."design_timer_trigger"."workflow_id" IS '工作流ID';
COMMENT ON COLUMN "public"."design_timer_trigger"."path" IS '路径';
COMMENT ON COLUMN "public"."design_timer_trigger"."trigger_time_type" IS '触发时间类型(preset:预设时间,cron:Cron表达式)';
COMMENT ON COLUMN "public"."design_timer_trigger"."trigger_time" IS '触发时间(HH:mm:ss)';
COMMENT ON COLUMN "public"."design_timer_trigger"."frequency_type" IS '频率类型(daily:每日,weekly:每周,monthly:每月,interval:间隔)';
COMMENT ON COLUMN "public"."design_timer_trigger"."frequency_day" IS '频率日期(周几/几号/间隔天数)';
COMMENT ON COLUMN "public"."design_timer_trigger"."cron_expression" IS 'Cron表达式';
COMMENT ON COLUMN "public"."design_timer_trigger"."timezone" IS '时区';
COMMENT ON COLUMN "public"."design_timer_trigger"."status" IS '状态(0:禁用,1:启用)';
COMMENT ON COLUMN "public"."design_timer_trigger"."description" IS '描述';
COMMENT ON COLUMN "public"."design_timer_trigger"."create_by" IS '创建人';
COMMENT ON COLUMN "public"."design_timer_trigger"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."design_timer_trigger"."update_by" IS '更新人';
COMMENT ON COLUMN "public"."design_timer_trigger"."update_time" IS '更新时间';

-- 创建索引
CREATE INDEX "idx_design_timer_trigger_name" ON "public"."design_timer_trigger" USING btree (
  "trigger_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_design_timer_trigger_workflow" ON "public"."design_timer_trigger" USING btree (
  "workflow_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_design_timer_trigger_user" ON "public"."design_timer_trigger" USING btree (
  "user_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_design_timer_trigger_status" ON "public"."design_timer_trigger" USING btree (
  "status" "pg_catalog"."int4_ops" ASC NULLS LAST
); 