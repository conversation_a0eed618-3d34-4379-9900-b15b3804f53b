FROM ubuntu:22.04

# 避免在构建过程中出现交互式提示
ARG DEBIAN_FRONTEND=noninteractive

# 字符集设置为U8
ENV LANG=C.UTF-8

# 换国内源，注意，不要在内网使用更新
RUN sed -i 's/archive.ubuntu.com/mirrors.aliyun.com/g' /etc/apt/sources.list

# 安装必要的 JRE 包
RUN apt-get update && \
    apt-get clean && \
    apt-get -y install --no-install-recommends openjdk-17-jre-headless

# 安装必要的 工具
RUN apt-get -y install --no-install-recommends tcpdump net-tools iproute2 watch nmon

# 安装 Playwright 浏览器依赖库(爬虫用)
# 分步安装，避免依赖冲突
RUN apt-get update && \
    apt-get -y install --no-install-recommends \
        ca-certificates \
        fonts-liberation \
        libasound2 \
        libatk-bridge2.0-0 \
        libatk1.0-0 \
        libatspi2.0-0 \
        libcairo2 \
        libdrm2 \
        libgbm1 \
        libglib2.0-0 \
        libgtk-3-0 \
        libnspr4 \
        libnss3 \
        libpango-1.0-0 \
        libx11-6 \
        libxcb1 \
        libxcomposite1 \
        libxdamage1 \
        libxext6 \
        libxfixes3 \
        libxkbcommon0 \
        libxrandr2 \
        libxss1 \
        libxtst6 \
        xvfb \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

RUN mkdir -p /usr/share/fonts/Fonts
COPY Fonts/* /usr/share/fonts/Fonts/
