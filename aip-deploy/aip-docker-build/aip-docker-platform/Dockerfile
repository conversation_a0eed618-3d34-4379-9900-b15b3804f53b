FROM aip-base:20250729

# 创建应用目录
RUN mkdir -p /aip-home/apps
RUN mkdir -p /aip-home/config
RUN mkdir -p /aip-home/exlibs
RUN mkdir -p /aip-home/logs

# 开发/测试环境：将应用目录映射出来，方便更新应用、配置、依赖、脚本
# 生产环境：应用、配置、依赖、脚本等全部打包以支持docker在集群中部署，仅将logs挂出来
VOLUME ["/aip-home"]

# docker提供运行环境，分为开发时和投产时
# 开发/测试环境：除了启动的starter外，不要往里面放其他东西
# 生产环境：除了日志，应尽量将需要的内容全部打包进去

# 设置 Playwright 浏览器路径环境变量
ENV PLAYWRIGHT_BROWSERS_PATH=/aip-home/home/<USER>

# 声明暴露的端口
EXPOSE 9120

# 启动程序
ENTRYPOINT ["java", "-cp", "/aip-home/apps/*:/aip-home/exlibs/*:/aip-home/config", "-Dfile.encoding=UTF-8","-Dlogging.config=/aip-home/config/logback-spring.xml",  "com.icarus.AIPBigGroupApplication", "--spring.profiles.active=uat", "--spring.config.additional-location=/aip-home/config/"]
