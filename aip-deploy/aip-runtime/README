# AIP环境说明

## 目录结构
$aip-home变量指向当前目录的上一级，包含了本地构建docker、运行时目录、备份目录在内的全部结构

$AIP_HOME
  - aip-builder             //构建docker用
    - aip-docker-base       //所有应用容器要基于aip-base
    - aip-docker-platform   //构建aip-platform的目录
  - aip-runtime  //运行时
    - apps       //被starter引用的包，通常是一套，如icarus-**.jar
    - config     //当前生效的配置
    - exlibs     //依赖包，主要是sdk等，不要打fatjar
    - logs       //当前日志，不要归档
    - scripts    //维护用脚本
  - aip-archive     //归档备份
    - app-bak       //运行时目录的完整备份，通过tar压缩的完整应用备份
    - db-bak        //通过pg_dump做的完整备份，包含建库脚本
    - libs-bak      //依赖库太大，除非是sdk升级，不要动它，但是初始安装完成，要做一次备份


## 升级说明

开发/测试环境：docker中只有运行环境、starter，所有配置、依赖、日志、脚本都映射出来了

开发/测试时的升级：docker中只有个starter，不用动它，只需要在aip-home中的aip-runtime/apps中更新相关包即可

生产环境：docker中除了logs全部打包，需要关停、删除containter然后再拉起新的版本（必须确定前一版本已经关停，不然会出现端口占用等情况导致升级失败）
