#!/bin/bash

### 容器管理脚本"
###
### 命令:
### build-base        构建基础镜像"
### build-app         构建应用镜像"
### run [profile]     启动容器(使用指定环境，如dev)"
### start             启动现有容器"
### stop              停止容器"
### status            查看容器状态"
### logs              查看容器日志"
### list-images       列出所有可用镜像"

# 获取脚本绝对路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
AIP_HOME="$(dirname "$SCRIPT_DIR")"

# 配置参数
CONTAINER_NAME="aip-platform"
BASE_IMAGE_NAME="aip-base"
APP_IMAGE_NAME="aip-platform"
EXPOSED_PORT=9120
DEFAULT_DOCKERFILE="$AIP_HOME/aip-docker-build/aip-docker-platform/Dockerfile"
BASE_DOCKERFILE="$AIP_HOME/aip-docker-build/aip-docker-base/Dockerfile"

# ================== 关键路径验证 ==================
echo "验证关键路径..."
critical_paths=(
    "$DEFAULT_DOCKERFILE"
    "$BASE_DOCKERFILE"
    "$AIP_HOME/aip-runtime/apps"
    "$AIP_HOME/aip-runtime/config"
)

for path in "${critical_paths[@]}"; do
    if [ ! -e "$path" ]; then
        echo "错误：关键路径不存在 - $path"
        echo "请检查部署结构或环境变量"
        exit 1
    fi
done
echo "关键路径验证通过"
# =================================================

# 获取宿主机时区
get_host_timezone() {
    if [ -f /etc/timezone ]; then
        cat /etc/timezone
    elif command -v timedatectl &> /dev/null; then
        timedatectl show --property=Timezone --value
    elif [ -f /etc/localtime ]; then
        find /usr/share/zoneinfo -type f -exec md5sum {} + | \
            grep $(md5sum /etc/localtime | cut -d' ' -f1) | \
            awk '{gsub("/usr/share/zoneinfo/", ""); print $2}'
    else
        echo "Asia/Shanghai"
    fi
}

# 准备日志目录
prepare_logs() {
    mkdir -p "$AIP_HOME/aip-runtime/logs/info"
    mkdir -p "$AIP_HOME/aip-runtime/logs/error"
    chmod -R 755 "$AIP_HOME/aip-runtime/logs"
}

# 构建基础镜像
build_base_image() {
    local tag=$(date +%Y%m%d)

    echo "正在构建基础镜像 $BASE_IMAGE_NAME:$tag..."
    docker build -t "$BASE_IMAGE_NAME:$tag" -t "$BASE_IMAGE_NAME:latest" \
           -f "$BASE_DOCKERFILE" "$(dirname "$BASE_DOCKERFILE")"

    if [ $? -eq 0 ]; then
        echo "基础镜像构建成功: $BASE_IMAGE_NAME:$tag (同时标记为 latest)"
    else
        echo "基础镜像构建失败"
        exit 1
    fi
}

# 构建应用镜像
build_app_image() {
    local timestamp=$(date +%Y%m%d-%H%M%S)
    local app_image_tag="$APP_IMAGE_NAME:$timestamp"

    echo "正在构建应用镜像 $app_image_tag..."
    docker build -t "$app_image_tag" -f "$DEFAULT_DOCKERFILE" "$(dirname "$DEFAULT_DOCKERFILE")"

    if [ $? -eq 0 ]; then
        echo "应用镜像构建成功: $app_image_tag"
        # 记录最后一次构建的镜像
        echo "$app_image_tag" > "$SCRIPT_DIR/last_image.txt"
        echo "$app_image_tag"
    else
        echo "应用镜像构建失败"
        exit 1
    fi
}

# 运行容器
run_container() {
    local profile=$1
    local host_tz=$(get_host_timezone)

    # 检查基础镜像是否存在
    if ! docker image inspect "$BASE_IMAGE_NAME:latest" &>/dev/null; then
        echo "基础镜像不存在，正在构建..."
        build_base_image
    fi

    # 获取应用镜像（自动构建或使用最新）
    local app_image=$(cat "$SCRIPT_DIR/last_image.txt" 2>/dev/null)
    if [ -z "$app_image" ] || ! docker image inspect "$app_image" &>/dev/null; then
        echo "应用镜像不存在或已过期，正在构建..."
        app_image=$(build_app_image)
    fi

    # 准备日志目录
    prepare_logs

    # 停止并移除同名容器（如果存在）
    docker stop $CONTAINER_NAME >/dev/null 2>&1
    docker rm $CONTAINER_NAME >/dev/null 2>&1

    echo "启动容器 $CONTAINER_NAME"
    echo "  镜像: $app_image"
    echo "  时区: $host_tz"
    echo "  环境: ${profile:-default}"

    # 构建docker run命令
    local docker_cmd="docker run -d --name $CONTAINER_NAME \
        -p $EXPOSED_PORT:9120 \
        -e TZ=$host_tz"

    # 添加profile环境变量
    if [ -n "$profile" ]; then
        docker_cmd+=" -e SPRING_PROFILES_ACTIVE=$profile"
    fi

    # 添加挂载卷
    docker_cmd+=" \
        -v $AIP_HOME/aip-runtime/apps:/aip-home/apps:ro \
        -v $AIP_HOME/aip-runtime/exlibs:/aip-home/exlibs:ro \
        -v $AIP_HOME/aip-runtime/config:/aip-home/config:ro \
        -v $AIP_HOME/aip-runtime/logs:/aip-home/logs:rw \
        -v $AIP_HOME/aip-runtime/home:/aip-home/home:rw \
        $app_image"

    # 执行命令
    eval "$docker_cmd"

    if [ $? -eq 0 ]; then
        echo "容器启动成功"
        echo "使用命令查看日志: docker logs -f $CONTAINER_NAME"
    else
        echo "容器启动失败"
    fi
}

# 检查容器状态
check_container_status() {
    if docker inspect $CONTAINER_NAME &>/dev/null; then
        if [ "$(docker inspect -f '{{.State.Running}}' $CONTAINER_NAME)" = "true" ]; then
            echo "状态: 运行中"
            echo "启动时间: $(docker inspect -f '{{.State.StartedAt}}' $CONTAINER_NAME)"

            # 显示镜像信息
            local image_id=$(docker inspect -f '{{.Image}}' $CONTAINER_NAME)
            local image_info=$(docker inspect -f '{{.RepoTags}}' $image_id 2>/dev/null || echo "未知镜像")
            echo "使用镜像: $image_info"

            # 显示环境变量
            local env_vars=$(docker inspect -f '{{range .Config.Env}}{{println .}}{{end}}' $CONTAINER_NAME)
            echo "环境变量:"
            echo "$env_vars" | grep -E 'TZ|SPRING_PROFILES_ACTIVE' | sed 's/^/  /'

            return 0
        else
            echo "状态: 已停止"
            echo "退出代码: $(docker inspect -f '{{.State.ExitCode}}' $CONTAINER_NAME)"
            return 1
        fi
    else
        echo "状态: 容器不存在"
        return 2
    fi
}

# 主程序
main() {
    case "$1" in
        start)
            docker start $CONTAINER_NAME
            check_container_status
            ;;
        stop)
            docker stop $CONTAINER_NAME
            echo "容器已停止"
            ;;
        status)
            check_container_status
            ;;
        build-base)
            build_base_image
            ;;
        build-app)
            build_app_image
            ;;
        run)
            local profile=${2:-}
            run_container "$profile"
            ;;
        logs)
            docker logs -f $CONTAINER_NAME
            ;;
        list-images)
            echo "可用应用镜像:"
            docker images --filter "reference=$APP_IMAGE_NAME:*" --format "table {{.Tag}}\t{{.CreatedSince}}\t{{.Size}}"
            echo ""
            echo "可用基础镜像:"
            docker images --filter "reference=$BASE_IMAGE_NAME:*" --format "table {{.Tag}}\t{{.CreatedSince}}\t{{.Size}}"
            ;;
        *)
            echo "容器管理脚本"
            echo "用法: $0 [command]"
            echo ""
            echo "命令:"
            echo "  build-base        构建基础镜像"
            echo "  build-app         构建应用镜像"
            echo "  run [profile]     启动容器(使用指定环境，如dev)"
            echo "  start             启动现有容器"
            echo "  stop              停止容器"
            echo "  status            查看容器状态"
            echo "  logs              查看容器日志"
            echo "  list-images       列出所有可用镜像"
            exit 1
            ;;
    esac
}

# 执行主程序
main "$@"