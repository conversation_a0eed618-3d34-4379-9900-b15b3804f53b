#!/bin/bash

 # 检查是否提供了操作命令
 if [ $# -lt 1 ]; then
     echo "用法: $0 [start|stop|update <Dockerfile路径>|status]"
     exit 1
 fi

 # 定义容器名称
 image_tag="aip-platform:latest"
 container_name="aip-platform"

 # 获取操作命令
 action=$1

 # 根据操作命令执行相应操作
 case $action in
     start)
         docker start $container_name
         if [ $? -eq 0 ]; then
             echo "容器 $container_name 已启动"
         else
             echo "启动容器 $container_name 失败"
         fi
         ;;
     stop)
         docker stop $container_name
         if [ $? -eq 0 ]; then
             echo "容器 $container_name 已停止"
         else
             echo "停止容器 $container_name 失败"
         fi
         ;;
     update)
         if [ $# -ne 2 ]; then
             echo "用法: $0 update <Dockerfile路径>"
             exit 1
         fi
         dockerfile_path=$2
         if [ ! -f "$dockerfile_path" ]; then
             echo "指定的 Dockerfile 路径 $dockerfile_path 不存在"
             exit 1
         fi

         # 构建镜像
         docker build -t $image_tag -f $dockerfile_path .
         if [ $? -ne 0 ]; then
             echo "构建 Docker 镜像失败"
             exit 1
         fi

         # 清理原来的容器
         docker stop $container_name
         docker rm $container_name

         # 获取宿主机时区
         HOST_TZ=$(timedatectl show --property=Timezone --value 2>/dev/null || cat /etc/timezone 2>/dev/null || echo "Asia/Shanghai")

        mkdir -p $AIP_HOME/aip-runtime/logs/{info,error}
        chmod -R 755 $AIP_HOME/aip-runtime/logs

         # 拉起新的容器
         docker run -d --name $container_name \
                    -p 9120:9120 \
                    -e TZ=$HOST_TZ \
                    -v /etc/localtime:/etc/localtime:ro \
                    -v $AIP_HOME/aip-runtime/apps:/aip-home/apps:ro \
                    -v $AIP_HOME/aip-runtime/exlibs:/aip-home/exlibs:ro \
                    -v $AIP_HOME/aip-runtime/config:/aip-home/config:ro \
                    -v $AIP_HOME/aip-runtime/logs:/aip-home/logs:rw \
                    -v $AIP_HOME/aip-runtime/home:/aip-home/home:rw \
                    $image_tag

         if [ $? -eq 0 ]; then
             echo "容器 $container_name 已更新"
         else
             echo "更新容器 $container_name 失败"
         fi
         ;;
     status)
         status=$(docker inspect -f '{{.State.Running}}' $container_name 2>/dev/null)
         if [ "$status" = "true" ]; then
             echo "容器 $container_name 正在运行"
         elif [ "$status" = "false" ]; then
             echo "容器 $container_name 已停止"
         else
             echo "容器 $container_name 不存在或检查状态时出错"
         fi
         ;;
     *)
         echo "无效的操作命令。可用的命令: start, stop, update <Dockerfile路径>, status"
         exit 1
         ;;
 esac
