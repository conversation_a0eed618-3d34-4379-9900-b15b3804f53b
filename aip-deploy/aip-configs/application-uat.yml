server:
  port: 9120

spring:
  servlet:
    multipart:
      max-file-size: 300MB
      max-request-size: 300MB
  mvc:
    throw-exception-if-no-handler-found: true
  web:
    resources:
      add-mappings: false
  config:
    import:
      - application-llm-uat.yml
  data:
    redis:
      host: **********
      port: 16379
      password: 123456
      timeout: 3000
      lettuce:
        pool:
          enabled: true # 启用连接池
          max-active: 8 # 最大连接数
          max-idle: 8   # 最大空闲连接数
          min-idle: 0   # 最小空闲连接数
          max-wait: -1ms # 最大等待时间（-1 表示无限等待）

  datasource:
    driver-class-name: org.postgresql.Driver
    url: *********************************************
    username: pgvector
    password: pgvector

  kafka:
    bootstrap-servers: **********:19092
    producer:
      batch-size: 16384
      acks: -1
      retries: 3
      #transaction-id-prefix: transaction
      buffer-memory: 33554432
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      properties:
        linger:
          ms: 2000
    consumer:
      group-id: runtime-group
      enable-auto-commit: true
      auto-commit-interval: 2000
      #      auto-offset-reset: latest
      max-poll-records: 1

      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      properties:
        max.poll.interval.ms: 86400000
        session:
          timeout:
            ms: 120000
        request:
          timeout:
            ms: 18000
    listener:
      missing-topics-fatal: false

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: assign_id
      table-prefix: t_
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.icarus.entity

vl:
  url: http://**************:31097/v1/chat/completions


file:
  linuxBasePath: /home/<USER>/file/
  #  winBasePath: C:\design
  winBasePath: C:\Users\<USER>\IdeaProjects\aip-biggroup-platform-v2-2\aip-designcanvas\src\main\resources\AI-Resources\AIDesigner
  upload:
    base-path: ${file.linuxBasePath}waveTomarkdown/  # 文件存储路径
    access-path: /markdown/  # 文件访问路径

minio:
  endpoint: http://**********:9000
  bucket-name: test-minio
  access-key: cmttMinIoKey300377
  secret-key: cmttSecretKey300377
thread:
  pool:
    core-size: 10
    max-size: 20
    queue-capacity: 100
    keep-alive-seconds: 300

flow:
  timeout: 300000  # 5分钟
node:
  timeout: 30000  # 1分钟

sysmanager:
  jasypt:
    secret: PBEWithMD5AndDES  #JWT加密认证
  init:
    roleId: 1111111111
  totp:
    authentication: true  # 是否启用双因素认证（关闭后会降低系统安全系数，建议打开）
    issuer: ICARUS平台
    imageSize: 200  # 二维码图片大小
    allowedDiscrepancy: 2 # 允许的偏差窗口个数

# 配置接口白名单
whitelist:
  urls:
    - "/aip/api/**"
    - "/aip/login"
    - "/aip/bind"
    - "/aip/free/**"
    - "/aip/assets/**"
    - "/aip/docker/**"
    - "/aip/channel/**"
    - "/aip/monitor/online/count" # 在线人数
    - "/aip/monitor/server/info" # 查看服务器信息
    - "/aip/designer-enum/list" # 查询所有枚举信息
    - "/aip/system/param/get/verificationMethod"
    - "/bi/**"
    - "/aip/vlm/**"
    - "/aip/directory/full-tree/*"
    - "/aip/canvas/get/*"
    - "/aip/designer-enum/list"
    - "/aip/debug/canvas/debug"
    - "/aip/agent/navigation"
    - "/aip/canvas/*"
    - "/aip/directory/*"
  #    - "/**"

  # QPS限流器(支持集群)
  # 设置默认的限流规则，对所有接口生效
  # 也支持对指定接口进行单独配置
rate-limiter:
  enabled: false    # 默认关闭
  default-rule:
    uri: "/**"
    window-size: 20       # 时间窗口大小 单位秒
    max-requests: 40      # 时间窗口内最大请求数
    num-of-windows: 5     # 小窗口个数
    strategy-type: default
  urls:
    - uri: "/aip/sys/user/list"   # 接口1
      window-size: 10
      max-requests: 10
      num-of-windows: 5
      strategy-type: default
    - uri: "/aip/free/**"
      window-size: 20
      max-requests: 20
      num-of-windows: 4
      strategy-type: default
  queue:
    max-size: 2     # 允许等待的请求数
    wait-time: 5    # 等待时间 单位秒  （要设置合理的最长等待时间，举个简单的例子，至少要比网关超时时间短，考虑页面使用体验，页面操作时不应一直等待）


# 日志配置
operation:
  log:
    # 日志保留天数
    retention-days: 30
    # 单表数据量上限
    max-records: 1000000
    # 是否启用定时清理
    enable-schedule-clean: true
    # 定时清理cron表达式
    clean-cron: "0 0 2 * * ?"
    # 是否记录请求体
    log-request-body: true
    # 是否记录响应体
    log-response-body: true
    # 是否记录堆栈信息
    log-stack-trace: true

    # 日志内容配置
    save-request-body: true
    save-response-body: true
    save-stack-trace: true
    max-text-length: 2000  # 文本字段最大长度

    # 敏感信息配置
    sensitive-fields:
      - password
      - cardNo
      - idCard
      - phone

    # 业务配置
    tenant-enabled: true  # 是否启用租户
    default-tenant-id: "000000"  # 默认租户ID

    # 异步配置
    async:
      core-pool-size: 2
      max-pool-size: 5
      queue-capacity: 100

    # 缓冲配置
    buffer:
      size: 1000
      batch-size: 100
      flush-interval: 5000

    # 归档配置
    archive:
      enabled: true
      months-to-keep: 3
      compress: true

    # 性能配置
    performance:
      use-batch: true
      batch-size: 100
      async-query: true

# 添加SSH监控配置
monitor:
  ssh:
    host: **********  # 要监控的服务器IP
    port: 22             # SSH端口
    username: root       # SSH用户名
    password: yss300377#   # SSH密码
  io:
    exec: cat /proc/diskstats | grep 'vda1 ' | awk '{print $6 * 512, $10 * 512}'

# 文件存储服务配置
fss:
  type: minio # fSS 类型 (目前仅支持minio)
  minio: # MinIO 文件存储服务
    endpoint: http://**********:9000 # 服务地址
    access-key: cmttMinIoKey300377 # 访问凭据
    secret-key: cmttSecretKey300377 # 凭据密钥
    bucket-name: test-minio # 存储桶名称

elasticsearch:
  host: **********
  port: 9200
  scheme: http

dashscope:
  api-key: sk-f80333dbad6a4a78a79517fd0749cae1  # 通义千问 API Key
ip:
  address: **********
  myServerUrl: http://**********:9120/

# 运行时配置
runtime:
  mode: normal  # normal: 普通模式, stream: 流模式
  use-stream-mode: false  # 兼容旧配置，设置为true时使用stream模式处理请求

#自定义配置类（开关等）
app-configs:
  default-settings:
    cmtt-canvas-dir-root-id: 1948319806292815873
  switcher:
    ifOpenVlmFilter: false

# 爬虫配置
crawler:
  playwright:
    idle-timeout: 5  # 浏览器空闲超时时间（分钟），超过此时间自动释放资源
    check-interval: 2  # 定时检查间隔（分钟），每隔此时间检查一次是否需要释放资源
    download-path: /aip-home/home/<USER>/playwright-downloads  # Playwright下载路径
    temp-path: ${java.io.tmpdir}/crawler_files   # 临时文件存储路径（使用独立目录避免冲突）
    cleanup-temp-files: true  # 临时文件清理开关
    browsers-path: /aip-home/home/<USER>/  # 浏览器安装目录（可选，留空使用默认路径）