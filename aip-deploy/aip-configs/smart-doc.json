{"serverUrl": "http://*************:9120", "isStrict": false, "allInOne": true, "outPath": "target/openapi", "coverOld": true, "style": "xt256", "projectName": "AIP平台接口文档", "packageFilters": "com.icarus.controller.DesignSysUserController,com.icarus.controller.LoginController,com.icarus.controller.CheckFreeController", "requestHeaders": [{"name": "Authorization", "type": "string", "desc": "JWT认证Token", "required": true}]}