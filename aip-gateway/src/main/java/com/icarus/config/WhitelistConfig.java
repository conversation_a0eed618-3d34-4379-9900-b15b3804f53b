package com.icarus.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * @ClassName WhitelistConfig
 * <AUTHOR>
 * @Date 2025/2/28
 * @Version 1.0
 **/
@Configuration
@ConfigurationProperties(prefix = "whitelist")
public class WhitelistConfig {

    private List<String> urls;

    public List<String> getUrls() {
        return urls;
    }

    public void setUrls(List<String> urls) {
        this.urls = urls;
    }
}
