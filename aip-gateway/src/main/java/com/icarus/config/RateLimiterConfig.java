package com.icarus.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName RateLimiterProperties
 * <AUTHOR>
 * @Date 2025/3/4
 * @Version 1.0
 **/
@Data
@Component
@ConfigurationProperties(prefix = "rate-limiter")
public class RateLimiterConfig {
    /**
     * 限流器开关
     */
    private boolean enabled = true;
    /**
     * 默认限流规则
     */
    private Rule defaultRule;
    /**
     * 各个 API 的限流规则
     */
    private List<Rule> urls = new ArrayList<>();

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Rule {
        /**
         * 接口路径
         */
        private String uri;
        /**
         * 窗口大小（毫秒）
         */
        private long windowSize;
        /**
         * 窗口内允许的最大请求数
         */
        private int maxRequests;
        /**
         * 小窗口数量
         */
        private int numOfWindows;
        /**
         * 限流策略类型
         */
        private String strategyType = "default";
    }
}
