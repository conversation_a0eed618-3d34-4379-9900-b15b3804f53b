package com.icarus.filter;

import cn.hutool.core.text.AntPathMatcher;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.icarus.common.redis.RedisUtil;
import com.icarus.common.rest.BaseResponse;
import com.icarus.config.WhitelistConfig;
import com.icarus.constant.CommonConstant;
import com.icarus.utils.JsonUtils;
import com.icarus.utils.JwtUtil;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebFilter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Objects;

/**
 * 鉴权过滤器，用于验证用户身份和权限
 * @ClassName AuthenticationFilter
 * <AUTHOR>
 * @Date 2025/2/28
 * @Version 1.0
 **/
@Order(2)
@Component
@WebFilter("/*")
public class AuthenticationFilter extends OncePerRequestFilter {

    @Value("${sysmanager.jasypt.secret}")
    private String secret;

    private static final AntPathMatcher MATCHER = new AntPathMatcher();
    private final WhitelistConfig whitelistConfig;
    private final RedisUtil redisUtil;


    /**
     * 全局过滤器，用于验证用户身份和权限。
     * @param whitelistConfig
     * @param redisUtil
     */
    public AuthenticationFilter(WhitelistConfig whitelistConfig, RedisUtil redisUtil) {
        this.whitelistConfig = whitelistConfig;
        this.redisUtil = redisUtil;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String requestURI = request.getRequestURI();
        // 检查是否在白名单中
        if (isWhitelisted(requestURI)) {
            filterChain.doFilter(request, response);
            return;
        }
        String token = request.getHeader(CommonConstant.AUTHORIZATION);

        if (token == null || !token.startsWith(CommonConstant.TOKEN_PREFIX)) {
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.getWriter().write(JsonUtils.objectToJson(new BaseResponse(HttpServletResponse.SC_UNAUTHORIZED,"Token is missing or invalid")));
            return;
        }
        try {
            DecodedJWT decodedJWT;
            try {
                token = token.replace(CommonConstant.TOKEN_PREFIX, "");
                decodedJWT = verifyToken(token);
            } catch (Exception e) {
                throw new JWTVerificationException(JsonUtils.objectToJson(new BaseResponse(HttpServletResponse.SC_UNAUTHORIZED,"Invalid or expired token")));
            }
            String username = decodedJWT.getClaim(CommonConstant.USER_NAME).asString();
            String userId = decodedJWT.getClaim(CommonConstant.USER_ID).asString();
            verifyAuth(userId, token, requestURI);
            // 添加主要的信息
            request.setAttribute(CommonConstant.USER_NAME, username);
            request.setAttribute(CommonConstant.USER_ID, userId);
        } catch (JWTVerificationException e) {
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.setContentType("text/plain; charset=UTF-8");
            response.setCharacterEncoding("UTF-8");
            response.getWriter().write(JsonUtils.objectToJson(new BaseResponse(HttpServletResponse.SC_UNAUTHORIZED,e.getMessage())));
            return;
        } catch(Exception e){
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            // 新增编码设置
            response.setContentType("text/plain; charset=UTF-8");
            response.setCharacterEncoding("UTF-8");
            response.getWriter().write(JsonUtils.objectToJson(new BaseResponse(HttpServletResponse.SC_INTERNAL_SERVER_ERROR,"服务器内部异常")));
            return;
        }
        filterChain.doFilter(request, response);
    }

    /**
     * 白名单内的接口直接放行
     * @param requestURI
     * @return
     */
    private boolean isWhitelisted(String requestURI) {
        return whitelistConfig.getUrls().stream().filter(url -> MATCHER.match(url, requestURI)).findAny().isPresent();
    }

    /**
     * 验证token
     * @param token
     * @return
     */
    private DecodedJWT verifyToken(String token) {
        Algorithm algorithm = Algorithm.HMAC256(secret);
        return JWT.require(algorithm)
                .build()
                .verify(token);
    }

    /**
     * 验证权限
     * @param userId
     * @param token
     * @param requestURI
     */
    private void verifyAuth(String userId, String token, String requestURI) {
        // 获取redis中的key，如果没有，说明过期，提示重新登录
        Object redisToken = redisUtil.get(CommonConstant.PREFIX_USER_TOKEN + userId);
        if(Objects.isNull(redisToken)){
            throw new JWTVerificationException("token失效");
        }
//        if(!token.equals(redisToken)){
//            throw new JWTVerificationException("账号已在其他客户端登录");
//        }
        // 如果后面要做接口权限、权限变更直接踢出等需求，可在此处打开代码
//        String permissionsKey = CommonConstant.PREFIX_USER_PERMISSION + username;
//        Object permissionsObj = redisUtil.get(permissionsKey);
//        // 如果没有权限信息，直接抛出错误
//        if (permissionsObj == null) {
//            throw new JWTVerificationException("用户权限未分配");
//        }
//        // 这里再判断用户权限(包括角色变化，权限变化)有没有发生变化如果有，需要提示并退出登录
//        String permissionsChange = CommonConstant.PREFIX_USER_PERMISSION_CHANGE + username;
//        Object permissionsChangeObj = redisUtil.get(permissionsChange);
//        if(!Objects.isNull(permissionsChangeObj)){
//            // 清除登录相关数据
//            redisUtil.del(permissionsChange, permissionsKey, CommonConstant.PREFIX_USER_TOKEN + username);
//            throw new JWTVerificationException("用户权限发生变化，请重新登录");
//        }
//        List<String> allowedUris = (List<String>) permissionsObj;
//
//        // 判断请求的 URI 是否在用户的权限列表中
//        boolean hasPermission = allowedUris.stream().anyMatch(uri -> MATCHER.match(uri, requestURI));
//        if (!hasPermission) {
//            throw new JWTVerificationException("无权限访问此接口");
//        }
        // 完成之后，续签token时间
        redisUtil.expire(CommonConstant.PREFIX_USER_TOKEN + userId, JwtUtil.EXPIRE_TIME / 1000);
    }

}
