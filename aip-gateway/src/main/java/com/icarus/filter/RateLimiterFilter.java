package com.icarus.filter;

import cn.hutool.core.text.AntPathMatcher;
import com.icarus.config.RateLimiterConfig;
import com.icarus.filter.strategy.RateLimitingStrategy;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Arrays;

/**
 * 限流过滤器
 * @ClassName RateLimiterFilter
 * <AUTHOR>
 * @Date 2025/3/4
 * @Version 1.0
 **/
@Order(1)
@Slf4j
@Component
public class RateLimiterFilter extends OncePerRequestFilter {

    private final RateLimiterConfig rateLimiterConfig;

    private final RateLimitingStrategy[] rateLimitingStrategys;

    public RateLimiterFilter(RateLimiterConfig rateLimiterConfig, RateLimitingStrategy[] rateLimitingStrategy) {
        this.rateLimiterConfig = rateLimiterConfig;
        this.rateLimitingStrategys = rateLimitingStrategy;
    }

    private static final AntPathMatcher MATCHER = new AntPathMatcher();

    /**
     * Lua 脚本（保证原子操作）
     * 兼顾全局限流的准确性和小窗口的平滑控制。
     */
    private static final String LUA_SCRIPT =
            "local key = KEYS[1] " +
                    "local maxCount = tonumber(ARGV[1]) " +
                    "local windowSize = tonumber(ARGV[2]) " +
                    "local currentTime = tonumber(ARGV[3]) " +
                    "local numOfWindows = tonumber(ARGV[4]) " +
                    "local perWindowSize = windowSize / numOfWindows " +  // 计算每个小窗口的大小
                    "local windowId = math.floor(currentTime / perWindowSize) " +

                    "redis.call('ZREMRANGEBYSCORE', key, 0, currentTime - windowSize) " +
                    "local wholeWindowCount = redis.call('ZCOUNT', key, currentTime - windowSize, currentTime) " + // 获取整个窗口的请求数量
                    "local smallWindowCount = redis.call('ZCOUNT', key, windowId * perWindowSize, (windowId + 1) * perWindowSize) " + // 获取当前小窗口的请求数量
                    "if wholeWindowCount < maxCount and smallWindowCount < (maxCount / numOfWindows) then " + // 同时判断整个窗口请求数和当前窗口请求数，防止单个时间点突发流量
                    "   redis.call('ZADD', key, currentTime, currentTime) " +  // 记录当前请求
                    "   redis.call('EXPIRE', key, windowSize / 1000 + 1) " +  // 设置过期时间
                    "   return 1 " +
                    "else " +
                    "   return 0 " +
                    "end";

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
       if(rateLimiterConfig.isEnabled()){
           String requestURI = request.getRequestURI();
           // 获取当前接口的限流规则
           RateLimiterConfig.Rule rule = rateLimiterConfig.getUrls().stream()
                   .filter(url -> MATCHER.match(url.getUri(), requestURI))
                   .findFirst()
                   .orElse(rateLimiterConfig.getDefaultRule());

           // 根据规则中的策略类型选择对应的限流策略
           RateLimitingStrategy strategy = Arrays.stream(rateLimitingStrategys)
                   .filter(s -> s.getStrategyType().equals(rule.getStrategyType()))
                   .findFirst()
                   .orElseGet(() -> {
                       log.warn("未找到匹配的限流策略类型: {}, 使用默认策略", rule.getStrategyType());
                       return Arrays.stream(rateLimitingStrategys)
                               .filter(s -> s.getStrategyType().equals("default"))
                               .findFirst()
                               .orElseGet(() -> rateLimitingStrategys[0]);
                   });
           strategy.doFilterInternal(request, response, filterChain, rule, rateLimiterConfig, LUA_SCRIPT);
       }else{
           filterChain.doFilter(request, response);
       }
    }

}
