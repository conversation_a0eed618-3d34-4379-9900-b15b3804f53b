package com.icarus.filter.strategy.impl;

import com.icarus.common.rest.BaseResponse;
import com.icarus.config.RateLimiterConfig;
import com.icarus.constant.GatewayConstant;
import com.icarus.filter.strategy.RateLimitingStrategy;
import com.icarus.utils.JsonUtils;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Value;

import java.io.IOException;
import java.time.Instant;
import java.util.Arrays;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Semaphore;

/**
 * 基于分布式的Semaphore的排队策略实现
 * 使用Redis进行全局限流，Semaphore进行分布式排队
 * 每个URI都有独立的Semaphore实例
 * <AUTHOR>
 */
@Slf4j
@Component
public class QueueStrategyImpl implements RateLimitingStrategy {
    private static final String STRATEGY_TYPE = "queue";
    private static final String SEMAPHORE_PREFIX = "prefix_queue_semaphore:";
    @Value("${rate-limiter.queue.max-size}")
    private int maxQueueSize;
    @Value("${rate-limiter.queue.wait-time}")
    private long maxWaitTime;
    private final RedisTemplate<String, Object> redisTemplate;
    private final ConcurrentHashMap<String, Semaphore> uriSemaphores = new ConcurrentHashMap<>();
    public QueueStrategyImpl(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public void doFilterInternal(HttpServletRequest request,
                               HttpServletResponse response,
                               FilterChain filterChain,
                               RateLimiterConfig.Rule rule,
                               RateLimiterConfig rateLimiterConfig,
                               String luaScript) throws ServletException, IOException {
        String requestURI = request.getRequestURI();
        String key = GatewayConstant.PREFIX_RATE_KEY + requestURI;
        long start = System.currentTimeMillis();
        // 首先检查是否可以直接通过
        if (checkRedisLimit(key, rule, luaScript)) {
            log.info("直接放行的请求{}", Instant.now());
            filterChain.doFilter(request, response);
            return;
        }

        // 获取该URI对应的信号量(单机)
//        Semaphore semaphore = getSemaphore(requestURI, rule);
        long queueWaitTime = maxWaitTime * 1000;
        boolean acquired = false;

        try {
            // 如果被限流，尝试获取信号量进行排队，如果超时没拿到许可，直接拒绝(单机)
//            acquired = semaphore.tryAcquire(queueWaitTime, TimeUnit.MILLISECONDS);
            // 尝试获取分布式信号量
            acquired = acquireDistributedSemaphore(requestURI, rule);
            if (!acquired) {
                log.warn("Request no permits in semaphore: URI={}, Method={}, IP={}",requestURI, request.getMethod(), request.getRemoteAddr());
                handleRejection(response, "系统繁忙，请稍后重试");
                return;
            }
            // 获取到信号量后，检查Redis限流直到超时
            while (!checkRedisLimit(key, rule, luaScript)) {
                if (System.currentTimeMillis()-start > queueWaitTime) {
                    log.warn("Request timeout while waiting in queue: URI={}, Method={}, IP={}",requestURI, request.getMethod(), request.getRemoteAddr());
                    handleRejection(response, "系统繁忙，请稍后重试");
                    return;
                }
                // 等待一小段时间后重试
                Thread.sleep(50);
            }
            try {
                log.info("通过信号器的请求{}", Instant.now());
                filterChain.doFilter(request, response);
            } catch (Exception e) {
                log.error("Error processing request in queue: {} from {}", requestURI, request.getRemoteAddr(), e);
                handleRejection(response, "处理请求时发生错误");
            }
        } catch (Exception e) {
            Thread.currentThread().interrupt();
            log.error("请求排队过程被中断", e);
        } finally {
            if (acquired) {
                releaseDistributedSemaphore(requestURI);
//                semaphore.release(); // (单机)
            }
        }
    }

    private static void handleRejection(HttpServletResponse response, String message) throws IOException {
        response.setStatus(GatewayConstant.SC_TOO_MANY_REQUESTS);
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write(JsonUtils.objectToJson(new BaseResponse(GatewayConstant.SC_TOO_MANY_REQUESTS, message)));
    }

    /**
     * 获取分布式信号量 (集群)
     * @param uri 请求URI
     * @param rule 限流规则
     * @return 是否获取成功
     */
    private boolean acquireDistributedSemaphore(String uri, RateLimiterConfig.Rule rule) {
        String semaphoreKey = SEMAPHORE_PREFIX  + uri;
        String luaScript =
                "local key = KEYS[1] " +
                        "local permits = tonumber(ARGV[1]) " +
                        "local windowSize = tonumber(ARGV[2]) " +
                        "local current = redis.call('get', key) " +
                        "current = current and tonumber(current) or 0 " +
                        "if current < permits then " +
                        "  redis.call('incr', key) " +
                        "  redis.call('expire', key, windowSize + 1) " +  // 设置过期时间，防止死锁
                        "  return 1 " +
                        "else " +
                            "return 0 " +
                        "end " ;
        try {
            DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>(luaScript, Long.class);
            Long result = redisTemplate.execute(
                    redisScript,
                    Arrays.asList(semaphoreKey),
                    maxQueueSize,
                    rule.getWindowSize()
            );
            boolean acquired = result != null && result > 0;
            if (acquired) {
                log.debug("Acquired distributed semaphore for URI: {}", uri);
            }
            return acquired;
        } catch (Exception e) {
            log.error("获取分布式信号量失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 释放分布式信号量 (lua保证原子性)
     * @param uri 请求URI
     */
    private void releaseDistributedSemaphore(String uri) {
        String semaphoreKey = SEMAPHORE_PREFIX + uri;
        String luaScript =
                "local key = KEYS[1] " +
                        "local current = redis.call('get', key) " +
                        "if current and tonumber(current) > 0 then " +
                        "  redis.call('decr', key) " +
                        "  return 1 " +
                        "else " +
                        "  return 0 " +
                        "end " ;
        try {
            DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>(luaScript, Long.class);
            redisTemplate.execute(redisScript, Arrays.asList(semaphoreKey));
        } catch (Exception e) {
            log.error("释放分布式信号量失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取或创建URI对应的信号量 (单机)
     * @param uri 请求URI
     * @param rule 限流规则
     * @return 对应的信号量实例
     */
    private Semaphore getSemaphore(String uri, RateLimiterConfig.Rule rule) {
        return uriSemaphores.computeIfAbsent(uri, k -> {
            // 使用规则中的配置，如果没有特殊配置则使用默认值
            int permits = rule.getMaxRequests() > 0 ? rule.getMaxRequests() : maxQueueSize;
            log.info("Creating new semaphore for URI: {} with permits: {}", uri, permits);
            return new Semaphore(permits, true);
        });
    }

    /**
     * 检查是否允许请求通过
     * @return 如果返回true，表示请求被允许；如果返回false，表示请求被限流
     */
    private boolean checkRedisLimit(String key, RateLimiterConfig.Rule rule, String luaScript) {
        DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>(luaScript, Long.class);
        Long result = redisTemplate.execute(redisScript,
                Arrays.asList(key),
                rule.getMaxRequests(),
                rule.getWindowSize() * 1000,
                System.currentTimeMillis(),
                rule.getNumOfWindows()
        );
        return result != null && result > 0;
    }

    @Override
    public String getStrategyType() {
        return STRATEGY_TYPE;
    }
}
