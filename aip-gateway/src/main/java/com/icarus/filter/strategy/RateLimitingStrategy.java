package com.icarus.filter.strategy;

import com.icarus.config.RateLimiterConfig;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.data.redis.core.RedisTemplate;

import java.io.IOException;

/**
 * 限流策略
 * @ClassName RateLimitingStrategy
 * <AUTHOR>
 * @Date 2025/3/5
 * @Version 1.0
 **/
public interface RateLimitingStrategy {

    /**
     * 处理限流逻辑
     * @param request 当前请求
     * @param response 响应对象
     * @param filterChain 过滤链
     * @param rule 当前接口的限流规则
     * @param luaScript lua脚本
     * @param rateLimiterConfig 配置类
     * @throws ServletException
     * @throws IOException
     */
    void doFilterInternal(HttpServletRequest request,
                          HttpServletResponse response,
                          Filter<PERSON>hain filterChain,
                          RateLimiterConfig.Rule rule,
                          RateLimiterConfig rateLimiterConfig,
                          String luaScript) throws ServletException, IOException;

    /**
     * 获取策略类型
     * @return 策略类型标识
     */
    String getStrategyType();

}
