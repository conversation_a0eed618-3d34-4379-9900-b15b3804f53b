package com.icarus.filter.strategy.impl;

import com.icarus.common.rest.BaseResponse;
import com.icarus.config.RateLimiterConfig;
import com.icarus.constant.GatewayConstant;
import com.icarus.filter.strategy.RateLimitingStrategy;
import com.icarus.utils.JsonUtils;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.Instant;
import java.util.Arrays;

/**
 * 拒绝策略：超出设置的最大请求数直接拒绝请求
 * @ClassName RejectStrategy
 * <AUTHOR>
 * @Date 2025/3/5
 * @Version 1.0
 **/
@Slf4j
@Component
public class RejectStrategyImpl implements RateLimitingStrategy {

    private static final String STRATEGY_TYPE = "default";

    private final RedisTemplate<String, Object> redisTemplate;


    public RejectStrategyImpl(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public void doFilterInternal(HttpServletRequest request,
                                 HttpServletResponse response,
                                 FilterChain filterChain,
                                 RateLimiterConfig.Rule rule,
                                 RateLimiterConfig rateLimiterConfig,
                                 String luaScript) throws ServletException, IOException {

        String requestURI = request.getRequestURI();
        String key = GatewayConstant.PREFIX_RATE_KEY + requestURI;
        long currentTimeMillis = System.currentTimeMillis();
        // 执行 Lua 脚本
        DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>(luaScript, Long.class);
        Long result = redisTemplate.execute(redisScript, Arrays.asList(key),
                rule.getMaxRequests(), rule.getWindowSize() * 1000,
                currentTimeMillis, rule.getNumOfWindows());

        if (result == null || result == 0) {
            // 超出限流，拒绝请求
            log.warn("Request rejected: Request URI: {} | Method: {} | IP: {} | Time: {} | Rejected: Exceeded rate limit",
                    request.getRequestURI(), request.getMethod(), request.getRemoteAddr(), Instant.now());
            response.setStatus(GatewayConstant.SC_TOO_MANY_REQUESTS);
            response.setContentType("text/plain; charset=UTF-8");
            response.setCharacterEncoding("UTF-8");
            response.getWriter().write(JsonUtils.objectToJson(new BaseResponse(GatewayConstant.SC_TOO_MANY_REQUESTS, "系统繁忙，请稍候重试")));
            return;
        }
        filterChain.doFilter(request, response);
    }

    @Override

    public String getStrategyType() {

        return STRATEGY_TYPE;

    }

}
