package com.icarus.handler;

import com.icarus.common.rest.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.NoHandlerFoundException;

/**
 * @ClassName GlobalExceptionHandler
 * <AUTHOR>
 * @Date 2025/2/28
 * @Version 1.0
 **/
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 参数校验异常
     * @param ex
     * @return
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public BaseResponse handleValidationExceptions(MethodArgumentNotValidException ex) {
//        String errorMsg = ex.getBindingResult()
//                .getAllErrors()
//                .stream()
//                .map(DefaultMessageSourceResolvable::getDefaultMessage)
//                .collect(Collectors.joining(", "));
        String errorMsg = ex.getBindingResult().getAllErrors().stream()
                .findFirst()
                .map(DefaultMessageSourceResolvable::getDefaultMessage)
                .orElse("参数校验失败");
        log.warn("参数校验异常: {}", errorMsg);
        return BaseResponse.badRequest(errorMsg);
    }

    /**
     * 资源不存在异常处理，如：404
     *
     * @param exception 资源不存在异常类
     * @return 返回请求资源不存在响应结果
     */
    @ResponseStatus(code = HttpStatus.NOT_FOUND)
    @ExceptionHandler(value = NoHandlerFoundException.class)
    public BaseResponse notFoundException(NoHandlerFoundException exception) {
        log.warn("请求资源: [{}] 未找到", exception.getRequestURL());
        log.error("error: ", exception);
        return BaseResponse.badRequest("请求资源未找到");
    }

    /**
     * 系统内部异常，如：空指针等，并且打印堆栈信息
     *
     * @param exception 异常类
     * @return 返回系统内部错误响应结果
     */
//    @ResponseStatus(code = HttpStatus.INTERNAL_SERVER_ERROR)
    @ExceptionHandler(value = Exception.class)
    public BaseResponse internalExceptionHandler(Exception exception) {
        log.error("异常类型: [{}], 异常信息: ", exception.getClass().getSimpleName(),exception);
        return BaseResponse.serviceError(exception.getMessage(),null);
    }


}
