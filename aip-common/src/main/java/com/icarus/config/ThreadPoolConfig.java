package com.icarus.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

@Configuration
@Slf4j
public class ThreadPoolConfig {
    
    @Value("${thread.pool.core-size:10}")
    private int corePoolSize;
    
    @Value("${thread.pool.max-size:20}")
    private int maxPoolSize;
    
    @Value("${thread.pool.queue-capacity:100}")
    private int queueCapacity;
    
    @Value("${thread.pool.keep-alive-seconds:300}")
    private int keepAliveSeconds;
    
    @Bean("flowExecutorPool")
    public ThreadPoolExecutor flowExecutorPool() {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
            corePoolSize,
            maxPoolSize,
            keepAliveSeconds,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(queueCapacity),
            new ThreadFactory() {
                private final AtomicInteger threadNumber = new AtomicInteger(1);
                @Override
                public Thread newThread(Runnable r) {
                    Thread thread = new Thread(r, "flow-executor-" + threadNumber.getAndIncrement());
                    thread.setDaemon(false);
                    thread.setPriority(Thread.NORM_PRIORITY);
                    return thread;
                }
            },
            new ThreadPoolExecutor.CallerRunsPolicy()
        );
        
        // 添加线程池监控
        executor.setRejectedExecutionHandler((r, e) -> {
            log.warn("Thread pool is full! Current pool size: {}, Active count: {}, Queue size: {}",
                    e.getPoolSize(), e.getActiveCount(), e.getQueue().size());
            throw new RejectedExecutionException("Thread pool is full!");
        });
        
        return executor;
    }
} 