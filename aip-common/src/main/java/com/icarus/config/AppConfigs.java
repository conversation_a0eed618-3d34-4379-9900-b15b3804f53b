package com.icarus.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Configuration;

import java.util.Objects;

@Data
@Configuration
@ConfigurationProperties(prefix = "app-configs")
public class AppConfigs implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    // aip 配置
    private SwitcherConfig switcher;

    private DefaultSettings defaultSettings = new DefaultSettings();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        AppConfigs.applicationContext = applicationContext;
    }

    // 获取当前实例的方法
    public static AppConfigs getInstance() {
        return Objects.requireNonNull(applicationContext.getBean(AppConfigs.class), "获取app-configs配置失败!");
    }

    public static SwitcherConfig switcher() {
        return Objects.requireNonNull(getInstance().getSwitcher(), "获取自定义开关配置失败!");
    }

    public static DefaultSettings defaultSettings() {
        return Objects.requireNonNull(getInstance().getDefaultSettings(), "获取自定义默认值配置失败!");
    }

    @Data
    public static class SwitcherConfig {
        private Boolean ifOpenVlmFilter;

    }

    @Data
    public static class DefaultSettings {
        private String cmttCanvasDirRootId = "1948319806292815873";
    }

}