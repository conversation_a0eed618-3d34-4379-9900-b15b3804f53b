package com.icarus.config;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;
@Component
@Slf4j
public class ThreadPoolMonitor {
    
    @Autowired
    @Qualifier("flowExecutorPool")
    private ThreadPoolExecutor flowExecutorPool;
    
    @Scheduled(fixedRate = 60000) // 每分钟记录一次
    public void monitorThreadPool() {
        log.info("Thread Pool Stats - " +
                "Pool Size: {}, " +
                "Active Threads: {}, " +
                "Queue Size: {}, " +
                "Completed Tasks: {}, " +
                "Total Tasks: {}",
                flowExecutorPool.getPoolSize(),
                flowExecutorPool.getActiveCount(),
                flowExecutorPool.getQueue().size(),
                flowExecutorPool.getCompletedTaskCount(),
                flowExecutorPool.getTaskCount());
    }
    
    // 可选：添加获取线程池状态的方法，用于监控接口
    public Map<String, Object> getThreadPoolStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("poolSize", flowExecutorPool.getPoolSize());
        stats.put("activeThreads", flowExecutorPool.getActiveCount());
        stats.put("queueSize", flowExecutorPool.getQueue().size());
        stats.put("completedTasks", flowExecutorPool.getCompletedTaskCount());
        stats.put("totalTasks", flowExecutorPool.getTaskCount());
        return stats;
    }
}