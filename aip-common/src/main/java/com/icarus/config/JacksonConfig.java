package com.icarus.config;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.DateSerializer;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * @ClassName JacksonConfig
 * <AUTHOR>
 * @Date 2025/2/28
 * @Version 1.0
 **/
@Configuration
public class JacksonConfig {

    private final static SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
    private final static LocalDateTimeSerializer localDateTimeSerializer = new LocalDateTimeSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        // 创建 JavaTimeModule 并指定日期时间格式
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        // 设置 LocalDateTime 格式化
        javaTimeModule.addSerializer(LocalDateTime.class, localDateTimeSerializer);
        // 忽略未知属性
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 注册该模块
        objectMapper.registerModule(javaTimeModule);
        // SimpleModule用于处理其他类型
        SimpleModule simpleModule = new SimpleModule();
        // Date类型 使用 yyyy-MM-dd 格式化
        simpleModule.addSerializer(Date.class, new DateSerializer(false, dateFormat));
        // 注册SimpleModule
        objectMapper.registerModule(simpleModule);
        // 让 Jackson 以字符串形式输出日期时间，而不是时间戳
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        return objectMapper;
    }
}
