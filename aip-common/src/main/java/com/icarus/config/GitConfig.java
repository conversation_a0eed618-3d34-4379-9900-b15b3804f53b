package com.icarus.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Git配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "git")
public class GitConfig {

    /**
     * git地址
     */
    private String gitUrl;

    /**
     * 分支
     */
    private String branchName;

    /**
     * 本地地址
     */
    private String localPath;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;
} 