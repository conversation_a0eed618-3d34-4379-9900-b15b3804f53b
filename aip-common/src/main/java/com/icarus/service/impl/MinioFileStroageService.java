package com.icarus.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import com.icarus.service.FileStorageService;
import io.minio.*;
import jakarta.annotation.PostConstruct;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import java.io.InputStream;
import java.time.LocalDateTime;
import com.icarus.service.model.FileInfo;


/**
 * MinIO文件存储服务
 * <AUTHOR>
 * @since 2025-03-04
 */
@Component
@ConditionalOnProperty(value = "fss.type", havingValue = "minio")
@ConfigurationProperties(prefix = "fss.minio")
@Data
@Slf4j
public class MinioFileStroageService implements FileStorageService {

    /**
     * 服务Endpoint
     */
    private String endpoint;
    /**
     * 访问凭据
     */
    private String accessKey;
    /**
     * 凭据密钥
     */
    private String secretKey;
    /**
     * 存储桶名称
     */
    private String bucketName;
    /**
     * minioClient
     */
    private MinioClient minioClient;

    // 依赖注入完成之后执行初始化
    @PostConstruct
    public void init() {
        minioClient = MinioClient.builder()
                .endpoint(endpoint)
                .credentials(accessKey, secretKey)
                .build();
    }


    /**
     * 上传文件
     * @param file 表单文件对象
     * @return 文件信息
     */
    @Override
    public FileInfo uploadFile(MultipartFile file,String contents) {
        // 创建存储桶(存储桶不存在)，如果有搭建好的minio服务，建议放在init方法中
        createBucketIfAbsent(bucketName);
        // 文件后缀
        String suffix = FileUtil.getSuffix(file.getOriginalFilename());
        // 文件夹名称
        String dateFolder = DateUtil.format(LocalDateTime.now(), "yyyyMMdd");
        // 文件名称
//        String fileName = IdUtil.simpleUUID()+"_"+file.getName() + "." + suffix;
        //使用原始文件名上传到minio
        String foldPackage = IdUtil.simpleUUID();
        String fileName = file.getOriginalFilename();
        //  try-with-resource 语法糖自动释放流
        try (InputStream inputStream = file.getInputStream()) {
            // 文件上传
            PutObjectArgs putObjectArgs = PutObjectArgs.builder()
                    .bucket(bucketName)
                    .object(dateFolder + "/" +contents + "/"+ foldPackage +"/"+ fileName)
                    .contentType(file.getContentType())
                    .stream(inputStream, inputStream.available(), -1)
                    .build();
            minioClient.putObject(putObjectArgs);
            // 返回文件在 MinIO 中的存储的相对路径
            return new FileInfo(file.getOriginalFilename(), dateFolder + "/" +contents +"/"+ foldPackage +"/"+ fileName);
        } catch (Exception e) {
            log.error("上传文件失败", e);
            throw new RuntimeException(e.getMessage());
        }
    }
    /**
     * 删除文件
     * @param filePath 文件相对路径
     * @return 是否删除成功
     */
    @Override
    public boolean deleteFile(String filePath) {
        Assert.notBlank(filePath, "删除文件路径不能为空");
        try {
            RemoveObjectArgs removeObjectArgs = RemoveObjectArgs.builder()
                    .bucket(bucketName)
                    .object(filePath)
                    .build();
            minioClient.removeObject(removeObjectArgs);
            return true;
        } catch (Exception e) {
            log.error("删除文件失败", e);
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public InputStream getFile(String filePath) {
        try {
            return minioClient.getObject(GetObjectArgs.builder().bucket(bucketName).object(filePath).build());
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
        }
        return null;
    }

    /**
     * 创建存储桶(存储桶不存在)
     *
     * @param bucketName 存储桶名称
     */
    @SneakyThrows
    private void createBucketIfAbsent(String bucketName) {
        BucketExistsArgs bucketExistsArgs = BucketExistsArgs.builder().bucket(bucketName).build();
        if (!minioClient.bucketExists(bucketExistsArgs)) {
            MakeBucketArgs makeBucketArgs = MakeBucketArgs.builder().bucket(bucketName).build();
            minioClient.makeBucket(makeBucketArgs);
            SetBucketPolicyArgs setBucketPolicyArgs = SetBucketPolicyArgs
                    .builder()
                    .bucket(bucketName)
                    .build();
            minioClient.setBucketPolicy(setBucketPolicyArgs);
        }
    }

}
