package com.icarus.service;

import com.icarus.service.model.FileInfo;
import org.springframework.web.multipart.MultipartFile;
import java.io.InputStream;

/**
 * 文件存储服务
 * <AUTHOR>
 * @since 2025-03-04
 */
public interface FileStorageService {

    /**
     * 上传文件
     * @param file 表单文件对象
     * @return 文件信息
     */
    FileInfo uploadFile(MultipartFile file,String contents);
    /**
     * 删除文件
     *
     * @param filePath 文件完整URL
     * @return 删除结果
     */
    boolean deleteFile(String filePath);

    /**
     * 获取文件流
     * @param filePath 文件完整URL
     * @return InputStream
     */
    InputStream getFile(String filePath);

}
