package com.icarus.enums;

import lombok.Getter;

/**
 * 消息组件类型枚举
 * 
 * 命名规则：
 * - 1xxx: 基础消息类
 * - 2xxx: 数据展示类
 * - 3xxx: 多媒体类
 * - 4xxx: 交互类
 */
@Getter
public enum MessageType {
    
    // 基础消息类 (1xxx)
    MESSAGE_1001("Message1001", "基础文本消息组件"),
    MESSAGE_1002("Message1002", "带示例的标准消息组件"),
    MESSAGE_1003("Message1003", "图文混合消息组件"),
    
    // 数据展示类 (2xxx)
    MESSAGE_2001("Message2001", "表格消息组件"),
    MESSAGE_2002("Message2002", "图表消息组件"),
    MESSAGE_2003("Message2003", "增强型Markdown组件"),
    
    // 多媒体类 (3xxx)
    MESSAGE_3001("Message3001", "语音消息组件"),
    MESSAGE_3002("Message3002", "图片组消息组件"),
    
    // 交互类 (4xxx)
    MESSAGE_4001("Message4001", "按钮组消息组件"),
    MESSAGE_4002("Message4002", "表单消息组件");

    private final String code;
    private final String desc;

    MessageType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据组件代码获取对应的枚举值
     * 
     * @param code 组件代码（如 "Message1001"）
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static MessageType fromCode(String code) {
        for (MessageType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}