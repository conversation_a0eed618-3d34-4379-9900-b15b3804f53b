package com.icarus.utils;

import cn.hutool.core.util.StrUtil;
import com.icarus.constant.CommonConstant;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Objects;


/**
 * <p>
 * 权限工具类
 * </p>
 *
 * <AUTHOR>
 * @since 2023/12/07
 */
@Slf4j
public class AuthUtil {


    /**
     * <p>
     * 得到用户代码
     * </p>
     *
     * @return {@link String}
     * <AUTHOR> by 2023/12/07
     */
    public static String getUserCode() {
        return getUserCode("");
    }

    /**
     * <p>
     * 得到用户代码
     * </p>
     *
     * @return {@link String}
     * <AUTHOR> by 2023/12/07
     */
    public static String getUserCode(String defaultValue) {
        final RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
			if (StrUtil.isEmpty(defaultValue)) {
				return CommonConstant.SYSTEM_OPERATION;
			}
            return defaultValue;
        }
        HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
        String userCode = defaultValue;
        if (request.getHeader("userCode") != null) {
            userCode = request.getHeader("userCode");
        }
        if (StrUtil.isEmpty(userCode)) {
            userCode = Objects.toString(request.getAttribute("userCode"), null);
        }
		if (StrUtil.isEmpty(userCode)) {
			userCode = CommonConstant.SYSTEM_OPERATION;
		}
        if (StrUtil.isEmpty(userCode)) {
            return getUserId();
        }
        return userCode;
    }


    /**
     * <p>
     * 得到用户id
     * </p>
     *
     * @param defaultValue default value
     * @return {@link String}
     * <AUTHOR> by 2023/12/07
     */
    public static String getUserId(String defaultValue) {
        final RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
			if (StrUtil.isEmpty(defaultValue)) {
				return CommonConstant.SYSTEM_OPERATION;
			}
            return defaultValue;
        }
        HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
        String userId = defaultValue;
        if (request.getHeader("userId") != null) {
            userId = request.getHeader("userId");
        }
        if (StrUtil.isEmpty(userId)) {
            userId = Objects.toString(request.getAttribute("userId"), null);
        }
		if (StrUtil.isEmpty(userId)) {
			userId = CommonConstant.SYSTEM_OPERATION;
		}
        return userId;
    }

    public static String getUserId() {
        return getUserId("");
    }


    /**
     * <p>
     * 获取用户名
     * </p>
     *
     * @param defaultValue default value
     * @return {@link String}
     * <AUTHOR> by 2023/12/07
     */
    public static String getUsername(String defaultValue) {
        final RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            return defaultValue;
        }
        HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
        String userName = defaultValue;
        if (request.getHeader("userName") != null) {
            userName = request.getHeader("userName");
            try {
                userName = URLDecoder.decode(userName, "UTF-8");
            } catch (UnsupportedEncodingException e) {
                log.error("解码失败", e);
                // ignore
            }
        }
        if (StrUtil.isEmpty(userName)) {
            userName = Objects.toString(request.getAttribute("username"), null);
        }
        return userName;
    }

    public static String getUsername() {
        return getUsername("");
    }

	/**
	 * <p>
	 * 获取用户电子邮件
	 * </p>
	 *
	 * @param defaultValue 默认值
	 * @return {@link String }
	 * <AUTHOR> by 2024-03-22
	 */
	public static String getUserEmail(String defaultValue) {
		final RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
		if (requestAttributes == null) {
			return defaultValue;
		}
		HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
		String userEmail = defaultValue;
		if (request.getHeader("userEmail") != null) {
			userEmail = request.getHeader("userEmail");
			try {
				userEmail = URLDecoder.decode(userEmail, "UTF-8");
			} catch (UnsupportedEncodingException e) {
				log.error("解码失败", e);
				// ignore
			}
		}
		return userEmail;
	}

	public static String getUserEmail() {
		return getUserEmail("");
	}
}
