package com.icarus.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.dataformat.yaml.YAMLMapper;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.dom4j.Document;
import org.dom4j.io.SAXReader;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 文件内容读取工具类
 * 支持多种格式文件的读取，包括：TXT、CSV、XLS、XLSX、DOC、DOCX、PDF、JSON、XML、YAML等
 * 所有读取的内容都会被转换为字符串格式，结构化数据会被转换为JSON格式
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class FileContentReader {

    /**
     * 读取文件内容
     * 根据文件类型调用相应的读取方法
     *
     * @param file     要读取的文件
     * @param fileType 文件类型（TXT, CSV, XLS, XLSX, DOC, DOCX, PDF, JSON, XML, YAML）
     * @param charset  字符编码（用于文本文件的读取）
     * @return 文件内容，对于结构化文件返回JSON格式的字符串
     * @throws Exception 如果文件读取过程中发生错误
     */
    public static String readContent(File file, String fileType, String charset) throws Exception {
        switch (fileType) {
            case "TXT":
                return readTextFile(file, charset);
            case "CSV":
                return readCsvFile(file, charset);
            case "XLS":
                return readXlsFile(file);
            case "XLSX":
                return readXlsxFile(file);
            case "DOC":
                return readDocFile(file);
            case "DOCX":
                return readDocxFile(file);
            case "PDF":
                return readPdfFile(file);
            case "JSON":
                return readJsonFile(file, charset);
            case "XML":
                return readXmlFile(file);
            case "YAML":
                return readYamlFile(file, charset);
            default:
                throw new IllegalArgumentException("不支持的文件类型: " + fileType);
        }
    }

    /**
     * 读取文本文件
     * 使用指定的字符编码读取文本文件内容
     *
     * @param file    文本文件
     * @param charset 字符编码
     * @return 文件内容字符串
     */
    private static String readTextFile(File file, String charset) {
        return FileUtil.readString(file, Charset.forName(charset));
    }

    /**
     * 读取CSV文件
     * 将CSV文件内容解析为JSON数组格式
     *
     * @param file    CSV文件
     * @param charset 字符编码
     * @return JSON数组格式的字符串
     * @throws IOException 如果文件读取失败
     */
    private static String readCsvFile(File file, String charset) throws IOException {
        try (CSVParser parser = CSVParser.parse(file, Charset.forName(charset), CSVFormat.DEFAULT)) {
            return JSONUtil.toJsonStr(parser.getRecords());
        }
    }

    /**
     * 读取旧版Excel文件（.xls格式）
     * 读取第一个工作表的内容，将其转换为JSON数组格式
     *
     * @param file Excel文件（.xls格式）
     * @return JSON数组格式的字符串
     * @throws IOException 如果文件读取失败
     */
    private static String readXlsFile(File file) throws IOException {
        List<List<String>> data = new ArrayList<>();
        try (HSSFWorkbook workbook = new HSSFWorkbook(new FileInputStream(file))) {
            Sheet sheet = workbook.getSheetAt(0);
            for (Row row : sheet) {
                List<String> rowData = new ArrayList<>();
                for (Cell cell : row) {
                    rowData.add(getCellValueAsString(cell));
                }
                data.add(rowData);
            }
        }
        return JSONUtil.toJsonStr(data);
    }

    /**
     * 读取新版Excel文件（.xlsx格式）
     * 读取第一个工作表的内容，将其转换为JSON数组格式
     *
     * @param file Excel文件（.xlsx格式）
     * @return JSON数组格式的字符串
     * @throws IOException 如果文件读取失败
     */
    private static String readXlsxFile(File file) throws IOException {
        List<List<String>> data = new ArrayList<>();
        try (XSSFWorkbook workbook = new XSSFWorkbook(new FileInputStream(file))) {
            Sheet sheet = workbook.getSheetAt(0);
            for (Row row : sheet) {
                List<String> rowData = new ArrayList<>();
                for (Cell cell : row) {
                    rowData.add(getCellValueAsString(cell));
                }
                data.add(rowData);
            }
        }
        return JSONUtil.toJsonStr(data);
    }

    /**
     * 获取Excel单元格的值
     * 根据单元格类型返回相应的字符串值
     *
     * @param cell Excel单元格
     * @return 单元格的字符串值
     */
    private static String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getLocalDateTimeCellValue().toString();
                }
                return String.valueOf(cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    /**
     * 读取PDF文件
     * 提取PDF文件中的文本内容
     *
     * @param file PDF文件
     * @return PDF文件的文本内容
     * @throws IOException 如果文件读取失败
     */
    private static String readPdfFile(File file) throws IOException {
        try (PDDocument document = Loader.loadPDF(file)) {
            PDFTextStripper stripper = new PDFTextStripper();
            return stripper.getText(document);
        }
    }

    /**
     * 读取旧版Word文档（.doc格式）
     * 提取Word文档中的文本内容
     *
     * @param file Word文档（.doc格式）
     * @return 文档的文本内容
     * @throws IOException 如果文件读取失败
     */
    private static String readDocFile(File file) throws IOException {
        try (FileInputStream fis = new FileInputStream(file);
                HWPFDocument document = new HWPFDocument(fis);
                WordExtractor extractor = new WordExtractor(document)) {

            return String.join("\n", extractor.getParagraphText());
        }
    }

    /**
     * 读取新版Word文档（.docx格式）
     * 提取Word文档中的文本内容
     *
     * @param file Word文档（.docx格式）
     * @return 文档的文本内容
     * @throws IOException 如果文件读取失败
     */
    private static String readDocxFile(File file) throws IOException {
        try (XWPFDocument document = new XWPFDocument(new FileInputStream(file))) {
            return document.getParagraphs().stream()
                    .map(XWPFParagraph::getText)
                    .collect(Collectors.joining("\n"));
        }
    }

    /**
     * 读取JSON文件
     * 验证JSON格式并返回格式化的JSON字符串
     *
     * @param file    JSON文件
     * @param charset 字符编码
     * @return 格式化的JSON字符串
     */
    private static String readJsonFile(File file, String charset) {
        String content = FileUtil.readString(file, Charset.forName(charset));
        // 验证JSON格式
        return JSONUtil.parseObj(content).toString();
    }

    /**
     * 读取XML文件
     * 解析XML文件并返回其字符串表示
     *
     * @param file XML文件
     * @return XML内容的字符串表示
     * @throws Exception 如果XML解析失败
     */
    private static String readXmlFile(File file) throws Exception {
        SAXReader reader = new SAXReader();
        Document document = reader.read(file);
        return document.asXML();
    }

    /**
     * 读取YAML文件
     * 将YAML内容转换为JSON格式
     *
     * @param file    YAML文件
     * @param charset 字符编码
     * @return JSON格式的字符串
     * @throws Exception 如果YAML解析失败
     */
    private static String readYamlFile(File file, String charset) throws Exception {
        String content = FileUtil.readString(file, Charset.forName(charset));
        YAMLMapper yamlMapper = new YAMLMapper();
        // 将YAML转换为JSON
        Object obj = yamlMapper.readValue(content, Object.class);
        return JSONUtil.toJsonStr(obj);
    }
}