package com.icarus.utils;

import com.icarus.common.minio.MinioUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.Arrays;
import java.util.List;

/**
 * Minio工具类使用示例
 * 此类仅作为示例，展示如何使用MinioUtil进行文件操作
 */
@Slf4j
public class MinioExample {

    @Resource
    private MinioUtil minioUtil;

    /**
     * 上传文件示例
     *
     * @param file 文件
     * @return 文件访问URL
     */
    public String uploadExample(MultipartFile file) {
        // 上传到默认存储桶，自动生成对象名
        String url = minioUtil.uploadFile(file, null, null);
        log.info("文件上传成功，访问URL: {}", url);
        return url;
    }

    /**
     * 上传文件到指定存储桶示例
     *
     * @param file 文件
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @return 文件访问URL
     */
    public String uploadToBucketExample(MultipartFile file, String bucketName, String objectName) {
        // 上传到指定存储桶，指定对象名
        String url = minioUtil.uploadFile(file, bucketName, objectName);
        log.info("文件上传成功，访问URL: {}", url);
        return url;
    }

    /**
     * 上传字节数组示例
     *
     * @param bytes 字节数组
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @param contentType 内容类型
     * @return 文件访问URL
     */
    public String uploadBytesExample(byte[] bytes, String bucketName, String objectName, String contentType) {
        // 上传字节数组
        String url = minioUtil.uploadFile(bytes, bucketName, objectName, contentType);
        log.info("字节数组上传成功，访问URL: {}", url);
        return url;
    }

    /**
     * 下载文件示例
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @return 文件流
     */
    public InputStream downloadExample(String bucketName, String objectName) {
        // 下载文件
        InputStream inputStream = minioUtil.downloadFile(bucketName, objectName);
        if (inputStream != null) {
            log.info("文件下载成功");
        } else {
            log.error("文件下载失败");
        }
        return inputStream;
    }

    /**
     * 删除文件示例
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @return 是否删除成功
     */
    public boolean deleteExample(String bucketName, String objectName) {
        // 删除文件
        boolean result = minioUtil.deleteFile(bucketName, objectName);
        if (result) {
            log.info("文件删除成功");
        } else {
            log.error("文件删除失败");
        }
        return result;
    }

    /**
     * 批量删除文件示例
     *
     * @param bucketName 存储桶名称
     * @param objectNames 对象名称列表
     * @return 删除失败的对象列表
     */
    public List<String> batchDeleteExample(String bucketName, String... objectNames) {
        // 批量删除文件
        List<String> errorList = minioUtil.deleteFiles(bucketName, Arrays.asList(objectNames));
        if (errorList.isEmpty()) {
            log.info("所有文件删除成功");
        } else {
            log.error("部分文件删除失败: {}", errorList);
        }
        return errorList;
    }

    /**
     * 获取文件URL示例
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @return 文件访问URL
     */
    public String getUrlExample(String bucketName, String objectName) {
        // 获取文件URL
        String url = minioUtil.getFileUrl(bucketName, objectName);
        log.info("文件访问URL: {}", url);
        return url;
    }

    /**
     * 复制文件示例
     *
     * @param sourceBucketName 源存储桶名称
     * @param sourceObjectName 源对象名称
     * @param targetBucketName 目标存储桶名称
     * @param targetObjectName 目标对象名称
     * @return 是否复制成功
     */
    public boolean copyExample(String sourceBucketName, String sourceObjectName, 
                              String targetBucketName, String targetObjectName) {
        // 复制文件
        boolean result = minioUtil.copyFile(sourceBucketName, sourceObjectName, 
                                           targetBucketName, targetObjectName);
        if (result) {
            log.info("文件复制成功");
        } else {
            log.error("文件复制失败");
        }
        return result;
    }
} 