package com.icarus.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.util.Map;

public class JsonPathParser {
    private static final Logger log = LoggerFactory.getLogger(JsonPathParser.class);

    /**
     * 解析路径表达式并从 JSONObject 中获取值
     * 支持格式：
     * - path.nodeId.to.field
     * 例如：bff9fa80-b68f-498f-90f9-ada27c46e9e7.body
     */
    public static Object parseJsonPath(JSONObject json, String expression) {
        if (!StringUtils.hasText(expression)) {
            throw new IllegalArgumentException("Expression cannot be empty");
        }

        // 1. 分割路径
        String[] segments = expression.split("\\.");
        if (segments.length < 1) {
            throw new IllegalArgumentException("Invalid path format");
        }

        // 2. 如果json是普通对象且只有一层路径，直接返回值
        if (!json.containsKey("output") && segments.length == 1) {
            return json.get(segments[0]);
        }

        // 3. 处理多层路径
        Object current = json;
        // 从第一段开始解析（因为nodeId已经在BaseNode中处理过了）
        for (String segment : segments) {
            if (current == null) {
                return null;
            }

            // 处理数组访问 example[0]
            if (segment.contains("[") && segment.contains("]")) {
                current = handleArrayAccess(current, segment);
            } else {
                // 普通字段访问
                if (current instanceof JSONObject) {
                    current = ((JSONObject) current).get(segment);
                } else if (current instanceof String && JSONUtil.isJsonObj((String) current)) {
                    // 尝试将字符串解析为 JSONObject
                    try {
                        JSONObject jsonObj = JSONUtil.parseObj((String) current);
                        current = jsonObj.get(segment);
                    } catch (Exception e) {
                        log.warn("Failed to parse string as JSONObject: {}", current, e);
                        return null;
                    }
                } else {
                    return null;
                }
            }
        }
        
        return current;
    }

    /**
     * 处理数组访问
     */
    private static Object handleArrayAccess(Object current, String segment) {
        String arrayName = segment.substring(0, segment.indexOf("["));
        String indexStr = segment.substring(
                segment.indexOf("[") + 1,
                segment.indexOf("]")
        );
        int index = Integer.parseInt(indexStr);

        // 先获取数组字段
        Object arrayField = ((JSONObject) current).get(arrayName);
        if (arrayField instanceof JSONArray) {
            return ((JSONArray) arrayField).get(index);
        }
        
        throw new IllegalArgumentException("Not an array field: " + arrayName);
    }

    /**
     * 从表达式中提取节点ID和路径
     */
    public static PathInfo extractPathInfo(String expression) {
        if (!StringUtils.hasText(expression)) {
            throw new IllegalArgumentException("Expression cannot be empty");
        }

        String[] segments = expression.split("\\.");
        if (segments.length < 1) {
            throw new IllegalArgumentException("Invalid path format");
        }

        // 提取节点ID（第一段）
        String nodeId = segments[0];

        // 构建剩余路径
        StringBuilder remainingPath = new StringBuilder();
        for (int i = 1; i < segments.length; i++) {
            if (i > 1) {
                remainingPath.append(".");
            }
            remainingPath.append(segments[i]);
        }

        return new PathInfo(nodeId, remainingPath.toString());
    }

    /**
     * 路径信息类
     */
    public static class PathInfo {
        private final String nodeId;
        private final String path;

        public PathInfo(String nodeId, String path) {
            this.nodeId = nodeId;
            this.path = path;
        }

        public String getNodeId() {
            return nodeId;
        }

        public String getPath() {
            return path;
        }

        @Override
        public String toString() {
            return "PathInfo{" +
                    "nodeId='" + nodeId + '\'' +
                    ", path='" + path + '\'' +
                    '}';
        }
    }

    /**
     * 根据 PathInfo 从 context 中获取值
     * @param pathInfo 路径信息
     * @param context 上下文数据
     * @return 找到的值
     */
    public static Object getValueByPathInfo(PathInfo pathInfo, Map<String, Object> context) {
        if (pathInfo == null || context == null) {
            return null;
        }

        try {
            // 先获取指定 nodeId 的数据
            Object nodeData = context.get(pathInfo.getNodeId()+".outputs");
            if (nodeData == null) {
                return null;
            }

            // 如果有具体路径，继续解析
            if (StrUtil.isNotEmpty(pathInfo.getPath())) {
                // 将数据转换为 JSONObject
                JSONObject jsonObject = JSONUtil.parseObj(nodeData);
                // 使用 JSONPath 获取具体值
                return JSONUtil.getByPath(jsonObject, pathInfo.getPath());
            }

            // 如果没有具体路径，直接返回节点数据
            return nodeData;
        } catch (Exception e) {
            log.error("Failed to get value by path info: {}", pathInfo, e);
            return null;
        }
    }
}