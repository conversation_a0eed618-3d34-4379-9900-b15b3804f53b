package com.icarus.utils;

import cn.hutool.json.JSONUtil;
import com.icarus.common.rest.AISearchResponse;
import com.icarus.constant.MessageStatus;
import com.icarus.starter.channel.queue.sse.SseEmitterManager;
import com.icarus.vo.AIHelperResponse;

public class SSEUtils {

    public static void sendTextMessage(String sessionId, String response) {
        SseEmitterManager.send(sessionId, response);
    }

    public static void sendTextMessage(String messageId,String sessionId, String type, Object text, String status) {
        AISearchResponse<Object> answer = AISearchResponse.builder().
                dialogueId(sessionId).
                messageId(messageId).
                type(type).
                data(text).
                status(status).
                build();

		sendTextMessage(sessionId,JSONUtil.toJsonStr(answer));
    }

	public static void sendTextMessage(String sessionId,String requestId,String tabId,String type, Object text, String status) {
		AIHelperResponse<Object> answer = AIHelperResponse.builder().
				dialogueId(sessionId).
				requestId(requestId).
				tabId(tabId).
				type(type).
				data(text).
				status(status).
				build();
		sendTextMessage(sessionId,JSONUtil.toJsonStr(answer));
	}

	public static void logSend(String pre,String info,String messageId,String sessionId, String logType) {


		StringBuilder data = new StringBuilder();
		data.append("正在"+pre);
		data.append("\n");
		data.append(info);


		AISearchResponse<Object> response = AISearchResponse.builder().
				dialogueId(sessionId).
				messageId(messageId).
				type(logType).
				build();
		response.setData(data.toString());
		response.setStatus(MessageStatus.SUCCESS.getStatus());
		SSEUtils.sendTextMessage(sessionId, JSONUtil.toJsonStr(response));
//		System.out.println("--------------------aaa-----"+data.toString());

	}


}
