package com.icarus.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static java.time.format.DateTimeFormatter.ofPattern;

/**
 * @ClassName JsonUtils
 * @Description
 * <AUTHOR>
 * @Date 2023/5/22 15:49
 * @Version 1.0
 **/
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class JsonUtils {

    /** 标准日期格式：yyyy-MM-dd HH:mm:ss */
    public static final String NORM_DATETIME_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static final DateTimeFormatter DATETIME = ofPattern(NORM_DATETIME_PATTERN);

    /**
     * 序列化 Mapper 对象
     */
    private static final ObjectMapper DEFAULT_OBJECT_MAPPER;

    static {
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        ObjectMapper objectMapper = new ObjectMapper();

        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(DATETIME));
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(DATETIME));

        objectMapper
            .registerModule(javaTimeModule)
            .setDateFormat(new SimpleDateFormat(NORM_DATETIME_PATTERN))
            .configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);

        DEFAULT_OBJECT_MAPPER = objectMapper;
    }

    /**
     * 将对象转换称 Json 字符串
     *
     * @param obj 需要序列化的对象
     * @return 返回 Json 字符串
     */
    public static String objectToJson(Object obj) {
        try {
            return DEFAULT_OBJECT_MAPPER.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("writeValueAsString error", e);
        }
    }

    /**
     * 将对象转换成 Map
     *
     * @param object 待转换的对象
     * @return 返回对应的 Map
     */
    public static Map<String, Object> objectToMap(Object object) {
        return jsonToObject(objectToJson(object), Map.class);
    }

    /**
     * 将 Json 字符串转换成 Map
     *
     * @param jsonString 需要转化的 Json 字符串
     * @return 返回 Map 对象
     */
    public static Map<String, Object> jsonToMap(String jsonString) {
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        try {
            return mapper.readValue(jsonString, Map.class);
        } catch (IOException e) {
            throw new RuntimeException("jsonToMap#readValue error", e);
        }
    }

    /**
     * 将 Json 字符串转换成对象
     *
     * @param jsonStr 需要转换的字符串
     * @param clazz 转换的目标类型
     * @param <T> 目标类
     * @return 返回转换的对象
     */
    public static <T> T jsonToObject(String jsonStr, Class<T> clazz) {
        DEFAULT_OBJECT_MAPPER.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
        DEFAULT_OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        try {
            return DEFAULT_OBJECT_MAPPER.readValue(jsonStr, clazz);
        } catch (IOException e) {
            throw new RuntimeException("jsonToObject error", e);
        }
    }

    /**
     * 将 Json 字符串转换成泛型对象
     *
     * @param jsonStr 需要转换的字符串
     * @param typeReference 转换的目标类型
     * @param <T> 目标类
     * @return 返回转换的对象
     * <AUTHOR>
     */
    public static <T> T jsonToObject(String jsonStr, TypeReference<T> typeReference) {
        DEFAULT_OBJECT_MAPPER.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
        DEFAULT_OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        try {
            return DEFAULT_OBJECT_MAPPER.readValue(jsonStr, typeReference);
        } catch (IOException e) {
            throw new RuntimeException("jsonToObject error", e);
        }
    }

    /**
     * 将 Json 字符串转换成 List 对象
     *
     * @param jsonArrayStr 待转换的 Json 字符串
     * @param clazz 集合中的类型
     * @param <T> 泛型类型
     * @return 返回转换之后的集合
     */
    public static <T> List<T> jsonToList(String jsonArrayStr, Class<T> clazz) {
        JavaType javaType = getCollectionType(clazz);
        try {
            return DEFAULT_OBJECT_MAPPER.readValue(jsonArrayStr, javaType);
        } catch (IOException e) {
            throw new RuntimeException("jsonToList error", e);
        }
    }

    /**
     * 获取集合类型
     *
     * @param elementClasses 元素类型
     * @return 返回集合的类型
     */
    private static JavaType getCollectionType(Class<?>... elementClasses) {
        return DEFAULT_OBJECT_MAPPER.getTypeFactory().constructParametricType(ArrayList.class, elementClasses);
    }
}
