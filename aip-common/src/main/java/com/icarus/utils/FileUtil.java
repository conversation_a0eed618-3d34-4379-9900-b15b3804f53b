package com.icarus.utils;

import lombok.extern.slf4j.Slf4j;
import org.mozilla.universalchardet.UniversalDetector;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;


@Slf4j
@Component
public class FileUtil {

	@Value("${file.linuxBasePath}")
	private String linuxBasePath;

	@Value("${file.winBasePath}")
	private String winBasePath;

	private static final String RESOURCES_DIR = String.join(File.separator, "AIP-DesignCanvas", "src", "main", "resources");
	private static final String BASE_PATH = String.join(File.separator, "AI-Resources", "AIDesigner") + File.separator;

	// 定义一个静态变量存储基础路径
	public static final Path AI_DESIGNER_PATH = Paths.get(System.getProperty("user.dir"), 
			"AIP-DesignCanvas",
			"src", 
			"main", 
			"resources",
			"AI-Resources",
			"AIDesigner");

	public String getRootPath(String modelName) {
		String os = System.getProperty("os.name").toLowerCase();
		String rootPath;
		if (os.contains("win")) {
			rootPath = winBasePath + "\\" + modelName;
		} else if (os.contains("nix") || os.contains("nux") || os.contains("linux") || os.contains("mac")) {
			rootPath = linuxBasePath + "/" + modelName;
		} else {
			throw new IllegalArgumentException("未知的系统，无法加载目录");
		}
		return rootPath;
	}

	/**
	 * 获取资源根目录的完整路径
	 * @return 完整的资源目录路径
	 */
	public static String getResourceBasePath() {
		return Paths.get(System.getProperty("user.dir"), RESOURCES_DIR, "AI-Resources", "AIDesigner").toString();
	}

	/**
	 * 获取相对于 resources 目录的路径
	 * @param relativePath 相对路径
	 * @return 标准化的相对路径
	 */
	public static String getResourceRelativePath(String relativePath) {
		return Paths.get(BASE_PATH, relativePath).toString();
	}


	// 判断目录是否存在，不存在则创建
	public static boolean createDirector(String path) {
		File folder = new File(path);
		if (!folder.exists()) {
			boolean success = folder.mkdirs();
			if (success) {
				log.info("{}文件夹创建成功", path);
				return true;
			} else {
				log.info("{}文件夹创建失败",path);
				return false;
			}
		} else {
			log.info("{}文件夹已存在",path);
			return true;
		}
	}


	/**
	 * 根据路径返回文件内容
	 *
	 * @param module module
	 * @param path path
	 * @param fileName fileName
	 * @throws IOException
	 */
	public String loadFile(String module, String path, String fileName) throws IOException {
		String rootPath = getRootPath(module);
		String absolutePath = cn.hutool.core.io.FileUtil.normalize(rootPath + File.separator + path + File.separator + fileName);

		List<String> lines = Files.readAllLines(Paths.get(absolutePath), StandardCharsets.UTF_8);
		StringBuilder fileContent = new StringBuilder();
		for (String line : lines){
			fileContent.append(line);
		}
		return fileContent.toString();
	}

	/**
	 * 通过映射路径读取文件内容
	 * @param mappedPath 映射的文件路径（如 test/22333.json）
	 * @return 文件内容
	 * @throws IOException 如果文件读取失败
	 */
	public String loadMappedFile(String mappedPath) throws IOException {
		try {
			// 规范化路径
			String normalizedPath = mappedPath.replace("\\", "/");
			Path fullPath = Paths.get(AI_DESIGNER_PATH.toString(), normalizedPath);
//			log.info("Reading file from: {}", fullPath.toAbsolutePath());
			log.info("Reading file from: {}", fullPath.toAbsolutePath());


			return new String(Files.readAllBytes(fullPath), StandardCharsets.UTF_8);

		} catch (IOException e) {
			log.error("Failed to read mapped file: {}", mappedPath, e);
			throw e;
		}
	}

	/**
	 * 将InputStream转换为File对象
	 * @param inputStream 输入流
	 * @param fileName 文件名
	 * @return File对象
	 */
	public  static File inputStreamToFile(InputStream inputStream, String fileName) throws IOException {
		// 创建临时文件
		File tempFile = File.createTempFile("temp_", "_" + fileName);
		tempFile.deleteOnExit(); // 程序退出时删除临时文件

		try (FileOutputStream outputStream = new FileOutputStream(tempFile)) {
			byte[] buffer = new byte[8192];
			int bytesRead;
			while ((bytesRead = inputStream.read(buffer)) != -1) {
				outputStream.write(buffer, 0, bytesRead);
			}
		}
		return tempFile;
	}

	/**
	 * 检测文件编码
	 * @param file 要检测的文件
	 * @return 检测到的编码名称
	 */
	public static String detectFileEncoding(File file) throws IOException {
		try (InputStream fis = new java.io.FileInputStream(file)) {
			byte[] buf = new byte[4096];
			UniversalDetector detector = new UniversalDetector(null);
			int nread;
			while ((nread = fis.read(buf)) > 0 && !detector.isDone()) {
				detector.handleData(buf, 0, nread);
			}

			detector.dataEnd();
			String encoding = detector.getDetectedCharset();
			detector.reset();

			if (encoding != null) {
				return encoding;
			}
		}

		// 如果检测失败，返回默认编码
		return StandardCharsets.UTF_8.name();
	}

	/**
	 * 递归删除目录及其内容
	 */
	public static void deleteDirectory(File directory) {
		if (directory == null || !directory.exists()) {
			return;
		}

		try {
			File[] files = directory.listFiles();
			if (files != null) {
				for (File file : files) {
					if (file.isDirectory()) {
						deleteDirectory(file);
					} else {
						boolean deleted = file.delete();
						if (!deleted) {
							log.warn("无法删除文件: {}", file.getAbsolutePath());
						}
					}
				}
			}
			boolean deleted = directory.delete();
			if (!deleted) {
				log.warn("无法删除目录: {}", directory.getAbsolutePath());
			}
		} catch (Exception e) {
			log.error("删除目录时发生错误: {}", directory.getAbsolutePath(), e);
		}
	}
}
