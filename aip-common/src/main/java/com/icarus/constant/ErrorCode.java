package com.icarus.constant;

import lombok.Getter;

/**
 * 错误码枚举
 */
@Getter
public enum ErrorCode {
    
    // 系统级错误 (500-599)
    SYSTEM_ERROR(500, "系统错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),
    TIMEOUT(504, "处理超时"),
    
    // 请求参数错误 (400-499)
    PARAM_INVALID(400, "参数无效"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    
    // 业务错误 (600-699)
    SELECTION_EMPTY(600, "未选择文本"),
    SELECTION_INVALID(601, "文本选择范围无效"),
    FILE_TYPE_NOT_SUPPORTED(602, "不支持的文件类型"),
    CONTENT_TOO_LONG(603, "内容超出长度限制"),
    AI_SERVICE_ERROR(604, "AI服务调用失败"),
    
    // 内容审核错误 (700-799)
    CONTENT_AUDIT_FAILED(700, "内容审核未通过"),
    SENSITIVE_CONTENT(701, "包含敏感内容"),
    
    // 未知错误
    UNKNOWN_ERROR(999, "未知错误");
    
    private final int code;
    private final String message;
    
    ErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
    }
    
    /**
     * 根据错误码获取枚举值
     */
    public static ErrorCode fromCode(int code) {
        for (ErrorCode errorCode : values()) {
            if (errorCode.code == code) {
                return errorCode;
            }
        }
        return UNKNOWN_ERROR;
    }
} 