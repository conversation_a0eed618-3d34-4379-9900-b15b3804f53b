package com.icarus.common.rest;

import com.icarus.constant.ErrorCode;
import lombok.Data;

/**
 * 处理结果响应对象
 */
@Data
public class ProcessResult {
    // 基础信息
    private String messageId;      // 消息ID
    private Long timestamp;        // 响应时间戳
    private String tabId;         // 对应的标签页ID
    private String sessionId;     // 会话ID
    private String requestId;     // 对应的请求ID
    
    // 消息状态
    private Status status;
    
    // 消息组信息（可选）
    private Group group;
    
    // 展示组件
    private Component component;
    
    @Data
    public static class Status {
        private Integer code;              // 状态码：200成功，400请求错误，500服务器错误等
        private String type;              // 消息状态类型：success/error/warning
        private String message;           // 状态描述
    }
    
    @Data
    public static class Group {
        private String groupId;           // 消息组ID
        private Integer index;            // 当前是组内第几条消息
        private Integer total;            // 消息组总数
    }
    
    @Data
    public static class Component {
        private String type;              // 具体前端组件类型
        private Object data;              // 具体内容数据
        private Window window;            // 窗口信息（可选）
    }
    
    @Data
    public static class Window {
        private Position position;
        private Size size;
    }
    
    @Data
    public static class Position {
        private Integer x;
        private Integer y;
    }
    
    @Data
    public static class Size {
        private Integer width;
        private Integer height;
    }
    
    /**
     * 快速创建成功响应
     */
    public static ProcessResult success(String message, Object data) {
        ProcessResult result = new ProcessResult();
        Status status = new Status();
        status.setCode(200);
        status.setType("success");
        status.setMessage(message);
        result.setStatus(status);
        
        Component component = new Component();
        component.setType("Message1001");
        component.setData(data);
        result.setComponent(component);
        
        result.setTimestamp(System.currentTimeMillis());
        return result;
    }
    
    /**
     * 快速创建错误响应
     */
    public static ProcessResult error(ErrorCode errorCode) {
        return error(errorCode, null);
    }
    
    /**
     * 创建带有详细信息的错误响应
     */
    public static ProcessResult error(ErrorCode errorCode, String detail) {
        ProcessResult result = new ProcessResult();
        Status status = new Status();
        status.setCode(errorCode.getCode());
        status.setType("error");
        status.setMessage(detail != null ? errorCode.getMessage() + ": " + detail : errorCode.getMessage());
        result.setStatus(status);
        result.setTimestamp(System.currentTimeMillis());
        return result;
    }
}