package com.icarus.vo;


import lombok.Data;

import java.util.List;

@Data
public class AIHelperRequest {
    // 会话id
    private String dialogueId;
    //1ai搜索2.润色 3.扩写4.缩写5.快速创作6.大纲编写7.总结
    private String type;
    //任务id
    private String taskId;
    //自定义prompt
    private String customPrompt;
    //基础文件id集合
    private List<String> fileIds;
    //生成文件名
    private String resultFileName;
    //目标文件名
    private String targetFileName;
    //基础文件名
    private String baseFileName;
    //原始文本
    private String originalText;
    //问题
    private String question;
    //是否有模板  0是没有模板1是有模板
    private String ifHaveTemplate;
    //是否查存量  0是不查1查
    private String ifSearchElement;
    //加工后文本
    private String formatText;

    //润色风格 1.快速润色2.更正式3.更学术4.党政风
    private String polishStyle;

    private Boolean isChat;


}
