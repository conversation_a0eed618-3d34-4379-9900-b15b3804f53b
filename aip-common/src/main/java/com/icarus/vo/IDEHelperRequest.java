package com.icarus.vo;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;

/**
 * IDE助手请求对象
 * 用于处理各种IDE场景下的AI辅助请求
 */
@Data
public class IDEHelperRequest {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    // 基础信息
    private String tabId;        // 标签页ID
    private String sessionId;    // 会话ID
    private Long timestamp;      // 请求时间戳
    private String requestId;    // 请求唯一标识
    
    // 应用信息
    private AppInfo app;
    
    // 功能信息
    private ActionInfo action;
    
    // 用户问题/输入
    private String question;
    
    // 配置信息
    private ConfigInfo config;
    
    /**
     * 应用信息
     */
    @Data
    public static class AppInfo {
        private String name;          // 应用名称（ WPS应用, Chrome）
        private AppContext context;
    }
    
    /**
     * 应用上下文信息
     */
    @Data
    public static class AppContext {
        private String selectedText;  // 选中的文本（可选）
        private String fileType;      // 文件类型，如.js, .doc（可选）
        private String filePath;      // 文件路径（可选）
        private SelectionRange selection;  // 选择范围（可选）
    }
    
    /**
     * 文本选择范围信息
     */
    @Data
    public static class SelectionRange {
        private Integer startLine;     // 选择起始行
        private Integer startColumn;   // 选择起始列
        private Integer endLine;       // 选择结束行
        private Integer endColumn;     // 选择结束列
    }
    
    /**
     * 功能动作信息
     */
    @Data
    public static class ActionInfo {
        private String type;      // 操作类型（expand: 扩写, abbreviate: 缩写, generateComment: 生成注释）
        private String menuName;  // 菜单名称
    }
    
    /**
     * 配置信息
     */
    @Data
    public static class ConfigInfo {
        private Boolean enableContentAudit;  // 是否启用内容审核
        private Boolean stream;              // 是否使用流式响应（可选）
    }
    @Override
    public String toString() {
        try {
            return OBJECT_MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(this);
        } catch (JsonProcessingException e) {
            return "Error converting to JSON: " + e.getMessage();
        }
    }
}