CREATE TABLE IF NOT EXISTS operation_log (
    -- 基础信息
    id BIGSERIAL PRIMARY KEY,
    operation_time TIMESTAMP NOT NULL,
    description VARCHAR(255),
    operation_type VARCHAR(50),
    execution_time BIGINT,

    -- 用户信息
    username VARCHAR(100),
    real_name VARCHAR(100),
    dept_name VARCHAR(100),
    ip VARCHAR(50),

    -- 请求信息
    request_method VARCHAR(10),
    request_url VARCHAR(255),
    request_params TEXT,
    request_body TEXT,
    content_type VARCHAR(100),
    request_headers TEXT,

    -- 方法信息
    class_name VARCHAR(255),
    method_name VARCHAR(100),
    method_params TEXT,
    return_value TEXT,

    -- 响应信息
    response_status INTEGER,
    response_headers TEXT,
    response_body TEXT,

    -- 客户端信息
    user_agent VARCHAR(500),
    browser_name VARCHAR(50),
    browser_version VARCHAR(50),
    os_name VARCHAR(50),
    device_type VARCHAR(50),

    -- 业务信息
    business_module VARCHAR(100),
    business_type VARCHAR(50),
    business_id VARCHAR(100),
    tenant_id VARCHAR(50)
    );

-- 添加列注释
COMMENT ON COLUMN operation_log.id IS '日志ID';
COMMENT ON COLUMN operation_log.operation_time IS '操作时间';
COMMENT ON COLUMN operation_log.description IS '操作描述';
COMMENT ON COLUMN operation_log.operation_type IS '操作类型';
COMMENT ON COLUMN operation_log.execution_time IS '执行时长(毫秒)';

-- 用户信息
COMMENT ON COLUMN operation_log.username IS '操作人用户名';
COMMENT ON COLUMN operation_log.real_name IS '操作人真实姓名';
COMMENT ON COLUMN operation_log.dept_name IS '部门名称';
COMMENT ON COLUMN operation_log.ip IS '操作人IP地址';

-- 请求信息
COMMENT ON COLUMN operation_log.request_method IS '请求方式';
COMMENT ON COLUMN operation_log.request_url IS '请求URL';
COMMENT ON COLUMN operation_log.request_params IS '请求参数';
COMMENT ON COLUMN operation_log.request_body IS '请求体内容';
COMMENT ON COLUMN operation_log.content_type IS '内容类型';
COMMENT ON COLUMN operation_log.request_headers IS '请求头信息';

-- 方法信息
COMMENT ON COLUMN operation_log.class_name IS '类名';
COMMENT ON COLUMN operation_log.method_name IS '方法名';
COMMENT ON COLUMN operation_log.method_params IS '方法参数';
COMMENT ON COLUMN operation_log.return_value IS '返回值';

-- 响应信息
COMMENT ON COLUMN operation_log.response_status IS 'HTTP响应状态码';
COMMENT ON COLUMN operation_log.response_headers IS '响应头信息';
COMMENT ON COLUMN operation_log.response_body IS '响应体内容';

-- 客户端信息
COMMENT ON COLUMN operation_log.user_agent IS '用户代理字符串';
COMMENT ON COLUMN operation_log.browser_name IS '浏览器名称';
COMMENT ON COLUMN operation_log.browser_version IS '浏览器版本';
COMMENT ON COLUMN operation_log.os_name IS '操作系统名称';
COMMENT ON COLUMN operation_log.device_type IS '设备类型';

-- 业务信息
COMMENT ON COLUMN operation_log.business_module IS '业务模块';
COMMENT ON COLUMN operation_log.business_type IS '业务类型';
COMMENT ON COLUMN operation_log.business_id IS '业务ID';
COMMENT ON COLUMN operation_log.tenant_id IS '租户ID';

-- 基础索引
CREATE INDEX idx_operation_time ON operation_log (operation_time);
CREATE INDEX idx_username ON operation_log (username);

-- 复合索引
CREATE INDEX idx_type_time ON operation_log (operation_type, operation_time);
CREATE INDEX idx_tenant_time ON operation_log (tenant_id, operation_time);
CREATE INDEX idx_business_time ON operation_log (business_module, business_type, operation_time);
CREATE INDEX idx_user_dept ON operation_log (username, dept_name);
CREATE INDEX idx_method_time ON operation_log (request_method, operation_time);
CREATE INDEX idx_status_time ON operation_log (response_status, operation_time);

