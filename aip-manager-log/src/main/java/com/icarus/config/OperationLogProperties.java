package com.icarus.config;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "operation.log")
public class OperationLogProperties {

    /**
     * 日志保留天数，默认30天
     */
    private Integer retentionDays = 30;

    /**
     * 单表数据量上限，默认100万条
     */
    private Long maxRecords = 1000000L;

    /**
     * 是否启用定时清理，默认true
     */
    private Boolean enableScheduleClean = true;

    /**
     * 定时清理cron表达式，默认每天凌晨2点执行
     */
    private String cleanCron = "0 0 2 * * ?";

    /**
     * 是否记录请求体，默认true
     */
    private Boolean logRequestBody = true;

    /**
     * 是否记录响应体，默认true
     */
    private Boolean logResponseBody = true;

    /**
     * 是否记录堆栈信息，默认true
     */
    private Boolean logStackTrace = true;
}