package com.icarus.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.springframework.util.StringUtils;

import java.util.Set;

public class SensitiveDataUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final String MASK = "******";

    public static String maskSensitiveData(String content, Set<String> sensitiveFields) {
        if (!StringUtils.hasText(content) || sensitiveFields == null || sensitiveFields.isEmpty()) {
            return content;
        }

        try {
            JsonNode jsonNode = objectMapper.readTree(content);
            maskNode(jsonNode, sensitiveFields);
            return objectMapper.writeValueAsString(jsonNode);
        } catch (Exception e) {
            return content;
        }
    }

    private static void maskNode(JsonNode node, Set<String> sensitiveFields) {
        if (node.isObject()) {
            ObjectNode objectNode = (ObjectNode) node;
            objectNode.fields().forEachRemaining(entry -> {
                if (sensitiveFields.contains(entry.getKey())) {
                    objectNode.put(entry.getKey(), MASK);
                } else {
                    maskNode(entry.getValue(), sensitiveFields);
                }
            });
        } else if (node.isArray()) {
            node.forEach(element -> maskNode(element, sensitiveFields));
        }
    }
}