package com.icarus.util;

import eu.bitwalker.useragentutils.UserAgent;

public class UserAgentUtil {

    public static String[] parseUserAgent(String userAgentString) {
        UserAgent userAgent = UserAgent.parseUserAgentString(userAgentString);
        String browserInfo = userAgent.getBrowser().getName() + " " + userAgent.getBrowserVersion();
        String osInfo = userAgent.getOperatingSystem().getName();
        String deviceType = userAgent.getOperatingSystem().getDeviceType().getName();

        return new String[]{browserInfo, osInfo, deviceType};
    }
}