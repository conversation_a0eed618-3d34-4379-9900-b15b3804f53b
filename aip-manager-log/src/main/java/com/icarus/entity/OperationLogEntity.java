package com.icarus.entity;

import lombok.Data;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;

/**
 * 操作日志实体类
 */
/**
 * 操作日志实体类
 */
@Data
@TableName("operation_log")
public class OperationLogEntity {
    /**
     * 基础信息
     */
    @TableId(type = IdType.AUTO)
    private Long id;                    // 日志ID
    private LocalDateTime operationTime; // 操作时间
    private String description;         // 操作描述
    private String operationType;       // 操作类型（如：新增、修改、删除、查询等）
    private Long executionTime;         // 执行时长(毫秒)

    /**
     * 用户信息
     */
    private String username;            // 操作人用户名
    private String realName;            // 操作人真实姓名
    private String deptName;            // 部门名称
    private String ip;                  // 操作人IP地址

    /**
     * 请求信息
     */
    private String requestMethod;       // 请求方式（GET、POST等）
    private String requestUrl;          // 请求URL
    private String requestParams;       // 请求参数（URL参数）
    private String requestBody;         // 请求体内容
    private String contentType;         // 内容类型
    private String requestHeaders;      // 请求头信息（JSON格式）

    /**
     * 方法信息
     */
    private String className;           // 类名
    private String methodName;          // 方法名
    private String methodParams;        // 方法参数
    private String returnValue;         // 返回值

    /**
     * 响应信息
     */
    private Integer responseStatus;     // HTTP响应状态码
    private String responseHeaders;     // 响应头信息
    private String responseBody;        // 响应体内容

    /**
     * 客户端信息
     */
    private String userAgent;           // 用户代理字符串
    private String browserName;         // 浏览器名称
    private String browserVersion;      // 浏览器版本
    private String osName;              // 操作系统名称
    private String deviceType;          // 设备类型（PC、Mobile等）

    /**
     * 业务信息
     */
    private String businessModule;      // 业务模块
    private String businessType;        // 业务类型
    private String businessId;          // 业务ID
    private String tenantId;            // 租户ID
}