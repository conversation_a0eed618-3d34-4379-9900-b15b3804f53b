package com.icarus.aspect;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.icarus.annotation.OperationLog;
import com.icarus.config.OperationLogProperties;
import com.icarus.entity.OperationLogEntity;
import com.icarus.service.OperationLogService;
import com.icarus.util.UserAgentUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.time.LocalDateTime;

@Aspect
@Component
public class OperationLogAspect {
    private static final Logger log = LoggerFactory.getLogger("OperationLog");
    private final OperationLogService logService;
    private final ObjectMapper objectMapper;
    private final OperationLogProperties properties;

    public OperationLogAspect(OperationLogService logService, ObjectMapper objectMapper, OperationLogProperties properties) {
        this.logService = logService;
        this.objectMapper = objectMapper;
        this.properties = properties;
    }

    @Around("@annotation(operationLog)")
    public Object around(ProceedingJoinPoint point, OperationLog operationLog) throws Throwable {
        long startTime = System.currentTimeMillis();
        Object result = point.proceed();
        saveLog(point, operationLog, startTime, result);
        return result;
    }

    private void saveLog(ProceedingJoinPoint point, OperationLog operationLog,
                         long startTime, Object result) {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            HttpServletResponse response = attributes.getResponse();

            OperationLogEntity log = new OperationLogEntity();

            // 基本信息
            log.setOperationTime(LocalDateTime.now());
            log.setDescription(operationLog.description());
            log.setOperationType(operationLog.operationType());
            log.setExecutionTime(System.currentTimeMillis() - startTime);

            // 请求信息
            log.setRequestMethod(request.getMethod());
            log.setRequestUrl(request.getRequestURL().toString());
            log.setIp(getClientIp(request));
            log.setClassName(point.getSignature().getDeclaringTypeName());
            log.setMethodName(point.getSignature().getName());

            // 用户信息 - 从请求头或session中获取
            log.setUsername(request.getHeader("X-User-Name"));
            log.setRealName(request.getHeader("X-Real-Name"));
            log.setDeptName(request.getHeader("X-Dept-Name"));

            // 请求头信息
            Map<String, String> headers = new HashMap<>();
            Collections.list(request.getHeaderNames())
                    .forEach(headerName -> headers.put(headerName, request.getHeader(headerName)));
            log.setRequestHeaders(objectMapper.writeValueAsString(headers));
            log.setContentType(request.getContentType());

            // User-Agent信息
            String userAgent = request.getHeader("User-Agent");
            log.setUserAgent(userAgent);
            if (userAgent != null) {
                String[] userAgentInfo = UserAgentUtil.parseUserAgent(userAgent);
                if(!isStringInArray(userAgentInfo,"Unknown")){
                    log.setBrowserName(userAgentInfo[0]);
                    log.setBrowserVersion(userAgentInfo[1]);
                    log.setOsName(userAgentInfo[2]);
                    log.setDeviceType(userAgentInfo[3]);
                }
            }

            // 请求参数
            log.setRequestParams(objectMapper.writeValueAsString(request.getParameterMap()));
            log.setMethodParams(objectMapper.writeValueAsString(point.getArgs()));

            // 根据配置决定是否记录请求体
            if (properties.getLogRequestBody() && request instanceof ContentCachingRequestWrapper) {
                ContentCachingRequestWrapper wrapper = (ContentCachingRequestWrapper) request;
                log.setRequestBody(new String(wrapper.getContentAsByteArray()));
            }

            // 响应信息
            if (response != null) {
                log.setResponseStatus(response.getStatus());
                log.setResponseHeaders(objectMapper.writeValueAsString(response.getHeaderNames()));
            }

            // 根据配置决定是否记录响应体
            if (properties.getLogResponseBody() && result != null) {
                log.setResponseBody(objectMapper.writeValueAsString(result));
                log.setReturnValue(objectMapper.writeValueAsString(result));
            }

            // 业务信息
//            log.setBusinessModule(operationLog.businessModule());
//            log.setBusinessType(operationLog.businessType());

            // 异步保存日志
            logService.saveLogAsync(log);
        } catch (Exception e) {
            log.error("记录操作日志失败", e);
        }
    }

    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }


    public boolean isStringInArray(String[] array, String target) {
        return Arrays.stream(array).anyMatch(str -> str.equals(target));
    }
}