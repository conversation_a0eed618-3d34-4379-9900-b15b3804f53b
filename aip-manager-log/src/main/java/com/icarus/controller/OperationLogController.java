package com.icarus.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.annotation.OperationLog;
import com.icarus.common.rest.BaseResponse;
import com.icarus.entity.OperationLogEntity;
import com.icarus.service.OperationLogService;
//import org.springframework.data.domain.Page;
//import org.springframework.data.domain.PageRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/aip/operation-logs")
@RequiredArgsConstructor
public class OperationLogController {

    private final OperationLogService operationLogService;
    /**
     * 分页查询操作日志
     */
    @GetMapping
    @OperationLog(description = "分页查询日志", operationType = "查询", businessModule = "日志模块")
    public BaseResponse<Page<OperationLogEntity>> queryLogs(
            @RequestParam(required = false) String operationType,
            @RequestParam(required = false) String description,
            @RequestParam(required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @RequestParam(defaultValue = "1") long current,
            @RequestParam(defaultValue = "10") long size) {
        try {
            Page<OperationLogEntity> page = new Page<>(current, size);
            Page<OperationLogEntity> result = operationLogService.queryLogs(operationType, description, startTime, endTime, page);
            return new BaseResponse<>(200, "查询成功", result);
        } catch (Exception e) {
            return BaseResponse.serviceError("查询操作日志失败: " + e.getMessage());
        }
    }

    /**
     * 导出操作日志
     */
    @GetMapping("/export")
    @OperationLog(description = "导出日志", operationType = "导出", businessModule = "日志模块")
    public ResponseEntity<byte[]> exportLogs(
            @RequestParam(required = false) String operationType,
            @RequestParam(required = false) String description,
            @RequestParam(required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {

        byte[] data = operationLogService.exportLogs(operationType, description, startTime, endTime);

        String filename = "操作日志_" +
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) +
                ".xlsx";

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        "attachment; filename=" + new String(filename.getBytes()))
                .contentType(MediaType.parseMediaType("application/vnd.ms-excel"))
                .contentLength(data.length)
                .body(data);
    }

    /**
     * 删除指定日期之前的日志
     */
    @DeleteMapping
    @OperationLog(description = "删除指定日期之前的日志", operationType = "删除", businessModule = "日志模块")
    public BaseResponse<Void> deleteLogsBefore(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime time) {
        try {
            operationLogService.deleteLogsBefore(time);
            return new BaseResponse<>(200, "删除成功", null);
        } catch (Exception e) {
            return BaseResponse.serviceError("删除日志失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除日志
     * @param param 包含要删除的日志ID列表的参数对象
     * @return 删除结果
     */
    @DeleteMapping("/deleteBatch")
    @OperationLog(description = "根据ID批量删除日志", operationType = "删除", businessModule = "日志模块")
    public BaseResponse<Void> deleteBatch(@RequestBody Map<String, List<Long>> param) {
        try {
            List<Long> ids = param.get("ids");
            if (ids == null || ids.isEmpty()) {
                return BaseResponse.badRequest("日志ID列表不能为空");
            }
            operationLogService.removeByIds(ids);
            return new BaseResponse<>(200, "批量删除成功", null);
        } catch (Exception e) {
            return BaseResponse.serviceError("批量删除日志失败: " + e.getMessage());
        }
    }

    /**
     * 获取日志详情
     */
    @GetMapping("/query/{id}")
    @OperationLog(description = "查询日志详情", operationType = "查询", businessModule = "日志模块")
    public BaseResponse<OperationLogEntity> getLog(@PathVariable Long id) {
        try {
            OperationLogEntity log = operationLogService.getById(id);
            if (log != null) {
                return new BaseResponse<>(200, "查询成功", log);
            } else {
                return BaseResponse.badRequest("日志不存在");
            }
        } catch (Exception e) {
            return BaseResponse.serviceError("查询日志详情失败: " + e.getMessage());
        }
    }
}