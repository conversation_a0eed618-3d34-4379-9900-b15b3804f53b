package com.icarus.controller;

import com.icarus.annotation.OperationLog;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api")
public class TestController {

    @OperationLog(description = "测试接口", operationType = "查询")
    @GetMapping("/test")
    public String test() {
        return "Hello World!";
    }

    @OperationLog(description = "测试异常接口", operationType = "异常测试")
    @GetMapping("/testException")
    public String testException() {
        throw new RuntimeException("这是一个测试异常");
    }
}