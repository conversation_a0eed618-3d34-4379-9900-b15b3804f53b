package com.icarus.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.entity.OperationLogEntity;
import com.icarus.mapper.OperationLogMapper;
import com.icarus.service.OperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class OperationLogServiceImpl extends ServiceImpl<OperationLogMapper, OperationLogEntity>
        implements OperationLogService {

    @Override
    public Page<OperationLogEntity> queryLogs(String operationType,
                                              String description,
                                              LocalDateTime startTime,
                                              LocalDateTime endTime,
                                              Page<OperationLogEntity> page) {
        LambdaQueryWrapper<OperationLogEntity> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(operationType != null && !operationType.isEmpty(),
                        OperationLogEntity::getOperationType, operationType)
                .like(description != null && !description.isEmpty(),
                        OperationLogEntity::getDescription, description)
                .ge(startTime != null,
                        OperationLogEntity::getOperationTime, startTime)
                .le(endTime != null,
                        OperationLogEntity::getOperationTime, endTime)
                .orderByDesc(OperationLogEntity::getOperationTime);

        return this.page(page, wrapper);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteLogsBefore(LocalDateTime time) {
        if (time == null) {
            return;
        }
        LambdaQueryWrapper<OperationLogEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.lt(OperationLogEntity::getOperationTime, time);
        this.remove(wrapper);
    }

    @Override
    public byte[] exportLogs(String operationType,
                             String description,
                             LocalDateTime startTime,
                             LocalDateTime endTime) {
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("操作日志");

            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                    "操作时间", "描述", "操作类型", "执行时长(ms)",
                    "用户名", "真实姓名", "部门", "IP地址",
                    "请求方式", "请求URL", "请求参数", "内容类型",
                    "响应状态", "浏览器", "操作系统", "设备类型",
                    "业务模块", "业务类型", "业务ID"
            };

            // 设置列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.setColumnWidth(i, 20 * 256); // 20个字符宽度
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }

            // 查询数据
            LambdaQueryWrapper<OperationLogEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(operationType != null && !operationType.isEmpty(),
                            OperationLogEntity::getOperationType, operationType)
                    .like(description != null && !description.isEmpty(),
                            OperationLogEntity::getDescription, description)
                    .ge(startTime != null,
                            OperationLogEntity::getOperationTime, startTime)
                    .le(endTime != null,
                            OperationLogEntity::getOperationTime, endTime)
                    .orderByDesc(OperationLogEntity::getOperationTime);

            List<OperationLogEntity> logs = this.list(wrapper);

            // 填充数据行
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            int rowNum = 1;
            for (OperationLogEntity log : logs) {
                Row row = sheet.createRow(rowNum++);
                int colNum = 0;

                // 基础信息
                row.createCell(colNum++).setCellValue(log.getOperationTime().format(formatter));
                row.createCell(colNum++).setCellValue(log.getDescription());
                row.createCell(colNum++).setCellValue(log.getOperationType());
                row.createCell(colNum++).setCellValue(log.getExecutionTime());

                // 用户信息
                row.createCell(colNum++).setCellValue(log.getUsername());
                row.createCell(colNum++).setCellValue(log.getRealName());
                row.createCell(colNum++).setCellValue(log.getDeptName());
                row.createCell(colNum++).setCellValue(log.getIp());

                // 请求信息
                row.createCell(colNum++).setCellValue(log.getRequestMethod());
                row.createCell(colNum++).setCellValue(log.getRequestUrl());
                row.createCell(colNum++).setCellValue(log.getRequestParams());
                row.createCell(colNum++).setCellValue(log.getContentType());

                // 响应信息
                row.createCell(colNum++).setCellValue(log.getResponseStatus());

                // 客户端信息
                row.createCell(colNum++).setCellValue(log.getBrowserName());
                row.createCell(colNum++).setCellValue(log.getOsName());
                row.createCell(colNum++).setCellValue(log.getDeviceType());

                // 业务信息
                row.createCell(colNum++).setCellValue(log.getBusinessModule());
                row.createCell(colNum++).setCellValue(log.getBusinessType());
                row.createCell(colNum++).setCellValue(log.getBusinessId());
            }

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();

        } catch (Exception e) {
            log.error("导出日志失败", e);
            throw new RuntimeException("导出日志失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveLogAsync(OperationLogEntity logEntity) {
        try {
            if (log == null) {
                return;
            }
            this.save(logEntity);
        } catch (Exception e) {
            log.error("异步保存操作日志失败", e);
            throw new RuntimeException("异步保存操作日志失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchAsync(List<OperationLogEntity> logs) {
        try {
            if (logs == null || logs.isEmpty()) {
                return;
            }
            this.saveBatchAsync(logs);
        } catch (Exception e) {
            log.error("异步批量保存操作日志失败", e);
            throw new RuntimeException("异步批量保存操作日志失败", e);
        }
    }
}