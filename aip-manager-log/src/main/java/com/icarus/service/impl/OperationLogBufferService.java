package com.icarus.service.impl;

import com.icarus.entity.OperationLogEntity;
import com.icarus.service.OperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

@Slf4j
@Service
public class OperationLogBufferService {

    private final BlockingQueue<OperationLogEntity> logBuffer = new LinkedBlockingQueue<>(1000);
    private final OperationLogService logService;

    public OperationLogBufferService(OperationLogService logService) {
        this.logService = logService;
    }

    public void addLog(OperationLogEntity log) {
        if (!logBuffer.offer(log)) {
            // 缓冲区满时直接写入数据库
            logService.saveLogAsync(log);
        }
    }

    @Scheduled(fixedRate = 5000) // 每5秒批量处理一次
    public void processBatch() {
        List<OperationLogEntity> logs = new ArrayList<>();
        logBuffer.drainTo(logs, 100); // 每次最多处理100条

        if (!logs.isEmpty()) {
            try {
                logService.saveBatchAsync(logs);
            } catch (Exception e) {
                log.error("批量保存操作日志失败", e);
            }
        }
    }
}