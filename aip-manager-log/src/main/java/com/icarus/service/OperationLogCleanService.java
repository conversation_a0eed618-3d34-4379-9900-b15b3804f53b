package com.icarus.service;

import com.icarus.config.OperationLogProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Slf4j
@Service
public class OperationLogCleanService {

    private final JdbcTemplate jdbcTemplate;
    private final OperationLogProperties properties;

    public OperationLogCleanService(JdbcTemplate jdbcTemplate, OperationLogProperties properties) {
        this.jdbcTemplate = jdbcTemplate;
        this.properties = properties;
    }

    /**
     * 定时清理过期日志
     */
    @Scheduled(cron = "${operation.log.clean-cron}")
    @Transactional
    public void scheduleClean() {
        if (!properties.getEnableScheduleClean()) {
            return;
        }
        try {
            cleanExpiredLogs();
            cleanExcessLogs();
        } catch (Exception e) {
            log.error("清理操作日志失败", e);
        }
    }

    /**
     * 清理过期日志
     */
    @Transactional
    public void cleanExpiredLogs() {
        LocalDateTime expireTime = LocalDateTime.now().minusDays(properties.getRetentionDays());
        String sql = "DELETE FROM operation_log WHERE operation_time < ?";
        int count = jdbcTemplate.update(sql, expireTime);
        log.info("已清理{}条过期操作日志", count);
    }

    /**
     * 清理超出数量限制的日志
     */
    @Transactional
    public void cleanExcessLogs() {
        String countSql = "SELECT COUNT(1) FROM operation_log";
        Long total = jdbcTemplate.queryForObject(countSql, Long.class);
        if (total == null || total <= properties.getMaxRecords()) {
            return;
        }

        long needToDelete = total - properties.getMaxRecords();
        String sql = "DELETE FROM operation_log WHERE id IN " +
                "(SELECT id FROM operation_log ORDER BY operation_time ASC LIMIT ?)";
        int count = jdbcTemplate.update(sql, needToDelete);
        log.info("已清理{}条超量操作日志", count);
    }
}