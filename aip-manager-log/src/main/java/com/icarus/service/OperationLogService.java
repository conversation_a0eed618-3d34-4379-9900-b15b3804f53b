package com.icarus.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.icarus.entity.OperationLogEntity;
import org.springframework.scheduling.annotation.Async;

import java.time.LocalDateTime;
import java.util.List;

public interface OperationLogService extends IService<OperationLogEntity> {

    /**
     * 查询日志
     */
    Page<OperationLogEntity> queryLogs(String operationType,
                                       String description,
                                       LocalDateTime startTime,
                                       LocalDateTime endTime,
                                       Page<OperationLogEntity> page);

    /**
     * 删除指定日期之前的日志
     */
    void deleteLogsBefore(LocalDateTime time);

    /**
     * 导出日志
     */
    byte[] exportLogs(String operationType,
                      String description,
                      LocalDateTime startTime,
                      LocalDateTime endTime);

    /**
     * 异步保存日志
     */
    @Async("operationLogExecutor")
    void saveLogAsync(OperationLogEntity log);

    /**
     * 异步批量保存日志
     */
    @Async("operationLogExecutor")
    void saveBatchAsync(List<OperationLogEntity> logs);
}