package com.icarus.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Slf4j
@Service
public class OperationLogTableService {

    private final JdbcTemplate jdbcTemplate;
    private static final String TABLE_TEMPLATE = "operation_log_%s";

    public OperationLogTableService(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * 创建新的日志表
     */
    @Transactional
    public void createLogTable(LocalDateTime dateTime) {
        String tableName = getTableName(dateTime);
        String sql = String.format(
                "CREATE TABLE IF NOT EXISTS %s LIKE operation_log",
                tableName
        );
        jdbcTemplate.execute(sql);
    }

    /**
     * 获取日志表名
     */
    public String getTableName(LocalDateTime dateTime) {
        return String.format(
                TABLE_TEMPLATE,
                dateTime.format(DateTimeFormatter.ofPattern("yyyyMM"))
        );
    }
}