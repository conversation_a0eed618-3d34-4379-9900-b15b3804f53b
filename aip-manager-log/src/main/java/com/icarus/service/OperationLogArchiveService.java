package com.icarus.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Slf4j
@Service
public class OperationLogArchiveService {

    private  JdbcTemplate jdbcTemplate;

    private static final String ARCHIVE_TABLE_PREFIX = "operation_log_archive_";

    @Scheduled(cron = "0 0 1 1 * ?") // 每月1号凌晨1点执行
    public void archiveLastMonthLogs() {
        LocalDateTime lastMonth = LocalDateTime.now().minusMonths(1);
        String archiveTable = ARCHIVE_TABLE_PREFIX +
                lastMonth.format(DateTimeFormatter.ofPattern("yyyyMM"));

        // 创建归档表
        createArchiveTable(archiveTable);

        // 移动数据
        moveDataToArchive(archiveTable, lastMonth);

        // 压缩归档表
        compressArchiveTable(archiveTable);
    }

    private void createArchiveTable(String tableName) {
        String sql = String.format("CREATE TABLE IF NOT EXISTS %s LIKE operation_log", tableName);
        jdbcTemplate.execute(sql);
    }

    private void moveDataToArchive(String archiveTable, LocalDateTime monthStart) {
        String sql = String.format(
                "INSERT INTO %s SELECT * FROM operation_log WHERE operation_time >= ? AND operation_time < ?",
                archiveTable
        );
        jdbcTemplate.update(sql,
                monthStart.withDayOfMonth(1),
                monthStart.plusMonths(1).withDayOfMonth(1)
        );

        // 删除已归档的数据
        jdbcTemplate.update(
                "DELETE FROM operation_log WHERE operation_time >= ? AND operation_time < ?",
                monthStart.withDayOfMonth(1),
                monthStart.plusMonths(1).withDayOfMonth(1)
        );
    }

    private void compressArchiveTable(String tableName) {
        String sql = String.format("ALTER TABLE %s ROW_FORMAT=COMPRESSED", tableName);
        jdbcTemplate.execute(sql);
    }
}